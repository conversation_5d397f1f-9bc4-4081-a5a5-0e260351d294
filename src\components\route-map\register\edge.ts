import G6 from '@antv/g6'

export const EdgeName = 'flow-cubic'
export const registerEdge = () => {
  G6.registerEdge(
    EdgeName,
    {
      getPath(points) {
        const startPoint = points[0]
        const endPoint = points[1]
        return [
          ['M', startPoint.x, startPoint.y],
          ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, startPoint.y],
          ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, endPoint.y],
          ['L', endPoint.x, endPoint.y]
        ]
      },
      getShapeStyle(cfg) {
        const startPoint = cfg.startPoint
        const endPoint = cfg.endPoint
        const controlPoints = this.getControlPoints(cfg)
        let points = [startPoint] // the start point
        // the control points
        if (controlPoints) {
          points = points.concat(controlPoints)
        }
        // the end point
        points.push(endPoint)
        const path = this.getPath(points)
        const style = Object.assign(
          {},
          G6.Global.defaultEdge.style,
          {
            endArrow: {
              fill: '#009dff',
              path: G6.Arrow.vee(8, 8) // 内置箭头，参数为箭头宽度、长度、偏移量 d（默认为 0）
            },
            stroke: '#009dff',
            path
          },
          cfg.style
        )
        return style
      },

      update: undefined //这里需要重写update不然默认继承line的方法
    },
    'line'
  )
}
