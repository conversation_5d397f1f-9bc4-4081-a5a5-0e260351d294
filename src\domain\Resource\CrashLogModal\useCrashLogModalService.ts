import { useGet } from '@/libs/service.request'
import Config from '@/config'
import { computed, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import { downloadTxt } from '@/libs/util'

export default function useCrashLogModalService(props, emit) {
  const crashLogLine = ref(100)
  const visible = computed({
    get() {
      return props.value ?? {}
    },
    set(value) {
      emit('input', value)
    }
  })
  const {
    data: crashLogData,
    run: getCrashLog,
    loading: crashLogLoading
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetPodCrashLog}`, {
        params: {
          pod: props.entity.podName,
          container: props.entity.container,
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          line: crashLogLine.value
        }
      })
    },
    {
      manual: true,
      initialData: '',
      formatResult: (res) => res.data.data
    }
  )

  const onDownloadCrashLog = () => {
    console.log(1111, crashLogData.value)
    debugger
    downloadTxt(crashLogData.value, `崩溃日志（${props.entity.podName} / ${props.entity.container}）`)
  }

  watch(
    () => [visible.value, props.entity],
    () => {
      visible.value && getCrashLog()
    },
    {
      deep: true
    }
  )

  return {
    visible,
    crashLogData,
    crashLogLine,
    crashLogLoading,
    getCrashLog,
    onDownloadCrashLog
  }
}
