<script lang="ts" setup>
import useWorkloadSubscribeMgtService from './useWorkloadSubscribeMgtService'
import { LinkButton } from '@/components'
const {
  tabKey,
  allDataKeyword,
  getAllData,
  allData,
  allDataLoading,
  subscribeData<PERSON>eyword,
  getSubscribeList,
  subscribeData,
  subscribeDataLoading,
  onUnsubscribe,
  jumpToDetail,
  onSubscribeOrNot,
  allColumns,
  subscribeColumns,
  allDataKind,
  subscribeDataKind
} = useWorkloadSubscribeMgtService()
</script>

<template>
  <div>
    <Tabs :value="tabKey" type="card">
      <TabPane label="订阅应用" name="subscribe">
        <Alert>订阅应用后, 当应用发生变更事件，我们将会以<strong> 飞书 </strong>消息推送给您。</Alert>
        <Input
          placeholder="请输入关键词搜索资源名"
          v-model="subscribeDataKeyword"
          style="margin-bottom: 16px"
          search
          @on-search="getSubscribeList"
        >
          <Select
            v-model="subscribeDataKind"
            @on-change="getSubscribeList"
            slot="prepend"
            style="width: 120px"
            clearable
            placeholder="选择资源种类"
          >
            <Option value="Deployment">Deployment</Option>
            <Option value="StatefulSet">StatefulSet</Option>
          </Select>
        </Input>
        <Table
          height="186"
          size="small"
          :columns="subscribeColumns"
          :data="subscribeData"
          :loading="subscribeDataLoading"
          tooltip-theme="light"
        >
          <template #name="{ row }">
            <LinkButton :text="row.name" @click="() => jumpToDetail(row)" ellipsis tooltip />
          </template>
          <template #ops="{ row }">
            <LinkButton text="退订" @click="() => onUnsubscribe(row)" />
          </template>
        </Table>
      </TabPane>
      <TabPane label="所有应用" name="all">
        <Alert>可以检索所有授权的应用，如果没有请查看 K8s 资源是否授权。 </Alert>
        <Input
          placeholder="请输入关键词搜索资源名"
          v-model="allDataKeyword"
          style="margin-bottom: 16px"
          search
          @on-search="getAllData"
        >
          <Select
            v-model="allDataKind"
            @on-change="getAllData"
            slot="prepend"
            style="width: 120px"
            clearable
            placeholder="选择资源种类"
          >
            <Option value="Deployment">Deployment</Option>
            <Option value="StatefulSet">StatefulSet</Option>
          </Select>
        </Input>

        <Table
          height="186"
          :columns="allColumns"
          size="small"
          :data="allData"
          :loading="allDataLoading"
          tooltip-theme="light"
        >
          <template #name="{ row }">
            <LinkButton :text="row.name" @click="() => jumpToDetail(row)" ellipsis tooltip />
          </template>

          <template #ops="{ row }">
            <LinkButton @click="() => onSubscribeOrNot(row)" :text="row.subscribeStatus ? '退订' : '订阅'" />
          </template>
        </Table>
      </TabPane>
    </Tabs>
  </div>
</template>

<style lang="less" scoped></style>
