import type { RequestConfig } from './type'

export const Config: RequestConfig = {
  /** 请求超时时间 */
  timeout: 30000,
  /** 跳过全屏loading */
  skipLoading: false,
  /** 跳过默认的错误处理，用于项目中部分特殊的接口 */
  skipErrorHandler: false,
  /** 错误提示类型 */
  errorShowType: 'NOTIFICATION',
  /** 是否启用本地日志功能 */
  enableLog: true
}

/** 全局配置注入逻辑 */
export const config = (map: RequestConfig) => {
  Object.entries(map).forEach(([key, val]) => {
    Config[key] = val
  })
}

export default Config
