<script lang="ts" setup>
import { SubSiteHeader } from '@/components'
</script>

<template>
  <div>
    <SubSiteHeader>
      <template #left>
        <slot name="header-left" />
      </template>
      <template #right>
        <slot name="header-right" />
      </template>
    </SubSiteHeader>
    <div class="container-wrapper">
      <slot name="default" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.container-wrapper {
  padding: 16px;
  //   height: ~'calc(100vh - 48px * 2)';
  margin-top: 46px;
}
</style>
