<script lang="ts" setup>
import { Container, Space } from '@/components'
import ProTable from '@/components/pro-table'
import { TABLE_COLUMNS } from './setting'
import useNodeMgtService from './useNodeMgtService'
import LinkButton from '@/components/link-button'
import PodListDialog from './dialog/pod-list.vue'
import MoreActionDialog from './dialog/more-action.vue'

const {
  clusterId,
  clusterList,
  getTableData,
  refObject,
  onOpenPodListModal,
  podListDialog,
  moreActionDialog,
  onOpenMoreActionModal,
  jumpToMonitor,
  onClusterChange,
  keyword,
  onSearch
} = useNodeMgtService()
</script>

<template>
  <Container>
    <Alert show-icon> 选择需要操作的集群进行节点的管理，如 Pod 驱逐，禁止调度等操作。 </Alert>
    <Space class="search-wrapper">
      <Select
        v-model="clusterId"
        :label-in-value="true"
        class="search-item"
        filterable
        placeholder="请选择集群"
        @on-change="onClusterChange"
      >
        <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>

      <Input v-model="keyword" class="search-item" clearable placeholder="请输入名称" />

      <Button type="primary" icon="ios-search" @click="onSearch">搜索</Button>
    </Space>
    <pro-table
      :columns="TABLE_COLUMNS"
      manual-request
      :pagination="false"
      :height="580"
      :request="getTableData"
      :action-ref="refObject"
      row-key="uid"
      :hideSearchOperation="true"
    >
      <template #name="{ row }">
        <div class="node-name-wrapper">
          <span>{{ row.name }}</span>
          <Tooltip content="查看监控, 若出现404请联系SRE添加权限" theme="light" placement="top" transfer>
            <img src="@/assets/grafana.svg" @click="() => jumpToMonitor(row)" />
          </Tooltip>
        </div>
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onOpenPodListModal(row)" text="容器组" />
          <link-button @click="() => onOpenMoreActionModal(row)" text="更多操作" />
        </space>
      </template>
    </pro-table>

    <!-- Pods列表弹窗 -->
    <PodListDialog v-model="podListDialog.visible" :currentNode="podListDialog.node" :currentClusterId="clusterId" />

    <!-- 更多操作弹窗 -->
    <MoreActionDialog
      v-model="moreActionDialog.visible"
      :currentNode="moreActionDialog.node"
      :currentClusterId="clusterId"
    />
  </Container>
</template>

<style lang="less" scoped>
.header {
  margin-bottom: 16px;
}
.search-wrapper {
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  align-items: center;
  &:hover {
    box-shadow: 0px 0px 4px 0px #ddd;
  }

  .search-item {
    width: 240px;
  }
}
.enter-btn {
  cursor: pointer;
  color: #2a7cc3;
  white-space: nowrap;
  align-items: center;
}
.node-name-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  img {
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
}
</style>
