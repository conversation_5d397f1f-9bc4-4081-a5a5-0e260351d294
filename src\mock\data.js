import Mock from 'mockjs'
import { doCustomTimes } from '@/libs/util'
import orgData from './data/org-data'
import { treeData } from './data/tree-select'
const Random = Mock.Random

export const getTableData = (req) => {
  let tableData = []
  doCustomTimes(5, () => {
    tableData.push(
      Mock.mock({
        name: '@name',
        email: '@email',
        createTime: '@date'
      })
    )
  })
  return tableData
}

export const uploadImage = (req) => {
  return Promise.resolve()
}

export const getOrgData = (req) => {
  return orgData
}

export const getTreeSelectData = (req) => {
  return treeData
}
