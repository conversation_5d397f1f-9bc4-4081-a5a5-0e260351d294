import dayjs from 'dayjs'

export interface ChartData {
  type: string
  date: string
  value: number
}
export interface MonitorData {
  metric: Record<string, string>

  values: [number, string][]
}

export const formatMonitorDataToChartData = (
  data: MonitorData[],
  formatValue?: (value: string) => number
): ChartData[] => {
  const chartDataList: ChartData[] = []
  data?.map((monitor) => {
    monitor.values?.map((i) => {
      const [secondsTimestamp, value] = i
      const _value = value === 'NaN' ? '0' : value
      const chartData: ChartData = {
        type: Object.values(monitor.metric)?.[0] ?? 'stdvar', // stdvar 唯一，接口不传
        date: dayjs(secondsTimestamp * 1000).format('YYYY-MM-DD HH:mm:ss'),
        value: formatValue ? formatValue(_value) : Number(Number(_value).toFixed(4))
      }
      chartDataList.push(chartData)
    })
  })

  return chartDataList
}
