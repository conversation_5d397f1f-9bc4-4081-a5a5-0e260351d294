<script setup lang="ts">
import { ref, watch, onMounted, PropType } from 'vue'
import { EventTable } from '@/domain/Resource'
import { getRelativeTime } from '@/libs/tools'
import { useRoute } from '@/libs/useVueInstance'
import { useRequest } from 'vue-request'
import { useGet } from '@/libs/service.request'
import Config from '@/config'

interface UrlParams {
  clusterId: string
  cluster: string
  namespace: string
  name: string
  uuid: String
}

const props = defineProps({
  type: {
    type: String as PropType<'deployment' | 'statefulset'>,
    default: 'deployment'
  },
  // 变化则刷新
  reloadFlag: Boolean
})

const route = useRoute()

const urlParams = ref<UrlParams>({} as UrlParams)

const spinShow = ref(false)

const tableColumns = ref([
  {
    title: 'Type',
    key: 'type',
    tooltip: true,
    width: 100
  },
  {
    title: 'Status',
    key: 'status',
    tooltip: true,
    width: 100
  },
  {
    title: 'Message',
    key: 'message',
    tooltip: true
  },
  {
    title: 'Reason',
    key: 'reason',
    width: 200,
    tooltip: true
  },
  {
    title: 'lastUpdateTime',
    key: 'lastUpdateTime',
    tooltip: true,
    width: 130,
    align: 'center',
    render: (h, params) => {
      // return h('div', {}, TimeTrans(params.row.lastUpdateTime))
      let d = new Date(params.row.lastUpdateTime)
      return h('div', {}, getRelativeTime(d.getTime()))
    }
  }
])
const tableData = ref([])

watch(
  () => props.reloadFlag,
  () => {
    console.log('触发 Deployment 更新')

    fetchDeploymentJson()
    spinShow.value = true
    setTimeout(() => {
      spinShow.value = false
    }, 1000)
  }
)

const { data: currentDeployJson, run: fetchDeploymentJson } = useRequest(
  () => {
    return useGet(
      `${Config.Api.Base}${props.type === 'deployment' ? Config.Api.GetDeploymentMeta : Config.Api.GetStatefulsetMeta}`,
      {
        params: {
          namespace: urlParams.value.namespace,
          ...(props.type === 'deployment'
            ? { cluster_id: urlParams.value.clusterId, deployment: urlParams.value.name }
            : { clusterId: urlParams.value.clusterId, name: urlParams.value.name })
        }
      }
    )
  },
  {
    manual: true,
    formatResult: (res) => {
      const data = res.data.data ? JSON.parse(res.data.data) : {}
      tableData.value = data?.status?.conditions
      return data
    },
    initialData: {
      metadata: {
        annotations: {},
        labels: {}
      },
      spec: {
        selector: {
          matchLabels: {}
        }
      }
    }
  }
)

onMounted(() => {
  urlParams.value = {
    clusterId: route.query.clusterId as string,
    cluster: route.query.cluster as string,
    namespace: route.query.namespace as string,
    name: (props.type === 'deployment' ? route.query.deployment : route.query.name) as string,
    uuid: route.query.uuid as string
  }

  fetchDeploymentJson()
})
</script>

<template>
  <div>
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <Card>
      <h5>基本信息</h5>
      <Row style="margin-top: 16px">
        <Col :span="4" class="info-key">Name</Col>
        <Col :span="18" class="info-value"
          ><b style="color: #000c17">{{ urlParams.name }}</b></Col
        >
      </Row>
      <Row style="margin-top: 16px">
        <Col :span="4" class="info-key">Namespace</Col>
        <Col :span="18" class="info-value"> {{ urlParams.namespace }}</Col>
      </Row>
      <Row style="margin-top: 16px">
        <Col :span="4" class="info-key">Cluster</Col>
        <Col :span="18" class="info-value">{{ urlParams.cluster }}</Col>
      </Row>
      <Row style="margin-top: 16px">
        <Col :span="4" class="info-key">注解（Annotation）</Col>
        <Col :span="18">
          <Row v-for="(value, key) in currentDeployJson.metadata.annotations" :key="key">
            <Col :span="24">
              <Tag color="blue"
                ><b>{{ key }} :</b> {{ value }}
              </Tag>
            </Col>
          </Row>
        </Col>
      </Row>
      <Row style="margin-top: 15px">
        <Col :span="4" class="info-key">标签（Label）</Col>
        <Col :span="18">
          <Row v-for="(value, key) in currentDeployJson.metadata.labels" :key="key">
            <Col :span="24">
              <Tag color="blue"
                ><b>{{ key }} :</b> {{ value }}</Tag
              >
            </Col>
          </Row>
        </Col>
      </Row>
      <Row style="margin-top: 15px">
        <Col :span="4" class="info-key">Selector</Col>
        <Col :span="18">
          <Row v-for="(value, key) in currentDeployJson.spec.selector.matchLabels" :key="key">
            <Col :span="24">
              <Tag color="blue"
                ><b>{{ key }} :</b> {{ value }}</Tag
              >
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px" v-if="props.type === 'deployment'">
      <h5>运行时信息</h5>
      <Table style="margin-top: 16px" :columns="tableColumns" :data="tableData" show-total size="small"></Table>
    </Card>
    <Card style="margin-top: 16px">
      <EventTable :uuid="urlParams.uuid" :clusterId="urlParams.clusterId" mini-title hide-refresh />
    </Card>
  </div>
</template>

<style scoped></style>
