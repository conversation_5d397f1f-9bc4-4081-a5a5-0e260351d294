<script lang="ts" setup>
import { PropType } from 'vue'
import { LinkButton, Space, Ellipsis, Yaml } from '@/components'
import { PortForwardModal } from '@/domain/PortForward'
import {
  PodAnalyzer,
  EventTable,
  CrashLogModal,
  ContainerFileDownload,
  EphemeralContainerModal
} from '@/domain/Resource'
import usePodCardListService from './usePodCardListService'
import { color } from '@/libs/consts'
import { PORT_COLUMNS } from './setting'
import { TimeTrans, formatMetricCPU, formatMetricMemory } from '@/libs/tools'
import { getPodStatusColor } from '@/domain/Resource/Pod/setting'

const props = defineProps({
  value: Object,
  kind: String as PropType<'Deployment' | 'StatefulSet' | 'Rayjob'>,
  entity: Object as PropType<{
    clusterId: string
    cluster: string
    namespace: string
    name: string
  }>
})
const emit = defineEmits(['input'])

const {
  currentPod,
  podList,
  getPodList,
  podListLoading,
  openAnalyzer,
  handleOpenAnalyzer,
  analyzeData,
  analyzeLoading,
  onRebuildPod,
  onChoosePod,
  onOpenLog,
  containerMetrics,
  containerMetricsLoading,
  getContainerMetrics,

  onBashClick,
  handleProxyOpenLog,
  openCrashLog,
  crashLogModalVisible,
  crashLogModalEntity,
  onFileModalOpen,
  fileEntity,
  fileModalVisible,
  handleOpenEphemeralContainerModal,
  ephemeralContainerEntity,
  ephemeralContainerModalVisible,
  podYaml,
  getPodYaml,
  podYamlVisible,
  onOpenPortForwardModal,
  portForwardModalVisible,
  portForwardEntity,
  onFilterPodName,
  filterPodName
} = usePodCardListService(props, emit)
</script>

<template>
  <div class="wrapper">
    <div class="left">
      <div class="card-wrapper">
        <div class="card-header">
          <span>Pod (容器组)</span>
          <span class="card-header-operation">
            <Input
              search
              placeholder="请搜索pod 名称"
              clearable
              v-model="filterPodName"
              @on-search="() => onFilterPodName()"
              @on-clear="() => onFilterPodName()"
              style="margin-right: 8px"
            />
            <LinkButton text="刷新" @click="getPodList" />
          </span>
        </div>
        <div class="card-content">
          <Spin v-if="podListLoading" fix />
          <div
            v-for="item in podList"
            :key="item.name"
            :class="`item-wrapper ${currentPod.name === item.name ? 'active' : ''}`"
            @click="() => onChoosePod(item)"
          >
            <Row type="flex" align="middle">
              <Col :span="16">
                <div class="line-ellipsis" :title="item.name">
                  <b>{{ item.name }}</b>
                </div>
              </Col>
              <Col :span="8" v-if="kind !== 'Rayjob'">
                <Button
                  @click="onRebuildPod(item.name)"
                  size="small"
                  ghost
                  type="error"
                  style="float: right; margin-left: 16px"
                  >重建</Button
                >
                <Button @click="handleOpenAnalyzer(item.name)" size="small" ghost type="primary" style="float: right"
                  >诊断</Button
                >
              </Col>
            </Row>
            <Row style="margin-top: 16px" type="flex" :wrap="false">
              <Col flex="16">
                <Space>
                  <span> {{ item.age ?? '-' }}</span>
                  <span>
                    <b style="background-color: #2a7cc3; color: #f9f9f9">P</b>
                    <span> {{ item.podIp ?? '-' }}</span>
                  </span>
                  <span>
                    <b style="background-color: #808695; color: #f9f9f9">H</b>
                    <span> {{ item.nodeIp ?? '-' }}</span>
                  </span>
                </Space>
              </Col>
              <Col flex="8" v-if="kind !== 'Rayjob'">
                <Button
                  @click="onOpenPortForwardModal(item.name)"
                  size="small"
                  ghost
                  type="primary"
                  style="float: right"
                  >端口转发</Button
                >
              </Col>
            </Row>
            <Row style="margin-top: 16px" type="flex" :wrap="false">
              <Space :size="4">
                <b class="line-ellipsis" :style="`color: ${getPodStatusColor(item.phase)};`" :title="item.phase">{{
                  item.phase
                }}</b>
                <span>|</span>
                <span style="color: '#a3a3a3'">{{ item.reason ? item.reason : '正常运转' }}</span>
              </Space>
            </Row>
            <Row style="margin-top: 16px" type="flex" :wrap="false">
              <Col flex="auto">
                <span>CPU / Mem（Usage）：</span>
                <span>
                  {{ `${formatMetricCPU(item.metrics?.cpuUsage)} / ${formatMetricMemory(item.metrics?.memUsage)}` }}
                </span>
              </Col>
            </Row>
          </div>
        </div>
        <pod-analyzer v-model="openAnalyzer" :analyze-data="analyzeData" :loading="analyzeLoading" />
      </div>
    </div>
    <div class="right">
      <Card style="margin-bottom: 16px">
        <EventTable :uuid="currentPod?.uuid" :clusterId="props.entity.clusterId" hide-refresh />
      </Card>

      <div class="card-wrapper">
        <Spin v-if="podListLoading" fix />
        <div class="card-header container">
          <span class="header-title"
            >[{{ currentPod.containers?.length ?? 0 + currentPod.initContainers?.length ?? 0 }}] 容器组
            {{ currentPod.name }}</span
          >
          <Space style="width: auto">
            <LinkButton text="刷新" @click="() => getPodList(true)" />
            <LinkButton text="YAML" @click="() => getPodYaml()" />
          </Space>
        </div>
        <div class="card-content container">
          <Space direction="vertical">
            <Row :gutter="16">
              <Col :span="8">
                <div class="info-key">所在节点</div>
                <div>{{ currentPod.nodeIp ?? '-' }}</div>
              </Col>
              <Col :span="8">
                <div class="info-key">容器IP</div>
                <div>{{ currentPod.podIp ?? '-' }}</div>
              </Col>
              <Col :span="8">
                <div class="info-key">状态</div>
                <div
                  class="line-ellipsis"
                  :style="`color: ${getPodStatusColor(currentPod.phase)}`"
                  :title="currentPod.phase"
                >
                  {{ currentPod.phase }}
                </div>
              </Col>
            </Row>
            <Row :gutter="16">
              <Col :span="12">
                <Card :padding="5">
                  <Icon
                    v-if="currentPod.conditions?.PodScheduled?.status === 'True'"
                    type="md-checkmark-circle"
                    style="color: #19be6b"
                  />
                  <Icon v-else type="md-close-circle" style="color: #ed4014" />
                  <span style="margin-left: 8px; font-size: 12px">已调度</span>
                  <span style="float: right">
                    {{ currentPod.conditions?.PodScheduled?.lastTransitionTime ?? '-' }}</span
                  >
                </Card>
              </Col>
              <Col :span="12">
                <Card :padding="5">
                  <Icon
                    v-if="currentPod.conditions?.Initialized?.status === 'True'"
                    type="md-checkmark-circle"
                    style="color: #19be6b"
                  />
                  <Icon v-else type="md-close-circle" style="color: #ed4014" />
                  <span style="margin-left: 8px; font-size: 12px">已初始化</span>
                  <span style="float: right"> {{ currentPod.conditions?.Initialized?.lastTransitionTime ?? '-' }}</span>
                </Card>
              </Col>
            </Row>
            <Row :gutter="16">
              <Col :span="12">
                <Card :padding="5">
                  <Icon
                    v-if="currentPod.conditions?.Ready?.status === 'True'"
                    type="md-checkmark-circle"
                    style="color: #19be6b"
                  />
                  <Icon v-else type="md-close-circle" style="color: #ed4014" />
                  <span style="margin-left: 8px; font-size: 12px">容器已就绪</span>
                  <span style="float: right"> {{ currentPod.conditions?.Ready?.lastTransitionTime ?? '-' }}</span>
                </Card>
              </Col>
              <Col :span="12">
                <Card :padding="5">
                  <Icon
                    v-if="currentPod.conditions?.ContainersReady?.status === 'True'"
                    type="md-checkmark-circle"
                    style="color: #19be6b"
                  />
                  <Icon v-else type="md-close-circle" style="color: #ed4014" />
                  <span style="margin-left: 8px; font-size: 12px">容器组已就绪</span>
                  <span style="float: right">
                    {{ currentPod.conditions?.ContainersReady?.lastTransitionTime ?? '-' }}</span
                  >
                </Card>
              </Col>
            </Row>
            <Card v-for="item in currentPod.initContainers" :key="item.name">
              <div class="container-item-wrapper">
                <div class="icon">
                  <img src="@/assets/docker.svg" style="height: 22px" />
                </div>
                <Space class="detail" direction="vertical" :size="8">
                  <Space style="justify-content: space-between">
                    <Space>
                      <Tag color="blue">初始化容器</Tag>
                      <Ellipsis style="font-weight: 600">{{ item.name }}</Ellipsis>
                    </Space>

                    <Tag v-if="item.state === 'running'" color="success">{{ item.state }}</Tag>
                    <Tag v-else>{{ item.state }}</Tag>
                  </Space>
                  <div class="desc">镜像: {{ item.image }}</div>

                  <Space :size="8">
                    <span class="desc">ExitCode：{{ item?.exitCode ? item.exitCode : '-' }}</span>
                    <span class="desc">Reason：{{ item?.reason ? item.reason : '-' }}</span></Space
                  >
                  <Space>
                    <span class="time-tag"> startedAt：{{ item.startedAt ? TimeTrans(item.startedAt) : '-' }}</span>
                    <span class="time-tag">finishedAt：{{ item.finishedAt ? TimeTrans(item.finishedAt) : '-' }}</span>
                  </Space>
                  <Button type="primary" @click="() => onOpenLog(item.name)" size="small" class="action-button"
                    >日志</Button
                  >
                </Space>
              </div>
            </Card>
            <Card v-for="item in currentPod.containers" :key="item.name">
              <div class="container-item-wrapper">
                <div class="icon">
                  <img src="@/assets/docker.svg" style="height: 22px" />
                </div>
                <Space class="detail" direction="vertical" :size="8">
                  <Space style="justify-content: space-between">
                    <Space style="width: auto">
                      <Tag color="primary">容器</Tag>
                      <Ellipsis style="font-weight: 600">{{ item.name }}</Ellipsis>
                    </Space>
                    <Space style="width: auto">
                      <Poptip trigger="hover" title="资源 需求 / 限制" width="300" placement="left">
                        <Button ghost size="small" type="warning" style="padding: 0" icon="md-alert" />
                        <div slot="content">
                          <Row>
                            <Col :span="6">
                              <b>CPU</b>
                            </Col>
                            <Col :span="7">
                              <b>{{ formatMetricCPU(item.metrics.cpuRequest) }}</b>
                            </Col>
                            <Col v-if="item.metrics.cpuLimit" :span="11">
                              <Col :span="4">
                                <b>--></b>
                              </Col>
                              <Col :span="7">
                                <b>{{ formatMetricCPU(item.metrics.cpuLimit) }}</b>
                              </Col>
                            </Col>
                          </Row>
                          <Row>
                            <Col :span="6">
                              <b>内存</b>
                            </Col>
                            <Col :span="7">
                              <b>{{ formatMetricMemory(item.metrics.memRequest) }}</b>
                            </Col>
                            <Col v-if="item.metrics.memLimit" :span="11">
                              <b>--></b>
                              <b>{{ formatMetricMemory(item.metrics.memLimit) }}</b>
                            </Col>
                          </Row>
                        </div>
                      </Poptip>
                      <Tag v-if="item.state === 'running'" color="success">{{ item.state }}</Tag>
                      <Tag v-else>{{ item.state }}</Tag>
                    </Space>
                  </Space>
                  <Space>
                    <Space v-if="containerMetrics[item.name]" style="width: auto">
                      <span style="color: #808695"
                        >CPU
                        <b style="color: #515a6e; font-size: 14px">{{
                          formatMetricCPU(containerMetrics[item.name].cpuUsage)
                        }}</b>
                      </span>
                      <span style="color: #808695"
                        >内存
                        <b style="color: #515a6e; font-size: 14px">{{
                          formatMetricMemory(containerMetrics[item.name].memUsage)
                        }}</b>
                      </span>
                      <span style="color: #515a6e">{{ TimeTrans(containerMetrics[item.name].timestamp * 1000) }}</span>
                    </Space>

                    <span v-else style="color: #ed4014; font-size: 12px"> Not found metrics-server </span>

                    <Button
                      :key="item.name"
                      :loading="containerMetricsLoading"
                      shape="circle"
                      size="small"
                      icon="ios-refresh"
                      @click="getContainerMetrics"
                    >
                      刷新
                    </Button>
                  </Space>
                  <div class="desc">镜像: {{ item.image }}</div>
                  <Space :size="8">
                    <span class="desc">ExitCode：{{ item?.exitCode ? item.exitCode : '-' }}</span>
                    <span class="desc">Reason：{{ item?.reason ? item.reason : '-' }}</span></Space
                  >
                  <Space :size="8">
                    <Tag :color="item.started ? 'green' : 'red'">
                      <Icon
                        :type="item.started ? 'md-checkmark-circle' : 'md-close-circle'"
                        :class="item.started ? 'status-work' : 'status-not-work'"
                      />
                      Started
                    </Tag>

                    <Tag :color="item.ready ? 'green' : 'red'">
                      <Icon
                        :type="item.ready ? 'md-checkmark-circle' : 'md-close-circle'"
                        :class="item.ready ? 'status-work' : 'status-not-work'"
                      />
                      Ready
                    </Tag>

                    <Tag>
                      <Icon type="md-refresh" style="font-size: 12px" />
                      RestartCount: {{ item.restartCount }}
                    </Tag>
                  </Space>

                  <Space>
                    <span class="time-tag">startedAt：{{ item.startedAt ? TimeTrans(item.startedAt) : '-' }}</span>
                    <span class="time-tag">finishedAt：{{ item.finishedAt ? TimeTrans(item.finishedAt) : '-' }}</span>
                  </Space>
                  <Space>
                    <Poptip placement="top-start" width="450">
                      <Button size="small" type="warning" class="action-button">端口 </Button>
                      <div slot="content">
                        <Table size="small" :columns="PORT_COLUMNS" :data="item.ports"></Table>
                      </div>
                    </Poptip>

                    <Button type="error" size="small" class="action-button" @click="() => onBashClick(item.name)">
                      Shell
                    </Button>

                    <Button type="primary" size="small" @click="() => onOpenLog(item.name)" class="action-button"
                      >日志
                    </Button>
                    <Dropdown
                      v-if="item.name === 'istio-proxy'"
                      trigger="click"
                      @on-click="
                        (logLevel) => {
                          handleProxyOpenLog(item.name, logLevel)
                        }
                      "
                      placement="top"
                    >
                      <Button type="primary" size="small" class="action-button"
                        >日志级别
                        <Icon type="ios-arrow-down"></Icon>
                      </Button>
                      <DropdownMenu slot="list">
                        <DropdownItem name="info" style="">level info</DropdownItem>
                        <DropdownItem name="debug" style="">level debug</DropdownItem>
                        <DropdownItem name="warning" style="">level warning</DropdownItem>
                      </DropdownMenu>
                    </Dropdown>

                    <Button
                      v-if="item.restartCount !== 0"
                      type="primary"
                      size="small"
                      @click="() => openCrashLog(item.name)"
                      class="action-button"
                      >崩溃日志
                    </Button>
                    <Button
                      v-if="item.name !== 'istio-proxy'"
                      type="primary"
                      size="small"
                      @click="() => onFileModalOpen(item.name)"
                      class="action-button"
                      >上传下载
                    </Button>
                    <Button
                      class="action-button"
                      type="primary"
                      size="small"
                      @click="() => handleOpenEphemeralContainerModal(item.name)"
                      >调试模式
                    </Button>
                  </Space>
                </Space>
              </div>
            </Card>
          </Space>
        </div>
      </div>
    </div>
    <Drawer title="查看 YAML" width="60" v-model="podYamlVisible" :mask-closable="false">
      <yaml v-model="podYaml"></yaml>
    </Drawer>
    <CrashLogModal v-model="crashLogModalVisible" :entity="crashLogModalEntity" />
    <ContainerFileDownload :entity="fileEntity" v-model="fileModalVisible" />
    <EphemeralContainerModal v-model="ephemeralContainerModalVisible" :entity="ephemeralContainerEntity" />
    <PortForwardModal v-model="portForwardModalVisible" :entity="portForwardEntity" type="AssociatedStatefulset" />
  </div>
</template>

<style lang="less" scoped>
.wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .left {
    width: 40%;
  }
  .right {
    width: ~'calc(60% - 16px)';
  }
  .card-wrapper {
    position: relative;
    border: 1px solid #e8eaec;

    border-radius: 8px;
    display: flex;
    flex-direction: column;

    &:hover {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
      border-color: #eee;
    }

    .card-header {
      border-radius: 8px 8px 0 0;
      position: relative;
      height: 100%;
      padding: 8px 18px;
      white-space: nowrap;
      overflow: hidden;
      background-color: #f8f8f9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;

      &.container {
        background-color: antiquewhite;
        .header-title {
          font-size: 14px;
        }
      }
      > span {
        font-weight: 600;
      }
      .card-header-operation {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
    .card-content {
      height: 100%;
      padding: 0;
      .item-wrapper {
        padding: 16px;
        &:not(:last-child) {
          border-bottom: 1px solid #dcdee2;
        }
        &.active {
          background-color: #bee5ff !important;
        }
      }
      &.container {
        padding: 0 16px 16px 16px;
      }
    }
  }
  .container-item-wrapper {
    display: flex;
    flex-direction: row;
    .desc {
      color: #808695;
      font-size: 12px;
    }
    .icon {
      width: 58px;
    }
    .detail {
      flex: 1 0 0%;
      display: flex;

      > .space-component {
        align-items: center;
      }
      .status-work {
        color: #19be6b;
        font-size: 12px;
      }
      .status-not-work {
        color: #ed4014;
        font-size: 12px;
      }
    }
    .time-tag {
      background-color: #f8f8f9;
    }
    .action-button {
      width: 80px;
      font-weight: 600;
    }
  }
}
</style>
