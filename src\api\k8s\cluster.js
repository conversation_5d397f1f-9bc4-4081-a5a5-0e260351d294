import axios from '@/libs/api.request'

export const ApiClusterListWithoutKubeConfig = (page) => {
  return axios.request({
    url: '/api/v1/cluster-sec/list',
    method: 'get',
    data: {},
    params: page
  })
}

export const ApiClusterList = (page) => {
  return axios.request({
    url: '/api/v1/cluster/list',
    method: 'get',
    data: {},
    params: page
  })
}

export const ApiClusterCreate = (data) => {
  return axios.request({
    url: '/api/v1/cluster/create',
    method: 'post',
    data: data,
    params: {}
  })
}

export const ApiClusterUpdate = (id, data) => {
  return axios.request({
    url: `/api/v1/cluster/update/${id}`,
    method: 'post',
    data: data,
    params: {}
  })
}

export const ApiClusterDelete = (id) => {
  return axios.request({
    url: `/api/v1/cluster/delete/${id}`,
    method: 'delete',
    data: {},
    params: {}
  })
}

export const ApiClusterRestartConnector = () => {
  return axios.request({
    url: `/api/v1/cluster/restart-connector`,
    method: 'post',
    data: {},
    params: {}
  })
}

export const ApiClusterSyncFromCmdb = () => {
  return axios.request({
    url: `/api/v1/cluster/sync-from-cmdb`,
    method: 'post',
    data: {},
    params: {}
  })
}


export const ApiListAllClusterNs = () => {
  return axios.request({
    url: `/api/v1/cluster/list-all-cluster-ns`,
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiClusterDetail = (cid) => {
  return axios.request({
    url: `/api/v1/cluster/get/${cid}`,
    method: 'get',
    data: {},
    params: {}
  })
}


export const ApiClusterRegion = (cluster_id) => {
  return axios.request({
    url: `/api/v1/cluster/region`,
    method: 'get',
    data: {},
    params: {
      cluster_id
    }
  })
}
