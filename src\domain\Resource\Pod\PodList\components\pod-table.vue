<template>
  <div class="component-pod-table">
    <Card style="margin-top: 16px" :bordered="false">
      <Table size="small" :loading="loading" :columns="columns" :data="podList.list" fixed-shadow="hide">
        <template #name="{ row }">
          <div class="name-wrapper">
            <LinkButton :text="row.name" ellipsis tooltip style="fontweight: 600" @click="onOpenDetail(row)" />
            <Space :size="8" style="width: auto">
              <Tooltip content="查看监控图" transfer placement="top" theme="light">
                <Icon type="md-stats" @click="onOpenContainerChart(row)"
              /></Tooltip>
              <Tooltip content="查看诊断报告" transfer placement="top" theme="light">
                <Icon type="ios-bug" @click="handleOpenAnalyzer(row.name)"
              /></Tooltip>
            </Space>
          </div>
        </template>
        <template slot-scope="{ row }" slot="ops">
          <!-- <a :style="`color: ${color.primary}; margin-right: 10px`" @click="handleOpenAnalyzer(row.name)">诊断</a> -->
          <a :style="`color: ${color.primary}; margin-right: 10px`" @click="showPodYaml(row.name)">YAML</a>
          <a :style="`color: ${color.error}`" @click="handleDeletePod(row)">
            {{ row.isRebuild ? '重建' : '删除' }}
          </a>
        </template>
      </Table>
      <Page
        style="margin-top: 16px"
        :current="podList.page"
        :page-size="podList.size"
        :total="podList.total"
        :page-size-opts="[10, 20, 30, 40]"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <pod-detail ref="refPodDetail" :podInfo="podInfo" :uuid="currentUuid" :onViewYamlCallBack="showPodYaml" />

    <Drawer
      :title="podYamlTitle"
      :closable="isEdit"
      width="60"
      v-model="openYaml"
      @on-close="handleCancel"
      :mask-closable="!isEdit"
    >
      <div class="edit-switch">开启编辑：<i-switch v-model="isEdit" @on-change="handlePodYamlEdit" /></div>
      <yaml v-model="currentYaml" ref="refYaml" style="max-height: 95%; overflow: auto" :forbiddenEdit="!isEdit" />
      <template v-if="isEdit">
        <div class="drawer-footer">
          <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleSubmit">确认</Button>
        </div>
      </template>
      <Spin size="large" fix v-if="spinShow"></Spin>
    </Drawer>

    <pod-analyzer v-model="openAnalyzer" :analyze-data="analyzeData" :loading="analyzeLoading" />
    <Modal :title="`监控图 - ${containerChartEntity.pod}`" width="85" footer-hide v-model="containerChartVisible">
      <ContainerChart :reloadFlag="containerChartVisible" :entity="containerChartEntity" prefix="pod-table" />
    </Modal>
  </div>
</template>

<script>
import PodDetail from './pod-detail.vue'
import { Yaml, LinkButton, Ellipsis, Space } from '@/components'

import PodAnalyzer from '@/domain/Resource/Pod/PodAnalyzer.vue'

import { color } from '@/libs/consts'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { formatMetricCPU, formatMetricMemory } from '@/libs/tools'
import { ApiDeletePod, ApiGetPodDetail, ApiUpdatePod } from '@/api/k8s/pod'
import { ApiResourceYamlGet } from '@/api/k8s/resource'
import { ApiPodAnalyze } from '@/api/k8s/namespace/worload/deployment/pod'
import { ContainerChart } from '@/domain/Resource'
import { getPodStatusColor } from '@/domain/Resource/Pod/setting'

export default {
  name: 'component-pod-table',
  props: ['podList', 'loading'],
  components: {
    PodDetail,
    Yaml,
    PodAnalyzer,
    ContainerChart,
    LinkButton,
    Space
  },
  inject: ['main'],
  computed: {
    color() {
      return color
    },
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  data() {
    return {
      openAnalyzer: false,
      analyzeData: {},
      analyzeLoading: false,
      columns: [
        {
          title: 'Name',
          key: 'name',
          slot: 'name',
          minWidth: 250,
          fixed: 'left'
        },
        {
          title: 'Ready',
          key: 'ready',
          tooltip: true,
          width: 100,
          tooltipTheme: 'light',
          align: 'center'
        },
        {
          title: 'Status',
          key: 'status',
          width: 280,

          render: (h, params) => {
            return h(Space, { props: { size: 4 } }, [
              h(
                Ellipsis,
                {
                  props: { type: 'text' },
                  style: {
                    color: this.getColor(params.row.status),
                    fontWeight: 600,
                    flexShrink: 0
                  }
                },
                params.row.status
              ),
              ...[
                h('span', ' | '),
                h(
                  Ellipsis,
                  {
                    props: { type: 'text' },
                    style: {
                      color: '#cdcdcd'
                    }
                  },

                  params.row.reason ? params.row.reason : '正常运转'
                )
              ]
            ])
          }
        },
        {
          title: '优先级',
          key: 'pc',
          width: 100,
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'CPU / Mem (Usage)',
          minWidth: 200,
          render: (h, params) => {
            return h('div', {}, `${formatMetricCPU(params.row.cpu)} / ${formatMetricMemory(params.row.mem)}`)
          }
        },
        {
          title: 'CPU / Mem (Request)',
          width: 210,
          render: (h, params) => {
            return h(
              'div',
              {},
              `${formatMetricCPU(params.row.cpuRequest)} / ${formatMetricMemory(params.row.memRequest)}`
            )
          }
        },
        {
          title: 'CPU / Mem (Limit)',
          minWidth: 220,
          render: (h, params) => {
            return h('div', {}, `${formatMetricCPU(params.row.cpuLimit)} / ${formatMetricMemory(params.row.memLimit)}`)
          }
        },

        {
          title: 'Restarts',
          key: 'restarts',
          align: 'center',
          width: 100,
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'Age',
          key: 'age',
          align: 'center',
          width: 150,
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'IP',
          key: 'ip',
          width: 150,
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center'
        },
        {
          title: 'Node',
          key: 'node',
          width: 150,
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center'
        },
        {
          title: '操作',
          key: 'ops',
          slot: 'ops',
          width: 108,
          fixed: 'right',
          align: 'center'
        }
      ],
      podInfo: {
        metadata: {},
        spec: {},
        status: {},
        eventList: []
      },
      showPodDetail: false,

      currentYaml: '',
      openYaml: false,
      podYamlTitle: '查看 YAML',
      isEdit: false,
      spinShow: false,
      currentPodName: '',
      currentPageSize: this.podList.size,
      currentUuid: '',
      containerChartVisible: false,
      containerChartEntity: {}
    }
  },
  methods: {
    async onOpenDetail(row) {
      await this.getPodDetail(row.name, row.workload)

      this.currentUuid = row.uid
      this.$refs.refPodDetail.isShow = true
    },
    async handleOpenAnalyzer(podName) {
      this.openAnalyzer = true
      this.analyzeLoading = true
      await ApiPodAnalyze({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: podName
      })
        .then((res) => {
          this.analyzeData = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          setTimeout(() => {
            this.analyzeLoading = false
          }, 1000)
        })
      // clusterId, namespace
    },
    getColor(status) {
      return getPodStatusColor(status)
    },
    handleCancel() {
      this.openYaml = false
      this.isEdit = false
      this.currentPodName = ''
    },
    // 提交编辑的yaml
    async handleSubmit() {
      try {
        const payload = {
          cluster_id: this.currentClusterId,
          yaml: this.currentYaml
        }
        await ApiUpdatePod(payload)

        this.handleCancel()
        this.$emit('reloadTable', { page: this.podList.page, size: this.podList.size })
        noticeSucceed(this, '编辑成功')
      } catch (error) {
        noticeError(this, `编辑失败, ${errorMessage(error)}`)
      }
    },
    async showPodYaml(name) {
      this.spinShow = true
      this.currentPodName = name
      // 修改Yaml title
      this.podYamlTitle = `Pod名称 ${name}`
      this.currentYaml = ''
      this.openYaml = true
      try {
        await this.getPodYaml(name, this.isEdit)
      } catch (error) {
        noticeError(this, `Pod YAML获取失败，${errorMessage(error)}`)
      }
      this.spinShow = false
    },
    // 切换为编辑模式
    async handlePodYamlEdit() {
      this.spinShow = true
      await this.getPodYaml(this.currentPodName, this.isEdit)
      this.spinShow = false
    },

    pageChange(page) {
      this.$emit('reloadTable', {
        page,
        size: this.currentPageSize,
        search: this.main.searchData,
        status: this.main.selectStatus
      })
    },
    pageSizeChange(pageSize) {
      this.currentPageSize = pageSize
      this.$emit('reloadTable', {
        page: 1,
        size: this.currentPageSize,
        search: this.main.searchData,
        status: this.main.selectStatus
      })
    },
    handleDeletePod(podItem) {
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>确认${podItem.isRebuild ? '重建' : '删除'} ${podItem.name} ?</p>`,
        loading: true,
        onOk: async () => {
          try {
            const payload = {
              cluster_id: this.currentClusterId,
              namespace: this.currentNamespace,
              name: podItem.name
            }
            await ApiDeletePod(payload)
            this.$emit('reloadTable', { page: this.podList.page, size: this.podList.size })
            noticeSucceed(this, `${podItem.isRebuild ? '重建' : '删除'}成功`)
          } catch (error) {
            noticeError(this, `${podItem.isRebuild ? '重建' : '删除'}失败, ${errorMessage(error)}`)
          }
          this.$Modal.remove()
        }
      })
    },
    // 获取pod详情
    async getPodDetail(name, workload) {
      try {
        const params = {
          cluster_id: this.currentClusterId,
          namespace: this.currentNamespace,
          name
        }
        const res = await ApiGetPodDetail(params)
        this.podInfo = { ...res.data.data.pod, workload }
      } catch (error) {
        noticeError(this, `Pod数据获取失败，${errorMessage(error)}`)
      }
    },

    // 获取podYaml
    async getPodYaml(name, isEdit) {
      try {
        const params = {
          resource: 'pods',
          cluster_id: this.currentClusterId,
          namespace: this.currentNamespace,
          resource_name: name,
          is_edit: isEdit,
          version: 'v1'
        }
        const res = await ApiResourceYamlGet(params)
        this.currentYaml = res.data.data.data
      } catch (error) {
        noticeError(this, `Yaml数据获取失败，${errorMessage(error)}`)
      }
    },
    onOpenContainerChart(record) {
      this.containerChartEntity = {
        pod: record.name,
        cluster: this.currentCluster,
        namespace: this.currentNamespace,
        workload: record.workload
      }
      this.containerChartVisible = true
    }
  }
}
</script>

<style scoped lang="less">
.edit-switch {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #17233d;
}

.name-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  a {
    width: ~'calc(100% - 48px)';
  }
  .ivu-icon {
    font-size: 16px;
    color: #2a7cc3;
    cursor: pointer;
  }
}

.drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 16px 16px;
  text-align: right;
  background: #fff;
}
</style>
