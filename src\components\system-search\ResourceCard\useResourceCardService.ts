import { getCurrentInstance, ref } from 'vue'
import { Container, Resource } from '../type'
import Config from '@/config'
import { useGet } from '@/libs/service.request'
import { EnumAction, EnumResourceType } from '../config'
import { useStore } from '@/libs/useVueInstance'
import { useRequest } from 'vue-request'

export default function useResourceCardService(props: { data: Resource }) {
  const { proxy } = getCurrentInstance()
  const store = useStore()

  const relativeModalVisible = ref()

  const setNewClusterAndNamespace = (callback) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `即将跳转到${props.data.clusterName}/${props.data.namespace}的资源中，是否确认跳转？`,
      loading: true,
      onOk: () => {
        store.commit('setCurrentClusterId', { uid: store.state.user.userId, clusterId: props.data.clusterId })
        store.commit('setCurrentCluster', { uid: store.state.user.userId, cluster: props.data.clusterName })
        store.commit('setCurrentNamespace', { uid: store.state.user.userId, namespace: props.data.namespace })
        callback()
        proxy.$Modal.remove()
      }
    })
  }

  const jumpToResourceDetail = () => {
    let path,
      query,
      isSwitchK8SResource = false
    switch (props.data.resource) {
      case EnumResourceType.Deployment:
        path = '/kubernetes/namespace/deployment-detail'
        query = {
          clusterId: props.data.clusterId.toString(),
          namespace: props.data.namespace,
          deployment: props.data.name,
          cluster: props.data.clusterName,
          uuid: props.data.uuid
        }
        break
      case EnumResourceType.StatefulSets:
        path = '/kubernetes/namespace/statefulset-detail'
        query = {
          clusterId: props.data.clusterId.toString(),
          namespace: props.data.namespace,
          name: props.data.name,
          cluster: props.data.clusterName,
          uuid: props.data.uuid
        }
        break
      case EnumResourceType.Pods:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/pod'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.Services:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/services'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.Secrets:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/secrets'
        query = {
          name: props.data.name
        }
        break

      case EnumResourceType.HPA:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/hpa'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.Configmaps:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/configmap'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.VirtualServices:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/mesh/virtualservice'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.DestinationRules:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/mesh/destinationrule'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.SideCars:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/mesh/sidecar'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.ServiceEntries:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/mesh/serviceentry'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.Gateways:
        path = '/gateway/gateway-management'
        query = {
          clusterId: props.data.clusterId.toString(),
          namespace: props.data.namespace,
          name: props.data.name
        }
        break
      case EnumResourceType.Nodes:
        path = '/node-mgt/node-management'
        query = {
          clusterId: props.data.clusterId.toString(),
          name: props.data.name
        }
        break
      case EnumResourceType.PVC:
        path = '/kubernetes/namespace/deployment-detail'
        query = {
          clusterId: props.data.clusterId.toString(),
          namespace: props.data.namespace,
          name: props.data.name,
          cluster: props.data.clusterName
        }
        break
      case EnumResourceType.WasmPlugins:
        path = '/kubernetes/namespace/deployment-detail'
        query = {
          clusterId: props.data.clusterId.toString(),
          namespace: props.data.namespace,
          name: props.data.name,
          cluster: props.data.clusterName
        }
        break
      case EnumResourceType.Job:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/job'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.EndPoint:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/end-point'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.CronJob:
        isSwitchK8SResource = true
        path = '/kubernetes/resource/namespace/cron-job'
        query = {
          name: props.data.name
        }
        break
      case EnumResourceType.Telemetry:
        isSwitchK8SResource = true
        path = 'kubernetes/resource/mesh/telemetry'
        query = {
          name: props.data.name
        }
        break
      default:
        break
    }
    if (!path) {
      proxy.$Message.info('该资源尚未在系统中集成，请通过YAML查看详情！')
      return
    }
    const router = proxy.$router.resolve({
      path,
      query
    })
    if (isSwitchK8SResource) {
      setNewClusterAndNamespace(() => window.open(router.href, '_blank'))
    } else {
      window.open(router.href, '_blank')
    }
  }

  const onOpenBash = (container) => {
    const r = proxy.$router.resolve({
      path: '/pod-console',
      query: {
        clusterId: props.data.clusterId.toString(),
        namespace: props.data.namespace,
        cluster: props.data.clusterName,
        pod: props.data.name,
        container,
        priority: 'true'
      }
    })
    window.open(r.href, '_blank')
  }

  const onOpenLog = (container) => {
    const r = proxy.$router.resolve({
      path: '/kubernetes/logs',
      query: {
        clusterId: props.data.clusterId.toString(),
        namespace: props.data.namespace,
        cluster: props.data.clusterName,
        pod: props.data.name,
        container
      }
    })
    window.open(r.href, '_blank')
  }

  const onOpenYaml = async () => {
    const res = await useGet(`${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`, {
      params: {
        ...props.data.gvr,
        cluster_id: props.data.clusterId,
        namespace: props.data.namespace,
        resource_name: props.data.name,
        is_edit: false
      }
    })
    if (res.success) {
      openYamlWindow(res.data.data)
    }
  }

  const openYamlWindow = (yamlJson) => {
    sessionStorage.setItem('yaml', yamlJson)

    // 创建一个居中的窗口
    const width = 700
    const height = 500
    // 计算窗口的起始位置 (居中)
    const left = (screen.width - width) / 2
    const top = (screen.height - height) / 2

    const url = `${window.location.origin}/pure-yaml`
    // 打开新的窗口并设置窗口的特性
    window.open(
      url,
      '_blank',
      'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no,' +
        'width=' +
        width +
        ',height=' +
        height +
        ',top=' +
        top +
        ',left=' +
        left
    )
  }

  const onActionBtnClick = (action: EnumAction, args?: any) => {
    if (!props.data.isAuthorized) {
      proxy.$Message.warning('请先授权该资源')
      return
    }
    switch (action) {
      case EnumAction.Shell:
        onOpenBash(args)
        break
      case EnumAction.Log:
        onOpenLog(args)
        break
      case EnumAction.Yaml:
        onOpenYaml()
        break
      case EnumAction.Relative:
      default:
        relativeModalVisible.value = true
        break
    }
  }

  const noticeUnAuthorized = () => {
    proxy.$Message.warning('该资源尚未授权，请先授权！')
  }

  const onContainersPopperShow = () => {
    GetPodContainers()
  }

  const {
    data: containers,
    loading: containersLoading,
    run: GetPodContainers
  } = useRequest(
    () => {
      return useGet<{ data: Container[] }>(`${Config.Api.Base}${Config.Api.GetPodContainers}`, {
        params: {
          clusterId: props.data.clusterId,
          namespace: props.data.namespace,
          name: props.data.name
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => {
        return res.data?.data
      }
    }
  )

  return {
    jumpToResourceDetail,
    onActionBtnClick,
    relativeModalVisible,
    noticeUnAuthorized,
    onContainersPopperShow,
    containers,
    containersLoading
  }
}
