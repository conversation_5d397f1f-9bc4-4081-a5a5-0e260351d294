import { Ellipsis } from '@/components'
import { color } from '@/libs/consts'
import { relativeTime } from '@/libs/tools'
export const TABLE_COLUMNS = [
  {
    title: 'UID',
    key: 'uid',
    width: 90,
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'DeploymentName',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'ObjectName',
    key: 'gwName',
    render: (h, params) => {
      return params.row.gwName?.length
        ? h(Ellipsis, {
            scopedSlots: {
              default: () => params.row.gwName.join(','),
              content: () => params.row.gwName?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Host',
    key: 'hosts',
    render: (h, params) => {
      return params.row.hosts?.length
        ? h(Ellipsis, {
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'READY',
    key: 'ready',
    tooltip: true,
    tooltipTheme: 'light',
    width: 80
  },
  {
    title: 'Available',
    key: 'available',
    tooltip: true,
    tooltipTheme: 'light',
    width: 80
  },
  {
    title: 'LBIP',
    key: 'lbIP',
    render: (h, params) => {
      return params.row.lbIP?.length
        ? h(Ellipsis, {
            scopedSlots: {
              default: () => params.row.lbIP.join(','),
              content: () => params.row.lbIP?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    width: 160,
    render: (h, params) => {
      return h(Ellipsis, relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 260,
    align: 'center'
  }
]

export const NAMESPACE_LIST = [
  { value: 'istio-ingress', label: 'istio-ingress' },
  { value: 'istio-system', label: 'istio-system' }
]

const getColor = (status) => {
  switch (status && status.toLowerCase()) {
    case 'running':
      return color['success']
    case 'pending':
      return color['info']
    case 'terminated':
      return color['error']
  }
}
export const LOG_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Namespace',
    key: 'namespace'
  },
  {
    title: 'Phase',
    key: 'phase',
    width: 120,
    align: 'center',
    render: (h, params) => {
      return h(
        Ellipsis,
        {
          style: {
            color: getColor(params.row.phase),
            fontWeight: 600,
            width: '100%'
          }
        },
        params.row.phase
      )
    }
  },
  {
    title: 'Age',
    key: 'age',
    slot: 'age',
    width: 160
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 80
  }
]
