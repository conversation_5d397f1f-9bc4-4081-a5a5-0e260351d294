import { ref, onMounted, getCurrentInstance, computed, nextTick, set, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList, usePost } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { VirtualService } from './type'
import { EnumFormStatus, FormSubmitParams, ResourceEntity } from '@/components/resource-form'
import { EnumArrayObjectModalStatus } from '@/components/pro-form'
import { useRequest } from 'vue-request'
import { EnumComponentType } from './enum'
import { cloneDeep } from 'lodash-es'
import { getTableColumns } from './setting'
import useForceUpdateNoticeService from '../useForceUpdateNoticeService'
import useSingleK8SService from '@/libs/useSingleK8SService'

export const DEFAULT_ENTITY = {
  resource: 'virtualservices',
  group: 'networking.istio.io',
  version: 'v1beta1'
}
export const SERVICES_ENTITY = { resource: 'services', group: '', version: 'v1' }
export const DESTINATION_RULE_ENTITY = {
  resource: 'destinationrules',
  group: 'networking.istio.io',
  version: 'v1beta1'
}

export default function useVirtualServiceService(props: {
  type: EnumComponentType
  K8SInstance?: { clusterId: string; namespace: string; relativeGatewayName: string }
}) {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const viewDetailVisible = ref(false)
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>({ ...props.K8SInstance, ...SERVICES_ENTITY })
  const yamlInitData = ref<string>()
  const httpFormRef = ref()
  const modal = ref({
    visible: false,
    data: {} as VirtualService,
    value: []
  })

  const isHostInit = ref()
  const tempHostOption = ref([])
  const portOptions = ref([])
  const subsetOptions = ref([])

  const isMirrorHostInit = ref()
  const tempMirrorHostOption = ref([])
  const portMirrorOptions = ref([])
  const subsetMirrorOptions = ref([])

  const tempGatewaysOption = ref([])
  const currentResourceNamespace = ref()
  const currentRelativeGatewayName = ref()
  const batchCopyModalVisible = ref(false)

  const formInitData = computed(() => ({
    name: undefined,
    namespace: K8SInstance.value?.namespace
  }))

  const isIndependentType = computed(() => props.type === EnumComponentType.Independent)

  // 防止多次注入同一个columns,解决table失焦不去除高亮效果的bug
  const columns = computed(() => getTableColumns(props.type))

  const { onForceUpdateNotice } = useForceUpdateNoticeService()

  const onCreate = () => {
    getDelegateCascaderList()
    initTempGatewaysOption()
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...DEFAULT_ENTITY,
      ...K8SInstance.value
    }
    yamlInitData.value = `apiVersion: networking.istio.io/v1beta1 
kind: VirtualService
metadata:
  name: 必须修改
  namespace: ${K8SInstance.value.namespace}
spec:`
    currentResourceNamespace.value = K8SInstance.value?.namespace
    currentRelativeGatewayName.value =
      props.type === EnumComponentType.AssociatedGateway
        ? [`${props.K8SInstance.namespace}/${props.K8SInstance.relativeGatewayName}`]
        : undefined
  }
  const onDelete = (record: VirtualService) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(
          `${Config.Api.Base}${
            props.type === EnumComponentType.AssociatedDeployment
              ? Config.Api.DeleteRelatedVirtualService
              : props.type === EnumComponentType.AssociatedGateway
              ? Config.Api.DeleteRelatedGatewayVirtualService
              : Config.Api.DeleteVirtualService
          }`,
          {
            params: {
              ...K8SInstance.value,
              resourceName: record.name,
              namespace: record.namespace
            }
          }
        )
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onEdit = (record: VirtualService) => {
    const editFun = () => {
      console.log('onEdit')
      getDelegateCascaderList()
      formVisible.value = true
      formStatus.value = EnumFormStatus.Edit
      formEntity.value = {
        ...K8SInstance.value,
        ...DEFAULT_ENTITY,
        resourceName: record.name,
        namespace: record.namespace
      }
      currentResourceNamespace.value = record.namespace
      currentRelativeGatewayName.value = record.gateways
    }
    if (record.isAllowEdit) {
      editFun()
      return
    }

    onForceUpdateNotice(
      {
        namespace: record.namespace,
        clusterId: K8SInstance.value.clusterId,
        objectName: record.name,
        kind: 'VirtualService'
      },
      editFun
    )
  }
  const { data: clusterCascaderList, run: getClusterCascaderList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetClusterCascaderList}`, {
        params: {
          clusterId: K8SInstance.value?.clusterId
        }
      })
    },
    {
      formatResult: (res) => res.data.data,
      ready: isIndependentType
    }
  )
  const onCopy = (record: VirtualService) => {
    modal.value = {
      visible: true,
      data: record,
      value: []
    }
  }
  const onCopySubmit = async () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认复制 ${modal.value.data.name}？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(`${Config.Api.Base}${Config.Api.CopyVirtualService}`, {
          newClusterId: modal.value.value[0],
          newNamespace: modal.value.value[1],
          originClusterId: K8SInstance.value.clusterId,
          originNamespace: K8SInstance.value.namespace,
          resourceName: modal.value.data.name
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
          modal.value.visible = false
          proxy.$Message.success(`复制成功`)
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onViewYaml = (
    record: VirtualService,
    type: 'virtualService' | 'destinationRule' | 'services' = 'virtualService'
  ) => {
    formEntity.value = {
      ...K8SInstance.value,
      ...(type === 'virtualService'
        ? DEFAULT_ENTITY
        : type === 'destinationRule'
        ? { ...DESTINATION_RULE_ENTITY }
        : { ...SERVICES_ENTITY }),
      resourceName: record.name,
      namespace: record.namespace
    }
    yamlHistoryParams.value = {
      kind: 'VirtualService',
      uuid: record.uid
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: VirtualService) => {
    console.log('onViewDetail')
    viewDetailVisible.value = true
    formEntity.value = {
      ...K8SInstance.value,
      ...DEFAULT_ENTITY,
      resourceName: record.name,
      namespace: record.namespace
    }
  }

  const getTableData: TableRequest = async (params) => {
    let url, data
    switch (props.type) {
      case EnumComponentType.AssociatedDeployment:
        url = `${Config.Api.Base}${Config.Api.GetRelatedVirtualServiceTableData}`
        data = {
          clusterId: proxy.$route.query.clusterId,
          namespace: proxy.$route.query.namespace,
          name: proxy.$route.query.deployment
        }
        break
      case EnumComponentType.AssociatedGateway:
        url = `${Config.Api.Base}${Config.Api.GetRelatedGatewayVirtualServiceTableData}`
        data = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          resourceName: props.K8SInstance.relativeGatewayName,
          page: params.pageIndex,
          size: params.pageSize,
          search: params.searchValue ?? ''
        }
        break
      default:
        url = `${Config.Api.Base}${Config.Api.GetVirtualServiceTableData}`
        data = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          page: params.pageIndex,
          size: params.pageSize,
          search: params.searchValue ?? ''
        }
        break
    }

    const res = await useGet<PageList<VirtualService[]>>(url, { params: data })
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data:
        (props.type === EnumComponentType.AssociatedDeployment
          ? (res.data as unknown as { data: VirtualService[] }).data
          : res.data?.list) ?? []
    }
  }

  const onCheckHttpData = (data, type, record, index, callback) => {
    httpFormRef.value.validate((valid) => {
      if (valid) {
        if (isDefaultRoute(record)) {
          const defaultRoute = data.HTTP.data?.filter(
            (i, idx) => idx !== index && (!i.match.switchCtl || !i?.match.data?.length)
          )

          if (!!defaultRoute?.length) {
            proxy.$Message.error('通配路由已存在, 如要配置普通路由, 必须配置Match（匹配项）。')
            callback(true)
          } else {
            proxy.$Message.success('当前路由缺少Match（匹配项）, 已自动转换为通配路由。')
            onSubmitHttp(data, type, record, index, true)
          }
        } else {
          onSubmitHttp(data, type, record, index, false)
        }
      } else {
        callback(true)
      }
    })
  }
  const onSubmitHttp = (data, type, record, index, isDefaultRoute) => {
    if (data.HTTP.data?.length) {
      const res = [...data.HTTP.data]
      if (isDefaultRoute) {
        if (type === EnumArrayObjectModalStatus.Create) {
          res.push(record)
        } else {
          res.splice(index, 1)
          res.push(record)
        }
        set(data.HTTP, 'data', res)
      } else {
        data.HTTP.data.splice(
          type === EnumArrayObjectModalStatus.Edit ? index : 0,
          type === EnumArrayObjectModalStatus.Edit ? 1 : 0,
          record
        )
      }
    } else {
      data.HTTP.data = [record]
    }
  }
  const initTempGatewaysOption = (gateways?) => {
    tempGatewaysOption.value = [...gatewaysOptions.value]
    if (gateways?.length) {
      const keys = gatewaysOptions.value?.map((i) => i.value)
      const combos = new Set([...keys, ...gateways])
      if (combos.size === keys.length + gateways.length) {
        tempGatewaysOption.value.push(...gateways?.map((gateway) => ({ value: gateway, label: gateway })))
      }
    }
  }
  const initTempHostOption = (data) => {
    const { host, port, subset } = data
    let isHostInit = false
    tempHostOption.value = [...hostOptions.value]
    if (host) {
      const keys = hostOptions.value?.map((i) => i.value)
      if (!keys.includes(host)) {
        tempHostOption.value.push({ value: host, label: host, isInit: true })
        isHostInit = true
      }
    }
    afterHostChange(host, data, isHostInit, port?.data, subset?.data)
  }
  const initTempMirrorHostOption = (data) => {
    const { host, port, subset } = data.mirror?.data ?? {}
    let isHostInit = false
    tempMirrorHostOption.value = [...hostOptions.value]
    if (host) {
      const keys = hostOptions.value?.map((i) => i.value)
      if (!keys.includes(host)) {
        tempMirrorHostOption.value.push({ value: host, label: host, isInit: true })
        isHostInit = true
      }
    }
    afterMirrorHostChange(host, data, isHostInit, port?.data, subset?.data)
  }

  const onInitFormat = (data) => {
    data.HTTP?.data?.forEach((i) => {
      if (i.route?.data?.delegate) {
        const arr = []
        if (i.route?.data?.delegate?.name && i.route?.data?.delegate?.namespace) {
          arr.push(i.route?.data?.delegate.namespace, i.route?.data?.delegate.name)
        }
        i.route.data.delegate = arr
      }
    })
    props.K8SInstance?.relativeGatewayName &&
      (data.gateways = {
        data: currentRelativeGatewayName.value?.map((i) => i),
        switchCtl: true
      })
    initTempGatewaysOption(data?.gateways?.data)
    return data
  }
  const onSubmitFormat = (params: FormSubmitParams) => {
    params.data.HTTP?.data?.forEach((i) => {
      i.loadBalancer?.data?.locality?.data?.distribute?.forEach((ie) => ie.from && (ie.from = String(ie.from)))
      if (i.route?.data?.delegate) {
        const [namespace, name] = i.route?.data?.delegate
        i.route.data.delegate = { namespace, name }
      }
    })
    return params
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }
  const { data: exportToList, run: getExportToList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetExportToList}?ClusterId=${
          props.type === EnumComponentType.AssociatedDeployment
            ? proxy.$route.query.clusterId
            : K8SInstance.value?.clusterId
        }`
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const { data: gatewaysOptions, run: getGatewaysOptions } = useRequest(
    () => {
      const url = `${Config.Api.Base}${Config.Api.GetGatewaysOptions}?clusterId=${
        props.type === EnumComponentType.AssociatedDeployment
          ? proxy.$route.query.clusterId
          : K8SInstance.value?.clusterId
      }`
      return useGet(url)
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data?.map((i) => ({ value: i, label: i })) ?? []
    }
  )

  const { data: hostOptions, run: getHostOptions } = useRequest(
    () => {
      const url =
        props.type === EnumComponentType.AssociatedDeployment
          ? `${Config.Api.Base}${Config.Api.GetRelatedVirtualServiceHostOptions}?clusterId=${proxy.$route.query?.clusterId}&namespace=${proxy.$route.query?.namespace}&name=${proxy.$route.query.deployment}`
          : `${Config.Api.Base}${Config.Api.GetVirtualServiceHostOptions}?clusterId=${K8SInstance.value?.clusterId}&namespace=${K8SInstance.value?.namespace}`
      return useGet(url)
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data?.map((i) => ({ value: i, label: i })) ?? []
    }
  )

  const { run: getPortOptions } = useRequest(
    (host, port) => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetVirtualServicePortOptions}?host=${host}&clusterId=${K8SInstance.value?.clusterId}&namespace=${currentResourceNamespace.value}`
      )
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data?.map((i) => ({ value: i, label: i })) ?? [],
      onSuccess(options, [host, port]) {
        if (port) {
          const keys = options?.map((i) => i.value)
          if (!keys.includes(port)) {
            options.push({ value: port, label: port, isInit: true })
          }
        }
        return options
      }
    }
  )

  const { run: getSubsetOptions } = useRequest(
    (host, subset) => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetVirtualServiceSubsetOptions}?host=${host}&clusterId=${K8SInstance.value?.clusterId}&namespace=${currentResourceNamespace.value}`
      )
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data?.map((i) => ({ value: i, label: i })) ?? [],
      onSuccess(options, [host, subset]) {
        if (subset) {
          const keys = options?.map((i) => i.value)
          if (!keys.includes(subset)) {
            options.push({ value: subset, label: subset, isInit: true })
          }
        }
        return options
      }
    }
  )

  const { data: delegateCascaderList, run: getDelegateCascaderList } = useRequest(
    () => {
      const url =
        props.type === EnumComponentType.AssociatedDeployment
          ? `${Config.Api.Base}${Config.Api.GetRelatedDelegateCascader}?clusterId=${K8SInstance.value?.clusterId}`
          : `${Config.Api.Base}${
              props.type === EnumComponentType.AssociatedGateway
                ? Config.Api.GetRelatedGatewayDelegateCascader
                : Config.Api.GetDelegateCascader
            }?clusterId=${K8SInstance.value?.clusterId}`
      return useGet(url)
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data
    }
  )
  const afterHostChange = async (host, data, isInit, port = undefined, subset = undefined) => {
    isHostInit.value = isInit
    if (host) {
      portOptions.value = await getPortOptions(host, port)
      subsetOptions.value = await getSubsetOptions(host, subset)
    }

    // 初始化会触发此函数，此时data?.port/data?.subset均尚未被各自的组件初始化，提前过滤避免报错
    data?.port && set(data.port, 'data', port)
    data?.subset && set(data.subset, 'data', subset)
  }
  const afterMirrorHostChange = async (host, data, isInit, port = undefined, subset = undefined) => {
    isMirrorHostInit.value = isInit
    if (host) {
      portMirrorOptions.value = await getPortOptions(host, port)
      subsetMirrorOptions.value = await getSubsetOptions(host, subset)
    }
    if (data.mirror?.data) {
      set(data.mirror.data.port, 'data', port)
      set(data.mirror.data.subset, 'data', subset)
    }
  }

  const onMatchKeyChange = (record, recordKey, key, index, val) => {
    if (!record?.[recordKey]?.data?.length) {
      set(record?.[recordKey], 'data', [])
    }
    if (!record[recordKey].data[index]) {
      set(record[recordKey].data, index, {})
    }
    set(record[recordKey].data[index], key, val)
  }

  const onAddMatch = (data, type, record, index) => {
    if (data.match.data?.length) {
      data.match.data.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.match.data = [record]
    }
  }

  const onAddRouteRoute = (data, type, record, index) => {
    if (data.route.data.route?.length) {
      data.route.data.route.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.route.data.route = [record]
    }
  }
  const onAllowOriginsKeyChange = (record, key, index, val) => {
    if (!record.corsPolicy?.data?.allowOrigins?.length) {
      set(record.corsPolicy?.data, 'allowOrigins', [])
    }
    if (!record.corsPolicy?.data?.allowOrigins[index]) {
      set(record.corsPolicy?.data?.allowOrigins, index, {})
    }
    set(record.corsPolicy?.data?.allowOrigins[index], key, val)
  }

  const isDefaultRoute = (data) => !data?.match?.switchCtl || !data?.match.data?.length

  const onHttpTableDragDrop = (data, dragIndex, targetIndex) => {
    // if (isDefaultRoute(data.HTTP.data?.[dragIndex]) || isDefaultRoute(data.HTTP.data?.[targetIndex])) {
    //   proxy.$Message.warning('调整优先级的路由缺少Match（匹配项），默认属于通配路由，不支持调整其优先级')
    //   return
    // }
    const tableData = cloneDeep(data.HTTP.data)
    const temp = cloneDeep(tableData[dragIndex])
    tableData.splice(dragIndex, 1)
    tableData.splice(targetIndex, 0, temp)
    set(data.HTTP, 'data', tableData)
  }

  const validateName = (rule, value, callback) => {
    if (!value) {
      callback(new Error('请输入Name（路由名称）'))
    } else {
      callback()
    }
  }

  const initK8SInstance = () => {
    if (props.type === EnumComponentType.AssociatedDeployment) {
      K8SInstance.value = {
        namespace: proxy.$route.query.namespace as string,
        clusterId: proxy.$route.query.clusterId as string
      }
    } else if (!props.K8SInstance || JSON.stringify(props.K8SInstance) == '{}') {
      store.commit('getCurrentClusterID', store.state.user.userId)
      store.commit('getCurrentNamespace', store.state.user.userId)
      K8SInstance.value = {
        namespace: store.state.k8s.currentNamespace,
        clusterId: store.state.k8s.currentClusterId
      }
    } else {
      K8SInstance.value = props.K8SInstance
    }
  }
  onMounted(() => {
    initK8SInstance()
    nextTick(() => {
      getExportToList()
      getHostOptions()
      getGatewaysOptions()
      getClusterCascaderList()
    })
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    nextTick(() => {
      getExportToList()
      getHostOptions()
      getGatewaysOptions()
      getClusterCascaderList()
    })
    refObject.tableRef.value.reload()
  })

  return {
    getTableData,
    refObject,
    onCreate,
    onEdit,
    onDelete,
    onCopy,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    formInitData,
    onInitFormat,
    onSubmitFormat,
    onSubmitSuccess,
    onViewDetail,
    viewDetailVisible,

    modal,
    onCopySubmit,
    clusterCascaderList,
    onMatchKeyChange,
    onAddMatch,
    delegateCascaderList,
    onAddRouteRoute,

    tempHostOption,
    initTempHostOption,
    isHostInit,
    afterHostChange,
    portOptions,
    subsetOptions,

    tempMirrorHostOption,
    initTempMirrorHostOption,
    isMirrorHostInit,
    afterMirrorHostChange,
    portMirrorOptions,
    subsetMirrorOptions,

    onAllowOriginsKeyChange,
    onCheckHttpData,
    exportToList,
    onHttpTableDragDrop,
    httpFormRef,
    validateName,
    tempGatewaysOption,
    columns,
    batchCopyModalVisible
  }
}
