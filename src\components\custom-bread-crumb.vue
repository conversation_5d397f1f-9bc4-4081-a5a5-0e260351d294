<template>
  <div class="custom-bread-crumb">
    <Breadcrumb>
      <BreadcrumbItem v-for="item in breadCrumbList" :to="item.to" :key="`bread-crumb-${item.name}`">
        <common-icon v-if="item?.icon" :type="item?.icon" />
        <span>{{ showTitle(item) }}</span>
      </BreadcrumbItem>
    </Breadcrumb>
  </div>
</template>
<script>
import { showTitle } from '@/libs/util'
import CommonIcon from '_c/common-icon'
export default {
  name: 'customBreadCrumb',
  components: {
    CommonIcon
  },

  methods: {
    showTitle(item) {
      return showTitle(item, this)
    },
    isCustomIcon(iconName) {
      return iconName.indexOf('_') === 0
    },
    getCustomIconName(iconName) {
      return iconName.slice(1)
    }
  },
  computed: {
    breadCrumbList() {
      return this.$store.state.app.breadCrumbList
    }
  }
}
</script>
<style scoped lang="less">
.custom-bread-crumb {
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  .ivu-breadcrumb,
  .ivu-breadcrumb > span {
    display: flex;
    align-items: center;
  }
  /deep/.ivu-icon {
    line-height: 21px;
    margin-right: 4px;
  }
  .iconfont {
    margin-right: 8px;
  }
}
</style>
