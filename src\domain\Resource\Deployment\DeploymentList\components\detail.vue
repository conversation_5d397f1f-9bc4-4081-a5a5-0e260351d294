<template>
  <div>
    <Card>
      <h4>基本信息</h4>
      <Row type="flex" :gutter="20" style="margin-top: 20px">
        <Col span="10" class="info-key">名称</Col>
        <Col span="14" class="info-value">{{ innerDetail.name }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">命名空间</Col>
        <Col span="14" class="info-value">{{ innerDetail.namespace }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">创建时间</Col>
        <Col span="14" class="info-value">{{ innerDetail.creationTimestamp | dateFormat }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">标签</Col>
        <Col span="14" class="info-value">
          <template v-for="(value, key) in innerDetail.labels">
            <Tag color="#2a7cc3" :key="key"
              ><b>{{ key }}: {{ value }}</b></Tag
            >
          </template>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { TimeTrans } from '@/libs/tools'

export default {
  name: 'detail',
  props: {
    detail: Object
  },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    }
  },
  data() {
    return {
      innerDetail: {}
    }
  },
  methods: {},
  mounted() {
    this.innerDetail = this.detail
  }
}
</script>

<style scoped></style>
