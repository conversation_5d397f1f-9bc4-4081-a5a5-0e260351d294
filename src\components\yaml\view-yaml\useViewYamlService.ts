import { useGet, usePost } from '@/libs/service.request'
import { useRequest } from 'vue-request'
import Config from '@/config'
import { computed, getCurrentInstance, h, nextTick, onMounted, ref, watch } from 'vue'
import { yaml2json } from '@/libs/util'
import store from '@/store'

export interface YamlHistoryParams {
  kind: string
  uuid: string
}
export interface SubmitParams {
  clusterId: string
  namespace?: string
  isApplyToUnifiedCluster?: boolean
  data: Record<string, any>
}
export interface ResourceEntity {
  clusterId: string
  clusterName: string
  namespace: string
  resourceName: string // 资源唯一id
  resource: string // 资源分类
  group: string
  version: string
}
export type ViewYamlProps = {
  resourceEntity: ResourceEntity
  value: boolean
  /** yaml回滚功能入参 */
  yamlHistoryParams?: YamlHistoryParams
  resourceType: string
  /** 不同步到统一集群 */
  notSynchronizeToUnifiedCluster?: boolean
  /** 是否校验yaml数据合法性 */
  isCheckYaml?: boolean
  /** 是否请求时只传集群，不传命名空间 */
  isSkipNamespaceInParams?: boolean
}

export default function useViewYamlService(props: ViewYamlProps) {
  const latestYaml = ref()
  const yamlHistoryVersion = ref()
  const isApplyToUnifiedCluster = ref(false)
  const { proxy } = getCurrentInstance()
  const synchronizeToUnifiedCluster = computed(() => !props.notSynchronizeToUnifiedCluster)
  const syncUnifiedClusterModal = ref({ visible: false, data: [], params: {} })

  const { data: isUnifiedCluster, run: getIsUnifiedCluster } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetIsUnifiedCluster}?ClusterName=${
          props.resourceEntity?.clusterName ?? store.state.k8s.currentCluster
        }`
      )
    },
    {
      manual: true,
      ready: synchronizeToUnifiedCluster,
      formatResult: (res) => {
        const isUnifiedCluster = res.data.isUnifiedCluster
        isApplyToUnifiedCluster.value = isUnifiedCluster
        return isUnifiedCluster
      },
      onSuccess: () => {
        getUnifiedClusterList()
      }
    }
  )

  const { data: unifiedClusterList, run: getUnifiedClusterList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetUnifiedClusterList}`, {
        params: {
          clusterId: props.resourceEntity?.clusterId ?? store.state.k8s.currentClusterId
        }
      })
    },
    { manual: true, formatResult: (res) => res.data.data ?? [] }
  )

  const {
    data: yamlData,
    loading: yamlLoading,
    run: getYaml
  } = useRequest(
    () => {
      const { clusterId, resourceName, ...rest } = props.resourceEntity
      const params = {
        ...rest,
        cluster_id: clusterId,
        resource_name: resourceName,
        is_edit: false
      }
      return useGet(`${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`, {
        params
      })
    },
    {
      manual: true,
      formatResult(res) {
        latestYaml.value = res.data.data
        return res.data.data
      }
    }
  )

  const {
    data: yamlHistoryList,
    loading: yamlHistoryLoading,
    run: getYamlHistoryList
  } = useRequest(
    () => {
      const params = {
        ...props.yamlHistoryParams,
        ...(props.isSkipNamespaceInParams ? {} : { namespace: props.resourceEntity.namespace }),
        cluster_id: props.resourceEntity.clusterId
      }
      return useGet(`${Config.Api.Base}${Config.Api.GetYamlHistoryList}`, {
        params
      })
    },
    {
      manual: true,
      ready: ref(false), // 暂时用不上
      formatResult(data) {
        return data.data.data
      }
    }
  )

  const onApplyVersion = async () => {
    if (!yamlHistoryVersion.value) {
      proxy.$Message.warning('请先选择版本')
      return
    }
    const params: SubmitParams = {
      clusterId: props.resourceEntity?.clusterId,
      ...(props.isSkipNamespaceInParams ? {} : { namespace: props.resourceEntity.namespace }),

      isApplyToUnifiedCluster: isApplyToUnifiedCluster.value,

      data: yaml2json(yamlData.value)
    }
    if (!!props.isCheckYaml) {
      onCheckBeforeSubmit(params)
    } else {
      onOpenConfirmRolloutModal(params)
    }
  }

  const onOpenConfirmRolloutModal = (params) => {
    proxy.$Modal.confirm({
      title: '温馨提示',
      content: '是否确认应用版本',
      loading: true,
      onOk: () => {
        onRolloutYaml(params).then(() => {
          proxy.$Modal.remove()
        })
        isApplyToUnifiedCluster.value && onSyncUnifiedCluster({ name: params?.data?.metadata?.name })
      }
    })
  }

  const onCheckBeforeSubmit = async (params) => {
    const res = await usePost(
      `${Config.Api.Base}${Config.Api.Resource}${props.resourceType}${Config.Api.CheckYamlData}`,
      { ...params, action: 'update' },
      {
        skipLoading: false
      }
    )

    if (res.success) {
      if (res.data?.data) {
        proxy.$Modal.confirm({
          title: '温馨提示',
          render(h) {
            return h('div', [h('div', '旧版本存在以下问题，是否仍确认应用版本？'), h('br'), h('div', res.data.data)])
          },
          loading: true,
          onOk: () => {
            onRolloutYaml(params).then(() => {
              proxy.$Modal.remove()
            })
            isApplyToUnifiedCluster.value && onSyncUnifiedCluster({ name: params?.data?.metadata?.name })
          }
        })
      } else {
        onOpenConfirmRolloutModal(params)
      }
    }
  }

  const onSyncUnifiedCluster = async (extraParams) => {
    const params = {
      clusterId: props.resourceEntity?.clusterId,
      ...(props.isSkipNamespaceInParams ? {} : { namespace: props.resourceEntity.namespace }),
      ...extraParams
    }
    const res = await usePost(
      `${Config.Api.Base}${Config.Api.Resource}${props.resourceType}${Config.Api.SyncUnifiedCluster}`,
      params,
      { skipLoading: false }
    )
    if (res.success) {
      syncUnifiedClusterModal.value = { visible: true, data: res.data.data, params }
    }
  }

  const onRolloutYaml = async (params) => {
    const res = await usePost(
      `${Config.Api.Base}${Config.Api.Resource}${props.resourceType}${Config.Api.RolloutYamlData}`,
      params,
      { skipLoading: false }
    )
    if (res.success) {
      proxy.$Message.success('应用版本成功')
      getYamlHistoryList()
      getYaml()
      yamlHistoryVersion.value = undefined
    }
  }

  watch(
    () => props.value,
    () => {
      if (props.value) {
        props.yamlHistoryParams && getYamlHistoryList()
        getYaml()
      } else {
        yamlData.value = undefined
      }
    }
  )

  watch(
    [() => props.resourceEntity, () => props.value],
    () => {
      props.value &&
        nextTick(() => {
          getIsUnifiedCluster()
        })
    },
    { immediate: true }
  )

  watch(yamlHistoryVersion, () => {
    if (yamlHistoryVersion.value) {
      yamlData.value = yamlHistoryList.value.find((i) => i.id === yamlHistoryVersion.value).yaml
    } else {
      yamlData.value = latestYaml.value
    }
  })

  return {
    yamlLoading,
    yamlData,
    yamlHistoryList,
    yamlHistoryLoading,
    yamlHistoryVersion,
    onApplyVersion,
    isUnifiedCluster,
    isApplyToUnifiedCluster,
    unifiedClusterList,
    syncUnifiedClusterModal,
    onSyncUnifiedCluster
  }
}
