/* eslint-disable @typescript-eslint/no-empty-function */

import { ModelConfig } from '@antv/g6-core'
import { RouterItem, TableRequestResponseData } from '../type'
import { MESSAGE_GROUP_KEY, getEllipsisTextNoticeKey } from '../register/node'
import { TreeGraph } from '@antv/g6'
import { EnumNodeType, getLinkUrl } from '../config'
import { useGet, usePost } from '@/libs/service.request'
import { mockTableRes } from '../mock'
import Config from '@/config'
import { getChildrenNodes } from '../util'
import { json2yaml } from '@/libs/util'

const childCollapseArr = [EnumNodeType['Http.Route'], EnumNodeType.VirtualService]
const onChildCollapsed = (children, graph, nodeType) => {
  children.forEach((node) => {
    const childCollapse = childCollapseArr.includes(nodeType) ? false : true
    const childModel = node.getModel()
    childModel.collapsed = childCollapse
    graph.layout()
    graph.setItemState(node, 'collapse', childCollapse)
    const targets = node.getNeighbors('target')
    targets.length && onChildCollapsed(targets, graph, nodeType)
  })
}
/**
 * 节点缩放
 * 节点右侧点击可以展开收缩子节点
 * 第四层节点（HTTP.Route）收缩时收缩全部子节点，再点击展开，展开所有子节点
 * 除此外的节点收缩时收缩全部子节点，再点击展开，展开第一层子节点
 */
export const handleCollapse = (graph: TreeGraph, e, currentLevel) => {
  const item = e.item
  const model = item.getModel()
  model.collapsed = !model.collapsed
  graph.layout()
  graph.setItemState(item, 'collapse', model.collapsed as boolean)

  if (model.collapsed === false) {
    const children = item.getNeighbors('target')
    children.length && onChildCollapsed(children, graph, model.nodeType)
  }

  // 重置子节点样式
  getChildrenNodes(item).forEach((node) => {
    graph.updateItem(node, {
      level: currentLevel
    })
  })
}

export const onNodeHover = (graph: TreeGraph, e, eventType: 'enter' | 'leave') => {
  const { target } = e
  //   console.log('onNodeHover', target)
  if (!target) return

  const item = target.cfg as ModelConfig & RouterItem

  switch (item.type) {
    case 'text':
      onToggleLabelNotice(graph, e, eventType === 'enter' ? 'show' : 'hidden')
      break

    default:
      e.item.getModel()?.level !== 0 && onToggleMessageNotice(graph, e, eventType === 'enter' ? 'show' : 'hidden')
      break
  }
}

const onToggleLabelNotice = (graph: TreeGraph, e, eventType: 'show' | 'hidden') => {
  const item = e.target.cfg as ModelConfig & RouterItem

  const { textKey, backgroundKey } = getEllipsisTextNoticeKey(item.name)
  const group = e.item._cfg.group
  const textNotice = group.find((e) => e.get('name') === textKey)
  const backgroundNotice = group.find((e) => e.get('name') === backgroundKey)

  if (textNotice || backgroundNotice) {
    if (eventType === 'show') {
      textNotice.show()
      backgroundNotice.show()
      const neighbors = e.item.getNeighbors('target')
      neighbors?.map((i) => i.toBack())
      backgroundNotice.toFront()
      textNotice.toFront()
    } else {
      textNotice.hide()
      backgroundNotice.hide()
    }
  }
}

const onToggleMessageNotice = (graph: TreeGraph, e, eventType: 'show' | 'hidden') => {
  const messageGroup = e?.item?._cfg?.group?.cfg?.children?.filter((i) => i.cfg.name === MESSAGE_GROUP_KEY)?.[0]

  if (messageGroup) {
    if (eventType === 'show') {
      const neighbors = e.item.getNeighbors()
      neighbors?.map((i) => i.toBack())
      e.item.toFront()
      messageGroup.toFront()
      messageGroup.show()
    } else {
      messageGroup.hide()
    }
  }
}

/**
 * 节点点击table
 * 1、打开弹出
 * 2、通过url和params请求参数columns + data
 * 3、渲染明细表格
 */
export const onTableBtnClick = async (graph: TreeGraph, e, message, openTableInModal) => {
  //   openTableInModal(mockTableRes)
  //   return
  const item = e.item.getModel() as ModelConfig & RouterItem
  console.log('onTableBtnClick', item)
  if (!item.operations?.table?.url) {
    message.warning(`缺少请求地址，无法请求列表数据，请联系相关人员处理！`)
    return
  }
  if (!item.operations?.table?.args) {
    message.warning(`缺少请求参数，无法请求列表数据，请联系相关人员处理！`)
    return
  }

  const res = await usePost<{ data: TableRequestResponseData }>(
    `${Config.Api.Base}${item.operations.table.url}`,
    item.operations.table.args
  )
  if (res.success) {
    openTableInModal(res.data.data)
  }
}

/**
 * 节点点击link 跳转istio，url拼接name
 */
export const onLinkBtnClick = (graph: TreeGraph, e) => {
  const item = e.item.getModel() as ModelConfig & RouterItem
  console.log('onTableBtnClick', item)
  const link = getLinkUrl(item)
  window.open(link, '_blank')
}

/**
 * 节点点击Yaml
 * 1. 根据参数请求 yaml/读取 json yaml
 * 2. 打开新浏览器渲染yaml
 */
export const onYamlBtnClick = async (graph: TreeGraph, e, openYamlWindow) => {
  const item = e.item.getModel() as ModelConfig & RouterItem
  console.log('onYamlBtnClick', item)
  if (item.operations?.view !== '') {
    const yamlJson = json2yaml(JSON.parse(item.operations.view))
    openYamlWindow(yamlJson)
  } else {
    const res = await useGet<TableRequestResponseData>(
      `${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`,
      {
        params: {
          ...item.gvr,
          cluster_id: item.clusterId,
          namespace: item.namespace,
          resource_name: item.name,
          is_edit: false
        }
      }
    )
    if (res.success) {
      openYamlWindow(res.data.data)
    }
  }
}

/**
 * 搜索高亮 - 被搜索出来的节点框住标识(缩略布局 / 正常布局)
 */
export const onSearchNodeInfo = (graph: TreeGraph, e) => {}
