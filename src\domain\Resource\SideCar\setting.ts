import { relativeTime } from '@/libs/tools'
import Space from '@/components/space'
import dayjs from 'dayjs'

export const TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,
    tooltipTheme: 'light',
    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]

export const EGRESS_TABLE_COLUMNS = [
  {
    title: '目标（namespace/hosts）',
    key: 'hosts',
    render: (h, params) => {
      return h(
        'div',
        params.row.hosts?.map((i) => h('div', i))
      )
    }
  },
  {
    title: '端口',
    key: 'port',
    render: (h, params) => {
      return h(
        'span',
        `${params.row.port?.data?.name ?? params.row.port?.name ?? '-'} | ${
          params.row.port?.data?.number ?? params.row.port?.number ?? '-'
        } | ${params.row.port?.data?.protocol ?? params.row.port?.protocol ?? '-'}`
      )
    }
  }
]

export const WORKLOAD_SELECTOR_TABLE_COLUMNS = [
  {
    title: 'Pod',
    key: 'pod'
  },
  {
    title: 'Label',
    key: 'label',
    render: (h, params) => {
      return h(
        Space,
        params.row?.labels?.map((i) => h('span', `${i.key}:${i.value}`))
      )
    }
  }
]

export const SIDECAR_DETAIL_CONFIG = [
  {
    title: 'Name',
    key: 'name',
    render: (h, data) => {
      return h('b', data.name)
    }
  },
  {
    title: 'Namespace',
    key: 'namespace'
  },
  {
    title: 'CreationTime',
    key: 'creationTime',
    render: (h, data) => {
      return h('span', dayjs(data.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm'))
    }
  },
  {
    title: 'Labels',
    key: 'labels',
    slot: 'labels'
  }
]
