<template>
  <div class="resource-overview-wrapper">
    <Alert show-icon>
      <span style="color: #2a7cc3">{{ currentNamespace }}</span> 空间下的所有资源及Pod状态统计;
      <p style="margin-top: 16px; margin-left: 16px">
        <b>应用资源: </b>
        管理无状态服务 (Deployment)、有状态服务(StatefulSet) , 适用于大部分普通用户(业务开发),
        配置上会提供明确的引导以及更强的易用性;
      </p>
      <p style="margin-top: 16px; margin-left: 16px">
        <b>命名空间资源: </b>
        以命名空间维度进行资源管理, 更适合专业(运维、熟悉整体架构)的用户, 部分配置仅提供原生的YAML配置.
        后续我们也会继续完善;
      </p>
    </Alert>
    <Row :gutter="10">
      <Col :span="18">
        <Card title="应用资源" :bordered="false">
          <Row :gutter="10">
            <Col :span="6">
              <div @click="gotoAppDeployment" class="card" style="border-left: 10px solid #e65e67">
                <strong>Deployment</strong>
                <span :class="`resource-number ${overviewData.appResource.deployment === 0 ? 'zero' : ''}`">{{
                  overviewData.appResource.deployment
                }}</span>
              </div>
            </Col>
            <Col :span="6">
              <div @click="gotoAppStatefulSet" class="card" style="border-left: 10px solid #f273b5">
                <strong>StatefulSet</strong>
                <span :class="`resource-number ${overviewData.appResource.statefulset === 0 ? 'zero' : ''}`">{{
                  overviewData.appResource.statefulset
                }}</span>
              </div>
            </Col>
            <!-- <Col :span="6">
              <div @click="gotoSubNamespace" class="card" style="border-left: 10px solid #6682f5">
                <strong>SubNamespace</strong>
                <span :class="`resource-number ${overviewData.appResource.subNamespace === 0 ? 'zero' : ''}`">{{
                  overviewData.appResource.subNamespace
                }}</span>
              </div>
            </Col> -->
          </Row>
        </Card>
        <Row>
          <div style="margin-top: 16px">
            <Card title="命名空间资源" :bordered="false">
              <Row :gutter="10">
                <Col :span="6">
                  <div @click="gotoDeployment" class="card" style="border-left: 10px solid #1890ff">
                    <strong>Deployment</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.deployment === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.deployment
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoStatefulSet" class="card" style="border-left: 10px solid #40a9ff">
                    <strong>StatefulSet</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.statefulset === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.statefulset
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoService" class="card" style="border-left: 10px solid #26c9c3">
                    <strong>Service</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.service === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.service
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoPod" class="card" style="border-left: 10px solid #45dad1">
                    <strong>Pod</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.pod === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.pod
                    }}</span>
                  </div>
                </Col>
              </Row>
              <Row :gutter="10" style="margin-top: 16px">
                <Col :span="6">
                  <div @click="gotoHPA" class="card" style="border-left: 10px solid #73d13d">
                    <strong>HPA</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.hpa === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.hpa
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoConfigmap" class="card" style="border-left: 10px solid #95de64">
                    <strong>Configmap</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.configmap === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.configmap
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoSecret" class="card" style="border-left: 10px solid #bae637">
                    <strong>Secret</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.secret === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.secret
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoVS" class="card" style="border-left: 10px solid #f8e139">
                    <strong>VirtualService</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.vs === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.vs
                    }}</span>
                  </div>
                </Col>
              </Row>
              <Row :gutter="10" style="margin-top: 16px">
                <Col :span="6">
                  <div @click="gotoDR" class="card" style="border-left: 10px solid #ffd666">
                    <strong>DestinationRule</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.dr === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.dr
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoSE" class="card" style="border-left: 10px solid #ffa940">
                    <strong>ServiceEntry</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.se === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.se
                    }}</span>
                  </div>
                </Col>
                <Col :span="6">
                  <div @click="gotoSideCar" class="card" style="border-left: 10px solid #f88d48">
                    <strong>SideCar</strong>
                    <span :class="`resource-number ${overviewData.k8sResource.sidecar === 0 ? 'zero' : ''}`">{{
                      overviewData.k8sResource.sidecar
                    }}</span>
                  </div>
                </Col>
              </Row>
            </Card>
          </div>
        </Row>
      </Col>
      <Col :span="6">
        <div class="right-box">
          <Card title="基础信息" :bordered="false">
            <div class="basic-item">
              <span>Cluster</span>
              <span>{{ currentClusterName }}</span>
            </div>
            <div class="basic-item">
              <span>Namespace</span>
              <span>{{ currentNamespace }} (<LinkButton text="查看YAML" @click="onOpenYamlDrawer" />)</span>
            </div>
          </Card>
          <Card title="控制面板" :bordered="false">
            <Alert>操作会影响空间下的资源.</Alert>
            <div class="content">
              <Row type="flex">
                <Col :span="14">
                  <Tooltip transfer placement="left" content="开启空间下所有代理(Sidecar) 的 AccessLog" max-width="200">
                    <div class="controller-prompt">
                      开启代理日志 <Icon type="ios-alert-outline" style="color: #2a7cc3" />
                    </div>
                  </Tooltip>
                </Col>
                <Col :span="10">
                  <div class="align-right">
                    <i-switch
                      :loading="loading.proxyLogSwitch"
                      v-model="proxyLogSwitch"
                      true-color="#19be6b"
                      @on-change="onProxyLogSwitch"
                    ></i-switch>
                  </div>
                </Col>
              </Row>

              <Row>
                <Col :span="14">
                  <Tooltip
                    transfer
                    placement="left"
                    content="配置空间下统一的出口白名单，即Sidecar配置"
                    max-width="200"
                  >
                    <div class="controller-prompt">
                      服务发现白名单 <Icon type="ios-alert-outline" style="color: #2a7cc3" />
                    </div>
                  </Tooltip>
                </Col>
                <Col :span="10">
                  <div class="align-right">
                    <LinkButton text="配置" @click="onOpenEgressModal" />
                  </div>
                </Col>
              </Row>

              <div class="sidecar-wrapper">
                <Tooltip
                  placement="left"
                  content="设置命名空间下自动注入到Pod的istio-proxy容器版本，close时为关闭自动注入"
                  max-width="200"
                  transfer
                >
                  <div class="controller-prompt">
                    注入istio-proxy容器 <Icon type="ios-alert-outline" style="color: #2a7cc3" />
                  </div>
                </Tooltip>

                <Select
                  filterable
                  style="width: 120px"
                  transfer
                  v-model="istioInjectStatus"
                  @on-select="onIstioInjectStatusChange"
                >
                  <Option v-for="item in istioInjectStatusList" :key="item.value" :value="item.value">{{
                    item.label
                  }}</Option>
                </Select>
              </div>
            </div>
          </Card>
        </div>
      </Col>
    </Row>
    <Drawer title="查看YAML" :mask-closable="false" :scrollable="false" :width="45" v-model="yamlDrawer.visible">
      <yaml v-model="yamlDrawer.data" />
    </Drawer>
    <Modal
      width="600"
      class-name="vertical-center-modal"
      title="服务发现出口白名单配置"
      v-model="sidecarModalVisible"
      :loading="sidecarModalLoading"
      :mask-closable="false"
      :transfer="false"
      @on-ok="onSubmitEgress"
    >
      <ArrayObject
        label="目标服务"
        :data="{ data: sideCarData }"
        name="data"
        addButtonPosition="top"
        canEdit
        :columns="sideCarCols"
        @on-ok="onCreateOrUpdateEgress"
        @on-delete="onDeleteEgress"
      >
        <template #default="{ record, visible }">
          <Form>
            <ProFormItem
              name="hosts"
              label="Host（目标服务）"
              :data="record"
              contentStyleMode="background"
              desc="用于配置具体的出口白名单服务,格式:{namespace}/{service}
                        namespace字段可以配置具体的命名空间或通配符  '*'  , '.';
                        service可以配置服务名 或 通配符'*'
                        例如:
                        infra/*  infra命名空间下的所有服务或主机
                        infra/cloud.infra.svc.cluster.local infra命名空间下的cloud服务"
              url="https://istio.io/latest/zh/docs/reference/config/networking/sidecar/#IstioEgressListener"
            >
              <MultiInput
                key="host"
                addLabel="添加"
                :data="record"
                :on-delete="(index) => record?.hosts?.splice(index, 1)"
                :on-init="() => genNonDuplicateArr(record?.hosts?.length || 1)"
              >
                <template #default="{ index }">
                  <Input
                    placeholder="{namespace}/{service}"
                    :value="record?.hosts?.[index]"
                    @on-change="(e) => onEgressHostsChange(record, index, e.target.value)"
                  />
                </template>
              </MultiInput>
            </ProFormItem>
            <ProFormItem
              name="port"
              label="Port（端口）"
              :data="record"
              contentStyleMode="background"
              :dataReloadFlag="visible"
              :mode="EnumFormItemControllerType.Switch"
              desc="定义了Host(目标服务)特定的端口信息。"
              url="https://istio.io/latest/zh/docs/reference/config/networking/gateway/#Port"
            >
              <template #default>
                <div class="multi-item-wrapper">
                  <Input
                    placeholder="端口名称"
                    :value="record.port?.data?.name"
                    @on-change="(e) => onEgressPortChange(record, 'name', e.target.value)"
                  />
                  <Input
                    placeholder="端口号"
                    :value="record.port?.data?.number"
                    @on-change="(e) => onEgressPortChange(record, 'number', e.target.value)"
                  />
                  <Select
                    placeholder="端口协议"
                    filterable
                    transfer
                    :value="record.port?.data?.protocol"
                    @on-change="(val) => onEgressPortChange(record, 'protocol', val)"
                  >
                    <Option v-for="item in formatEnumToLabelValue(EnumPorts)" :key="item.value" :value="item.value">{{
                      item.label
                    }}</Option>
                  </Select>
                </div>
              </template>
            </ProFormItem>
          </Form>
        </template>
      </ArrayObject>
    </Modal>
    <div style="margin-top: 16px">
      <Card title="Pod 状态统计" :bordered="false">
        <Row :gutter="10">
          <Col span="6">
            <div @click="gotoPodWithStatus('Pending')" class="card" style="border-left: 10px solid #1890ff">
              <strong>Pending</strong>
              <span :class="`resource-number ${overviewData.podStatus.pending === 0 ? 'zero' : ''}`">{{
                overviewData.podStatus.pending
              }}</span>
            </div>
          </Col>
          <Col span="6">
            <div @click="gotoPodWithStatus('Running')" class="card" style="border-left: 10px solid #73d13d">
              <strong>Running</strong>
              <span :class="`resource-number ${overviewData.podStatus.running === 0 ? 'zero' : ''}`">{{
                overviewData.podStatus.running
              }}</span>
            </div>
          </Col>
          <Col span="6">
            <div @click="gotoPodWithStatus('Succeeded')" class="card" style="border-left: 10px solid #26c9c3">
              <strong>Succeeded</strong>
              <span :class="`resource-number ${overviewData.podStatus.succeeded === 0 ? 'zero' : ''}`">{{
                overviewData.podStatus.succeeded
              }}</span>
            </div>
          </Col>
          <Col span="6">
            <div @click="gotoPodWithStatus('Failed')" class="card" style="border-left: 10px solid #e65e67">
              <strong>Failed</strong>
              <span :class="`resource-number ${overviewData.podStatus.failed === 0 ? 'zero' : ''}`">{{
                overviewData.podStatus.failed
              }}</span>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  </div>
</template>

<script>
import { set } from 'vue'
import { Tooltip } from 'view-design'

import config from '@/config'
import { genNonDuplicateArr } from '@/libs/tools'
import { usePost, useGet } from '@/libs/service.request'
import useSingleK8SService from '@/libs/useSingleK8SService'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'

import {
  ApiNamespaceOverviewList,
  ApiNamespaceTelemetryEnvoyAccessLogSet,
  ApiNamespaceTelemetryEnvoyAccessLogGet
} from '@/api/k8s/namespace/app/resourceOverview'
import { Yaml, LinkButton } from '@/components'
import { EnumPorts } from '@/domain/Resource/SideCar/enum'
import { ArrayObject, ProFormItem, MultiInput, EnumFormItemControllerType } from '@/components/pro-form/index'

export default {
  name: 'ResourceOverview',
  components: {
    ArrayObject,
    ProFormItem,
    MultiInput,
    LinkButton,
    Tooltip,
    Yaml
  },
  setup() {
    const { K8SKey } = useSingleK8SService()
    return {
      K8SKey
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentClusterName() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  data() {
    return {
      mTitle: '信息统计',
      overviewData: {
        appResource: {
          deployment: 0,
          statefulset: 0,
          subNamespace: 0
        },
        k8sResource: {
          deployment: 0,
          statefulset: 0,
          service: 0,
          pod: 0,
          hpa: 0,
          configmap: 0,
          secret: 0,
          vs: 0,
          dr: 0,
          se: 0,
          sidecar: 0
        },
        podStatus: {
          pending: 0,
          running: 0,
          succeeded: 0,
          failed: 0
        }
      },
      proxyLogSwitch: false,
      loading: {
        proxyLogSwitch: false
      },
      sideCarCols: [
        {
          title: '目标（namespace/hosts）',
          key: 'hosts',
          minWidth: 200,
          render: (h, params) => {
            return h(Tooltip, {
              class: 'text-ellipsis',
              props: {
                transferClassName: 'text-ellipsis',
                transfer: true,
                placement: 'top-start',
                theme: 'light'
              },
              scopedSlots: {
                default: () => params.row.hosts?.join(','),
                content: () => params.row.hosts?.map((i) => h('p', '-  ' + i))
              }
            })
          }
        },
        {
          title: '端口',
          key: 'port',
          width: 180,
          render: (h, params) => {
            const text = `${params.row.port?.data?.name ?? params.row.port?.name ?? '-'} | ${
              params.row.port?.data?.number ?? params.row.port?.number ?? '-'
            } | ${params.row.port?.data?.protocol ?? params.row.port?.protocol ?? '-'}`
            return h(
              Tooltip,
              {
                class: 'text-ellipsis',
                props: {
                  transferClassName: 'text-ellipsis',
                  content: text,
                  transfer: true,
                  placement: 'top-start',
                  theme: 'light'
                }
              },
              text
            )
          }
        }
      ],
      sideCarData: [],
      EnumFormItemControllerType: EnumFormItemControllerType,
      EnumPorts: EnumPorts,
      sidecarModalVisible: false,
      sidecarModalLoading: true,
      istioInjectStatus: undefined,
      istioInjectStatusList: [],
      yamlDrawer: {
        visible: false
      }
    }
  },
  methods: {
    init() {
      this.fetchOverViewData()
      this.fetchProxyLogSwitch()
      this.getEgressTableList()
      this.initIstioInjectStatusList()
      this.getIstioInjectStatus()
    },
    fetchProxyLogSwitch() {
      ApiNamespaceTelemetryEnvoyAccessLogGet({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace
      })
        .then((res) => {
          this.proxyLogSwitch = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    onProxyLogSwitch() {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认开启代理日志？`,
        loading: true,
        onCancel: () => {
          this.proxyLogSwitch = !this.proxyLogSwitch
        },
        onOk: async () => {
          await ApiNamespaceTelemetryEnvoyAccessLogSet({
            clusterId: this.currentClusterId,
            namespace: this.currentNamespace,
            status: this.proxyLogSwitch
          })
            .then((res) => {
              noticeSucceed(this, '编辑成功！')
            })
            .catch((err) => {
              this.proxyLogSwitch = !this.proxyLogSwitch
              noticeError(this, errorMessage(err))
            })
          this.$Modal.remove()
        }
      })
    },
    gotoAppDeployment() {
      this.$router.push({
        name: 'deployment-list'
      })
    },
    gotoAppStatefulSet() {
      this.$router.push({
        name: 'stateful-set-list'
      })
    },
    // gotoSubNamespace() {
    //   // namespace-subnamespace-list
    //   this.$router.push({
    //     name: 'namespace-subnamespace-list'
    //   })
    // },
    gotoDeployment() {
      this.$router.push({
        name: 'workloads-deployment-list'
      })
    },
    gotoStatefulSet() {
      this.$router.push({
        name: 'workloads-stateful-set-list'
      })
    },
    gotoService() {
      this.$router.push({
        name: 'namespace-services'
      })
    },
    gotoPod() {
      this.$router.push({
        name: 'namespace-pod'
      })
    },
    gotoHPA() {
      this.$router.push({
        name: 'namespace-hpa'
      })
    },
    gotoConfigmap() {
      this.$router.push({
        name: 'namespace-configmap'
      })
    },
    gotoSecret() {
      this.$router.push({
        name: 'namespace-secrets'
      })
    },
    gotoVS() {
      this.$router.push({
        name: 'namespace-mesh-virtualservice'
      })
    },
    gotoDR() {
      this.$router.push({
        name: 'namespace-mesh-destinationRule'
      })
    },
    gotoSE() {
      this.$router.push({
        name: 'namespace-mesh-serviceEntry'
      })
    },
    gotoSideCar() {
      this.$router.push({
        name: 'namespace-mesh-sideCar'
      })
    },
    gotoPodWithStatus(status) {
      this.$router.push({
        name: 'namespace-pod',
        query: {
          status: status
        }
      })
    },
    async fetchOverViewData() {
      await ApiNamespaceOverviewList({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace
      })
        .then((res) => {
          this.overviewData = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    genNonDuplicateArr(length) {
      return genNonDuplicateArr(length)
    },
    formatEnumToLabelValue(enumObj) {
      const arr = []
      for (const [value, label] of Object.entries(enumObj)) {
        arr.push({ value, label: label.toString() })
      }
      return arr
    },
    onEgressHostsChange(data, index, val) {
      if (!data.hosts?.length) {
        set(data, 'hosts', [])
      }
      data.hosts.splice(index, 1, val)
    },
    onEgressPortChange(data, key, val) {
      if (!data.port?.data) {
        set(data.port, 'data', {})
      }
      set(data.port.data, key, val)
    },
    onCreateOrUpdateEgress(type, record, index) {
      console.log(type, record, index)
      const params = [...this.sideCarData]
      params.splice(index, type === 'create' ? 0 : 1, record)
      this.sideCarData = params
    },
    onDeleteEgress(index) {
      console.log(index)
      const params = [...this.sideCarData]
      params.splice(index, 1)
      this.sideCarData = params
    },
    async getEgressTableList() {
      const res = await useGet(
        `${config.Api.Base}${config.Api.GetEgressTableList}?clusterId=${this.currentClusterId}&namespace=${this.currentNamespace}`
      )
      if (res.success) {
        this.sideCarData = res.data.data ?? []
      }
    },
    async onSubmitEgress() {
      this.sidecarModalLoading = true
      const res = await usePost(`${config.Api.Base}${config.Api.UpdateEgress}`, {
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        data: this.sideCarData
      })
      if (res.success) {
        this.$Message.success('操作成功')
        await this.$nextTick()
        this.getEgressTableList()
        this.sidecarModalVisible = false
      }
      this.sidecarModalLoading = false
    },
    onOpenEgressModal() {
      this.getEgressTableList()
      this.sidecarModalVisible = true
      this.sidecarModalLoading = true
    },
    async onOpenYamlDrawer() {
      const res = await useGet(`${config.Api.Base}${config.Api.Resource}${config.Api.GetLatestYaml}`, {
        params: {
          resource: 'namespaces',
          cluster_id: this.currentClusterId,
          namespace: '',
          resource_name: this.currentNamespace,
          group: '',
          version: 'v1',
          is_edit: false
        }
      })
      if (res.success) {
        this.yamlDrawer = {
          data: res.data.data,
          visible: true
        }
      }
    },
    async initIstioInjectStatusList() {
      const res = await useGet(
        `${config.Api.Base}${config.Api.GetIstioInjectStatusList}?clusterId=${this.currentClusterId}`
      )
      this.istioInjectStatusList =
        res.data.data?.map((i) => ({ value: i.tag, label: `${i.tag}${i.rev ? `[${i.rev}]` : ''}` })) ?? []
    },
    async getIstioInjectStatus() {
      const res = await useGet(
        `${config.Api.Base}${config.Api.GetIstioInjectStatus}?clusterId=${this.currentClusterId}&namespace=${this.currentNamespace}`
      )
      if (res.success) {
        this.istioInjectStatus = res.data.data?.tag
      }
    },
    async onIstioInjectStatusChange(val) {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认命名空间注入istio-proxy容器？`,
        loading: true,
        onCancel: () => {
          this.getIstioInjectStatus()
        },
        onOk: async () => {
          const res = await usePost(`${config.Api.Base}${config.Api.UpdateIstioInjectStatus}`, {
            clusterId: this.currentClusterId,
            namespace: this.currentNamespace,
            tag: val.value // 改成select触发的val变成了整个对象。。。。
          })
          if (res.success) {
            this.$Message.success('编辑成功')
            await this.$nextTick()
          }
          this.getIstioInjectStatus()
          this.$Modal.remove()
        }
      })
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    K8SKey() {
      this.init()
    }
  }
}
</script>

<style scoped lang="less">
.resource-overview-wrapper {
  .card {
    border: 1px solid #e8eaec;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    &:hover {
      cursor: pointer;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
      border-color: #eee;
      transition: all 0.3s ease-in-out;
    }
  }

  .resource-number {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    &.zero {
      color: #c5c8ce;
    }
  }

  .controller-prompt {
    color: #515a6e;
  }
  .whitelist-title {
    color: #3a3333;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .multi-item-wrapper {
    display: flex;
    flex: 1 0 0%;
    > div {
      flex: 1 0 0%;
      &:not(:last-child) {
        margin-right: 16px;
      }
    }
  }
  /deep/.ivu-card-head p {
    color: #515a6e;
  }
  /deep/.vertical-center-modal {
    display: flex;
    align-items: center;
    justify-content: center;

    .ivu-modal {
      top: 0;
      .ivu-modal-body {
        max-height: 70vh;
        overflow: auto;
      }
    }
  }
  .sidecar-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .right-box {
    height: 470px;
    display: flex;
    flex-direction: column;
    > .ivu-card:first-child {
      height: 146px;
      margin-bottom: 16px;
      .basic-item {
        &:first-child {
          margin-bottom: 16px;
        }
        &:last-child > span:last-child {
          display: flex;
          align-items: center;
        }
      }
    }
    > .ivu-card:last-child {
      flex: 1 0 0%;
      /deep/ .ivu-card-body {
        height: calc(~'100% - 51px');
        .content {
          display: flex;
          flex-direction: column;
          // height: calc(~'100% - 44px');
          > div {
            height: 32px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .basic-item {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
  }
  .align-right {
    text-align: right;
  }
}
</style>
<style lang="less">
.text-ellipsis {
  display: flex;
  margin: 0;
  > .ivu-tooltip-rel {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ivu-tooltip-inner {
    max-width: unset;
    p {
      max-width: 500px;
      white-space: pre-wrap;
      margin-bottom: 16px;
    }
  }
}
</style>
