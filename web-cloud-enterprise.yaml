apiVersion: apps/v1
kind: Deployment
metadata:
 name: web-cloud-enterprise
 labels: 
   app: web-cloud-enterprise
spec:
 replicas: 1
 selector:
   matchLabels: 
     app: web-cloud-enterprise
 template:
   metadata:
     labels:
       app: web-cloud-enterprise
   spec:
     containers:
     - name: web-cloud-enterprise
       image: web-cloud-enterprise:v1.0

---
apiVersion: v1
kind: Service
metadata:
 name: web-cloud-enterprise
spec:
 type: NodePort
 ports:
 - port: 81
   nodePort: 31234 
 selector:
   app: web-cloud-enterprise