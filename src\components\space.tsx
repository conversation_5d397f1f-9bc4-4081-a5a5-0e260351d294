import styled from 'vue-styled-components'
import { PropType, defineComponent } from 'vue'

const SpaceCom = styled('div', {
  direction: { type: String as PropType<'vertical' | 'horizontal'>, default: 'horizontal' },
  size: { type: Number, default: 16 },
  justify: { type: Boolean, default: false }
})`
  width: 100%;
  display: inline-flex;
  flex-direction: ${(props) => (props.direction === 'horizontal' ? 'row' : 'column')};
  justify-content: ${(props) => (props.justify ? 'center' : 'normal')};
  > :not(:last-child) {
    margin: ${(props) => (props.direction === 'horizontal' ? `0 ${props.size}px 0 0` : `0 0 ${props.size}px 0`)};
  }
`

export default defineComponent({
  name: 'Space',
  props: {
    direction: { type: String as PropType<'vertical' | 'horizontal'>, default: 'horizontal' },
    size: { type: Number, default: 16 },
    justify: { type: Boolean, default: false }
  },

  setup(props, { attrs, slots, emit }) {
    return () => (
      <SpaceCom
        class="space-component"
        {...attrs}
        direction={props.direction}
        size={props.size}
        justify={props.justify}
        onClick={() => emit('click')}
        onScroll={(e) => emit('scroll', e)}
      >
        {slots.default?.()}
      </SpaceCom>
    )
  }
})
