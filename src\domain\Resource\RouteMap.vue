<script lang="ts" setup>
import { RouteMap } from '@/components/route-map'
import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { nextTick, onMounted, ref, getCurrentInstance, watch } from 'vue'
import { useRequest } from 'vue-request'
import { RouterItem } from '@/components/route-map/type'
import store from '@/store'
import { Space } from '@/components'
import useSingleK8SService from '@/libs/useSingleK8SService'

const domain = ref()
const virtualServices = ref<string[]>()
const K8SInstance = ref<{ namespace: string; clusterId: string }>()
const { proxy } = getCurrentInstance()
const { K8SKey } = useSingleK8SService({
  skipNamespaceNotice: true
})

const {
  data: domainList,
  run: getDomainList,
  loading: domainListLoading
} = useRequest(
  () => {
    return useGet(`${Config.Api.Base}${Config.Api.GetDomainList}`, {
      params: {
        clusterId: K8SInstance.value.clusterId,
        namespace: K8SInstance.value.namespace
      }
    })
  },
  {
    manual: true,
    formatResult: (res) => {
      return res.data.data?.map((i) => ({ value: i, label: i }))
    },
    onSuccess: () => {
      virtualServiceList.value = undefined
      virtualServices.value = undefined
      routeData.value = undefined
    }
  }
)

const {
  run: getVirtualServiceList,
  data: virtualServiceList,
  loading: virtualServiceListLoading
} = useRequest(
  () => {
    return useGet<{ data: { name: string; namespace: string }[] }>(
      `${Config.Api.Base}${Config.Api.GetDomainVirtualServiceList}`,
      {
        params: {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          domain: domain.value
        }
      }
    )
  },
  {
    manual: true,
    formatResult: (res) => {
      const set = new Set([])
      res.data.data?.forEach((i) => {
        set.add(`${i.namespace}/${i.name}`)
      })
      return Array.from(set).map((i) => ({ value: i, label: i }))
    },
    onSuccess: async (data) => {
      if (!data?.length || data?.length <= 10) {
        virtualServices.value = data?.map((i) => i.value)
        proxy.$Message.success('检测到vs节点少于10个，已自动请求全部数据')
        await nextTick()
        getRouteData()
      } else {
        proxy.$Message.info('检测到vs节点大于10个，请选择需要查看的vs节点再请求')
        routeData.value = undefined
      }
    }
  }
)

const {
  run: getRouteData,
  data: routeData,
  loading
} = useRequest(
  () => {
    return usePost<{ data: RouterItem }>(`${Config.Api.Base}${Config.Api.GetDomainRouterTree}`, {
      clusterId: K8SInstance.value.clusterId,
      domain: domain.value,
      vsInfos: virtualServices.value?.map((i) => {
        const [namespace, name] = i.split('/')
        return { name, namespace }
      })
    })
  },
  {
    manual: true,
    formatResult: (res) => res.data.data
  }
)
const onDomainChange = () => {
  if (domain.value) {
    getVirtualServiceList()
    virtualServices.value = []
  }
}

const onVirtualServicesChange = (val) => {
  if (virtualServices.value.length > 20) {
    proxy.$Message.warning('为了页面的最优性能，请不要选择展示超过10个VirtualService节点')
    virtualServices.value.splice(virtualServices.value.indexOf(val), 1)
  }
}

const onSearch = () => {
  if (!domain.value) {
    proxy.$Message.warning('请选择Domain')
  } else {
    getRouteData()
  }
}

const initK8SInstance = () => {
  store.commit('getCurrentClusterID', store.state.user.userId)
  store.commit('getCurrentNamespace', store.state.user.userId)
  K8SInstance.value = {
    namespace: store.state.k8s.currentNamespace,
    clusterId: store.state.k8s.currentClusterId
  }

  nextTick(getDomainList)
}

onMounted(() => {
  initK8SInstance()
})

watch(K8SKey, () => {
  initK8SInstance()
})
</script>

<template>
  <div>
    <Alert show-icon>
      <strong>网格路由图</strong>
      可以通过选择当前集群的完整域名纵观的查阅通过哪些配置进行路由匹配后到达目标的应用，有助于您快速了解该域名整体的路由架构。
      <a href="https://q9jvw0u5f5.feishu.cn/wiki/E9bywfVSeiDnBHkM6FAcbEyznzf" target="_blank"
        ><Icon type="ios-link" />使用手册
      </a>
      <div class="notice">注意：现阶段仅支持 FQDN 的域名进行查找</div>
    </Alert>
    <div class="operate">
      <Space>
        <Select
          v-model="domain"
          placeholder="请选择Domain"
          filterable
          :loading="domainListLoading"
          @on-change="onDomainChange"
        >
          <Option v-for="item in domainList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>

        <Select
          v-model="virtualServices"
          :placeholder="domain ? '请选择VirtualService' : '请先选择Domain'"
          filterable
          :loading="virtualServiceListLoading"
          multiple
          @on-change="onVirtualServicesChange"
          :max-tag-count="5"
        >
          <Option v-for="item in virtualServiceList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>

        <Button type="primary" icon="md-search" @click="onSearch" :loading="loading">搜索</Button>
      </Space>
      <!-- <span class="notice">* 现阶段仅支持全域名domain的查找</span> -->
    </div>
    <RouteMap
      containerHeight="calc(100vh - 144px)"
      :routeData="routeData"
      :loading="virtualServiceListLoading || loading"
      class="map"
    >
      <template #alert>
        <Alert show-icon> 路由图提供部分配置错误定位的能力, 在每个节点下方会通过颜色进行标记。 </Alert>
      </template>
    </RouteMap>
  </div>
</template>

<style lang="less" scoped>
.notice {
  color: #ed4014;
  margin-top: 8px;
}
.operate {
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;

  > div {
    // margin-bottom: 8px;
    > .ivu-select:first-child {
      width: 280px;
    }
  }
}
.map {
  padding: 16px;
}
</style>
