import {
  login,
  logout,
  getUserInfo,
  getMessage,
  getContentByMsgId,
  hasRead,
  removeReaded,
  restoreTrash,
  getUnreadCount
} from '@/api/user'
import { setToken, getToken } from '@/libs/util'
import { ApiResourcePermissionRefresh } from '@/api/k8s/authResource'
import { axiosInterceptorRequest } from '@/libs/service.request'

export default {
  state: {
    userName: '',
    userId: '',
    avatarName: '',
    token: getToken(),
    access: '',
    hasGetInfo: false,
    unreadCount: 0,
    messageUnreadList: [],
    messageReadedList: [],
    messageTrashList: [],
    messageContentStore: {}
  },
  mutations: {
    setAvatar(state, avatarName) {
      state.avatarName = avatarName
    },
    setUserId(state, id) {
      state.userId = id
    },
    setUserName(state, name) {
      state.userName = name
    },
    setAccess(state, access) {
      state.access = access
    },
    setToken(state, token) {
      state.token = token
      axiosInterceptorRequest({
        headers: {
          Authorization: 'Bearer ' + token
        }
      })
      setToken(token)
    },
    setHasGetInfo(state, status) {
      state.hasGetInfo = status
    },
    setMessageCount(state, count) {
      state.unreadCount = count
    },
    setMessageUnreadList(state, list) {
      state.messageUnreadList = list
    },
    setMessageReadedList(state, list) {
      state.messageReadedList = list
    },
    setMessageTrashList(state, list) {
      state.messageTrashList = list
    },
    updateMessageContentStore(state, { msg_id, content }) {
      state.messageContentStore[msg_id] = content
    },
    moveMsg(state, { from, to, msg_id }) {
      const index = state[from].findIndex((_) => _.msg_id === msg_id)
      const msgItem = state[from].splice(index, 1)[0]
      msgItem.loading = false
      state[to].unshift(msgItem)
    }
  },
  getters: {
    messageUnreadCount: (state) => state.messageUnreadList.length,
    messageReadedCount: (state) => state.messageReadedList.length,
    messageTrashCount: (state) => state.messageTrashList.length
  },
  actions: {
    // 刷新k8s资源权限
    refreshK8sResourcePermission() {
      ApiResourcePermissionRefresh()
        .then((res) => {
          console.log('成功：刷新用户k8s资源权限。')
        })
        .catch((err) => {
          console.log('失败：刷新用户k8s资源权限。')
          throw err
        })
    },
    // 登录
    handleLogin({ commit, dispatch }, { userName, password }) {
      userName = userName.trim()
      return new Promise<void>((resolve, reject) => {
        login({
          userName,
          password
        })
          .then((res) => {
            const data = res.data.data
            console.log(data)
            commit('setToken', data.token)
            // dispatch('refreshK8sResourcePermission')
            resolve()
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 退出登录
    handleLogOut({ state, commit }) {
      return new Promise<void>((resolve, reject) => {
        logout()
          .then(() => {
            commit('setToken', '')
            commit('setAccess', [])
            commit('setCurrentCluster', { uid: state.userId, cluster: '' })
            commit('setCurrentNamespace', { uid: state.userId, namespace: '' })
            commit('setCurrentClusterId', { uid: state.userId, clusterId: '' })
            resolve()
          })
          .catch((err) => {
            reject(err)
          })
        // 如果你的退出登录无需请求接口，则可以直接使用下面三行代码而无需使用logout调用接口
        // commit('setToken', '')
        // commit('setAccess', [])
        // resolve()
      })
    },
    // 获取用户相关信息
    getUserInfo({ state, commit, dispatch }) {
      return new Promise((resolve, reject) => {
        try {
          getUserInfo()
            .then((res) => {
              const data = res.data.data.profile
              console.log(data)

              commit('setAvatar', data.username[0].toUpperCase())
              commit('setUserName', data.username)
              commit('setUserId', data.id)
              commit('setAccess', data.access)
              commit('setHasGetInfo', true)
              // dispatch('refreshK8sResourcePermission')
              // refreshK8sResourcePermission interval 5 min
              // setInterval(() => {dispatch('refreshK8sResourcePermission')}, 300000)
              resolve(data)
            })
            .catch((err) => {
              reject(err)
            })
        } catch (error) {
          reject(error)
        }
      })
    },
    // 此方法用来获取未读消息条数，接口只返回数值，不返回消息列表
    getUnreadMessageCount({ state, commit }) {
      getUnreadCount().then((res) => {
        const { data } = res
        commit('setMessageCount', data)
      })
    },
    // 获取消息列表，其中包含未读、已读、回收站三个列表
    getMessageList({ state, commit }) {
      return new Promise<void>((resolve, reject) => {
        getMessage()
          .then((res) => {
            const { unread, readed, trash } = res.data
            commit(
              'setMessageUnreadList',
              unread.sort((a, b) => new Date(b.create_time).valueOf() - new Date(a.create_time).valueOf())
            )
            commit(
              'setMessageReadedList',
              readed
                .map((_) => {
                  _.loading = false
                  return _
                })
                .sort((a, b) => new Date(b.create_time).valueOf() - new Date(a.create_time).valueOf())
            )
            commit(
              'setMessageTrashList',
              trash
                .map((_) => {
                  _.loading = false
                  return _
                })
                .sort((a, b) => new Date(b.create_time).valueOf() - new Date(a.create_time).valueOf())
            )
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 根据当前点击的消息的id获取内容
    getContentByMsgId({ state, commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        const contentItem = state.messageContentStore[msg_id]
        if (contentItem) {
          resolve(contentItem)
        } else {
          getContentByMsgId(msg_id).then((res) => {
            const content = res.data
            commit('updateMessageContentStore', { msg_id, content })
            resolve(content)
          })
        }
      })
    },
    // 把一个未读消息标记为已读
    hasRead({ state, commit }, { msg_id }) {
      return new Promise<void>((resolve, reject) => {
        hasRead(msg_id)
          .then(() => {
            commit('moveMsg', {
              from: 'messageUnreadList',
              to: 'messageReadedList',
              msg_id
            })
            commit('setMessageCount', state.unreadCount - 1)
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 删除一个已读消息到回收站
    removeReaded({ commit }, { msg_id }) {
      return new Promise<void>((resolve, reject) => {
        removeReaded(msg_id)
          .then(() => {
            commit('moveMsg', {
              from: 'messageReadedList',
              to: 'messageTrashList',
              msg_id
            })
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 还原一个已删除消息到已读消息
    restoreTrash({ commit }, { msg_id }) {
      return new Promise<void>((resolve, reject) => {
        restoreTrash(msg_id)
          .then(() => {
            commit('moveMsg', {
              from: 'messageTrashList',
              to: 'messageReadedList',
              msg_id
            })
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    }
  }
}
