import axios from '@/libs/api.request'


export const ApiSecretList = (params) => {
  return axios.request({
    url: `/api/v1/resource/secret/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiSecretGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/secret/get`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiSecretDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/secret/delete`,
    method: 'delete',
    data: data,
  })
}

export const ApiSecretYamlCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/secret/yaml/create`,
    method: 'post',
    data: data,
  })
}


export const ApiSecretYamlUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/secret/yaml/update`,
    method: 'post',
    data: data,
  })
}
