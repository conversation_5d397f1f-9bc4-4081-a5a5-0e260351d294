<script lang="ts" setup>
import { VirtualService, EnumComponentType as EnumVirtualServiceComponentType } from '@/domain/Resource/VirtualService'
import { ref, watch } from 'vue'

interface Props {
  clusterId: string
  clusterName: string
  namespace: string
  gatewayName?: string
  value: boolean
}

const props = defineProps<Props>()
defineEmits(['input'])
const modalRef = ref()

watch(
  () => props.value,
  () => {
    if (props.value) {
      modalRef.value.modalIndex = 950
      modalRef.value.handleGetModalIndex = () => {
        return modalRef.value.modalIndex - 1000
      }
    }
  }
)
</script>

<template>
  <Modal
    ref="modalRef"
    :value="props.value"
    @on-visible-change="
      (val) => {
        $emit('input', val)
      }
    "
    title="路由配置列表"
    width="65"
    footer-hide
  >
    <VirtualService
      v-if="props.value"
      :type="EnumVirtualServiceComponentType.AssociatedGateway"
      :K8SInstance="{
        clusterId: props.clusterId,
        namespace: props.namespace,
        clusterName: props.clusterName,
        relativeGatewayName: props.gatewayName
      }"
    />
  </Modal>
</template>

<style lang="less" scoped></style>
