<script lang="ts" setup>
import { Space, ViewYaml, ProTable, LinkButton, ResourceForm, EnumFormStatus } from '@/components'
import { EventTable, BatchCopy } from '@/domain/Resource'
import { EnumResourceType } from '@/components/system-search/config'
import {
  ProFormSelect,
  ProFormText,
  ProFormItem,
  ArrayObject,
  ProFormRadio,
  EnumFormItemControllerType,
  MultiInput
} from '@/components/pro-form'
import { TABLE_COLUMNS, RELATED_POD_COLUMNS } from './setting'
import useRayJobService from './useRayJobService'
import { computed, getCurrentInstance, ref } from 'vue'
import { genNonDuplicateArr } from '@/libs/tools'
import { usePost } from '@/libs/service.request'
import Config from '@/config'
import { yaml2json } from '@/libs/util'

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  onSubmitSuccess,
  onViewDetail,
  detailModal,
  K8SInstance,
  detailInfo,
  relatedPodInfo,
  onJumpToPod
} = useRayJobService()

const { proxy } = getCurrentInstance()
const batchCopyModalVisible = ref(false)
const onMountKeyChange = (data: any, key: string, index: number, value: string) => {
  if (data.mounts?.[index]) {
    data.mounts[index][key] = value
  }
}
const handleSubmit = async (data, model) => {
  console.log(data, model)
  if (model === 'Yaml') {
    const obj = yaml2json(data)
    const res = await usePost(`${Config.Api.Base}/api/v1/mlops/ray/job/yaml/create`, {
      namespace: K8SInstance.value?.namespace,
      clusterId: K8SInstance.value?.clusterId,
      data: obj
    })
    if (res.errorMsg) {
      return
    }
  } else {
    const res = await usePost(`${Config.Api.Base}/api/v1/mlops/ray/job/create`, data)
    if (res.errorMsg) {
      return
    }
  }
  formVisible.value = false
  onSubmitSuccess()
}
const formInitData = computed(() => ({
  jobName: '',
  namespace: K8SInstance.value?.namespace,
  clusterId: K8SInstance.value?.clusterId,
  imageUrl: '',
  startCmd: '',
  worker: {
    replicas: 3,
    resources: {
      requests: {
        cpu: '8',
        memory: '16G',
        '52tt.com/gpu-core': '100',
        '52tt.com/gpu-memory': '44'
      },
      limits: {
        cpu: '16',
        memory: '64G',
        '52tt.com/gpu-core': '100',
        '52tt.com/gpu-memory': '44'
      }
    }
  },
  mounts: [
    // {
    //   pvc: 'jarvis-cache',
    //   mountPath: '/home/<USER>/.cache/modelscope',
    //   subPath: ''
    // }
  ]
}))

const toDashboard = async (row) => {
  window.open(row.dashboardUrl, '_blank')
}
</script>

<template>
  <div>
    <!-- <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>当前页面中您可以管理 Job 资源, 主要用于限制当前空间出口流量允许到达的空间。</p>
      </Space>
    </Alert> -->
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
      :on-create="onCreate"
    >
      <template #operate-buttons>
        <Button size="small" type="primary" ghost icon="md-copy" @click="() => (batchCopyModalVisible = true)"
          >批量复制</Button
        >
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <!-- <link-button @click="() => onEdit(row)" text="编辑" /> -->
          <link-button @click="toDashboard(row)" text="跳转到 dashboard" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>

    <resource-form
      resourceType="rayjob"
      v-model="formVisible"
      resource-version="V1"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      notSynchronizeToUnifiedCluster
      isSkipCheck
      hideFooter
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" :readonly="true" />
        <ProFormText
          name="jobName"
          label="RayJob Name"
          :data="data"
          :readonly="formStatus === EnumFormStatus.Edit"
          size="md"
        />
        <ProFormText name="imageUrl" label="镜像" :data="data" :readonly="false" size="md" />
        <ProFormText name="startCmd" label="训练启动命令" :data="data" :readonly="false" size="md" />
        <ProFormItem
          name="workloadSelector"
          label="worker资源申请明细"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
        >
          <ProFormText
            name="replicas"
            label="副本数（>=1，副本数为1则为单机训练）"
            :data="data.worker"
            :readonly="false"
            size="md"
          />
          <ProFormItem
            name="Requests"
            label="Requests"
            contentStyleMode="background"
            :dataReloadFlag="dataReloadFlag"
            contentClassName="p-0"
          >
            <div>
              <ProFormText
                class="half"
                name="cpu"
                label="cpu"
                :data="data.worker.resources.requests"
                :readonly="false"
              />
              <ProFormText
                class="half"
                name="memory"
                label="memory"
                :data="data.worker.resources.requests"
                :readonly="false"
              />
              <ProFormText
                class="half"
                name="52tt.com/gpu-core"
                label="52tt.com/gpu-core"
                :data="data.worker.resources.requests"
                :readonly="false"
              />
              <ProFormText
                class="half"
                name="52tt.com/gpu-memory"
                label="52tt.com/gpu-memory"
                :data="data.worker.resources.requests"
                :readonly="false"
              />
            </div>
          </ProFormItem>
          <ProFormItem
            name="Limits"
            label="Limits"
            :data="data"
            contentStyleMode="background"
            :dataReloadFlag="dataReloadFlag"
            contentClassName="p-0"
          >
            <div>
              <ProFormText class="half" name="cpu" label="cpu" :data="data.worker.resources.limits" :readonly="false" />
              <ProFormText
                class="half"
                name="memory"
                label="memory"
                :data="data.worker.resources.limits"
                :readonly="false"
              />
              <ProFormText
                class="half"
                name="52tt.com/gpu-core"
                label="52tt.com/gpu-core"
                :data="data.worker.resources.limits"
                :readonly="false"
              />
              <ProFormText
                class="half"
                name="52tt.com/gpu-memory"
                label="52tt.com/gpu-memory"
                :data="data.worker.resources.limits"
                :readonly="false"
              />
            </div>
          </ProFormItem>
        </ProFormItem>
        <ProFormItem
          name="workloadSelector"
          label="挂载点"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
        >
          <MultiInput
            addLabel="新增卷"
            :data="data"
            :on-add="
              () => {
                data.mounts.push({ pvc: '', mountPath: '', subPath: '' })
              }
            "
            :on-delete="(index) => data.mounts?.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data.mounts?.length || 1)"
          >
            <template #default="{ index }">
              <div class="multi-item-wrapper">
                <Input
                  placeholder="pvc"
                  :value="data.mounts?.[index]?.pvc"
                  @on-change="(e) => onMountKeyChange(data, 'pvc', index, e.target.value)"
                />
                <Input
                  placeholder="mountPath"
                  :value="data.mounts?.[index]?.mountPath"
                  @on-change="(e) => onMountKeyChange(data, 'mountPath', index, e.target.value)"
                />
                <Input
                  placeholder="subPath"
                  :value="data.mounts?.[index]?.subPath"
                  @on-change="(e) => onMountKeyChange(data, 'subPath', index, e.target.value)"
                />
              </div>
            </template>
          </MultiInput>
        </ProFormItem>
      </template>

      <template #footer="{ data, model }">
        <Button type="primary" @click="() => handleSubmit(data, model)">提交</Button>
      </template>
    </resource-form>

    <Drawer title="查看详情" v-model="detailModal.visible" :closable="true" width="50">
      <Card>
        <h4>基本信息</h4>

        <Row type="flex" :gutter="20" style="margin-top: 20px">
          <Col span="10" class="info-key">Name</Col>
          <Col span="14" class="info-value">{{ detailInfo.name }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Namespace</Col>
          <Col span="14" class="info-value">{{ detailInfo.namespace }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">BackoffLimit（重试）</Col>
          <Col span="14" class="info-value">{{ detailInfo.backoffLimit }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">CompletionMode（完成模式）</Col>
          <Col span="14" class="info-value">{{ detailInfo.completionMode }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Completions（当前完成个数）</Col>
          <Col span="14" class="info-value">{{ detailInfo.completions }}</Col> </Row
        ><Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Parallelism（并行个数，1为串行模式)）</Col>
          <Col span="14" class="info-value">{{ detailInfo.parallelism }}</Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <h4>关联Pod</h4>

        <Table style="margin-top: 16px" :columns="RELATED_POD_COLUMNS" :data="relatedPodInfo">
          <template #name="{ row }">
            <LinkButton @click="() => onJumpToPod(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
          </template>
          <template #ops="{ row }">
            <LinkButton @click="() => onViewYaml(row, 'pod')" text="YAML" />
          </template>
        </Table>
      </Card>
      <Card style="margin-top: 16px">
        <EventTable :uuid="detailModal.data.uuid" :clusterId="K8SInstance?.clusterId" />
      </Card>
    </Drawer>

    <view-yaml
      resourceType="rayjobs"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V1"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
    <BatchCopy v-model="batchCopyModalVisible" resourceType="Job" />
  </div>
</template>
<style lang="less" scoped>
.multi-item-wrapper {
  display: flex;
  flex: 1 0 0%;
  > div {
    flex: 1 0 0%;
    &:not(:last-child) {
      margin-right: 16px;
    }
  }
}
.half {
  width: 50%;
  display: inline-block;
}
</style>
