import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { computed, getCurrentInstance, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import { YamlGVR } from '..'

export const BATCH_COPY_RESULT_COLUMNS = [
  {
    title: 'ResourceName',
    key: 'name'
  },
  {
    title: 'Status',
    key: 'status',
    width: '100',
    slot: 'status'
  },
  {
    title: 'Message',
    key: 'message',
    render: (h, params) => h('div', params.row.message ? params.row.message : '-')
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: '100'
  }
]

export default function useBatchCopyService(props: { value: boolean; resourceType: string }, emit) {
  const store = useStore()
  const { proxy } = getCurrentInstance()

  const data = ref()
  const targetClusterId = ref()
  const targetCluster = ref()
  const targetNamespace = ref()
  const targetResourceKeys = ref([])
  const batchCopyLoading = ref(false)
  const batchCopyResult = ref()
  const batchCopyResultVisible = ref(false)
  const viewYamlVisible = ref(false)
  const yamlEntity = ref()
  const init = ref(false)

  const visible = computed({
    get() {
      return props.value
    },
    set(value) {
      emit('input', value)
    }
  })
  const cluster = computed(() => {
    store.commit('getCurrentCluster', store.state.user.userId)
    const data = store.state.k8s.currentCluster
    return data
  })
  const namespace = computed(() => {
    store.commit('getCurrentNamespace', store.state.user.userId)
    const data = store.state.k8s.currentNamespace
    return data
  })
  const clusterID = computed(() => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    const data = store.state.k8s.currentClusterId
    return data
  })

  const { data: clusterList, run: getClusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string }[] }>(`${Config.Api.Base}${Config.Api.GetClusterList}`)
    },
    {
      manual: true,
      formatResult(res) {
        return res.data.data?.map((i) => ({ ...i, id: i.id.toString() }))
      }
    }
  )

  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet<{ data: { name: string }[] }>(`${Config.Api.Base}${Config.Api.GetNamespaceList}`, {
        params: {
          clusterName: targetCluster.value
        }
      })
    },
    {
      manual: true,
      formatResult(res) {
        if (targetClusterId.value === clusterID.value) return res.data.data?.filter((i) => i.name !== namespace.value)
        return res.data.data
      }
    }
  )

  const onSelectCluster = (item) => {
    targetCluster.value = clusterList.value?.filter((i) => i.id === item.value)?.[0]?.name
    targetNamespace.value = undefined
    namespaceList.value = []
    getNamespaceList()
  }

  const {
    data: resourceList,
    loading: resourceListLoading,
    run: getResourceList
  } = useRequest(
    () => {
      return useGet<{ data: { name: string }[] }>(`${Config.Api.Base}${Config.Api.GetSimpleResourceList}`, {
        params: {
          clusterID: clusterID.value,
          namespace: namespace.value,
          kind: props.resourceType
        }
      })
    },
    {
      manual: true,
      formatResult(res) {
        return res.data.data?.map((i) => ({ key: i, label: i }))
      }
    }
  )

  const onResourceListChange = (newTargetKeys, direction, moveKeys) => {
    console.log(newTargetKeys)
    console.log(direction)
    console.log(moveKeys)
    targetResourceKeys.value = newTargetKeys
  }

  const onCopy = async (data: string[]) => {
    batchCopyLoading.value = true
    const res = await usePost(
      `${Config.Api.Base}/api/v2/resource/${Config.Api.BatchCopy}`,
      {
        originClusterId: clusterID.value,
        originNamespace: namespace.value,
        targetClusterId: targetClusterId.value,
        targetNamespace: targetNamespace.value,
        names: data,
        rsKind: props.resourceType
      }
    )
    batchCopyLoading.value = false
    if (res.success) {
      proxy.$Message.success('操作成功')
      return res.data.result
    }
  }

  const onBatchCopy = () => {
    if (!targetClusterId.value) {
      proxy.$Message.warning('请选择目标集群！')
      return
    }
    if (!targetNamespace.value) {
      proxy.$Message.warning('请选择目标命名空间！')
      return
    }
    if (!targetResourceKeys.value?.length) {
      proxy.$Message.warning('请选择复制资源对象！')
      return
    }
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认复制？`,
      loading: true,
      onOk: async () => {
        batchCopyResult.value = await onCopy(targetResourceKeys.value)
        proxy.$Modal.remove()
        visible.value = false
        batchCopyResultVisible.value = true
      }
    })
  }

  const onRetry = async (record) => {
    batchCopyResult.value = await onCopy([record.name])
  }

  const onOpenYaml = (record) => {
    /** 强行修改drawer层级，修复iview modal和drawer 每次点击都叠加z-index，但叠加的数量不一致导致的覆盖问题 */
    const modal = document.getElementsByClassName('batch-result-modal')?.[0] as HTMLElement
    const drawer = document.getElementsByClassName('batch-result-yaml-drawer')?.[0] as HTMLElement
    if (modal && drawer) {
      drawer.style.zIndex = (Number(modal.style.zIndex) + 1).toString()
    }

    yamlEntity.value = {
      namespace: record.namespace,
      clusterId: record.clusterId,
      resourceName: record.name,
      ...YamlGVR[props.resourceType]
    }

    viewYamlVisible.value = true
  }

  watch(
    () => props.value,
    () => {
      props.value && getClusterList()
    }
  )

  watch(
    () => [namespace.value, clusterID.value, props.resourceType, props.value],
    (value, oldValue) => {
      if (props.value) {
        if (!init.value) {
          getResourceList()
          init.value = true
          return
        }

        const [newNamespace, newClusterId, newResourceType] = value
        const [oldNamespace, oldClusterId, oldResourceType] = oldValue
        if (newNamespace !== oldNamespace || newClusterId !== oldClusterId || newResourceType !== oldResourceType) {
          getResourceList()
        }
      }
    },
    {
      immediate: true
    }
  )
console.log(resourceList,'resourceList')
  return {
    data,
    visible,
    cluster,
    clusterID,
    namespace,
    targetClusterId,
    targetNamespace,
    clusterList,
    namespaceList,
    onSelectCluster,
    resourceList,
    targetResourceKeys,
    onResourceListChange,
    resourceListLoading,
    onBatchCopy,
    batchCopyResultVisible,
    batchCopyResult,
    onCopy,
    viewYamlVisible,
    yamlEntity,
    onOpenYaml,
    onRetry
  }
}
