<script lang="ts" setup>
import { ResourceContainer, BatchCopy } from '@/domain/Resource'
import Space from '@/components/space'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import { DetailDrawer, DetailCard } from '@/components/detail-drawer'

import { ResourceForm, EnumFormStatus } from '@/components/resource-form'

import { ProFormText, ProFormItem, ArrayObject, EnumFormItemControllerType, MultiInput } from '@/components/pro-form'
import { genNonDuplicateArr } from '@/libs/tools'
import { formatEnumToLabelValue, EnumPorts } from './enum'

import { TABLE_COLUMNS, EGRESS_TABLE_COLUMNS, WORKLOAD_SELECTOR_TABLE_COLUMNS, SIDECAR_DETAIL_CONFIG } from './setting'
import useSideCarService from './useSideCarService'
import { getCurrentInstance, ref } from 'vue'

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  formInitData,
  onEgressPortChange,
  onWorkloadSelectorKeyChange,
  onEgressHostsChange,
  onInitFormat,
  onSubmitFormat,
  onSubmitEgressForm,
  onSubmitSuccess,
  onViewDetail,
  viewDetailVisible,
  onInitDetailFormat
} = useSideCarService()

const { proxy } = getCurrentInstance()
const batchCopyModalVisible = ref(false)
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>当前页面中您可以管理 SideCar 资源, 主要用于限制当前空间出口流量允许到达的空间。</p>
      </Space>
    </Alert>
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
      :on-create="onCreate"
    >
      <template #operate-buttons>
        <Button size="small" type="primary" ghost icon="md-copy" @click="() => (batchCopyModalVisible = true)">批量复制</Button>
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>

    <view-yaml
      resourceType="sidecar"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      isCheckYaml
      
    />
    <resource-form
      resourceType="sidecar"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      :onInitFormat="onInitFormat"
      :onSubmitFormat="onSubmitFormat"
      
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" readonly />
        <ProFormText name="name" label="Name（名称）" :data="data" :readonly="formStatus === EnumFormStatus.Edit" />
        <ProFormItem
          name="workloadSelector"
          label="WorkloadSelector（选择作用的工作负载，不启用则作用于当前空间）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          :mode="EnumFormItemControllerType.Switch"
        >
          <MultiInput
            addLabel="添加匹配label"
            :data="data"
            :on-delete="(index) => data.workloadSelector?.data?.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data.workloadSelector?.data?.length || 1)"
          >
            <template #default="{ index }">
              <div class="multi-item-wrapper">
                <Input
                  placeholder="Key"
                  :value="data.workloadSelector?.data?.[index]?.key"
                  @on-change="(e) => onWorkloadSelectorKeyChange(data, 'key', index, e.target.value)"
                />
                <Input
                  placeholder="Value"
                  :value="data.workloadSelector?.data?.[index]?.value"
                  @on-change="(e) => onWorkloadSelectorKeyChange(data, 'value', index, e.target.value)"
                />
              </div>
            </template>
          </MultiInput>
        </ProFormItem>
        <ProFormItem
          name="egress"
          label="Egress（出口白名单）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          :mode="EnumFormItemControllerType.Switch"
          desc="当前命名空间工作负载访问的出口白名单。"
          url="https://istio.io/latest/zh/docs/reference/config/networking/sidecar/#IstioEgressListener"
        >
          <ArrayObject
            label="目标服务"
            addButtonPosition="top"
            :data="data.egress"
            name="data"
            canEdit
            :columns="EGRESS_TABLE_COLUMNS"
            @on-ok="(type, record, index) => onSubmitEgressForm(data, type, record, index)"
            @on-delete="(index) => data.egress?.data?.splice(index, 1)"
          >
            <template #default="{ record, visible }">
              <Form>
                <ProFormItem
                  name="hosts"
                  label="Host（目标服务）"
                  :data="record"
                  contentStyleMode="background"
                  desc="用于配置具体的出口白名单服务,格式:{namespace}/{service}
                        namespace字段可以配置具体的命名空间或通配符  '*'  , '.';
                        service可以配置服务名 或 通配符'*'
                        例如: 
                        infra/*  infra命名空间下的所有服务或主机
                        infra/cloud.infra.svc.cluster.local infra命名空间下的cloud服务"
                  url="https://istio.io/latest/zh/docs/reference/config/networking/sidecar/#IstioEgressListener"
                >
                  <MultiInput
                    key="host"
                    addLabel="添加"
                    :data="record"
                    :on-delete="(index) => record?.hosts?.splice(index, 1)"
                    :on-init="() => genNonDuplicateArr(record?.hosts?.length || 1)"
                  >
                    <template #default="{ index }">
                      <div class="multi-item-wrapper">
                        <Input
                          placeholder="{namespace}/{service}"
                          :value="record?.hosts?.[index]"
                          @on-change="(e) => onEgressHostsChange(record, index, e.target.value)"
                        />
                      </div>
                    </template>
                  </MultiInput>
                </ProFormItem>
                <ProFormItem
                  name="port"
                  label="Port（端口）"
                  :data="record"
                  contentStyleMode="background"
                  :dataReloadFlag="visible"
                  :mode="EnumFormItemControllerType.Switch"
                  desc="定义了Host(目标服务)特定的端口信息。"
                  url="https://istio.io/latest/zh/docs/reference/config/networking/gateway/#Port"
                >
                  <template #default>
                    <div class="multi-item-wrapper">
                      <Input
                        placeholder="端口名称"
                        :value="record.port?.data?.name"
                        @on-change="(e) => onEgressPortChange(record, 'name', e.target.value)"
                      />
                      <Input
                        placeholder="端口号"
                        :value="record.port?.data?.number"
                        @on-change="(e) => onEgressPortChange(record, 'number', e.target.value)"
                      />
                      <Select
                        placeholder="端口协议"
                        transfer
                        filterable
                        :value="record.port?.data?.protocol"
                        @on-change="(val) => onEgressPortChange(record, 'protocol', val)"
                      >
                        <Option
                          v-for="item in formatEnumToLabelValue(EnumPorts)"
                          :key="item.value"
                          :value="item.value"
                          >{{ item.label }}</Option
                        >
                      </Select>
                    </div>
                  </template>
                </ProFormItem>
              </Form>
            </template>
          </ArrayObject>
        </ProFormItem>
      </template>
    </resource-form>

    <detail-drawer
      resourceType="sidecar"
      :title="'SideCar 详情'"
      v-model="viewDetailVisible"
      :resource-entity="formEntity"
      :onInitFormat="onInitDetailFormat"
    >
      <template #default="{ data }">
        <detail-card :title="'基本信息'" :data="data" :config="SIDECAR_DETAIL_CONFIG">
          <template #labels="{ data }">
            <template v-for="(label, index) in data.labels">
              <Tag :key="index" color="#2a7cc3"> {{ label.key }}:{{ label.value }} </Tag>
            </template>
          </template>
        </detail-card>
        <detail-card :title="'WorkloadSelector（空则作用于当前空间）'" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="WORKLOAD_SELECTOR_TABLE_COLUMNS" :data="data.workloadSelector" />
          </template>
        </detail-card>
        <detail-card :title="'Egress（出口限制）'" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="EGRESS_TABLE_COLUMNS" :data="data.egress" />
          </template>
        </detail-card>
      </template>
    </detail-drawer>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="Sidecar" />
  </div>
</template>

<style lang="less" scoped>
.multi-item-wrapper {
  display: flex;
  flex: 1 0 0%;
  > div {
    flex: 1 0 0%;
    &:not(:last-child) {
      margin-right: 16px;
    }
  }
}
</style>
