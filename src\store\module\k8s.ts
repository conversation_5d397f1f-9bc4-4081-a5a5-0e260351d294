const CurrentCluster = 'CurrentCluster'
const CurrentNamespace = 'CurrentNamespace'
const CurrentClusterID = 'CurrentClusterID'
export default {
  state: {
    isShowCluster: true,
    currentCluster: 'none',
    currentNamespace: 'none',
    currentClusterId: null
  },
  mutations: {
    setCurrentClusterId(state, { uid, clusterId }) {
      if (!clusterId) {
        localStorage.removeItem(`${uid}:${CurrentClusterID}`)
      } else {
        localStorage.setItem(`${uid}:${CurrentClusterID}`, clusterId)
      }

      state.currentClusterId = clusterId
    },
    setCurrentCluster(state, { uid, cluster }) {
      if (!cluster) {
        localStorage.removeItem(`${uid}:${CurrentCluster}`)
      } else {
        localStorage.setItem(`${uid}:${CurrentCluster}`, cluster)
      }

      state.currentCluster = cluster
    },
    setCurrentNamespace(state, { uid, namespace }) {
      if (!namespace) {
        localStorage.removeItem(`${uid}:${CurrentNamespace}`)
      } else {
        localStorage.setItem(`${uid}:${CurrentNamespace}`, namespace)
      }

      state.currentNamespace = namespace
    },
    getCurrentClusterID(state, uid) {
      const cId = localStorage.getItem(`${uid}:${CurrentClusterID}`)
      state.currentClusterId = cId
      return cId
    },
    getCurrentCluster(state, uid) {
      const c = localStorage.getItem(`${uid}:${CurrentCluster}`)
      state.currentCluster = c
      return state.currentCluster
    },
    getCurrentNamespace(state, uid) {
      const n = localStorage.getItem(`${uid}:${CurrentNamespace}`)

      state.currentNamespace = n

      return state.currentNamespace
    }
  },
  getters: {}
}
