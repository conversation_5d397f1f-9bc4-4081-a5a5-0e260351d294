<script setup lang="ts">
import { ref } from 'vue'
import { useRequest } from 'vue-request'
import { useGet } from '@/libs/service.request'
import Config from '@/config'
import dayjs from 'dayjs'

const props = defineProps<{
  text: string
  clusterId: string
  namespace: string
  pod: string
}>()

const CONTAINER_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'State',
    key: 'state',
    slot: 'state',
    width: '90'
  },
  {
    title: 'StartedAt',
    key: 'startedAt',
    slot: 'startedAt'
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: '90'
  }
]

const onContainersPopperShow = () => {
  run()
}

const { data, loading, run } = useRequest(
  () => {
    return useGet(`${Config.Api.Base}${Config.Api.GetPodRelativeContainerList}`, {
      params: {
        clusterId: props.clusterId,
        namespace: props.namespace,
        name: props.pod
      }
    })
  },
  {
    manual: true,
    formatResult: (res) => {
      return res.data.data
    }
  }
)
</script>

<template>
  <Poptip trigger="hover" transfer @on-popper-show="onContainersPopperShow" :width="542">
    <a>{{ props.text }}</a>
    <template #content>
      <Table :loading="loading" :columns="CONTAINER_COLUMNS" size="small" :width="510" :height="144" :data="data">
        <template #state="{ row }">
          <span :class="`container-state-${row.state.toLowerCase()}`">
            {{ row.state }}
          </span>
        </template>
        <template #startedAt="{ row }">{{ dayjs(row.startedAt).format('YYYY-MM-DD HH:mm:ss') }}</template>
        <template #ops="{ row }">
          <slot name="action" :record="row"></slot>
        </template>
      </Table>
    </template>
  </Poptip>
</template>

<style lang="less" scoped>
.container-state-running {
  color: #1abe6b;
}
.container-state-terminated {
  color: #ed4041;
}
.container-state-waiting {
  color: #515a6e;
}
</style>
