module.exports = {
  arrowParens: "always", // 单个参数的箭头函数使用括号
  bracketSameLine: false, // 开始标签的右尖括号是否跟随在最后一行属性末尾
  bracketSpacing: true, // 是否在对象属性与大括号之间填充空格
  embeddedLanguageFormatting: "auto", // 是否格式化一些文件中被嵌入的代码片段的风格，如果插件可以识别
  htmlWhitespaceSensitivity: "css", // 祖师爷都不改的css规则
  insertPragma: false, // 是否在文件插入标记表明该文件已被格式化处理过了
  jsxSingleQuote: false, // 是否在JSX中使用单引号
  printWidth: 120, // 代码行的宽度
  requirePragma: false, // 是否仅格式化文件开始位置存在特殊注释的代码
  semi: false, // 是否在代码语句结尾添加分号
  singleQuote: true, // 是否使用单引号，JSX单独设置
  tabWidth: 2, // 指定每次缩进的空格数
  trailingComma: "none", // 在多行以逗号分割的句法中尽可能补充尾行逗号
  useTabs: false, // 是否用制表符代替空格执行缩进
  vueIndentScriptAndStyle: false, // 是否在Vue文件中对代码和标签进行缩进，script和style部分
  endOfLine: "lf", // 设置换行风格，避免不同操作系统造成的大量代码diff
};
