import { relativeTime } from '@/libs/tools'
import dayjs from 'dayjs'
import { Table, Poptip } from 'view-design'
import { EnumComponentType } from './enum'
import { Ellipsis } from '@/components'

export const getTableColumns = (type) => [
  {
    title: 'Name',
    key: 'name',
    slot: 'name',
    tooltip: true,
    tooltipTheme: 'light'
  },
  ...(type === EnumComponentType.AssociatedDeployment
    ? [
        {
          title: 'Namespace',
          key: 'namespace',
          width: 200,
          tooltip: true,
          tooltipTheme: 'light'
        }
      ]
    : []),
  {
    title: 'Gateways',
    key: 'gateways',
    render: (h, params) => {
      return params.row.gateways?.length ? params.row.gateways?.map((i) => h(Ellipsis, i)) : h('div', '-')
    }
  },
  {
    title: 'Hosts',
    key: 'hosts',
    render: (h, params) => {
      return params.row.hosts?.length
        ? h(Ellipsis, {
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('div', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Age',
    key: 'creationTimestamp',

    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]

export const HTTP_COLUMNS = (data: Record<string, any>[]) => [
  {
    title: 'Name（名称）',
    key: 'name',
    width: 220,
    render: (h, { row, index }) => {
      const isDefault = data?.findIndex((i) => !i.match.switchCtl && !i.match.data?.length) === index
      return h('div', [row.name, isDefault ? h('span', { style: { color: '#e74f4c' } }, ' [default]') : ''])
    }
  },
  {
    title: 'Match（匹配项）',
    key: 'match',
    width: 140,
    render: (h, { row }) => {
      return row.match.switchCtl
        ? h(Poptip, {
            props: {
              placement: 'top-start',
              transfer: true,
              width: 542,
              trigger: 'click'
            },
            scopedSlots: {
              default: () => h('a', '查看匹配项'),
              content: () =>
                h(Table, {
                  props: {
                    data: row.match.data ?? [],
                    columns: MATCH_COLUMNS,
                    size: 'small',
                    width: 510,
                    height: 144
                  }
                })
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Route（目标主机）',
    key: 'route.route',
    tooltip: true,
    width: 220,
    render: (h, { row }) => {
      return row.route.switchCtl && row.route.data.chosenCtl === 'route'
        ? row.route.data.route?.map((i) => {
            const text = `${i.host ?? '-'} | 
                ${i.port.switchCtl ? i.port.data ?? '-' : '-'} | 
                ${i.subset.switchCtl ? i.subset.data ?? '-' : '-'} | 
                ${i.weight.switchCtl ? i.weight.data ?? '-' : '-'} `

            return h(Ellipsis, text)
          })
        : h('div', '-')
    }
  },
  {
    title: 'Delegate（路由委派）',
    key: 'route.delegate',
    tooltip: true,
    width: 200,
    render: (h, { row }) => {
      if (row.route.switchCtl && row.route.data.chosenCtl === 'delegate' && row.route.data.delegate) {
        const [namespace, delegator] = row.route.data.delegate
        const text = `${namespace ?? '-'} | 
                ${delegator ?? '-'} `
        return h(Ellipsis, text)
      }
      return h('div', '-')
    }
  },
  {
    title: 'Redirect（重定向）',
    key: 'route.redirect',

    tooltip: true,
    render: (h, { row }) => {
      if (row.route.switchCtl && row.route.data.chosenCtl === 'redirect') {
        const i = row.route.data.redirect
        const text = `
            ${i.scheme.switchCtl ? i.scheme.data ?? '-' : '-'} | 
            ${i.authority.switchCtl ? i.authority.data ?? '-' : '-'} | 
            ${i.redirectPort.switchCtl ? i.redirectPort.data ?? '-' : '-'} | 
            ${i.uri.switchCtl ? i.uri.data ?? '-' : '-'} `
        return h(Ellipsis, text)
      }
      return h('div', '-')
    }
  }
]

export const MATCH_COLUMNS = [
  {
    title: 'URI',
    key: 'uri',
    tooltip: true,
    width: 345,
    render: (h, { row }) => {
      return row.uri.switchCtl
        ? h(Ellipsis, `${row.uri.data.type ?? '-'} : ${row.uri.data.value ?? '-'}`)
        : h('div', '-')
    }
  },
  {
    title: 'Headers',
    key: 'headers',
    width: 270,
    tooltip: true,
    render: (h, { row }) => {
      return row.headers.switchCtl && !!row.headers.data.length
        ? row.headers.data?.map((i) => {
            const text = `${i.key ? i.key : '-'} : ${i.value ? i.value : '-'}`
            return h(Ellipsis, text)
          })
        : h('div', '-')
    }
  },
  {
    title: 'QueryParams',
    key: 'queryParams',
    width: 200,
    tooltip: true,
    render: (h, { row }) => {
      return row.queryParams.switchCtl && !!row.queryParams.data.length
        ? row.queryParams.data?.map((i) => {
            const text = `${i.key ? i.key : '-'} : ${i.value ? i.value : '-'}`
            return h(Ellipsis, text)
          })
        : h('div', '-')
    }
  },
  {
    title: 'Port',
    width: 140,
    key: 'port',
    render: (h, { row }) => h('span', row.port.switchCtl ? `${row.port.data ?? '-'}` : '-')
  }
]
export const ROUTE_COLUMNS = [
  {
    title: 'Host',
    key: 'host',
    minWidth: 450,
    render: (h, { row }) => h(Ellipsis, row.host ?? '-')
  },
  {
    title: 'Port',
    key: 'port',
    width: 180,
    render: (h, { row }) => h(Ellipsis, row.port.switchCtl ? `${row.port.data ?? '-'}` : '-')
  },
  {
    title: 'Subset',
    key: 'subset',
    width: 180,
    render: (h, { row }) => h(Ellipsis, row.subset.switchCtl ? `${row.subset.data ?? '-'}` : '-')
  },
  {
    title: 'Weight',
    key: 'weight',
    width: 180,
    render: (h, { row }) => h(Ellipsis, row.weight.switchCtl ? `${row.weight.data ?? '-'}` : '-')
  }
]

export const DETAIL_BASE_INFO = [
  {
    title: 'Name',
    key: 'name',
    render: (h, data) => h('b', data.name)
  },
  {
    title: 'Namespace',
    key: 'namespace'
  },
  {
    title: 'CreationTime',
    key: 'creationTime',
    render: (h, data) => {
      return h('span', dayjs(data?.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm'))
    }
  },
  {
    title: 'Gateways',
    key: 'gateways',
    render: (h, data) => {
      return data.gateways?.map((i) => h('div', i))
    }
  },
  {
    title: 'Hosts',
    key: 'hosts',
    render: (h, data) => {
      return data.hosts?.map((i) => h('div', i))
    }
  },
  {
    title: 'Labels',
    key: 'labels',
    slot: 'labels'
  }
]

export const DETAIL_HTTP_COLUMNS = [
  { title: '路由名称', key: 'name' },
  {
    title: '匹配类型',
    key: 'type',
    render: (h, params) => {
      return h('div', params.row.type?.join(','))
    }
  },
  {
    title: '路由目标',
    key: 'destination',
    render: (h, params) => {
      return h(Ellipsis, params.row.destination)
    }
  }
]

export const DETAIL_RELATED_SERVICE_COLUMNS = [
  { title: 'Namespace', key: 'namespace', tooltip: true, tooltipTheme: 'light' },
  { title: 'Name', key: 'name', tooltip: true, tooltipTheme: 'light' },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 90,
    align: 'center'
  }
]

export const DETAIL_RELATED_DESTINATION_RULE_COLUMNS = [
  { title: 'Namespace', key: 'namespace', tooltip: true, tooltipTheme: 'light' },
  { title: 'Name', key: 'name', tooltip: true, tooltipTheme: 'light' },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 90,
    align: 'center'
  }
]
