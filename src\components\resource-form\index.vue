<script lang="ts" setup>
import { EnumComponentType, EnumFormStatus, EnumResourceFormModel, FormSubmitParams, ResourceEntity } from './type'
import useResourceFormService from './useResourceFormService'
import { Yaml, LinkButton, Space } from '@/components'
import { computed } from 'vue'
import { SyncUnifiedClusterModalColumns } from './setting'

export interface ResourceFormProps {
  /** 资源状态，不传默认Independent */
  type?: EnumComponentType.Associated | EnumComponentType.Independent
  /** 后端版本 - 不影响入参返参 */
  resourceVersion?: 'V1' | 'V2'
  /** 资源类型(api) */
  resourceType: String
  /** 表单打开 / 关闭状态 */
  value: Boolean
  /** 表单状态 */
  status: EnumFormStatus
  /** 资源实体 */
  resourceEntity: ResourceEntity
  /** 初始化的yaml数据 */
  yamlInitData?: string
  /** 初始化的表单数据 */
  formInitData?: Record<string, any>
  /** 提交成功回调 */
  onSubmitCallBack: () => void
  /** 请求表单数据的格式化回调 */
  onInitFormat?: (data: Record<string, any>) => Record<string, any>
  /** 提交表单数据的格式化回调 */
  onSubmitFormat?: (params: FormSubmitParams) => FormSubmitParams
  /** 表单宽度 */
  width?: number | string
  /** 不同步到统一集群 */
  notSynchronizeToUnifiedCluster?: boolean
  /** 预览的额外入参 */
  getPreviewParams?: () => Record<string, string>
  /** 强制禁用form模式，只开启yaml模式 */
  forbiddenForm?: boolean
  /** 是否跳过内部检查 - 额外兼容不存在内部检查的资源 */
  isSkipCheck?: boolean
  /** 是否请求时只传集群，不传命名空间 */
  isSkipNamespaceInParams?: boolean
  // 写 rayjob 的时候加的，新的新建流程跟原来的不一样
  hideFooter?: boolean
}
const props = defineProps<ResourceFormProps>()
const emit = defineEmits(['input'])

const visible = computed({
  get() {
    return props.value
  },
  set(value) {
    emit('input', value)
  }
})

const {
  formModel,
  isConvertible,
  yamlData,
  yamlLoading,
  formDataLoading,
  yamlRef,
  onCheckBeforeSubmit,
  formData,
  dataReloadFlag,
  isUnifiedCluster,
  isApplyToUnifiedCluster,
  onReviewFormModel,
  modal,
  onCancel,
  formModelLoading,
  unifiedClusterList,
  syncUnifiedClusterModal,
  onSyncUnifiedCluster,
  previewYamlRef
} = useResourceFormService(props, visible)

const TAB_NAME = `resource-form-${props.resourceType}`
</script>

<template>
  <div>
    <Drawer
      :title="props.status === EnumFormStatus.Edit ? '编辑服务' : '创建服务'"
      :mask-closable="false"
      :scrollable="false"
      :width="props.width ?? 45"
      v-model="visible"
    >
      <Tabs type="card" v-model="formModel" :name="TAB_NAME">
        <TabPane label="表单模式" :name="EnumResourceFormModel.Form" :disabled="!isConvertible" :tab="TAB_NAME">
          <Spin v-if="formModelLoading || formDataLoading" fix></Spin>

          <Form ref="formData" :model="formData" @submit.native.prevent>
            <slot name="form" :data="formData" :dataReloadFlag="dataReloadFlag"></slot>
          </Form>
        </TabPane>
        <TabPane label="YAML模式" :name="EnumResourceFormModel.Yaml" :tab="TAB_NAME">
          <Spin v-if="yamlLoading" fix></Spin>
          <yaml ref="yamlRef" style="padding: 16px" v-model="yamlData" />
        </TabPane>
      </Tabs>
      <div class="drawer-footer">
        <Space style="align-items: center">
          <template v-if="!hideFooter">
            <Button v-show="formModel === EnumResourceFormModel.Form" type="warning" @click="() => onReviewFormModel()">
              预览
            </Button>
            <Button type="primary" @click="() => onReviewFormModel(true)">提交</Button>
            <Button @click="onCancel"> 取消 </Button>
            <Checkbox v-show="isUnifiedCluster" v-model="isApplyToUnifiedCluster" style="color: rgb(231, 79, 76)"
              >同步到统一集群
              <Poptip always trigger="hover" placement="top" transfer transfer-class-name="form-item-poptip">
                <div slot="content">
                  <div>统一集群列表:</div>
                  <div v-for="item in unifiedClusterList" :key="item.id">- {{ item.name }}</div>
                </div>
                <Icon
                  type="ios-alert-outline"
                  style="color: #2a7cc3; font-size: 14px; cursor: pointer; margin-right: 16px"
                />
              </Poptip>
            </Checkbox>
          </template>
          <slot name="footer" :model="formModel" :data="formModel === EnumResourceFormModel.Form ? formData : yamlData"></slot>
        </Space>
      </div>
    </Drawer>
    <Modal
      v-model="modal.visible"
      :title="modal.isClickBySubmit ? '提交前预览' : '预览'"
      :footer-hide="!modal.isClickBySubmit"
      width="60"
      okText="确认提交"
      :mask-closable="false"
      @on-ok="onCheckBeforeSubmit"
    >
      <yaml
        ref="previewYamlRef"
        v-model="modal.data"
        :forbiddenEdit="true"
        style="max-height: 58vh; overflow: hidden"
        :isDiffMode="true"
        :lastYamlData="modal.lastYaml"
      />
    </Modal>

    <Modal width="40" v-model="syncUnifiedClusterModal.visible" title="同步结果集" footer-hide>
      <Table :columns="SyncUnifiedClusterModalColumns" :data="syncUnifiedClusterModal.data">
        <template #name="{ row }"> {{ row.cluster }} / {{ row.namespace }} </template>
        <template #status="{ row }">
          <span v-if="row.status === 'succeed'" style="color: #1abe6b">同步成功</span>
          <span v-else style="color: #ed4014">同步失败{{ row.message ? `（${row.message}）` : '-' }}</span>
        </template>
        <template #ops="{ row }">
          <LinkButton
            :disabled="row.status === 'succeed'"
            text="重试"
            @click="() => onSyncUnifiedCluster(syncUnifiedClusterModal.params)"
          />
        </template>
      </Table>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.ivu-tabs-tabpane {
  height: calc(~'100vh - 51px - 116px - 16px');
  overflow: auto;
}
</style>
