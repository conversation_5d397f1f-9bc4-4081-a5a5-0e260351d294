<script lang="ts" setup>
import useBackupService from './useBackupService'
import { Space, ProTable, LinkButton, Yaml } from '@/components'
import { EnumTabType } from './type'
import { color } from '@/libs/consts'
import { OBJECT_NAME_COLUMNS } from './setting'
const {
  activeTab,
  groupResource,
  groupResourceList,
  clusterId,
  onClusterChange,
  clusterList,
  namespace,
  namespaceList,
  keyword,
  onSearch,
  onCreate,
  columns,
  getTableData,
  refObject,
  onExec,
  onDelete,
  onOpenYaml,
  createModalVisible,
  onCreateClusterChange,
  createClusterList,
  createNamespaceList,
  createGroupResourceList,
  onCreateGroupResourceChange,
  isCreateNamespaceIllegal,
  createObjectNameList,
  formData,
  onCreateNamespaceChange,
  onOpenCreateModal,
  ruleValidate,
  isCreateNamespaceRequired,
  formRef,
  getObjectNamesTableData,
  objectNameRefObject,
  objectNameSearch,
  onSearchObjectNames,
  onSelectObjectNames,
  yamlModal,
  clearSelection,
  viewDesignTableRef
} = useBackupService()
</script>

<template>
  <div class="backup-mgt-wrapper">
    <Alert show-icon
      >通过灵活的备份策略和高效的恢复策略，确保用户能够在面临数据丢失或系统故障时，迅速且准确地恢复其重要资源。</Alert
    >

    <RadioGroup v-model="activeTab" type="button">
      <Radio :label="EnumTabType.backupStrategy">备份策略</Radio>
      <Radio :label="EnumTabType.recoverStrategy">恢复策略</Radio>
      <Radio :label="EnumTabType.backupList">备份列表</Radio>
    </RadioGroup>

    <Space class="search-wrapper">
      <Select
        class="search-item"
        filterable
        clearable
        label-in-value
        placeholder="请选择集群"
        @on-change="onClusterChange"
      >
        <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select
        v-model="groupResource"
        class="search-item group-resource"
        filterable
        :disabled="!clusterId"
        clearable
        placeholder="请选择API 组 / 资源"
      >
        <Option v-for="item in groupResourceList" :value="item" :key="item">{{ item }}</Option>
      </Select>
      <Select
        v-model="namespace"
        class="search-item"
        filterable
        clearable
        :disabled="!clusterId"
        placeholder="请选择命名空间"
      >
        <Option v-for="item in namespaceList" :value="item" :key="item">{{ item }}</Option>
      </Select>

      <Input
        v-if="activeTab === EnumTabType.backupList"
        v-model="keyword"
        placeholder="请输入关键字"
        style="width: 220px"
        clearable
      />

      <Button type="primary" icon="ios-search" @click="onSearch()">搜索</Button>
      <Button v-if="activeTab !== EnumTabType.backupList" type="primary" icon="md-add" @click="onOpenCreateModal()"
        >新建</Button
      >
    </Space>

    <div class="table-wrapper">
      <pro-table
        :columns="columns"
        :request="getTableData"
        :action-ref="refObject"
        hideSearchOperation
        :pagination="{ defaultPageSize: 20, simple: true }"
        :max-height="550"
      >
        <template #groupResource="{ row }">
          <span>{{ `${row.group ? row.group : '-'} / ${row.resource ? row.resource : '-'}` }}</span>
        </template>
        <template #isPersistent="{ row }">
          <span :style="`font-weight: 600; color: ${row.isPersistent ? color.success : color.warning}`">{{
            row.isPersistent ? '永久' : '临时'
          }}</span>
        </template>
        <template #objectName="{ row }">
          <template v-if="row.brObjectNames">
            <Poptip trigger="hover" transfer>
              <template #content>
                <div class="object-name-poptip-wrapper">
                  <Tag v-for="item in row.brObjectNames.split(',')" :key="item" color="blue">{{ item }}</Tag>
                </div></template
              >
              <div class="object-name-wrapper">
                <span v-for="(item, index) in row.brObjectNames.split(',')" :key="item">
                  <Tag color="blue" v-if="index < 50">{{ item }}</Tag>
                </span>
              </div>
            </Poptip>
          </template>
          <span v-else>全部</span>
        </template>
        <template #ops="{ row }">
          <Space justify>
            <LinkButton v-if="activeTab === EnumTabType.backupList" @click="() => onOpenYaml(row)" text="YAML" />

            <LinkButton
              v-if="activeTab !== EnumTabType.backupList"
              @click="() => onExec(row)"
              :text="activeTab === EnumTabType.backupStrategy ? '立即备份' : '立即恢复'"
            />
            <LinkButton
              v-if="activeTab !== EnumTabType.backupList"
              @click="() => onDelete(row)"
              text="删除"
              type="danger"
            />
          </Space>
        </template>
      </pro-table>
    </div>

    <Modal
      v-model="createModalVisible"
      title="新建策略"
      :width="40"
      :mask-closable="false"
      class-name="backup-mgt-create-modal"
    >
      <Form :model="formData" label-position="top" ref="formRef" :rules="ruleValidate">
        <FormItem label="Cluster（集群）" prop="clusterId">
          <Select
            v-model="formData.clusterId"
            label-in-value
            filterable
            clearable
            placeholder="请选择集群"
            @on-change="onCreateClusterChange"
          >
            <Option
              v-for="item in createClusterList"
              :value="activeTab === EnumTabType.backupStrategy ? item.id : item.clusterId"
              :key="activeTab === EnumTabType.backupStrategy ? item.id : item.clusterId"
              >{{ activeTab === EnumTabType.backupStrategy ? item.name : item.clusterName }}</Option
            >
          </Select>
        </FormItem>

        <FormItem label="Group / Resource （API组 / 资源）" prop="groupResource">
          <Select
            v-model="formData.groupResource"
            filterable
            :disabled="!formData.clusterId"
            clearable
            label-in-value
            placeholder="请选择API 组 / 资源"
            @on-change="onCreateGroupResourceChange"
          >
            <Option v-for="item in createGroupResourceList" :value="item.value" :key="item.value">{{
              item.label
            }}</Option>
          </Select>
        </FormItem>

        <FormItem label="Namespace（命名空间）" prop="namespace" :required="isCreateNamespaceRequired">
          <Select
            v-model="formData.namespace"
            filterable
            clearable
            :disabled="!isCreateNamespaceRequired"
            placeholder="请选择命名空间"
            @on-change="onCreateNamespaceChange"
          >
            <Option v-for="item in createNamespaceList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>

        <FormItem label="ObjectName（资源对象）" prop="objectNames" v-if="activeTab === EnumTabType.backupStrategy">
          <Select
            v-model="formData.objectNames"
            filterable
            multiple
            :disabled="!formData.groupResource || !isCreateNamespaceIllegal"
            placeholder="请选择资源对象"
          >
            <Option v-for="item in createObjectNameList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <div class="object-name-wrapper" v-else>
          <div class="object-name-title">ObjectName（资源对象）</div>
          <div class="object-name-operation">
            <Input
              v-model="objectNameSearch"
              placeholder="请输入名称搜索"
              style="width: 280px"
              clearable
              search
              @on-search="onSearchObjectNames"
            />
            <link-button :disabled="!formData.objectNames?.length" text="清空已选" @click="clearSelection" />
          </div>
          <div class="object-name-selection">
            已选内容：
            <span v-if="!formData.objectNames?.length">全选</span>
            <div v-for="item in formData.objectNames" :key="item" class="selection-item">
              <span class="object">{{ item.split('/')[0] }}</span>
              <span class="version">（{{ item.split('/')[1] }}）</span>
            </div>
          </div>
          <pro-table
            :key="`object-name/${formData.namespace}/${formData.groupResource}/${formData.clusterId}`"
            :columns="OBJECT_NAME_COLUMNS"
            :request="getObjectNamesTableData"
            :action-ref="objectNameRefObject"
            :viewDesignTableRef="viewDesignTableRef"
            hideSearchOperation
            manual-request
            :pagination="{ defaultPageSize: 10, simple: true }"
            :max-height="300"
            row-key="objectName"
            @on-select="(selection, row) => onSelectObjectNames([row], 'push')"
            @on-select-cancel="(selection, row) => onSelectObjectNames([row], 'pop')"
            @on-select-all="(selection) => onSelectObjectNames(selection, 'push')"
            @on-select-all-cancel="() => onSelectObjectNames(viewDesignTableRef.tableRef.value.data ?? [], 'pop')"
          >
            <template #ops="{ row }">
              <LinkButton @click="() => onOpenYaml(row)" text="YAML" />
            </template>
          </pro-table>
        </div>

        <FormItem label="生效期限" prop="isPersistent" v-if="activeTab === EnumTabType.backupStrategy">
          <template #label>
            <div class="label-wrapper">
              <span>生效期限 </span>
              <Poptip
                always
                trigger="hover"
                content="选择永久生效时，当该规则关联的资源对象变化时会持续进行自动备份"
                placement="right"
                transfer
                transfer-class-name="form-item-poptip"
              >
                <Icon
                  type="ios-alert-outline"
                  style="color: #2a7cc3; font-size: 14px; cursor: pointer; margin-right: 16px"
                />
              </Poptip>
            </div>
          </template>
          <RadioGroup v-model="formData.isPersistent">
            <Radio label="true">永久生效</Radio>
            <Radio label="false">临时生效</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="备注" prop="desc" v-if="activeTab === EnumTabType.backupStrategy">
          <Input v-model="formData.desc" type="textarea" :rows="4" placeholder="请输入备注" />
        </FormItem>
      </Form>

      <template #footer>
        <Button type="text" @click="() => (createModalVisible = false)">取消</Button>
        <Button type="primary" @click="onCreate">确定</Button>
      </template>
    </Modal>

    <Modal v-model="yamlModal.visible" title="查看YAML" footer-hide :width="60" class="backup-mgt-yaml-modal">
      <yaml v-if="yamlModal.data" :value="yamlModal.data" :forbiddenEdit="true" style="height: 60vh" />
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.backup-mgt-wrapper {
  .search-wrapper,
  .table-wrapper {
    padding: 16px;
    background: #fff;
    align-items: center;
    &:hover {
      box-shadow: 0px 0px 4px 0px #ddd;
    }
  }
  .search-wrapper {
    margin: 16px 0;

    .search-item {
      width: 140px;
      &.group-resource {
        width: 300px;
      }
      &.time-range {
        width: 240px;
      }
    }
  }
  .type-item {
    font-weight: 600;

    &.normal {
      color: #2a7cc3;
    }
    &.warning {
      color: #ff9800;
    }
  }
  .table-wrapper /deep/div div div {
    box-shadow: none !important;
  }
  :deep(td.ivu-table-expanded-cell) {
    padding: 16px;
  }
}
.label-wrapper {
  display: inline-flex;
  align-items: center;
  > span {
    margin-right: 8px;
  }
}
.object-name-wrapper {
  .object-name-title {
    margin-bottom: 8px;
  }
  .object-name-operation {
    display: flex;
    align-items: center;
    a {
      margin-left: 8px;
    }
  }
  .object-name-selection {
    margin: 8px 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    max-height: 120px;
    overflow-y: auto;
    .selection-item {
      width: 50%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .object {
      font-weight: 600;
    }
  }
}
</style>
<style lang="less">
.backup-mgt-create-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    max-height: 70vh;
    overflow: auto;
  }
}
.object-name-wrapper {
  overflow: hidden;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.ivu-poptip {
  &,
  /deep/.ivu-poptip-rel {
    height: 100%;
    display: block;
  }
}
</style>
<style lang="less">
.object-name-poptip-wrapper {
  max-width: 548px;
  max-height: 240px;
  white-space: pre-wrap;
}
</style>
