import { usePost } from '@/libs/service.request'
import Config from '@/config'
import { useRequest } from 'vue-request'
import { HistoryParam, Resource } from '../type'
import { computed } from 'vue'

export default function useRelativeResourceModalService(props: { data: Resource; value: boolean }) {
  const visible = computed(() => props.value)

  const { data, loading } = useRequest(
    () => {
      return usePost<{ data: HistoryParam[] }>(
        `${Config.Api.Base}${Config.Api.GetMultiClusterSearchRelateResource}`,
        props.data
      )
    },
    {
      ready: visible,
      formatResult: (res) => {
        return res.data?.data
      }
    }
  )

  return { data, loading }
}
