{"name": "iview-admin", "version": "2.0.0", "author": "<PERSON>son<<EMAIL>>", "private": false, "scripts": {"local": "vue-cli-service serve --open --mode local", "dev": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "build:alpha": "vue-cli-service build --mode alpha", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "engines": {"node": ">=16.x", "pnpm": "=8.5.0"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@antv/g6": "^4.8.20", "axios": "1.2.2", "clipboard": "^2.0.0", "codemirror": "^5.65.5", "core-js": "^3.8.3", "countup": "^1.8.2", "dayjs": "^1.7.7", "diff-match-patch": "^1.0.5", "echarts": "^4.0.4", "iview": "3.5.4", "js-cookie": "^2.2.0", "js-yaml": "^4.1.0", "json2yaml": "^1.1.0", "lodash-es": "^4.17.21", "view-design": "^4.7.0", "vue": "2.7.14", "vue-class-component": "^7.2.3", "vue-json-viewer": "^2.2.22", "vue-matomo": "^4.2.0", "vue-property-decorator": "^9.1.2", "vue-request": "^1.2.4", "vue-router": "^3.0.1", "vue-styled-components": "^1.6.0", "vue-template-compiler": "2.7.14", "vuedraggable": "^2.24.3", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-typescript": "^7.21.3", "@types/lodash-es": "^4.17.7", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "@vue/test-utils": "^1.0.0-beta.10", "chai": "^4.1.2", "eslint": "^7.32.0", "eslint-config-alloy": "^4.8.0", "eslint-plugin-vue": "^8.0.3", "less": "^2.7.3", "less-loader": "^4.0.5", "lint-staged": "^6.0.0", "mockjs": "^1.0.1-beta3", "typescript": "~4.5.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/typescript"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}, "packageManager": "pnpm@8.5.0+sha256.b013403809dc950d169f16423b74e9b6c749f6934adb067abe90e9a16b89935f"}