<script lang="ts" setup>
import { COLUMNS } from './setting'
import Space from '@/components/space'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import { BatchCopy } from '@/domain/Resource'

import { ResourceForm } from '@/components/resource-form'
import { EnumComponentType } from '@/domain/Resource'

import usePVCService from './usePVCService'
import { PropType, ref } from 'vue'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  }
})
const {
  getTableData,
  refObject,

  onEdit,
  onDelete,

  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,

  onSubmitSuccess,
  onCreate,
  yamlInitData
} = usePVCService(props)

const batchCopyModalVisible = ref(false)
</script>

<template>
  <div>
    <Alert show-icon>
      当前页面中您可以管理 PVC 资源. 请谨慎操作修改，因为
      <span style="color: #ed4014">不当的修改</span> 可能会影响已部署的服务。
    </Alert>
    <pro-table
      :columns="COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      v-bind="{
        ...(props.type === EnumComponentType.Independent
          ? {
              search: [{ value: 'keyword', label: '名称', initData: props.name }],
              'on-create': onCreate
            }
          : { pagination: false })
      }"
    >
      <template #operate-buttons>
        <Button
          v-if="props.type === EnumComponentType.Independent"
          size="small"
          type="primary"
          ghost
          icon="md-copy"
          @click="() => (batchCopyModalVisible = true)"
          >批量复制</Button
        >
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button v-if="props.type === EnumComponentType.Independent" @click="() => onEdit(row)" text="编辑" />
          <link-button
            v-if="props.type === EnumComponentType.Independent"
            @click="() => onDelete(row)"
            text="删除"
            type="danger"
          />
        </space>
      </template>
    </pro-table>
    <resource-form
      resourceType="pvc"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :onSubmitCallBack="onSubmitSuccess"
      :yamlInitData="yamlInitData"
      resource-version="V1"
      width="1200"
      forbiddenForm
      notSynchronizeToUnifiedCluster
      isSkipCheck
    />

    <view-yaml
      resourceType="pvc"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
    <BatchCopy v-model="batchCopyModalVisible" resourceType="PersistentVolumeClaim" />
  </div>
</template>

<style lang="less" scoped>
.dashed-wrapper {
  padding: 16px 16px 0 16px;
  border: 1px dashed#dcdee2;
  background: #fff;
  border-radius: 4px;
}

.single-line {
  display: flex;
  align-items: center;

  > div {
    margin: 0;

    &:last-child {
      flex: 1 0 0%;
    }
  }
}
.filter-from-item-margin > .ivu-form-item {
  margin-bottom: 0;
}
.filter-last-from-item-margin > .ivu-form-item:last-child {
  margin-bottom: 0;
}
</style>

<style lang="less">
.text-ellipsis {
  display: flex;
  margin: 0;
  > .ivu-tooltip-rel {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ivu-tooltip-inner {
    max-width: unset;
  }
}
.input-suffix-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-left: 1px solid #cccc;
}
</style>
