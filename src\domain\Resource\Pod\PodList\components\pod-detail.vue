<template>
  <div class="component-pod-detail">
    <Drawer :closable="true" width="75" v-model="isShow">
      <template #header>
        <h4>Pod详情</h4>
      </template>
      <Card>
        <h4>基本信息</h4>
        <Row type="flex" :gutter="20" style="margin-top: 20px">
          <Col span="10" class="info-key">Name</Col>
          <Col span="14" class="info-value">{{ podInfo.metadata.name }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Namespace</Col>
          <Col span="14" class="info-value">{{ podInfo.metadata.namespace }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Priority</Col>
          <Col span="14" class="info-value">{{ podInfo.spec.priority }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">ServiceAccount</Col>
          <Col span="14" class="info-value">{{ podInfo.spec.serviceAccount }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">IP</Col>
          <Col span="14" class="info-value">{{ podInfo.status.podIP }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Node</Col>
          <Col span="14" class="info-value">{{ podInfo.status.hostIP }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Status</Col>
          <Col span="14" class="info-value">
            <Tag :color="getColor(podInfo.status.phase)">{{ podInfo.status.phase }}</Tag>
          </Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Labels</Col>
          <Col span="14" class="info-value">
            <template v-for="(item, index) in getPodLabels(podInfo.metadata.labels)">
              <Tag color="#2a7cc3" :key="index">{{ item }}</Tag>
            </template>
          </Col>
        </Row>
      </Card>

      <Card style="margin-top: 16px">
        <div class="flex-between">
          <h4>容器列表</h4>
          <Space class="title-container" :size="8">
            <Button
              size="small"
              icon="md-list-box"
              type="primary"
              ghost
              @click="() => onViewYamlCallBack(podInfo.metadata.name)"
              >YAML</Button
            >
            <Button size="small" icon="ios-archive" type="primary" ghost @click="onOpenPortForwardModal()"
              >端口转发</Button
            >
            <Button size="small" icon="md-stats" type="primary" ghost @click="onOpenContainerChart()">监控</Button>
            <Button size="small" icon="ios-bug" type="primary" ghost @click="handleOpenAnalyzer()">诊断</Button>
          </Space>
        </div>
        <h5 style="margin-top: 16px">初始化容器</h5>
        <Table style="margin-top: 16px" size="small" :columns="getColumnsSetting(true)" :data="initContainerData">
          <template slot-scope="{ row }" slot="ready">
            <Icon
              :type="row.ready === 'true' ? 'ios-checkmark-circle' : 'ios-close-circle'"
              :color="row.ready === 'true' ? color.success : color.error"
              size="16"
            />
          </template>
        </Table>

        <h5 style="margin-top: 16px">容器</h5>
        <Table style="margin-top: 16px" size="small" :columns="getColumnsSetting()" :data="containerData">
          <template slot-scope="{ row }" slot="ready">
            <Icon
              :type="row.ready === 'true' ? 'ios-checkmark-circle' : 'ios-close-circle'"
              :color="row.ready === 'true' ? color.success : color.error"
              size="16"
            />
          </template>
        </Table>
      </Card>

      <Card style="margin-top: 16px">
        <EventTable :uuid="uuid" :clusterId="currentClusterId" v-if="uuid && currentClusterId" />
      </Card>
    </Drawer>

    <Modal v-model="crashLogModalVisible" footer-hide width="90" class-name="modal-wrapper">
      <template #header>
        <Space class="crash-log-modal-title">
          <span class="title">查看崩溃日志</span>
          <Divider type="vertical" />
          <Space :size="8">
            <span>查看日志行数</span>
            <InputNumber
              size="small"
              :min="1"
              v-model="crashLogLine"
              :step="1000"
              placeholder="请输入查看行数"
              :formatter="(value) => parseInt(`${value}`)"
            />
          </Space>
          <Divider type="vertical" />
          <div class="header-btn" @click="getCrashLog">
            <Icon class="header-icon" type="ios-search" />
            查看
          </div>
          <Divider type="vertical" />
          <div class="header-btn" @click="onDownloadCrashLog">
            <Icon class="header-icon" type="md-download" />
            下载
          </div>
        </Space>
      </template>
      <Spin fix v-if="crashLogModalLoading">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      </Spin>
      <TxtEditor :value="crashLogModalData" :forbiddenEdit="true" class="text-editor" />
    </Modal>
    <ContainerFileDownload :entity="fileEntity" v-model="fileModal" />
    <pod-analyzer v-model="openAnalyzer" :analyze-data="analyzeData" :loading="analyzeLoading" />
    <Modal :title="`监控图 - ${containerChartEntity.pod}`" width="85" footer-hide v-model="containerChartVisible">
      <ContainerChart :reloadFlag="containerChartVisible" :entity="containerChartEntity" prefix="pod-detail" />
    </Modal>
    <EphemeralContainerModal v-model="ephemeralContainerModal" :entity="ephemeralContainerEntity" />
    <PortForwardModal v-model="portForwardModal" :entity="portForwardEntity" type="AssociatedPod" />
  </div>
</template>

<script>
import { color } from '@/libs/consts'
import { LinkButton, Space } from '@/components'
import Config from '@/config'
import { errorMessage, noticeError, noticeSucceed, downloadTxt } from '@/libs/util'
import TxtEditor from '@/components/txt-editor'
import { useGet, usePost } from '@/libs/service.request'
import { Dropdown, DropdownMenu, DropdownItem } from 'view-design'
import { EventTable, ContainerFileDownload, ContainerChart, EphemeralContainerModal } from '@/domain/Resource'
import PodAnalyzer from '@/domain/Resource/Pod/PodAnalyzer.vue'
import { ApiPodAnalyze } from '@/api/k8s/namespace/worload/deployment/pod'
import { PortForwardModal } from '@/domain/PortForward'

export default {
  name: 'component-pod-detail',
  props: ['podInfo', 'uuid', 'onViewYamlCallBack'],
  components: {
    TxtEditor,
    Space,
    EventTable,
    ContainerFileDownload,
    ContainerChart,
    PodAnalyzer,
    EphemeralContainerModal,
    PortForwardModal
  },
  computed: {
    color() {
      return color
    },
    initContainerData() {
      const data = this.podInfo.status.initContainerStatuses
      if (this.podInfo.status.phase !== 'Pending' && data) {
        return this.handleContainerData(data)
      }
      return []
    },
    containerData() {
      const data = this.podInfo.status.containerStatuses
      if (this.podInfo.status.phase !== 'Pending' && data) {
        return this.handleContainerData(data)
      }
      return []
    },

    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  data() {
    return {
      isShow: false,

      crashLogModalVisible: false,
      crashLogModalLoading: false,
      crashLogModalData: '',
      crashLogContainer: '',
      crashLogLine: 1000,

      fileEntity: {},
      fileModal: false,
      containerChartVisible: false,
      containerChartEntity: {},
      openAnalyzer: false,
      analyzeData: {},
      analyzeLoading: false,

      ephemeralContainerModal: false,
      ephemeralContainerEntity: {},
      portForwardEntity: {},
      portForwardModal: false
    }
  },
  methods: {
    getColor(status) {
      switch (status && status.toLowerCase()) {
        case 'running':
          return color['success']
        case 'pending':
          return color['info']
        case 'terminated':
          return color['error']
      }
    },
    // 处理podlabels
    getPodLabels(labels) {
      // 对象处理为键值对(xxx=xxx)格式
      const arr = []
      for (const key in labels) {
        arr.push(`${key}=${labels[key]}`)
      }
      return arr
    },
    // 对container的数据进行处理
    handleContainerData(data) {
      // 对数据进行处理
      return data.map((item) => {
        const state = Object.keys(item.state)[0]
        const startedAt = new Date(item.state[`${state}`].startedAt).toLocaleString()

        return {
          name: item.name,
          state,
          ready: `${item.ready}`,
          restartCount: item.restartCount,
          startedAt,
          image: item.image
        }
      })
    },
    // 刷新事件列表
    refreshEventList() {
      this.$emit('getEventList', this.podInfo.metadata.uid)
    },
    // 获取容器Log
    handleOpenLog(containerName) {
      let r = this.$router.resolve({
        path: '/kubernetes/logs',
        query: {
          clusterId: this.currentClusterId,
          namespace: this.currentNamespace,
          cluster: this.currentCluster,
          pod: this.podInfo.metadata.name,
          container: containerName
        }
      })
      window.open(r.href, '_blank')
    },
    // 打开shell
    async handleOpenShell(containerName) {
      try {
        const r = this.$router.resolve({
          path: '/pod-console',
          query: {
            clusterId: this.currentClusterId,
            cluster: this.currentCluster,
            namespace: this.currentNamespace,
            pod: this.podInfo.metadata.name,
            container: containerName,
            priority: true
          }
        })
        window.open(r.href, '_blank')
      } catch (error) {
        noticeError(this, `获取Terminal失败 ${errorMessage(error)}`)
      }
    },
    async openCrashLog(record) {
      this.crashLogModalVisible = true

      this.crashLogContainer = record.name
      this.getCrashLog()
    },
    async getCrashLog() {
      this.crashLogModalLoading = true
      try {
        const res = await useGet(`${Config.Api.Base}${Config.Api.GetPodCrashLog}`, {
          params: {
            pod: this.podInfo.metadata.name,
            container: this.crashLogContainer,
            clusterId: this.currentClusterId,
            namespace: this.currentNamespace,
            line: this.crashLogLine
          }
        })
        this.crashLogModalLoading = false
        res.success && (this.crashLogModalData = res.data.data)
      } catch (error) {
        this.crashLogModalLoading = false
      }
    },
    onDownloadCrashLog() {
      downloadTxt(this.crashLogModalData, `崩溃日志（${this.podInfo.metadata.name} / ${this.crashLogContainer}）`)
    },
    async handleProxyOpenLog(level) {
      await usePost(`${Config.Api.Base}${Config.Api.SetDeployPodSidecarLogLevel}`, {
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        pod: this.podInfo.metadata.name,
        level: level
      })
        .then(() => {
          noticeSucceed(this, `设置日志级别成功`)
        })
        .catch((err) => {
          noticeError(this, `设置日志级别失败. ${errorMessage(err)}`)
        })
    },
    onFileModalOpen(record) {
      this.fileEntity = {
        container: record.name,
        pod: this.podInfo.metadata.name,
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace
      }
      this.fileModal = true
    },
    getColumnsSetting(isInitContainer) {
      return [
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          tooltipTheme: 'light',
          minWidth: 80
          //   width: 200
        },
        {
          title: 'State',
          key: 'state',

          align: 'center',
          minWidth: 90,
          render: (h, params) => {
            return h(
              'span',
              {
                style: {
                  color: this.getColor(params.row.state),
                  fontWeight: 700
                }
              },
              params.row.state
            )
          }
        },
        {
          title: 'Ready',
          key: 'ready',
          width: 75,
          slot: 'ready',
          align: 'center'
        },
        {
          title: 'RestartCount',
          key: 'restartCount',
          width: 120,
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'StartedAt',
          key: 'startedAt',
          width: 135,
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'Image',
          key: 'image',
          align: 'center',
          minWidth: 100,
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  props: {
                    placement: 'bottom',
                    theme: 'light',
                    transfer: true,
                    maxWidth: 300
                  },
                  class: {
                    'text-tooltip': true
                  }
                },
                [
                  params.row.image,
                  h(
                    'span',
                    {
                      slot: 'content',
                      style: {
                        whiteSpace: 'normal',
                        wordBreak: 'break-all'
                      }
                    },
                    params.row.image
                  )
                ]
              )
            ])
          }
        },
        {
          title: '操作',
          key: 'ops',
          slot: 'ops',
          align: 'center',
          width: 380,

          render: (h, params) => {
            return h(
              Space,
              {
                style: {
                  alignItems: 'center'
                },
                props: {
                  justify: true
                }
              },
              [
                h(
                  'a',
                  {
                    style: {
                      color: color.primary
                    },
                    on: {
                      click: () => {
                        this.handleOpenLog(params.row.name)
                      }
                    }
                  },
                  '日志'
                ),
                params.row.name === 'istio-proxy'
                  ? h(
                      Dropdown,
                      {
                        props: { transfer: true, trigger: 'click' },
                        on: {
                          'on-click': (level) => {
                            this.handleProxyOpenLog(level)
                          }
                        },
                        scopedSlots: {
                          list: () =>
                            h(DropdownMenu, [
                              h(
                                DropdownItem,
                                {
                                  props: { name: 'info' }
                                },
                                'level info'
                              ),
                              h(
                                DropdownItem,
                                {
                                  props: { name: 'debug' }
                                },
                                'level debug'
                              ),
                              h(
                                DropdownItem,
                                {
                                  props: { name: 'warning' }
                                },
                                'level warning'
                              )
                            ])
                        }
                      },
                      [h('a', '日志级别')]
                    )
                  : h(
                      'a',
                      {
                        style: { color: '#9e9e9e', cursor: 'not-allowed' }
                      },
                      '日志级别'
                    ),
                h(
                  'a',
                  {
                    style: !!params.row.restartCount
                      ? {
                          color: color.primary,
                          marginRight: '16px'
                        }
                      : { color: '#9e9e9e', cursor: 'not-allowed' },
                    on: {
                      click: () => {
                        !!params.row.restartCount && this.openCrashLog(params.row)
                      }
                    }
                  },
                  '崩溃日志'
                ),
                !isInitContainer
                  ? [
                      h(LinkButton, {
                        props: {
                          text: '上传下载',
                          disabled: params.row.name === 'istio-proxy'
                        },
                        on: {
                          click: () => this.onFileModalOpen(params.row)
                        }
                      }),
                      h(
                        'a',
                        {
                          style: {
                            color: color.primary
                          },
                          on: {
                            click: () => {
                              this.ephemeralContainerEntity = {
                                container: params.row.name,
                                pod: this.podInfo.metadata.name,
                                clusterId: this.currentClusterId,
                                namespace: this.currentNamespace,
                                cluster: this.currentCluster
                              }
                              this.ephemeralContainerModal = true
                            }
                          }
                        },
                        '调试模式'
                      )
                    ]
                  : null,
                h(
                  'a',
                  {
                    style: {
                      color: color.error
                    },
                    on: {
                      click: () => {
                        this.handleOpenShell(params.row.name)
                      }
                    }
                  },
                  'SHELL'
                )
              ]
            )
          }
        }
      ]
    },

    onOpenContainerChart() {
      this.containerChartEntity = {
        pod: this.podInfo.metadata.name,
        cluster: this.currentCluster,
        namespace: this.currentNamespace,
        workload: this.podInfo.workload
      }
      this.containerChartVisible = true
    },
    async handleOpenAnalyzer() {
      this.openAnalyzer = true
      this.analyzeLoading = true
      await ApiPodAnalyze({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: this.podInfo.metadata.name
      })
        .then((res) => {
          this.analyzeData = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          setTimeout(() => {
            this.analyzeLoading = false
          }, 1000)
        })
      // clusterId, namespace
    },
    onOpenPortForwardModal() {
      this.portForwardEntity = {
        clusterId: this.currentClusterId,
        cluster: this.currentCluster,
        namespace: this.currentNamespace,
        relativePodName: this.podInfo.metadata.name,
        relativeName: this.podInfo.metadata.name
      }
      this.portForwardModal = true
    }
  }
}
</script>

<style lang="less" scoped>
.title-container {
  display: flex;
  align-items: center;
  a {
    width: ~'calc(100% - 20px)';
  }
  .ivu-icon {
    font-size: 16px;
    color: #2a7cc3;
    cursor: pointer;
  }
}
:deep(.text-tooltip.ivu-tooltip) {
  width: 100%;
}

:deep(.text-tooltip.ivu-tooltip) {
  .ivu-tooltip-rel {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

/deep/.modal-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    position: relative;
    height: 84vh;
    overflow: auto;
  }
}

.crash-log-modal-title,
.crash-log-modal-title span {
  display: flex;
  align-items: center;
  .title {
    font-weight: 600;
  }
  .ivu-input-number {
    width: 120px;
  }
  .header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #2a7cc3;
    cursor: pointer;
    > .header-icon {
      margin-right: 8px;
    }
  }
  .space-component {
    display: inline-flex;
    width: auto;
    align-items: center;
  }
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .space-component {
    width: auto;
  }
}
</style>
