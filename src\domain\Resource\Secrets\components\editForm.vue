<template>
  <div>
    <Tabs type="card" v-model="currentMode" @on-click="handleModeChange">
      <TabPane label="表单模式" name="formMode" :disabled="disableFormMode">
        <div style="height: 76vh; overflow: auto">
          <div style="padding-bottom: 25px"></div>
        </div>
      </TabPane>
      <TabPane label="YAML模式" name="yamlMode">
        <div style="overflow: auto; height: 76vh">
          <yaml style="overflow: auto; padding-bottom: 25px" v-model="editYaml" ref="refYaml" />
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { Yaml } from '@/components'
import { errorMessage, noticeError, yaml2json } from '@/libs/util'
import { ApiResourceYamlGet } from '@/api/k8s/resource'

export default {
  name: 'edit-form',
  components: { Yaml },
  props: {
    createOrUpdate: String,
    clusterId: String,
    namespace: String,
    resourceName: String
  },
  data() {
    return {
      currentMode: 'yamlMode',
      disableFormMode: true,
      editYaml: '',
      editForm: ''
    }
  },
  methods: {
    commitYamlOrForm() {
      if (this.currentMode === 'yamlMode') {
        if (this.createOrUpdate === 'create') {
          this.$emit('createWithYaml', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            data: yaml2json(this.editYaml)
          })
          return
        }
        if (this.createOrUpdate === 'update') {
          this.$emit('updateWithYaml', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            data: yaml2json(this.editYaml)
          })
          return
        }

        return
      }
      if (this.currentMode === 'formMode') {
      }
    },
    handleModeChange(mode) {
      console.log(`current mode: ${mode}`)
      this.currentMode = mode
    },
    resetYaml() {
      this.$nextTick(() => {
        this.editYaml = 'apiVersion: v1\n' + 'kind: Secret\n' + 'metadata:'
        this.$refs.refYaml.refresh()
      })
    },
    async getUpdateYaml() {
      let resource = 'secrets'
      let group = ''
      let version = 'v1'
      await ApiResourceYamlGet({
        resource: resource,
        cluster_id: this.clusterId,
        resource_name: this.resourceName,
        namespace: this.namespace,
        is_edit: true,
        version: version,
        group: group
      })
        .then((res) => {
          this.editYaml = res.data.data.data
          this.$nextTick(() => {
            this.$refs.refYaml.refresh()
          })
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    init() {
      if (this.createOrUpdate === 'create') {
        this.resetYaml()
        return
      }
      // update
      this.getUpdateYaml()
    }
  },
  mounted() {
    this.init()
  }
}
</script>

<style scoped></style>
