<template>
  <div>
    <Card>
      <h4>基本信息</h4>
      <Row type="flex" :gutter="20" style="margin-top: 20px">
        <Col span="10" class="info-key">名称</Col>
        <Col span="14" class="info-value">{{ innerDetail.name }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">命名空间</Col>
        <Col span="14" class="info-value">{{ innerDetail.namespace }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">创建时间</Col>
        <Col span="14" class="info-value">{{ innerDetail.creationTimestamp | dateFormat }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">标签</Col>
        <Col span="14" class="info-value">
          <template v-for="(value, key) in innerDetail.labels">
            <Tag color="#2a7cc3" :key="key"
              ><b>{{ key }}: {{ value }}</b></Tag
            >
          </template>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px">
      <h4>配置列表</h4>
      <Table style="margin-top: 20px" size="small" :loading="tableLoading" :columns="tableCols" :data="tableData">
      </Table>
    </Card>
    <div>
      <Modal width="60" v-model="openEdit" :title="editTitle" footer-hide>
        <div>
          <Button @click="handleJsonPretty" type="primary" style="float: right; margin-left: 16px" size="small"
            >JSON 格式化</Button
          >
          <Button @click="handleB64Decode" type="primary" style="float: right" size="small">Base64 解码</Button>
        </div>
        <br /><br />
        <div>
          <Input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 20 }"
            v-model="editContent"
            placeholder="Enter something..."
            style="width: 100%"
          />
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
import { TimeTrans } from '@/libs/tools'
import { color } from '@/libs/consts'
import { noticeError } from '@/libs/util'

export default {
  name: 'detail',
  props: {
    detail: Object
  },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    }
  },
  data() {
    return {
      innerDetail: {},
      tableLoading: false,
      tableCols: [
        {
          title: '名称',
          key: 'key',
          tooltip: true
        },
        {
          title: '操作',
          key: 'ops',
          align: 'center',
          render: (h, params) => {
            return h(
              'a',
              {
                style: {
                  cursor: 'pointer',
                  color: color.primary
                },
                on: {
                  click: () => {
                    this.openEdit = true
                    this.editTitle = '查看 ' + params.row.key
                    this.editContent = params.row.value
                  }
                }
              },
              '查看'
            )
          }
        }
      ],
      tableData: [],
      editTitle: '',
      editContent: '',
      openEdit: false
    }
  },
  methods: {
    handleJsonPretty() {
      try {
        this.editContent = JSON.stringify(JSON.parse(this.editContent), null, 4)
      } catch (e) {
        noticeError(this, `数据 Json 格式化失败`)
        throw e
      }
    },
    handleB64Decode() {
      try {
        this.editContent = window.atob(this.editContent)
      } catch (e) {
        noticeError(this, `数据 Base64 解码失败`)
        throw e
      }
    }
  },
  mounted() {
    this.innerDetail = this.detail
    this.tableData = this.detail.data
  }
}
</script>

<style scoped></style>
