<template>
  <div>
    <Alert show-icon>
      当前页面中您可以轻松管理 HPA 资源，如创建、修改和删除。同时该资源提供表单模式，简化了配置过程。
    </Alert>
    <Card style="margin-bottom: 16px" :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            ref="input"
            v-model="page.search"
            clearable
            placeholder="搜索 HPA 名称"
            search
            enter-button
            style="width: 100%"
            @on-search="handleSearch"
            @on-clear="handleClear"
          />
        </Col>
      </Row>
      <Row type="flex">
        <Col span="24">
          <Button
            type="primary"
            size="small"
            icon="ios-add"
            @click="
              () => {
                this.openEdit = true
                this.editTitle = '创建'
                this.editMode = 'create'
              }
            "
            >创建</Button
          >
          <Button style="margin-left: 16px" size="small" type="primary" ghost icon="ios-copy" @click="() => batchCopyModalVisible = true">批量复制</Button>
          <Button style="margin-left: 16px" size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable"
            >刷新</Button
          >
        </Col>
      </Row>
    </Card>
    <Card :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <div>
      <Drawer :title="yamlTitle" width="60" v-model="openYaml">
        <Alert show-icon>没有修改版本原因: 未从平台做过配置修改。</Alert>
        <div>
          <Select
            @on-change="setCurrentYaml"
            @on-clear="
              () => {
                this.handleYamlGet(this.currentResourceName)
              }
            "
            clearable
            v-model="currentYaml"
            placeholder="历史版本"
            style="width: 250px; margin-bottom: 16px"
          >
            <Option v-for="item in historyVersionList" :value="item.yaml" :key="item.id">{{ item.createdAt }}</Option>
          </Select>
        </div>
        <div style="max-height: 90%; overflow: auto">
          <yaml v-model="currentYaml" ref="refYaml" :forbiddenEdit="true" />
        </div>
      </Drawer>
    </div>
    <div>
      <Drawer :title="detailTitle" :closable="false" width="60" v-model="openDetail">
        <hpa-detail
          :detail="detailObject"
          :cluster-id="currentClusterId"
          :namespace="currentNamespace"
          :cluster-name="currentCluster"
          v-if="openDetail"
        ></hpa-detail>
      </Drawer>
    </div>
    <div>
      <Drawer :title="editTitle" :mask-closable="false" width="50" v-model="openEdit" :styles="styles">
        <div>
          <Spin fix v-if="commitBtnLoading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <hpa-form
            ref="hpaFrom"
            :cluster-id="currentClusterId"
            :namespace="currentNamespace"
            :create-or-update="editMode"
            :name="editHpaName"
            :yamlName="yamlEditName"
            :kind="editHpaKind"
            @createWithForm="createWithForm"
            @createWithYaml="createWithYaml"
            @updateWithForm="updateWithForm"
            @updateWithYaml="updateWithYaml"
            v-if="openEdit"
          ></hpa-form>
        </div>
        <div class="drawer-footer">
          <Button :loading="commitBtnLoading" style="margin-right: 16px" type="primary" @click="handleHpaFromCommit"
            >提交</Button
          >
          <Button @click="openEdit = false">取消</Button>
        </div>
      </Drawer>
    </div>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="HorizontalPodAutoscaler" />
  </div>
</template>

<script>
import HpaForm from './components/hpa-form'
import HpaDetail from './components/hpa-detail'

import { color } from '@/libs/consts'
import { relativeTime } from '@/libs/tools'
import useSingleK8SService from '@/libs/useSingleK8SService'
import { Ellipsis, Yaml, LinkButton, Space } from '@/components'
import { BatchCopy } from '@/domain/Resource'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'

import {
  ApiHpaList,
  ApiHpaGet,
  ApiHpaDelete,
  ApiHpaFormCreate,
  ApiHpaFormUpdate,
  ApiHpaYamlCreate,
  ApiHpaYamlUpdate
} from '@/api/k8s/namespace/hpa'
import { ApiResourceYamlGet, ApiResourceOpRecordList } from '@/api/k8s/resource'

export default {
  name: 'k8s-hpa',
  components: { Yaml, HpaDetail, HpaForm, BatchCopy },
  setup() {
    const { K8SKey } = useSingleK8SService()
    return { K8SKey }
  },
  data() {
    return {
      styles: {
        // height: 'calc(100% - 55px)',
        // overflow: 'auto',
        // paddingBottom: '53px',
        position: 'static',
        overflow: 'unset'
      },
      historyVersionList: [],
      openYaml: false,
      yamlTitle: '详情',
      currentYaml: 'apiVersion:\nkind:\nmetadata:\nspec:',
      openEdit: false,
      editTitle: '编辑',
      editMode: 'create',
      editHpaName: '',
      editHpaKind: '',
      yamlEditName: '',
      currentResourceName: '',
      detailTitle: '详情',
      detailObject: undefined,
      openDetail: false,
      loading: {
        table: false,
        edit: true
      },
      columns: [
        {
          title: 'Name',
          key: 'name',
          minWidth: 170,
          render: (h, params) => {
            return h(LinkButton, {
              props: {
                text: params.row.name,
                ellipsis: true,
                tooltip: true
              },
              style: {
                fontWeight: 600
              },
              on: {
                click: async () => {
                  this.detailTitle = `详情 ${params.row.name}`
                  await ApiHpaGet({
                    clusterId: this.currentClusterId,
                    namespace: this.currentNamespace,
                    name: params.row.name
                  })
                    .then((res) => {
                      this.detailObject = res.data.data.data
                    })
                    .catch((err) => {
                      this.$Message.error(errorMessage(err))
                      throw err
                    })
                  this.openDetail = true
                }
              }
            })
          }
        },
        {
          title: 'Kind',
          key: 'kind',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 120
        },
        {
          title: 'Reference',
          key: 'scaleTargetRef',
          minWidth: 200,
          render: (h, params) => {
            return h(Ellipsis, `${params.row.scaleTargetRef.kind}/${params.row.scaleTargetRef.name}`)
          }
        },
        {
          title: 'Targets',
          key: 'targets',
          width: 120,
          render: (h, params) => {
            const targets = params.row.targets?.split(',')
            return targets.length
              ? h(Ellipsis, {
                  scopedSlots: {
                    default: () =>
                      targets?.length > 8
                        ? h('div', [
                            targets.slice(0, 8)?.map((i) => h(Ellipsis, { props: { type: 'text' } }, i)),
                            h('div', '...')
                          ])
                        : targets?.map((i) => h('div', i)),
                    content: () => targets?.map((i) => h('p', `- ${i}`))
                  }
                })
              : h('div', '-')
          }
        },
        {
          title: 'Min',
          key: 'minPods',
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center',
          width: 100
        },
        {
          title: 'Max',
          key: 'maxPods',
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true,
          width: 100
        },
        {
          title: 'Replicas',
          key: 'replicas',
          tooltipTheme: 'light',
          tooltip: true,
          width: 100,
          align: 'center'
        },
        {
          title: 'Age',
          key: 'age',
          width: 150,
          render: (h, params) => {
            return h(Ellipsis, {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 160,
          align: 'center',
          render: (h, params) => {
            return h(Space, { props: { justify: true } }, [
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    marginRight: '16px'
                  },
                  on: {
                    click: async () => {
                      this.openYaml = true
                      this.yamlTitle = `${params.row.name}`
                      this.fetchHistoryVersionList(params.row.uuid)
                      await this.handleYamlGet(params.row.name, params.row.kind)
                      this.currentResourceName = params.row.name
                      this.$nextTick(() => {
                        this.$refs.refYaml.refresh()
                      })
                    }
                  }
                },
                'YAML'
              ),
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    marginRight: '16px'
                  },
                  on: {
                    click: () => {
                      this.openEdit = true
                      this.editTitle = '编辑 ' + params.row.name
                      this.editMode = 'update'
                      this.editHpaName = params.row.name
                      this.editHpaKind = params.row.kind
                      this.yamlEditName = params.row.yamlEditName
                    }
                  }
                },
                '编辑'
              ),
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.error
                  },
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除: <b>${params.row.name}</b></p>`,
                        loading: true,
                        onOk: async () => {
                          await this.deleteRecord(params.row.name, params.row.kind)
                          this.reloadTable()
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        search: this.$route.query?.name ?? '',
        total: 0,
        clusterId: '',
        namespace: ''
      },
      commitBtnLoading: false,
      batchCopyModalVisible: false
    }
  },
  computed: {
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    },
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    }
  },
  methods: {
    setCurrentYaml(yaml) {
      this.$nextTick(() => {
        this.currentYaml = yaml
        this.$refs.refYaml.refresh()
      })
    },
    async deleteRecord(name, kind) {
      await ApiHpaDelete({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: name,
        kind: kind
      })
        .then((res) => {
          noticeSucceed(this, 'delete succeed.')
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async fetchHistoryVersionList(uuid) {
      await ApiResourceOpRecordList({
        uuid: uuid,
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        kind: 'HorizontalPodAutoscaler'
      })
        .then((res) => {
          this.historyVersionList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, `获取历史版本失败, ${errorMessage(err)}`)
          throw err
        })
    },
    async handleYamlGet(resourceName, kind) {
      let resource
      let group
      let version
      // if (kind === "多维度HPA") {
      //   resource = "scaledobjects"
      //   group = "keda.sh"
      //   version = "v1alpha1"
      // } else {
      resource = 'horizontalpodautoscalers'
      group = 'autoscaling'
      version = 'v2'
      // version = 'v2beta2'
      // }
      await ApiResourceYamlGet({
        resource: resource,
        group: group,
        version: version,
        cluster_id: this.currentClusterId,
        namespace: this.currentNamespace,
        is_edit: false,
        resource_name: resourceName
      })
        .then((res) => {
          console.log(res.data.data)
          this.currentYaml = res.data.data.data
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
    },
    async reloadTable() {
      this.loading.table = true
      this.page.clusterId = this.currentClusterId
      this.page.namespace = this.currentNamespace
      await ApiHpaList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.loading.table = false
        })
    },
    handleHpaFromCommit() {
      this.$refs.hpaFrom.commitYamlOrForm()
    },
    async createWithForm(data) {
      this.commitBtnLoading = true
      ApiHpaFormCreate(data)
        .then((res) => {
          noticeSucceed(this, '创建成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `创建失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async updateWithForm(data) {
      console.log(JSON.stringify(data))
      this.commitBtnLoading = true
      ApiHpaFormUpdate(data)
        .then((res) => {
          noticeSucceed(this, '更新成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `更新失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async createWithYaml(data) {
      this.commitBtnLoading = true
      ApiHpaYamlCreate(data)
        .then((res) => {
          noticeSucceed(this, '创建成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `创建失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async updateWithYaml(data) {
      this.commitBtnLoading = true
      ApiHpaYamlUpdate(data)
        .then((res) => {
          noticeSucceed(this, '更新成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `更新失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    }
  },
  mounted() {
    this.reloadTable()
    this.$refs.input.focus({
      cursor: 'start'
    })
  },
  watch: {
    K8SKey() {
      this.reloadTable()
      this.$refs.input.focus({
        cursor: 'end'
      })
    }
  }
}
</script>

<style scoped></style>
