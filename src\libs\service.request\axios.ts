import type { AxiosRequestConfig, AxiosResponse, Axios, AxiosHeaders } from 'axios'
import axios from 'axios'
import type { RequestConfig, RequestError, Response, UseAxiosResponse } from './type'
import { getErrorMsg, getErrorObj, handleError } from './plugin'
import Config from './config'

import Vue from 'vue'

const requestWrapper = async <T = any>(
  request: () => Promise<AxiosResponse<Response<T>>>,
  config: RequestConfig
): Promise<UseAxiosResponse<T>> => {
  // 标准的错误信息
  let errorObj: RequestError<T> | undefined
  let errorMsg = ''
  let response: AxiosResponse<Response<T>>
  const { skipLoading, skipErrorHandler } = config
  if (!skipLoading) {
    // @ts-ignore
    config.vue.$Spin.show({
      render: (h) => {
        return h('div', [
          h('Icon', {
            class: 'demo-spin-icon-load',
            props: {
              type: 'ios-loading',
              size: 18
            }
          }),
          h('div', 'Loading')
        ])
      }
    })
  }

  try {
    response = await request()
    if (response?.data?.code !== 0) {
      throw response
    }
    return {
      success: true,
      ...(config.isReturnNativeResponse ? null : response.data),
      response,
      status: response.status
    }
  } catch (e: any) {
    console.log(e?.toString?.(), e)
    response = e?.response ?? e
    errorObj = getErrorObj(e, response, config)
    const [msg] = getErrorMsg(errorObj, config.errorMsgPath)
    errorMsg = msg
    errorObj = { ...errorObj, errorMsg }
  } finally {
    if (!skipLoading) {
      // @ts-ignore
      config.vue.$Spin.hide()
    }

    if (!skipErrorHandler && errorObj) {
      handleError(errorObj)
    }
  }
  return {
    ...(config.isReturnNativeResponse ? null : response?.data),
    status: response?.status,
    success: response?.data?.code === 0 || false,
    response,
    errorMsg
  }
}

export const useGet = <T = any>(url: string, config?: RequestConfig) => {
  const requestConfig = { ...Config, ...config }
  return requestWrapper(() => axios.get<Response<T>>(url, requestConfig), {
    ...requestConfig,
    url
  })
}

export const usePost = <T = any>(
  url: string,
  data?: Object,
  config?: RequestConfig

  // eslint-disable-next-line max-params
) => {
  const requestConfig = { ...Config, ...config }

  return requestWrapper(() => axios.post<Response<T>>(url, data, requestConfig), { ...requestConfig, url, data })
}

export const usePut = <T = any>(
  url: string,
  data?: Object,
  config?: RequestConfig

  // eslint-disable-next-line max-params
) => {
  const requestConfig = { ...Config, ...config }

  return requestWrapper(() => axios.put<Response<T>>(url, data, requestConfig), { ...requestConfig, url, data })
}

export const useDelete = <T = any>(url: string, config?: RequestConfig) => {
  const requestConfig = { ...Config, ...config }
  return requestWrapper(() => axios.delete<Response<T>>(url, requestConfig), {
    ...requestConfig,
    url
  })
}

let interceptorRequestID: number
// 缓存不同头部类型的临时变量
let axiosHeaders = {}
/** 请求拦截 */
export const axiosInterceptorRequest = (config: AxiosRequestConfig<any>) => {
  interceptorRequestID !== undefined && axios.interceptors.request.eject(interceptorRequestID)
  axiosHeaders = { ...axiosHeaders, ...config.headers }
  // 注入头部参数
  interceptorRequestID = axios.interceptors.request.use((config) => {
    config.headers = {
      ...axiosHeaders,
      ...config.headers
    } as AxiosHeaders
    return config
  })
}

let interceptorResponseID: number
/** 响应拦截 */
export const axiosInterceptorResponse = (...arg: Axios['interceptors']['response']['use']['arguments']) => {
  interceptorResponseID !== undefined && axios.interceptors.response.eject(interceptorResponseID)
  // 注入头部参数
  interceptorResponseID = axios.interceptors.response.use(arg)
}
