FROM cr.ttyuyin.com/public/node:16.20.2 as builder

COPY . .
RUN npm install pnpm@8.5.0 --registry=http://yw-nexus.ttyuyin.com:8081/repository/group-npm -g && pnpm fetch --registry=http://yw-nexus.ttyuyin.com:8081/repository/group-npm
RUN pnpm install 
RUN pnpm run build

FROM cr.ttyuyin.com/devops/nginx:2022 as production-stage
RUN rm /etc/nginx/nginx.conf
COPY --from=builder ./nginx.conf /etc/nginx/

COPY --from=builder /dist/ /usr/share/nginx/html/

EXPOSE 80
EXPOSE 443