<script lang="ts" setup>
import { PropType } from 'vue'
import { Space, ProTable, LinkButton } from '@/components'

import usePortForwardService from './usePortForwardService'
import { getColumns } from './setting'
import { Entity, EnumComponentType } from '../type'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  },
  entity: { type: Object as PropType<Entity>, default: () => ({}) }
})
const {
  getTableData,
  refObject,
  onOpenCreateModal,
  onCreate,
  onRebuild,
  onDelete,
  onRefresh,

  cluster,
  clusterList,
  namespace,
  namespaceList,
  pod,
  createModal,
  podList,
  portList,
  createFormRef,
  onCreatePodList,
  onCreatePortList
} = usePortForwardService(props)
</script>

<template>
  <div>
    <Alert show-icon v-if="props.type === EnumComponentType.Independent">
      Pod: 通过该类型创建的端口转发, 当 Pod 被调度或被Kill, 系统会回收端口转发已设置的策略。
    </Alert>
    <Alert show-icon v-else> 注意: 若 Pod 被调度或 Killed 所配置的转发端口将会被回收 </Alert>
    <Space class="search-wrapper" v-if="props.type === EnumComponentType.Independent">
      <Select
        :label-in-value="true"
        class="search-item"
        filterable
        clearable
        placeholder="请选择集群"
        @on-change="
          (value) => {
            cluster = value
          }
        "
      >
        <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select
        v-model="namespace"
        class="search-item"
        filterable
        clearable
        :disabled="!cluster"
        placeholder="请选择命名空间"
      >
        <Option v-for="item in namespaceList" :value="item.name" :key="item.name">{{ item.name }}</Option>
      </Select>
      <Input v-model="pod" class="search-item" clearable placeholder="请输入Pod名称" />

      <Button type="primary" icon="ios-search" @click="onRefresh">搜索</Button>
    </Space>
    <Space v-else class="operation-in-modal">
      <Button type="primary" size="small" icon="md-refresh" ghost @click="onRefresh">刷新</Button>
      <Button
        v-if="props.type !== EnumComponentType.Independent"
        type="primary"
        size="small"
        icon="md-add"
        @click="onOpenCreateModal"
        >创建</Button
      >
    </Space>
    <div :class="props.type === EnumComponentType.Independent ? 'table-in-page' : 'table-in-modal'">
      <pro-table
        manual-request
        :columns="getColumns(props.type)"
        :request="getTableData"
        :action-ref="refObject"
        row-key="uid"
        :hideSearchOperation="true"
        v-bind="{
          ...(props.type !== EnumComponentType.Independent
            ? {
                pagination: false
              }
            : {})
        }"
      >
        <template #ops="{ row }">
          <space justify>
            <link-button @click="() => onRebuild(row)" text="重建" />
            <link-button @click="() => onDelete(row)" text="删除" type="danger" />
          </space>
        </template>
      </pro-table>
    </div>
    <Modal title="创建端口转发" v-model="createModal.visible">
      <Space direction="vertical">
        <Form ref="createFormRef" :model="createModal.data" label-position="top">
          <FormItem
            label="Name（名称）"
            :rules="{ required: true, message: '请输入名称', trigger: 'blur' }"
            prop="name"
          >
            <Input v-model="createModal.data.name" placeholder="请输入名称" />
          </FormItem>
          <FormItem
            label="TargetPort（目标端口）"
            :rules="[
              {
                required: true,
                message: '请输入或选择目标端口',
                trigger: 'change'
              }
            ]"
            prop="targetPort"
          >
            <Select
              v-model="createModal.data.targetPort"
              placeholder="请输入或选择目标端口"
              filterable
              allow-create
              @on-create="onCreatePortList"
            >
              <Option v-for="item in portList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          <FormItem
            label="Pod（目标容器组 ）"
            :rules="{ required: true, message: '请输入或选择目标容器组', trigger: 'change' }"
            prop="pod"
          >
            <Select
              v-model="createModal.data.pod"
              placeholder="请输入或选择目标容器组"
              filterable
              allow-create
              @on-create="onCreatePodList"
            >
              <Option v-for="item in podList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
        </Form>
      </Space>

      <template #footer>
        <Button type="text" @click="() => (createModal.visible = false)">取消</Button>
        <Button type="primary" @click="onCreate">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.search-wrapper {
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  align-items: center;
  &:hover {
    box-shadow: 0px 0px 4px 0px #ddd;
  }

  .search-item {
    width: 240px;
  }
}
.operation-in-modal {
  margin-bottom: 16px;
}

.table-in-page {
  padding: 16px;
  background: #fff;
  &:hover {
    box-shadow: 0px 0px 4px 0px #ddd;
  }
}
.table-in-modal {
}
</style>
