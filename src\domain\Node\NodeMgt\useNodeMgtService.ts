import { useGet } from '@/libs/service.request'
import Config from '@/config'

import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useStore } from '@/libs/useVueInstance'
import { useRequest } from 'vue-request'

export default function useNodeMgtService() {
  const { proxy } = getCurrentInstance()
  const store = useStore()

  const podListDialog = ref({})
  const moreActionDialog = ref({})

  const clusterId = ref()
  const cluster = ref()

  const keyword = ref(proxy.$route.query.name)
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }

  const { data: clusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetClusterList}`
      )
    },
    {
      formatResult: (res) => res.data.data
    }
  )

  const getTableData: TableRequest = async (params) => {
    const [
      nodeListRes,
      {
        data: { data: nodeMetricsList }
      }
    ] = await Promise.all([
      useGet<{ nodes: any[] }>(`${Config.Api.Base}${Config.Api.GetNodeList}/${clusterId.value}`),
      useGet<{ data: any[] }>(`${Config.Api.Base}${Config.Api.GetNodeMetricsList}?clusterId=${clusterId.value}`)
    ])

    const {
      data: { nodes: nodeList }
    } = nodeListRes

    let data = !nodeMetricsList?.length
      ? nodeList ?? []
      : nodeList?.map((node) => {
          const nodeMetrics = nodeMetricsList?.find((i) => i.name === node.name)

          node.cpu = nodeMetrics.cpu
          node.cpu.percent = Number.parseFloat(((node.cpu.usage / node.cpu.total) * 100).toFixed(1))
          node.cpu.usage = (node.cpu.usage / 1000).toFixed(1)
          node.cpu.total = (node.cpu.total / 1000).toFixed(0)

          node.mem = nodeMetrics.mem
          node.mem.percent = Number.parseFloat(((node.mem.usage / node.mem.total) * 100).toFixed(1))
          node.mem.usage = (node.mem.usage / 1024 / 1024 / 1024).toFixed(1)
          node.mem.total = (node.mem.total / 1024 / 1024 / 1024).toFixed(1)

          node.pods = nodeMetrics.pods
          node.pods.percent = Number.parseFloat(((node.pods.usage / node.pods.total) * 100).toFixed(1))
          node.pods.usage = node.pods.usage
          node.pods.total = node.pods.total

          return node
        }) ?? []

    // 请求需要前端手动过滤
    if (keyword.value) {
      data = data?.filter((i) => i.name.includes(keyword.value))
    }
    return {
      success: nodeListRes.success,
      total: nodeList.length ?? 0,
      data
    }
  }

  const onOpenPodListModal = (record) => {
    podListDialog.value = {
      visible: true,
      node: record.name
    }
  }

  const onOpenMoreActionModal = (record) => {
    moreActionDialog.value = {
      visible: true,
      node: record.name
    }
  }

  const jumpToMonitor = (record) => {
    const url = `https://yw-monitor.ttyuyin.com/d/k8s_views_nodes/views-nodes?orgId=7&refresh=30s&var-datasource=k8s-yw-thanos-query-frontend&var-resolution=30s&var-cluster=${cluster.value}&var-node=${record.internal_ip}&var-instance=${record.internal_ip}&from=now-12h&to=now`
    window.open(url, '_blank')
  }

  const onClusterChange = (item) => {
    clusterId.value = item.value
    cluster.value = item.label
  }

  const onSearch = () => {
    refObject.tableRef.value?.reload()
  }

  watch(
    () => [clusterId.value, refObject.tableRef.value],
    () => {
      clusterId.value && refObject.tableRef.value?.reload()
    },
    { immediate: true }
  )

  onMounted(() => {
    store.commit('getCurrentClusterID', store.state.user.userId)

    store.commit('getCurrentCluster', store.state.user.userId)

    clusterId.value = proxy.$route.query.clusterId
      ? Number(proxy.$route.query.clusterId)
      : Number(store.state.k8s.currentClusterId)
    cluster.value = proxy.$route.query.cluster ?? store.state.k8s.currentCluster
  })

  return {
    cluster,
    clusterId,
    clusterList,
    getTableData,
    refObject,
    onOpenPodListModal,
    podListDialog,
    moreActionDialog,
    onOpenMoreActionModal,
    jumpToMonitor,
    onClusterChange,
    keyword,
    onSearch
  }
}
