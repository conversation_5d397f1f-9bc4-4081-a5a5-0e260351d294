import { ref } from 'vue'
import { Area, Datum } from '@antv/g2plot'
import { chartTheme } from './chartTheme'

import { ChartType } from './useContainerChartService'

function formatFileSize(fileSize, dot: number) {
  if (fileSize < 1024) {
    return fileSize + ' B'
  } else if (fileSize < 1024 * 1024) {
    return (fileSize / 1024).toFixed(dot ?? 0) + ' KB'
  } else if (fileSize < 1024 * 1024 * 1024) {
    return (fileSize / (1024 * 1024)).toFixed(dot ?? 0) + ' MB'
  } else {
    return (fileSize / (1024 * 1024 * 1024)).toFixed(dot ?? 0) + ' GB'
  }
}

export default function useChartService(type: ChartType, prefix: string) {
  const chartRef = ref()
  const CONTAINER_ID = `${prefix}-${type}-container`

  const onFormatUnit = (data, dot) => {
    switch (type) {
      case 'mem usage':
        return formatFileSize(data, 2)
      case 'receive bandwidth':
      case 'transmit bandwidth':
        return formatFileSize(data, 2) + '/s'
      case 'cpu throttling':
        return Number(data).toFixed(2) + '%'
      case 'cpu usage':
      case 'cpu usage(workload stdvar)':
      default:
        return Number(data).toFixed(4)
    }
  }

  const getTickInterval = () => {
    switch (type) {
      case 'mem usage':
        return 1024 * 1024 * 1024
      case 'receive bandwidth':
      case 'transmit bandwidth':
        return 1000 * 1024
      case 'cpu throttling':
      // return 0.1
      case 'cpu usage(workload stdvar)':
      case 'cpu usage':
      default:
        return
    }
  }

  const renderChart = (data) => {
    if (!chartRef.value) {
      chartRef.value = new Area(CONTAINER_ID, {
        data,
        padding: 'auto',
        xField: 'date',
        yField: 'value',
        seriesField: 'type',
        yAxis: {
          grid: { line: { style: { stroke: '#e8e8e8', lineWidth: 1, lineDash: [3, 4] } } },
          label: { formatter: (v) => onFormatUnit(v, 0) },
          tickLine: { alignTick: true },
          tickInterval: getTickInterval()
        },
        xAxis: {
          // type: 'timeCat',
          tickCount: data.length > 40 ? Math.ceil(data.length / 4) : 4
        },
        tooltip: {
          formatter: (datum: Datum) => {
            return { name: datum.type, value: onFormatUnit(datum.value, 4) }
          }
        },
        theme: chartTheme,
        legend: {
          position: 'bottom',
          flipPage: true
        }
      })
      chartRef.value.render()
    } else {
      chartRef.value.changeData(data)
    }
  }
  return { CONTAINER_ID, renderChart }
}
