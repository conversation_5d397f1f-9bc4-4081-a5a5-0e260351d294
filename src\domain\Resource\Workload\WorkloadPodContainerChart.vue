<script lang="ts" setup>
import { useGet } from '@/libs/service.request'
import { PropType, computed, ref, watch } from 'vue'
import Config from '@/config'
import { useRequest } from 'vue-request'
import { ContainerChart } from '@/domain/Resource'
const props = defineProps({
  reloadFlag: Boolean,
  kind: String as PropType<'Deployment' | 'StatefulSet'>,
  entity: Object as PropType<{
    clusterId: string
    cluster: string
    namespace: string
    name: string
  }>,
  initPodName: String
})

const containerChartEntity = computed(() => {
  const { clusterId, name, ...rest } = props.entity
  return { ...rest, pod: pod.value, workload: name }
})
const pod = ref()

const { data, run } = useRequest(
  () => {
    return useGet<{ data: string[] }>(`${Config.Api.Base}${Config.Api.GetWorkloadPodList}`, {
      params: { ...props.entity, kind: props.kind }
    })
  },
  {
    manual: true,
    formatResult: (res) => res.data.data,
    onSuccess: (data) => {
      console.log(4234234323, data)
      if (!props.initPodName) {
        pod.value = data?.[0]
      }
    }
  }
)

watch(
  () => props.entity,
  () => {
    props.entity?.name && run()
  },
  { deep: true, immediate: true }
)

watch(
  () => props.initPodName,
  (val) => {
    val && (pod.value = val)
  },
  {
    immediate: true
  }
)
</script>

<template>
  <ContainerChart :reloadFlag="pod" :entity="containerChartEntity" prefix="workload">
    <template #operation>
      <Select v-model="pod" style="width: 240px">
        <Option v-for="item in data" :key="item" :value="item">{{ item }}</Option>
      </Select>
    </template>
  </ContainerChart>
</template>

<style lang="less" scoped></style>
