import { Ellipsis, Space } from '@/components'
import { relativeTime } from '@/libs/tools'
import { getPodStatusColor } from '../Pod/setting'

export const TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Completions',
    key: 'completions',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Duration',
    key: 'duration',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,
    tooltipTheme: 'light',
    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]
export const RELATED_POD_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Namespace',
    key: 'namespace',
    tooltip: true,
    tooltipTheme: 'light'
  },

  {
    title: 'Status',
    key: 'phase',
    width: 280,

    render: (h, params) => {
      return h(Space, { props: { size: 4 } }, [
        h(
          Ellipsis,
          {
            props: { type: 'text' },
            style: {
              color: getPodStatusColor(params.row.phase),
              fontWeight: 600,
              flexShrink: 0
            }
          },
          params.row.phase
        ),
        ...[
          h('span', ' | '),
          h(
            Ellipsis,
            {
              props: { type: 'text' },
              style: {
                color: '#cdcdcd'
              }
            },

            params.row.reason ? params.row.reason : '正常运转'
          )
        ]
      ])
    }
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,

    width: 100,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 90,
    align: 'center'
  }
]
