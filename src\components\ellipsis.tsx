import { Tooltip } from 'view-design'
import { PropType, defineComponent } from 'vue'
import './ellipsis.less'

const Ellipsis = defineComponent({
  name: 'Ellipsis',
  props: {
    type: { type: String as PropType<'tooltip' | 'text'>, default: 'tooltip' }
  },
  data() {
    return {
      isContentSlot: false
    }
  },
  mounted() {
    this.isContentSlot = !!this.$scopedSlots.content
  },
  render() {
    return this.type === 'tooltip' ? (
      <Tooltip
        class="ellipsis-wrapper"
        transferClassName="ellipsis-tooltip-wrapper"
        transfer
        placement="top-start"
        theme="light"
        scopedSlots={{
          content: this.isContentSlot ? this.$scopedSlots.content : this.$scopedSlots.default,
          default: this.$scopedSlots.default
        }}
      />
    ) : (
      <div class="ellipsis-text-wrapper" title={this.$slots.default?.[0].text ?? ''} {...this.props}>
        {this.$slots.default}
      </div>
    )
  }
})

export default Ellipsis
