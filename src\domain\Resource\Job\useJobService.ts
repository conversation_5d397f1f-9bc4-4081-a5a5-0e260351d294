import { ref, onMounted, getCurrentInstance, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'

import { YamlGVR } from '../config'
import { YamlHistoryParams } from '@/components/yaml'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList, Response } from '@/libs/service.request'
import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import useSingleK8SService from '@/libs/useSingleK8SService'
import { useRequest } from 'vue-request'

interface Job {
  name: string
  uuid: string
}

export default function useJobService() {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const detailModal = ref({
    visible: false,
    data: {} as Job
  })

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...YamlGVR['Job']
    }

    yamlInitData.value = `apiVersion: v1 
kind: Job
metadata:
    name: 必须修改
    namespace: ${K8SInstance.value.namespace}
spec:`
  }

  const onDelete = (record: Job) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteJob}`, {
          params: {
            ...K8SInstance.value,
            name: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onEdit = (record: Job) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...YamlGVR['Job'],
      resourceName: record.name
    }
  }

  const onViewYaml = (record: Job, type: 'job' | 'pod' = 'job') => {
    yamlHistoryParams.value = {
      ...K8SInstance.value,
      ...(type === 'job' ? { kind: 'Job' } : { kind: 'Pod' }),
      uuid: record.uuid
    }
    formEntity.value = {
      ...K8SInstance.value,
      ...(type === 'job' ? YamlGVR['Job'] : YamlGVR['Pod']),
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: Job) => {
    console.log('onViewDetail')
    detailModal.value = {
      visible: true,
      data: record
    }
    getDetailInfo()
    getRelatedPodInfo()
  }

  const { data: detailInfo, run: getDetailInfo } = useRequest(
    () => {
      return useGet<Response<Record<string, string>>>(`${Config.Api.Base}${Config.Api.GetJobDetailData}`, {
        params: {
          ...K8SInstance.value,
          name: detailModal.value.data.name
        }
      })
    },
    {
      manual: true,
      initialData: {},
      formatResult(res) {
        return res.data.data
      }
    }
  )

  const { data: relatedPodInfo, run: getRelatedPodInfo } = useRequest(
    () => {
      return useGet<Response<Record<string, string>[]>>(`${Config.Api.Base}${Config.Api.GetJobRelativePod}`, {
        params: {
          ...K8SInstance.value,
          name: detailModal.value.data.name
        }
      })
    },
    {
      manual: true,
      initialData: [],
      formatResult(res) {
        return res.data.data
      }
    }
  )

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<Job[]>>(
      `${Config.Api.Base}${Config.Api.GetJobTableData}?search=${params.searchValue ?? ''}&clusterId=${
        K8SInstance.value.clusterId
      }&namespace=${K8SInstance.value.namespace}&page=${params.pageIndex}&size=${params.pageSize}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const initK8SInstance = () => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    store.commit('getCurrentNamespace', store.state.user.userId)
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId
    }
  }

  const onJumpToPod = (record) => {
    const path = '/kubernetes/resource/namespace/pod'
    const query = {
      name: record.name
    }
    const router = proxy.$router.resolve({
      path,
      query
    })
    window.open(router.href, '_blank')
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value?.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value?.reload()
  })
  return {
    getTableData,
    refObject,
    onCreate,
    onEdit,
    onDelete,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    onSubmitSuccess,
    onViewDetail,
    detailModal,
    detailInfo,
    K8SInstance,
    relatedPodInfo,
    onJumpToPod
  }
}
