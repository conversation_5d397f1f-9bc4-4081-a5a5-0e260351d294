import axios from '@/libs/api.request'

export const ApiKubectlAuthRoleWithUserList = (page) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-with-user/list',
    method: 'get',
    params: page
  })
}

export const ApiKubectlGlobalRoleCreate = (data) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-global/create',
    method: 'post',
    data: data
  })
}

export const ApiKubectlGlobalRoleUpdate = (data) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-global/update',
    method: 'post',
    data: data
  })
}

export const ApiKubectlGlobalRoleDelete = (cluster_id, user_id, role_id) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-global/delete',
    method: 'delete',
    data: {
      cluster_id,
      user_id,
      role_id
    }
  })
}

export const ApiKubectlNsRoleCreate = (data) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-namespace/create',
    method: 'post',
    data: data
  })
}

export const ApiKubectlNsRoleUpdate = (data) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-namespace/update',
    method: 'post',
    data: data
  })
}

export const ApiKubectlNsRoleDelete = (cluster_id, user_id, role_id) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/role-namespace/delete',
    method: 'delete',
    data: {
      cluster_id,
      user_id,
      role_id
    }
  })
}

export const ApiKubectlExecPodGet = (cluster_id) => {
  return axios.request({
    url: '/api/v1/kubectl/auth/exec-pod/get',
    method: 'get',
    params: {
      cluster_id
    }
  })
}

export const ApiKubectlRoleTemplateGet = (template_name) => {
  return axios.request({
    url: '/api/v1/kubectl/role/template/get',
    method: 'get',
    params: {
      template_name
    }
  })
}

export const ApiKubectlRoleTemplateNameList = () => {
  return axios.request({
    url: '/api/v1/kubectl/role/template-name/list',
    method: 'get'
  })
}

export const ApiKubectlConnStatusUpdate = (cluster_id, pod, is_close) => {
  return axios.request({
    url: '/api/v1/kubectl/conn-status/update',
    method: 'post',
    data: {
      cluster_id,
      pod,
      is_close
    }
  })
}
