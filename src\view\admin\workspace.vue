<script lang="ts" setup>
import { Container, Space } from '@/components'
import useUserInfoService, { UserInfoServiceProvide } from '@/domain/Auth/useUserInfoService'
import { ViewAuth, UserInfo } from '@/domain/Auth'
import { FavorList, WorkloadSubscribeMgt } from '@/domain/Resource'
import { provide } from 'vue'

const UserInfoService = useUserInfoService()
provide(UserInfoServiceProvide, UserInfoService)
</script>

<template>
  <Container>
    <Space direction="vertical" class="wrapper">
      <div class="welcome">
        <img src="@/assets/images/logo.png" class="icon-watermark" />
        <div class="title">HI, 欢迎回来 牵星 - 容器云平台 ~~</div>
        <div class="desc">面向云原生应用的企业级容器管理平台, 容器管理、统一的认证鉴权与审计功能</div>
      </div>
      <Space class="row">
        <div class="col">
          <Space class="title info">
            <Icon type="md-information-circle" />
            <span>基本信息</span>
          </Space>
          <div class="content">
            <UserInfo />
          </div>
        </div>
        <div class="col">
          <Space class="title auth">
            <Icon type="md-key" />
            <span>权限信息</span>
          </Space>
          <div class="content">
            <ViewAuth />
          </div>
        </div>
      </Space>
      <Space class="row">
        <div class="col large">
          <Space class="title deployment">
            <Icon type="md-cube" />
            <span>应用列表</span>
          </Space>
          <div class="content">
            <WorkloadSubscribeMgt />
          </div>
        </div>
        <div class="col large">
          <Space class="title favor">
            <Icon type="md-star" />
            <span>收藏资源</span>
          </Space>
          <div class="content">
            <FavorList />
          </div>
        </div>
      </Space>
    </Space>
  </Container>
</template>

<style lang="less" scoped>
.wrapper {
  height: 100%;
  display: block;
  .welcome {
    border-radius: 6px;
    background: linear-gradient(90deg, #2a7cc3, #a9d6fb);
    color: #fff;
    padding: 16px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    .icon-watermark {
      position: absolute;
      height: 124px;
      z-index: 2;
      opacity: 0.5;
      pointer-events: none;
      right: -276px;
      object-fit: cover;
      object-position: left;
      top: 0;
    }
    .title {
      font-size: 16px;
      margin-bottom: 8px;
    }
  }
  .row {
    flex: 1 0 0%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .col {
      background: #fff;
      height: 100%;
      flex: 1 0 0%;
      border-radius: 6px;
      height: 338px;
      flex-shrink: 0;
      width: ~'calc(50% - 8px)';
      &:hover {
        box-shadow: 0px 0px 5px 0px #c9c9c9;
      }
      &.large {
        height: 400px;
      }
      .title {
        height: 36px;
        border-radius: 6px;
        font-weight: 600;
        align-items: center;
        padding: 0 16px;
        color: #fff;
        &.info {
          background: #2a7cc3;
        }
        &.auth {
          background: #ffaf36;
        }
        &.deployment {
          background: #fd9148;
        }
        &.favor {
          background: #38a996;
        }
      }
      .content {
        padding: 16px;
        max-height: 500px;
        overflow: auto;
        height: ~'calc(100% - 36px)';
        &.tabs {
          padding: 0 16px;
        }
      }
    }
  }
}
</style>
