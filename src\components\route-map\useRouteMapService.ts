import G6, { ModelConfig } from '@antv/g6'
import { NodeName, EdgeName } from './register'
import { getCurrentInstance, onMounted, ref, watch } from 'vue'
import bindEvent from './event'
import { EnumStatus, RouterItem, TableData, TableRequestResponseData } from './type'
import { useGet } from '@/libs/service.request'
import Config from '@/config'
import { onZoomChange } from './event/view'
import { cloneDeep } from 'lodash-es'

export const CONTAINER_ID = 'container'

export default function useRouteMapService(props: { routeData?: RouterItem }) {
  const graphRef = ref()
  const showType = ref<EnumStatus[]>([EnumStatus.succeed, EnumStatus.warning, EnumStatus.error])
  const { proxy } = getCurrentInstance()
  const routeData = ref<RouterItem>(props.routeData)
  const activeNodesCount = ref<number>(0)

  const modal = ref<Partial<TableRequestResponseData> & { visible: boolean }>({
    visible: false
  })

  const onShowTypeChange = (checks: EnumStatus[]) => {
    if (checks.length < 1) {
      proxy.$Message.warning('至少选择一个类型')
      showType.value = [...showType.value]
    } else {
      showType.value = checks
      if (checks.length === 3) {
        // 全选无需递归
        graphRef.value.updateChild(props.routeData)
        routeData.value = props.routeData
      } else {
        onFilterRouteNode([...checks])
      }
    }
  }

  const onFilterRouteNode = (statusList: EnumStatus[]) => {
    let data = cloneDeep(props.routeData)

    const filterChildren = data.children.length ? treeForeach(data.children, statusList) : []
    if (filterChildren.length) {
      data.children = filterChildren
    } else {
      data = null
    }
    routeData.value = data
    graphRef.value.updateChild(data)
  }

  function treeForeach(tree: RouterItem[], statusList) {
    const filterChildrenTree = []
    tree?.forEach((i) => {
      if (statusList.includes(i.state.status)) {
        filterChildrenTree.push(i)
      } else if (i.children?.length) {
        const filterChildren = treeForeach(i.children, statusList)
        if (filterChildren.length) {
          filterChildrenTree.push({ ...i, children: filterChildren })
        }
      }
    })
    return filterChildrenTree
  }

  const openTableInModal = (args: TableRequestResponseData) => {
    modal.value = {
      data: args.data,
      // 赋予slot
      columns: args.columns?.map((i) => ({
        ...i,
        ...(i.key === 'name' ? { slot: 'name' } : null),
        ...(i.key === 'age' ? { slot: 'age' } : null)
      })),
      visible: true
    }
  }
  const onOpenYamlFromTable = async (record: TableData) => {
    const res = await useGet<TableRequestResponseData>(
      `${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`,
      {
        params: {
          ...record.gvr,
          cluster_id: record.clusterId,
          namespace: record.namespace,
          resource_name: record.name,
          is_edit: false
        }
      }
    )
    if (res.success) {
      openYamlWindow(res.data.data)
    }
  }

  const openYamlWindow = (yamlJson) => {
    sessionStorage.setItem('yaml', yamlJson)

    // 创建一个居中的窗口
    const width = 700
    const height = 500
    // 计算窗口的起始位置 (居中)
    const left = (screen.width - width) / 2
    const top = (screen.height - height) / 2

    const url = `${window.location.origin}/pure-yaml`
    // 打开新的窗口并设置窗口的特性
    window.open(
      url,
      '_blank',
      'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no,' +
        'width=' +
        width +
        ',height=' +
        height +
        ',top=' +
        top +
        ',left=' +
        left
    )
  }
  const onSearch = (val) => {
    if (!graphRef.value) return
    console.log(11, val, graphRef.value.getNodes())
    const nodes = graphRef.value.getNodes()
    if (!val) {
      activeNodesCount.value = 0

      nodes?.map((i) => graphRef.value.setItemState(i, 'search', false))
      return
    }

    const activeNodes = []
    const inactiveNodes = []

    nodes?.forEach((i) => {
      const model = i.get('model') as ModelConfig & RouterItem
      if (model.name.includes(val)) {
        activeNodes.push(i)
      } else if (model.descriptions?.l1 || model.descriptions?.l2) {
        const containDesc = !!Object.values({
          ...(model.descriptions?.l1 ?? {}),
          ...(model.descriptions?.l2 ?? {})
        })?.filter((i) => i.includes(val))?.length
        containDesc ? activeNodes.push(i) : inactiveNodes.push(i)
      } else {
        inactiveNodes.push(i)
      }
    })
    activeNodes?.map((i) => graphRef.value.setItemState(i, 'search', true))
    inactiveNodes?.map((i) => graphRef.value.setItemState(i, 'search', false))
    activeNodesCount.value = activeNodes.length
  }

  const initGraph = () => {
    const minimap = new G6.Minimap({
      size: [150, 100],
      type: 'delegate'
    })
    const graph = new G6.TreeGraph({
      container: CONTAINER_ID,
      modes: {
        default: ['zoom-canvas', { type: 'drag-canvas', allowDragOnItem: true }]
      },
      fitView: true,
      animate: false,
      defaultNode: {
        type: NodeName
      },
      defaultEdge: {
        type: EdgeName
      },
      layout: {
        type: 'compactBox',
        direction: 'LR',
        getHeight: function getHeight(d) {
          return 16
        },
        getWidth: function getWidth() {
          return 16
        },
        getVGap: function getVGap(d) {
          return 60
        },
        getHGap: function getHGap() {
          return 140
        }
      },
      maxZoom: 2,
      minZoom: 0.2,
      plugins: [minimap]
    })
    graph.data(props.routeData)
    // graph.data(mockData1)
    graph.render()
    graphRef.value = graph
    onZoomChange(graph, 1)
    bindEvent(graph, { message: proxy.$Message, openTableInModal, openYamlWindow })
    // 关闭局部渲染，解决过滤重绘时的杂乱线条  https://github.com/antvis/G6/issues/2848
    graph.get('canvas').set('localRefresh', false)
  }
  onMounted(() => {
    props.routeData && initGraph()
  })

  watch(
    () => props.routeData,
    () => {
      if (props.routeData) {
        routeData.value = props.routeData
        if (graphRef.value) {
          graphRef.value.updateChild(props.routeData)
        } else {
          initGraph()
        }
      }
    }
  )

  return {
    modal,
    onSearch,
    onOpenYamlFromTable,
    showType,
    onShowTypeChange,
    routeData,
    activeNodesCount
  }
}
