import { ref, onMounted, getCurrentInstance, watch, computed } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { SideCar } from './type'
import { EnumFormStatus, FormSubmitParams, ResourceEntity } from '@/components/resource-form'
import { EnumArrayObjectModalStatus } from '@/components/pro-form'
import useSingleK8SService from '@/libs/useSingleK8SService'

export const SERVICE_ENTRY = { resource: 'sidecars', group: 'networking.istio.io', version: 'v1beta1' }

export default function useServiceEntryService() {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const viewDetailVisible = ref(false)
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const formInitData = computed(() => ({
    name: undefined,
    namespace: K8SInstance.value?.namespace,
    egress: {
      data: []
    },
    workloadSelector: {
      data: []
    }
  }))

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...SERVICE_ENTRY
    }
    yamlInitData.value = `apiVersion: networking.istio.io/v1beta1 
      kind: SideCar
      metadata:
        name: 必须修改
        namespace: ${K8SInstance.value.namespace}
      spec:
    `
  }

  const onDelete = (record: SideCar) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteSideCar}`, {
          params: {
            ...K8SInstance.value,
            resourceName: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onEdit = (record: SideCar) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
  }

  const onViewYaml = (record: SideCar) => {
    yamlHistoryParams.value = {
      ...K8SInstance.value,
      kind: 'Sidecar',
      uuid: record.uid
    }
    formEntity.value = {
      ...K8SInstance.value,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: SideCar) => {
    console.log('onViewDetail')
    viewDetailVisible.value = true
    formEntity.value = {
      ...K8SInstance.value,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
  }

  const onInitDetailFormat = (data) => {
    const labels = Object.entries(data?.workloadSelector?.labels || {})?.map(([key, value]) => ({ key, value }))

    return {
      ...data,
      labels: Object.entries(data?.labels || {})?.map(([key, value]) => ({ key, value })),
      workloadSelector: data?.workloadSelector?.pods?.map((pod) => ({
        pod,
        labels
      }))
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<SideCar[]>>(
      `${Config.Api.Base}${Config.Api.GetSideCarTableData}?search=${params.searchValue ?? ''}&clusterId=${
        K8SInstance.value.clusterId
      }&namespace=${K8SInstance.value.namespace}&page=${params.pageIndex}&size=${params.pageSize}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onEgressPortChange = (data, key, val) => {
    if (!data.port?.data) {
      data.port = Object.assign({}, data.port, { data: {} })
    }

    data.port.data[key] = val
  }

  const onEgressHostsChange = (record, index, val) => {
    if (!record.hosts?.length) {
      record.hosts = []
    }

    record.hosts.splice(index, 1, val)
  }

  const onWorkloadSelectorKeyChange = (data, key, index, val) => {
    if (!data.workloadSelector?.data?.length) {
      data.workloadSelector.data = []
    }
    if (!data.workloadSelector.data?.[index]) {
      data.workloadSelector.data[index] = {}
    }
    data.workloadSelector.data[index][key] = val
  }

  const onSubmitEgressForm = (data, type, record, index) => {
    if (data.egress?.data?.length) {
      data.egress?.data?.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.egress.data = [record]
    }
  }

  const onInitFormat = (data) => {
    const workloadSelector = []
    if (data.workloadSelector.data) {
      for (const [key, value] of Object.entries(data.workloadSelector.data)) {
        workloadSelector.push({ key, value })
      }
    }
    data.workloadSelector.data = workloadSelector

    console.log('onInitFormat', data)
    return data
  }
  const onSubmitFormat = (params: FormSubmitParams) => {
    const workloadSelector = {}
    params.data.workloadSelector?.data?.forEach((i) => {
      workloadSelector[i.key] = i.value
    })

    params.data.workloadSelector.data = workloadSelector
    return params
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const initK8SInstance = () => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    store.commit('getCurrentNamespace', store.state.user.userId)
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId
    }
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  return {
    getTableData,
    refObject,
    onCreate,
    onDelete,
    onEdit,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    formInitData,
    onEgressPortChange,
    onInitFormat,
    onWorkloadSelectorKeyChange,
    onSubmitFormat,
    onEgressHostsChange,
    onSubmitEgressForm,
    onSubmitSuccess,
    onViewDetail,
    viewDetailVisible,
    onInitDetailFormat
  }
}
