import type { AxiosRequestConfig, AxiosResponse } from 'axios'

type EnumToUnion<T extends string> = `${T}`

/** 错误展示形式 */
export enum ErrorShowType {
  SILENT = 'SILENT', // 不提示错误
  WARN_MESSAGE = 'WARN_MESSAGE', // 警告信息提示,message warn弹出错误信息
  ERROR_MESSAGE = 'ERROR_MESSAGE', // 错误信息提示: message error ,记录日志，弹出错误信息及日志ID
  NOTIFICATION = 'NOTIFICATION' // 通知提示 notification 记录日志，弹出错误信息、日志信息及反馈服务器按钮
}

export interface RequestConfig<T = any> extends AxiosRequestConfig<T> {
  /** 跳过全屏loading */
  skipLoading?: boolean
  /** 跳过默认的错误处理，用于项目中部分特殊的接口 */
  skipErrorHandler?: boolean
  /** 错误提示类型 */
  errorShowType?: EnumToUnion<ErrorShowType>
  /** 接口错误信息path */
  errorMsgPath?: string
  /** 是否启用本地日志功能 */
  enableLog?: boolean
  /** 是否返回原生响应头,不进行格式化处理 */
  isReturnNativeResponse?: boolean
  vue?: Vue
}
/** 后端返参类型 */
export interface PageList<T> {
  list: T
  page: number
  size: number
  total: number
}

/** 后端返参类型 */
export interface Response<T> {
  code: number
  message?: string
  data?: T
}

/** 请求响应类型，基于后端返参扩展 */
export type UseAxiosResponse<T> = Response<T> & {
  success: boolean
  /** http状态码 */
  status?: number
  /** 原始response */
  response?: AxiosResponse<Response<T>>
  /** 按标准处理的错误信息 */
  errorMsg?: string
}

/** 请求错误类型，基于基本错误类型扩展 */
export interface RequestError<T = any> extends Error {
  // code?: string;
  response: AxiosResponse<Response<T>>
  /** 按标准处理的错误信息 */
  errorMsg: string

  /** 请求配置 */
  requestConfig: RequestConfig
}
