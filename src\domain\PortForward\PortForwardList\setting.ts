import { EnumComponentType } from '../type'
import dayjs from 'dayjs'
import { color } from '@/libs/consts'

export const getColumns = (type: EnumComponentType) => [
  ...(type === EnumComponentType.Independent
    ? [
        {
          title: '集群',
          key: 'clusterName',
          tooltip: true,
          tooltipTheme: 'light',

          width: '180'
        },
        {
          title: '命名空间',
          key: 'namespace',
          tooltip: true,
          tooltipTheme: 'light',
          width: '160'
        }
      ]
    : []),
  {
    title: '名称',
    key: 'name',
    tooltip: true,
    tooltipTheme: 'light',
    width: '160'
  },
  ...(type === EnumComponentType.Independent
    ? [
        {
          title: 'Pod',
          key: 'pod',
          tooltip: true,
          tooltipTheme: 'light'
        }
      ]
    : []),
  {
    title: '目标端口',
    key: 'targetPort',
    tooltip: true,
    width: '80',
    tooltipTheme: 'light'
  },
  {
    title: '代理端口',
    key: 'proxyPort',
    tooltip: true,
    width: '80',
    tooltipTheme: 'light'
  },
  {
    title: '代理地址',
    key: 'proxyAddress',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: '健康状态',
    key: 'status',
    tooltip: true,
    tooltipTheme: 'light',

    width: '80',
    render: (h, params) => {
      return h(
        'div',
        {
          style: {
            color: params.row.status === 'healthy' ? color.success : color.error
          }
        },
        params.row.status ? params.row.status : '-'
      )
    }
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 160,
    render: (h, params) => {
      return h('div', params.row.updatedAt ? dayjs(params.row.updatedAt).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  },
  {
    title: '最后修改人',
    key: 'editor',
    width: 120,
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 100,
    align: 'center'
  }
]
