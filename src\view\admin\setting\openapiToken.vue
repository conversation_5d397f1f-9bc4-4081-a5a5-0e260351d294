<template>
  <Container>
    <Alert show-icon> OpenApi Token 用于生成 app 访问本服务需要的验证信息。</Alert>
    <Card :bordered="false">
      <div style="margin-bottom: 16px">
        <Button
          s
          size="small"
          type="primary"
          icon="md-add"
          @click="
            () => {
              openModal = true
            }
          "
          >创建 token</Button
        >
        <Button s size="small" icon="ios-refresh" style="margin-left: 16px" @click="reloadTable">刷新</Button>
      </div>
      <Table :loading="loading.table" size="small" :data="tableData" :columns="tableCols"></Table>
    </Card>
    <Modal v-model="openModal" title="添加 OpenApi Token" @on-ok="handleSubmit">
      <Form :model="tokenForm" ref="tokenForm">
        <FormItem label="App Name">
          <Input v-model="tokenForm.name" placeholder="App 名"></Input>
        </FormItem>
        <FormItem label="Description">
          <Input v-model="tokenForm.desc" placeholder="描述"></Input>
        </FormItem>
      </Form>
    </Modal>
  </Container>
</template>

<script>
import { Container } from '@/components'
import { ApiOpenAuthTokenCreate, ApiOpenAuthTokenDelete, ApiOpenAuthTokenList } from '@/api/k8s/openapi'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { color } from '@/libs/consts'

export default {
  name: 'openapiToken',
  components: { Container },
  data() {
    return {
      mTitle: 'OpenApi Token',
      openModal: false,
      tokenForm: {
        name: null,
        desc: null
      },
      loading: {
        table: false
      },
      tableData: [],
      tableCols: [
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          width: 200
        },
        {
          title: 'Description',
          key: 'desc',
          tooltip: true,
          width: 300
        },
        {
          title: 'Token',
          key: 'token',
          tooltip: true,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'Tooltip',
                {
                  props: {
                    content: 'Copy',
                    placement: 'left-end'
                  }
                },
                [
                  h('Icon', {
                    props: {
                      type: 'md-copy'
                    },
                    style: {
                      fontSize: '18px',
                      color: color.primary,
                      cursor: 'pointer'
                    },
                    on: {
                      click: () => {
                        var cInput = document.createElement('input')
                        cInput.value = params.row.token
                        document.body.appendChild(cInput)
                        cInput.select()
                        document.execCommand('copy')
                        noticeSucceed(this, 'Copy succeed !!')
                        document.body.removeChild(cInput)
                      }
                    }
                  })
                ]
              ),
              h(
                'span',
                {
                  style: {
                    marginLeft: '8px',
                    position: 'relative',
                    top: '8px',
                    fontSize: '16px'
                  }
                },
                '*'.repeat(50)
              )
            ])
          }
        },
        {
          title: 'CreatedAt',
          key: 'created_at',
          tooltip: true,
          width: 180
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 100,
          render: (h, params) => {
            return h(
              'b',
              {
                style: {
                  fontSize: '12px',
                  color: color.error,
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: `Tips`,
                      content: `<p>是否删除 ${params.row.name} 确认操作 ?</p>`,
                      loading: true,
                      onOk: async () => {
                        await ApiOpenAuthTokenDelete(params.row.id)
                          .then((res) => {
                            noticeSucceed(this, 'Delete succeed.')
                            this.reloadTable()
                          })
                          .catch((err) => {
                            noticeError(this, errorMessage(err))
                          })
                        this.$Modal.remove()
                      }
                    })
                  }
                }
              },
              '删除'
            )
          }
        }
      ]
    }
  },
  methods: {
    async handleSubmit() {
      await ApiOpenAuthTokenCreate(this.tokenForm.name, this.tokenForm.desc)
        .then((res) => {
          noticeSucceed(this, 'Submit succeed !!')
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      this.tokenForm = {
        name: null,
        desc: null
      }
    },
    async reloadTable() {
      this.loading.table = true
      this.tableData = []
      await ApiOpenAuthTokenList()
        .then((res) => {
          if (res.data.data.list !== null) {
            this.tableData = res.data.data.list
          }
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          this.loading.table = false
        })
    }
  },
  mounted() {
    this.reloadTable()
  }
}
</script>

<style scoped></style>
