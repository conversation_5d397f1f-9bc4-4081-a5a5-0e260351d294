import axios from '@/libs/api.request'

export const ApiResourceEventChannelList = () => {
  return axios.request({
    url: '/api/v1/resource/event/channel/list',
    method: 'get',
    params: {}
  })
}

export const ApiResourceYamlGet = (params) => {
  return axios.request({
    url: '/api/v2/resource/yaml/get',
    method: 'get',
    params
  })
}

export const ApiResourceOpRecordList = (params) => {
  return axios.request({
    url: '/api/v1/k8s-op-record/resource/list',
    method: 'get',
    params: params
  })
}
