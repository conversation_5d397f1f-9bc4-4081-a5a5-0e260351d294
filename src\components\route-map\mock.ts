import { RouterItem, TableRequestResponseData } from './type'

export const mockData = {
  id: 'g1',
  name: 'Name1',
  count: 123456,
  label: '538.90',
  currency: 'Yuan',
  rate: 1.0,
  status: 'B',
  variableName: 'V1',
  variableValue: 0.341,
  variableUp: false,
  children: [
    {
      id: 'g12',
      name: 'Deal with LONG label LONG label LONG label LONG label',
      count: 123456,
      label: '338.00',
      rate: 0.627,
      status: 'R',
      currency: 'Yuan',
      variableName: 'V2',
      variableValue: 0.179,
      variableUp: true,
      children: [
        {
          id: 'g121',
          name: 'Name3',
          collapsed: true,
          count: 123456,
          label: '138.00',
          rate: 0.123,
          status: 'B',
          currency: 'Yuan',
          variableName: 'V2',
          variableValue: 0.27,
          variableUp: true,
          children: [
            {
              id: 'g1211',
              name: 'Name4',
              count: 123456,
              label: '138.00',
              rate: 1.0,
              status: 'B',
              currency: 'Yuan',
              variableName: 'V1',
              variableValue: 0.164,
              variableUp: false,
              children: []
            }
          ]
        },
        {
          id: 'g122',
          name: 'Name5',
          collapsed: true,
          count: 123456,
          label: '100.00',
          rate: 0.296,
          status: 'G',
          currency: 'Yuan',
          variableName: 'V1',
          variableValue: 0.259,
          variableUp: true,
          children: [
            {
              id: 'g1221',
              name: 'Name6',
              count: 123456,
              label: '40.00',
              rate: 0.4,
              status: 'G',
              currency: 'Yuan',
              variableName: 'V1',
              variableValue: 0.135,
              variableUp: true,
              children: [
                {
                  id: 'g12211',
                  name: 'Name6-1',
                  count: 123456,
                  label: '40.00',
                  rate: 1.0,
                  status: 'R',
                  currency: 'Yuan',
                  variableName: 'V1',
                  variableValue: 0.181,
                  variableUp: true,
                  children: []
                }
              ]
            },
            {
              id: 'g1212122',
              name: 'Name7',
              count: 123456,
              label: '60.00',
              rate: 0.6,
              status: 'G',
              currency: 'Yuan',
              variableName: 'V1',
              variableValue: 0.239,
              variableUp: false,
              children: []
            }
          ]
        },
        {
          id: '11122',
          name: 'Name15',
          collapsed: true,
          count: 123456,
          label: '100.00',
          rate: 0.296,
          status: 'G',
          currency: 'Yuan',
          variableName: 'V1',
          variableValue: 0.259,
          variableUp: true,
          children: [
            {
              id: '222222',
              name: 'Name16',
              count: 123456,
              label: '40.00',
              rate: 0.4,
              status: 'G',
              currency: 'Yuan',
              variableName: 'V1',
              variableValue: 0.135,
              variableUp: true,
              children: [
                {
                  id: 'g1212123122121',
                  name: 'Name16-1',
                  count: 123456,
                  label: '40.00',
                  rate: 1.0,
                  status: 'R',
                  currency: 'Yuan',
                  variableName: 'V1',
                  variableValue: 0.181,
                  variableUp: true,
                  children: []
                }
              ]
            },
            {
              id: 'g1222222',
              name: 'Name7',
              count: 123456,
              label: '60.00',
              rate: 0.6,
              status: 'G',
              currency: 'Yuan',
              variableName: 'V1',
              variableValue: 0.239,
              variableUp: false,
              children: []
            }
          ]
        },
        {
          id: 'g123',
          name: 'Name8',
          collapsed: true,
          count: 123456,
          label: '100.00',
          rate: 0.296,
          status: 'DI',
          currency: 'Yuan',
          variableName: 'V2',
          variableValue: 0.131,
          variableUp: false,
          children: [
            {
              id: 'g1231',
              name: 'Name8-1',
              count: 123456,
              label: '100.00',
              rate: 1.0,
              status: 'DI',
              currency: 'Yuan',
              variableName: 'V2',
              variableValue: 0.131,
              variableUp: false,
              children: []
            }
          ]
        }
      ]
    },
    {
      id: 'g13',
      name: 'Name9',
      count: 123456,
      label: '100.90',
      rate: 0.187,
      status: 'B',
      currency: 'Yuan',
      variableName: 'V2',
      variableValue: 0.221,
      variableUp: true,
      children: [
        {
          id: 'g131',
          name: 'Name10',
          count: 123456,
          label: '33.90',
          rate: 0.336,
          status: 'R',
          currency: 'Yuan',
          variableName: 'V1',
          variableValue: 0.12,
          variableUp: true,
          children: []
        },
        {
          id: 'g132',
          name: 'Name11',
          count: 123456,
          label: '67.00',
          rate: 0.664,
          status: 'G',
          currency: 'Yuan',
          variableName: 'V1',
          variableValue: 0.241,
          variableUp: false,
          children: []
        }
      ]
    },
    {
      id: 'g14',
      name: 'Name12',
      count: 123456,
      label: '100.00',
      rate: 0.186,
      status: 'G',
      currency: 'Yuan',
      variableName: 'V2',
      variableValue: 0.531,
      variableUp: true,
      children: []
    }
  ]
}

export const mockData1: RouterItem = {
  nodeType: 'Domain',
  name: 'a.constack.ttyuyin.com',
  clusterId: 1111,
  clusterName: '111',
  namespace: 'xxxx',
  gvr: {
    group: '',
    version: '',
    resource: ''
  },
  descriptions: {
    l1: null,
    l2: null
  },
  operations: {},
  state: null,
  children: [
    {
      nodeType: 'Gateway',
      gvr: {
        resource: 'deployments',
        group: 'apps',
        version: 'v1'
      },
      name: 'istio-ingressgateway-zt-net-public',
      clusterId: 122,
      clusterName: '111',
      namespace: 'istio-ingress',
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          TLS: 'ttyuyin-com-2024/SAMPLE '
        },
        l2: {
          IP: '************ ***********'
        }
      },
      state: {
        status: 'warning', // succeed, warning, error, none, none 则不展示状态
        message: 'a.constack.ttyuyin.com:443 在 ingressgateway-xxx 配置上存在相同配置'
      },

      operations: {
        link: true,
        view: '', // 如果是空对象则请求 yaml, 否则直接展示json对象
        table: {
          url: '',
          args: {}
        }
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'guild-delegator-80',
          clusterId: 123,
          clusterName: '111',
          namespace: 'xxxx',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          operations: {
            link: true,
            view: ''
          },
          state: {
            status: 'succeed', // succeed, warning, error
            message: 'xxxxxx'
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: 'Http.Route',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: []
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route22',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              }
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route222',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local',
                  Route2: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75',
                  Route6: 'a-contack.svc.clusterName.local'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'busybox-xxx-test-configmap-secret',
                  clusterId: 123,
                  clusterName: '111',
                  namespace: 'xxxx',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: {
                      Port: 'HTTP:80 HTTPS:443'
                    },
                    l2: {
                      Opts: '80 | v2 | 75'
                    }
                  },
                  state: {
                    status: 'warning', // succeed, warning, error
                    message: 'xxxx'
                  },
                  operations: {
                    link: true,
                    view: ''
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'web-workload-test',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'busybox-json',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: '',
                            link: true
                          },
                          children: []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      nodeType: 'Gateway',
      gvr: {
        resource: 'deployments',
        group: 'apps',
        version: 'v1'
      },
      name: 'istio-ingressgateway-zt-net-public',
      clusterId: 122,
      clusterName: '111',
      namespace: 'istio-ingress',
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          TLS: 'ttyuyin-com-2024/SAMPLE '
        },
        l2: {
          IP: '************ ***********'
        }
      },
      state: {
        status: 'warning', // succeed, warning, error, none, none 则不展示状态
        message: 'a.constack.ttyuyin.com:443 在 ingressgateway-xxx 配置上存在相同配置'
      },

      operations: {
        link: true,
        view: '', // 如果是空对象则请求 yaml, 否则直接展示json对象
        table: {
          url: '',
          args: {}
        }
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'guild-delegator-80',
          clusterId: 123,
          clusterName: '111',
          namespace: 'xxxx',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          operations: {
            link: true,
            view: ''
          },
          state: {
            status: 'succeed', // succeed, warning, error
            message: 'xxxxxx'
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: 'Http.Route',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: []
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route22',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              }
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route222',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local',
                  Route2: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75',
                  Route6: 'a-contack.svc.clusterName.local'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'busybox-xxx-test-configmap-secret',
                  clusterId: 123,
                  clusterName: '111',
                  namespace: 'xxxx',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: {
                      Port: 'HTTP:80 HTTPS:443'
                    },
                    l2: {
                      Opts: '80 | v2 | 75'
                    }
                  },
                  state: {
                    status: 'warning', // succeed, warning, error
                    message: 'xxxx'
                  },
                  operations: {
                    link: true,
                    view: ''
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'web-workload-test',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'busybox-json',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: '',
                            link: true
                          },
                          children: []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      nodeType: 'Gateway',
      gvr: {
        resource: 'deployments',
        group: 'apps',
        version: 'v1'
      },
      name: 'istio-ingressgateway-zt-net-public',
      clusterId: 122,
      clusterName: '111',
      namespace: 'istio-ingress',
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          TLS: 'ttyuyin-com-2024/SAMPLE '
        },
        l2: {
          IP: '************ ***********'
        }
      },
      state: {
        status: 'warning', // succeed, warning, error, none, none 则不展示状态
        message: 'a.constack.ttyuyin.com:443 在 ingressgateway-xxx 配置上存在相同配置'
      },

      operations: {
        link: true,
        view: '', // 如果是空对象则请求 yaml, 否则直接展示json对象
        table: {
          url: '',
          args: {}
        }
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'guild-delegator-80',
          clusterId: 123,
          clusterName: '111',
          namespace: 'xxxx',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          operations: {
            link: true,
            view: ''
          },
          state: {
            status: 'succeed', // succeed, warning, error
            message: 'xxxxxx'
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: 'Http.Route',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: []
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route22',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              }
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route222',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local',
                  Route2: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75',
                  Route6: 'a-contack.svc.clusterName.local'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'busybox-xxx-test-configmap-secret',
                  clusterId: 123,
                  clusterName: '111',
                  namespace: 'xxxx',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: {
                      Port: 'HTTP:80 HTTPS:443'
                    },
                    l2: {
                      Opts: '80 | v2 | 75'
                    }
                  },
                  state: {
                    status: 'warning', // succeed, warning, error
                    message: 'xxxx'
                  },
                  operations: {
                    link: true,
                    view: ''
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'web-workload-test',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'busybox-json',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: '',
                            link: true
                          },
                          children: []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      nodeType: 'Gateway',
      gvr: {
        resource: 'deployments',
        group: 'apps',
        version: 'v1'
      },
      name: 'istio-ingressgateway-zt-net-public',
      clusterId: 122,
      clusterName: '111',
      namespace: 'istio-ingress',
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          TLS: 'ttyuyin-com-2024/SAMPLE '
        },
        l2: {
          IP: '************ ***********'
        }
      },
      state: {
        status: 'warning', // succeed, warning, error, none, none 则不展示状态
        message: 'a.constack.ttyuyin.com:443 在 ingressgateway-xxx 配置上存在相同配置'
      },

      operations: {
        link: true,
        view: '', // 如果是空对象则请求 yaml, 否则直接展示json对象
        table: {
          url: '',
          args: {}
        }
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'guild-delegator-80',
          clusterId: 123,
          clusterName: '111',
          namespace: 'xxxx',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          operations: {
            link: true,
            view: ''
          },
          state: {
            status: 'succeed', // succeed, warning, error
            message: 'xxxxxx'
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: 'Http.Route',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: []
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route22',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              }
            },
            {
              nodeType: 'Http.Route',
              name: 'Http.Route222',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local',
                  Route2: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75',
                  Route6: 'a-contack.svc.clusterName.local'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'busybox-xxx-test-configmap-secret',
                  clusterId: 123,
                  clusterName: '111',
                  namespace: 'xxxx',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: {
                      Port: 'HTTP:80 HTTPS:443'
                    },
                    l2: {
                      Opts: '80 | v2 | 75'
                    }
                  },
                  state: {
                    status: 'warning', // succeed, warning, error
                    message: 'xxxx'
                  },
                  operations: {
                    link: true,
                    view: ''
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'web-workload-test',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'busybox-json',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: '',
                            link: true
                          },
                          children: []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      nodeType: 'Gateway',
      gvr: {
        group: '',
        version: '',
        resource: ''
      },
      name: 'ingressgateway-tt-internal1',
      clusterId: 123,
      clusterName: '111',
      namespace: 'xxxx',
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443',
          TLS: 'ttyuyin-com-2024/SAMPLE '
        },
        l2: {
          IP: '************ ***********'
        }
      },
      state: {
        status: 'warning', // succeed, warning, error, none, none 则不展示状态S
        message: 'xxxxxx'
      },

      operations: {
        link: true,
        view: '' // 如果是空对象则请求 yaml, 否则直接展示json对象
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'a-constack-vs-main1',
          clusterId: 123,
          clusterName: '111',
          namespace: 'xxxx',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          operations: {
            link: true,
            view: ''
          },
          state: {
            status: 'succeed', // succeed, warning, error
            message: 'xxxxxx'
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: 'Http.Route1',
              clusterId: 123,
              clusterName: '111',
              namespace: 'xxxx',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'a-contack.svc.clusterName.local'
                },
                l2: {
                  Opts: '80 | v2 | 75'
                }
              },
              state: {
                status: 'succeed', // succeed, warning, error
                message: 'xxxxxx'
              },
              operations: {
                view: ''
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'Service1',
                  clusterId: 123,
                  clusterName: '111',
                  namespace: 'xxxx',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: {
                      Port: 'HTTP:80 HTTPS:443'
                    },
                    l2: {
                      Opts: '80 | v2 | 75'
                    }
                  },
                  state: {
                    status: 'warning', // succeed, warning, error
                    message: 'xxxx'
                  },
                  operations: {
                    link: true,
                    view: ''
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'DR',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'pod111',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: ''
                          },
                          children: []
                        }
                      ]
                    },
                    {
                      nodeType: 'DestinationRule',
                      name: 'DR2',
                      clusterId: 123,
                      clusterName: '111',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      namespace: 'xxxx',
                      descriptions: {
                        l1: {
                          Selector: 'version:v2 | app:user-auth'
                        },
                        l2: {}
                      },
                      state: {
                        status: 'error', // succeed, warning, error
                        message: 'xxxx'
                      },
                      operations: {
                        link: true,
                        view: ''
                      },
                      children: [
                        {
                          nodeType: 'Pod',
                          name: 'pod1112',
                          clusterId: 123,
                          clusterName: '111',
                          namespace: 'xxxx',
                          gvr: {
                            group: '',
                            version: '',
                            resource: ''
                          },
                          state: {
                            status: 'error', // succeed, warning, error, none
                            message: 'xxxx'
                          },
                          operations: {
                            view: ''
                          },
                          children: []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

export const mockData2: RouterItem = {
  nodeType: 'Domain',
  name: 'www.test.com',
  clusterId: 10003,
  clusterName: '111',
  namespace: '',
  gvr: {
    group: '',
    version: '',
    resource: ''
  },
  descriptions: {
    l1: null,
    l2: null
  },
  operations: {},
  state: null,
  children: [
    {
      nodeType: 'Gateway',
      name: 'ingressgateway-tt-internal',
      clusterId: 10003,
      clusterName: '111',
      namespace: 'istio-system',
      gvr: {
        group: '',
        version: '',
        resource: ''
      },
      descriptions: {
        l1: {
          Port: 'HTTP:80 HTTPS:443',
          TLS: 'ttyuyin-com-2024 / SIMPLE'
        },
        l2: {
          IP: '************ **********'
        }
      },
      operations: {},
      state: {
        status: 'warning',
        message: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxS'
      },
      children: [
        {
          nodeType: 'VirtualService',
          name: 'vs',
          clusterId: 10003,
          clusterName: '111',
          namespace: 'sample',
          gvr: {
            group: '',
            version: '',
            resource: ''
          },
          descriptions: {
            l1: null,
            l2: null
          },
          operations: {},
          state: {
            status: 'succeed',
            message: null
          },
          children: [
            {
              nodeType: 'Http.Route',
              name: '',
              clusterId: 10003,
              clusterName: '111',
              namespace: 'sample',
              gvr: {
                group: '',
                version: '',
                resource: ''
              },
              descriptions: {
                l1: {
                  Route: 'helloworld.sample.svc.clusterName.local'
                },
                l2: null
              },
              operations: {},
              state: {
                status: 'error',
                message: 'xxxxxxxxxxxxxxxxxxxxxxxx'
              },
              children: [
                {
                  nodeType: 'Service',
                  name: 'helloworld',
                  clusterId: 10003,
                  clusterName: '111',
                  namespace: 'sample',
                  gvr: {
                    group: '',
                    version: '',
                    resource: ''
                  },
                  descriptions: {
                    l1: null,
                    l2: null
                  },
                  operations: {},
                  state: {
                    status: 'succeed',
                    message: null
                  },
                  children: [
                    {
                      nodeType: 'DestinationRule',
                      name: 'dr',
                      clusterId: 10003,
                      clusterName: '111',
                      namespace: 'sample',
                      gvr: {
                        group: '',
                        version: '',
                        resource: ''
                      },
                      descriptions: {
                        l1: null,
                        l2: null
                      },
                      operations: {},
                      state: {
                        status: 'succeed',
                        message: null
                      },
                      children: null
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      nodeType: 'Gateway',
      name: 'gw-1',
      clusterId: 10003,
      clusterName: '111',
      namespace: 'default',
      gvr: {
        group: '',
        version: '',
        resource: ''
      },
      descriptions: {
        l1: null,
        l2: null
      },
      operations: {},
      state: {
        status: 'succeed',
        message: null
      },

      children: []
    }
  ]
}

export const mockTableRes: TableRequestResponseData = {
  columns: [
    {
      title: 'Name',
      key: 'name'
    },
    {
      title: 'Namespace',
      key: 'namespace'
    },
    {
      title: 'Phase',
      key: 'phase'
    },
    {
      title: 'Created',
      key: 'created'
    }
  ],
  data: [
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'usybox-5ccbd685f7-fr7ps',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    },
    {
      name: 'busybox-5ccbd685f7-rmm8s',
      clusterId: 10001,
      clusterName: '111',
      namespace: 'infra',
      gvr: {
        group: '',
        version: 'v1',
        resource: 'pods'
      },
      phase: 'Running',
      created: '0000-00-00 00:00:00'
    }
  ]
}
