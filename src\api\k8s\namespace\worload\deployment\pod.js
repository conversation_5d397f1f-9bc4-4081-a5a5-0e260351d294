import axios from '@/libs/api.request'


export const ApiDeployPodDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/pod/delete`,
    method: 'delete',
    data: data,
  })
}

export const ApiDeployPodSidecarLogLevelSet = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/pod/sidecar/log-level/set`,
    method: 'post',
    data: data,
  })
}

export const ApiPodAnalyze = (params) => {
  return axios.request({
    url: `/api/v1/resource/pod/analyze`,
    method: 'get',
    params: params,
  })
}


