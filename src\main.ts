import Vue from 'vue'
import App from './App.vue'
import iView from 'view-design'

import './index.less'
import '@/assets/icons/iconfont.css'

import zh from 'view-design/dist/locale/zh-CN'

import router from '@/router'
import store from '@/store'
import config from '@/config'
import importDirective from '@/directive'
import installPlugin from '@/plugin'
import VueMatomo from 'vue-matomo'

import { RequestConfigProvide, axiosInterceptorRequest } from './libs/service.request'
import { getToken } from './libs/util'

// 实际打包时应该不引入mock
/* eslint-disable */
if (process.env.VUE_APP_ENV !== 'production') require('@/mock')
if (process.env.VUE_APP_ENV === 'production') {
  Vue.use(VueMatomo, {
    host: 'https://yw-matomo.ttyuyin.com',
    siteId: 8
  })
} else if (process.env.VUE_APP_ENV === 'alpha') {
  Vue.use(VueMatomo, {
    host: 'https://yw-matomo.ttyuyin.com',
    siteId: 9
  })
}

axiosInterceptorRequest({
  headers: {
    Authorization: 'Bearer ' + getToken()
  }
})

Vue.use(iView, { locale: zh })

/**
 * @description 注册admin内置插件
 */
installPlugin(Vue)
/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false
/**
 * @description 全局注册应用配置
 */
Vue.prototype.$config = config
/**
 * 注册指令
 */
importDirective(Vue)

/* eslint-disable no-new */
const vue = new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})

RequestConfigProvide({
  timeout: 180000,
  skipLoading: true,
  errorShowType: 'WARN_MESSAGE',
  vue: vue,
  errorMsgPath: 'message'
})
