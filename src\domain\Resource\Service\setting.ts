import { relativeTime } from '@/libs/tools'

export const COLUMNS = [
  {
    title: 'Name',
    slot: 'name',
    key: 'name',
    tooltip: true,
    minWidth: 150,
    maxWidth: 240,
    tooltipTheme: 'light'
  },
  {
    title: 'Type',
    key: 'type',
    tooltip: true,
    tooltipTheme: 'light',
    width: 120
  },
  {
    title: 'ClusterIP',
    key: 'cluster_ip',
    tooltip: true,
    tooltipTheme: 'light',
    width: 150
  },
  {
    title: 'LoadBalancer IP',
    key: 'lb_ip',
    tooltip: true,
    tooltipTheme: 'light',
    width: 150,
    align: 'center'
  },
  {
    title: 'Port(s)',
    slot: 'ports',
    tooltipTheme: 'light',
    minWidth: 200
  },
  {
    title: 'Age',
    key: 'created',
    width: 160,

    align: 'center',
    render: (h, params) => {
      return h('div', relativeTime(params.row.created))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 160,
    align: 'center'
  }
]

export const POD_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: 'Owner',
    key: 'owner',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: 'CreateTime',
    key: 'created',
    tooltip: true,
    width: 200
  }
]

export const PORT_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'ContainerPort',
    key: 'containerPort',
    width: 150,
    align: 'center',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: 'HostPort',
    key: 'hostPort',
    width: 140,
    align: 'center',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: 'Protocol',
    key: 'protocol',
    tooltipTheme: 'light',
    tooltip: true,
    align: 'center',
    width: 120
  }
]
