import { ExtractPropTypes, PropType } from 'vue'

export function detailCardProps() {
  return {
    title: String, // 标题
    data: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    }, // 数据对象
    config: {
      type: Array as PropType<ConfigType[]>,
      default: () => ([])
    } // 数据配置
  }
}

export interface ConfigType {
  title: string // 标签名
  key: string // 属性名
  slot: string // 插槽
  render: Function // render函数
}

export type DetailCardProps = Partial<ExtractPropTypes<ReturnType<typeof detailCardProps>>>