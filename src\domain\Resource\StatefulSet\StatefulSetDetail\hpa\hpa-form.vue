<template>
  <div>
    <Alert show-icon> 如果存在不支持的字段， 则无法使用【表单模式】进行编辑； </Alert>
    <Tabs type="card" v-model="currentMode" @on-click="handleModeChange">
      <TabPane label="表单模式" name="formMode" :disabled="disableFormMode">
        <div style="height: 76vh; overflow: auto">
          <div style="padding-bottom: 25px">
            <Alert type="info"> 若不提供任何【触发策略】和【定时伸缩策略】则默认创建 cpu >= 80% 的触发策略 </Alert>
            <Card>
              <Form :model="editForm" label-position="top">
                <FormItem label="名称: (自动生成)">
                  <Tag color="primary">{{ editForm.name }}</Tag>
                </FormItem>
                <template v-if="!isWorkloadMode">
                  <FormItem label="工作负载类型:">
                    <Select
                      :disabled="disableWorkloadSelected"
                      v-model="editForm.scaleTargetRef.kind"
                      @on-change="fetchWorkloadList"
                      style="width: 300px"
                    >
                      <Option value="Deployment">Deployment (无状态服务)</Option>
                      <Option value="StatefulSet">StatefulSet (有状态服务)</Option>
                    </Select>
                  </FormItem>
                  <FormItem label="关联工作负载:">
                    <Select
                      :disabled="disableWorkloadSelected"
                      v-model="editForm.scaleTargetRef.name"
                      @on-change="handleWorkloadNameChange"
                      style="width: 300px"
                    >
                      <Option v-for="(item, index) in workLoadNameList" :key="index" :value="item">{{ item }}</Option>
                    </Select>
                  </FormItem>
                </template>

                <FormItem label="触发策略: ">
                  <div slot="label" style="display: flex; justify-content: space-between; flex: 1">
                    <div>触发策略:</div>
                    <div>
                      <Poptip placement="left" trigger="hover">
                        <div slot="content">
                          <p style="text-align: left">描述:</p>
                          <p style="text-align: left; padding-left: 8px">可以根据策略指标、策略类型来设定扩容阈值;</p>
                          <p style="text-align: left">示例:</p>
                          <p style="text-align: left; padding-left: 8px">
                            1. [cpu | Utilization | 90], 当Pod的cpu平均使用率到90%时触发扩容
                          </p>
                          <p style="text-align: left; padding-left: 8px">
                            2. [mem | AverageValue | 100Mi] 当Pod的内存使用量达到100Mi时触发扩容
                          </p>
                        </div>
                        <Icon type="ios-alert" style="font-size: 20px; color: orange; margin-right: 16px" />
                      </Poptip>
                      <Tooltip content="官方文档" placement="top">
                        <a
                          target="_blank"
                          href="https://kubernetes.io/zh-cn/docs/tasks/run-application/horizontal-pod-autoscale/#support-for-resource-metrics"
                        >
                          <Icon
                            type="md-open"
                            style="cursor: pointer; font-size: 20px; color: #2a7cc3; margin-right: 16px"
                          >
                          </Icon>
                        </a>
                      </Tooltip>
                    </div>
                  </div>
                  <div style="background-color: #eaebef; width: 500px">
                    <template v-for="(item, idx) in editForm.resourcePolicy">
                      <div style="padding: 16px 16px 0 16px" :key="idx">
                        <Select placeholder="策略指标" style="width: 100px" v-model="item.name">
                          <Option value="cpu">CPU</Option>
                          <Option value="memory">内存</Option>
                        </Select>
                        <Select placeholder="策略类型" style="width: 180px; margin-left: 16px" v-model="item.type">
                          <Option value="Utilization">利用率 (Utilization)</Option>
                          <Option value="AverageValue">平均值 (AverageValue)</Option>
                        </Select>
                        <template>
                          <div style="display: inline-block" v-if="item.name === 'cpu' && item.type === 'AverageValue'">
                            <Input placeholder="1000" style="width: 100px; margin-left: 16px" v-model="item.value" />m
                          </div>
                          <div
                            style="display: inline-block"
                            v-else-if="item.name === 'memory' && item.type === 'AverageValue'"
                          >
                            <Input placeholder="1024" style="width: 100px; margin-left: 16px" v-model="item.value" />Mi
                          </div>
                          <div style="display: inline-block" v-else>
                            <Input placeholder="90" style="width: 100px; margin-left: 16px" v-model="item.value" />%
                          </div>
                        </template>
                        <a style="color: #ed4014; cursor: pointer; margin-left: 16px" @click="deleteResourcePolicy(idx)"
                          >删除</a
                        >
                      </div>
                    </template>
                    <div style="padding: 16px">
                      <b @click="addResourcePolicy" style="color: #2a7cc3; cursor: pointer">新增指标</b>
                    </div>
                  </div>
                </FormItem>
                <FormItem label="实例范围: ">
                  <div>
                    <InputNumber style="width: 100px" :min="1" v-model="editForm.minPods"></InputNumber>
                    ~
                    <InputNumber style="width: 100px" :min="1" v-model="editForm.maxPods"></InputNumber>
                  </div>
                </FormItem>
                <FormItem label="定时伸缩">
                  <div slot="label" style="display: flex; justify-content: space-between; flex: 1">
                    <div>
                      <span>定时伸缩策略:</span>
                      <Poptip
                        trigger="hover"
                        v-if="disableKeda"
                        placement="right"
                        content="该集群不支持多维度HPA, 请联系【登强】"
                      >
                        <Checkbox style="margin-left: 20px" disabled> 启用 </Checkbox>
                      </Poptip>
                      <Checkbox
                        @on-change="
                          (v) => {
                            this.cronPolicySwitch = v
                          }
                        "
                        v-else
                        style="margin-left: 20px"
                        v-model="editForm.cornPolicy.switchCtl"
                        >启用
                        <span style="color: #808695; font-size: 7px"
                          >(启用后HPA名字会替换成 keda-hpa-{原HPA名字})</span
                        ></Checkbox
                      >
                    </div>

                    <div>
                      <Poptip placement="left" trigger="hover">
                        <div slot="content">
                          <p style="text-align: left">描述:</p>
                          <p style="text-align: left; padding-left: 8px">
                            当时间窗口开始时，它将根据配置从最小副本数扩展到期望副本数直到结束时间;
                          </p>
                          <p style="text-align: left">字段:</p>
                          <p style="text-align: left; padding-left: 8px">1. 时区: 定时伸缩时间所用的时区;</p>
                          <p style="text-align: left; padding-left: 8px">
                            2. 开始时间/结束时间: 该字段遵循corn表达式语法(分 时 日 月 周几);
                          </p>
                          <p style="text-align: left">示例:</p>
                          <p style="text-align: left; padding-left: 8px">
                            开始时间: 30 17 * * * 结束时间: 59 23 * * * ,
                            <br />在每天的17.30将副本数扩容到期望副本数,23.59时恢复到最小副本数
                          </p>
                        </div>
                        <Icon type="ios-alert" style="font-size: 20px; color: orange; margin-right: 16px" />
                      </Poptip>
                      <Tooltip content="官方文档" placement="top">
                        <a target="_blank" href="https://keda.sh/docs/2.10/scalers/cron/">
                          <Icon
                            type="md-open"
                            style="cursor: pointer; font-size: 20px; color: #2a7cc3; margin-right: 16px"
                          >
                          </Icon>
                        </a>
                      </Tooltip>
                    </div>
                  </div>

                  <div v-if="editForm.cornPolicy.switchCtl">
                    <Card>
                      <b @click="addCronPolicy" style="color: #2a7cc3; cursor: pointer">添加定时条件</b>
                      <template v-for="(item, idx) in editForm.cornPolicy.data">
                        <div
                          :key="idx"
                          style="background-color: #eaebef; width: 510px; padding-bottom: 16px; margin-bottom: 16px"
                        >
                          <div style="height: 16px">
                            <a
                              @click="deleteCronPolicy(idx)"
                              style="color: #ed4014; cursor: pointer; float: right; margin-right: 16px"
                              >删除</a
                            >
                          </div>
                          <div style="padding: 16px 16px 0 16px">
                            <div style="display: inline-block; margin-right: 16px">
                              <span style="margin-right: 20px">副本数: </span>
                              <Input v-model="item.desiredReplicas" style="width: 150px" placeholder="10"></Input>
                            </div>
                            <div style="display: inline-block">
                              <span style="margin-right: 32px">时区: </span>
                              <Select
                                v-model="item.timeZone"
                                filterable
                                placeholder="Asia/Shanghai"
                                style="width: 150px"
                              >
                                <Option v-for="(tz, idx) in timezoneList" :key="idx" :value="tz"> {{ tz }} </Option>
                              </Select>
                            </div>
                          </div>
                          <div style="padding: 16px 16px 0 16px">
                            <div style="display: inline-block; margin-right: 16px">
                              <span style="margin-right: 8px">开始时间: </span>
                              <Input v-model="item.start" style="width: 150px" placeholder="* * * * *"></Input>
                            </div>
                            <div style="display: inline-block; margin-right: 16px">
                              <span style="margin-right: 8px">结束时间: </span>
                              <Input v-model="item.end" style="width: 150px" placeholder="* * * * *"></Input>
                            </div>
                          </div>
                        </div>
                      </template>
                    </Card>
                  </div>
                </FormItem>
              </Form>
            </Card>
          </div>
        </div>
      </TabPane>
      <TabPane label="YAML模式" name="yamlMode">
        <div style="overflow: auto; height: 76vh">
          <yaml style="overflow: auto; padding-bottom: 25px" v-model="editYaml" ref="refYaml" />
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { Yaml } from '@/components'
import {
  ApiHpaFormGet,
  ApiHpaWorkloadNameList,
  ApiHpaFormCanConvert,
  ApiHpaTimezoneList,
  ApiHpaKedaIsSupport
} from '@/api/k8s/namespace/hpa'
import { ApiResourceYamlGet } from '@/api/k8s/resource'
import { errorMessage, yaml2json, noticeError } from '@/libs/util'

export default {
  name: 'hpa-form',
  components: {
    Yaml
  },
  props: {
    createOrUpdate: String,
    clusterId: String,
    namespace: String,
    name: String,
    kind: String,
    yamlName: String,
    isWorkloadMode: Boolean,
    workloadName: String
  },
  data() {
    return {
      timezoneList: [],
      editYaml: '',
      originEditForm: {
        name: 'null',
        scaleTargetRef: {
          kind: '',
          name: '',
          apiVersion: ''
        },
        minPods: 1,
        maxPods: 2,
        resourcePolicy: [
          {
            name: '', // memory
            type: '',
            value: ''
          }
        ],
        cornPolicy: {
          data: [
            {
              desiredReplicas: '',
              start: '',
              end: '',
              timeZone: ''
            }
          ],
          switchCtl: false
        }
      },
      editForm: {
        name: '',
        scaleTargetRef: {
          kind: '',
          name: '',
          apiVersion: 'apps/v1'
        },
        minPods: 1,
        maxPods: 2,
        resourcePolicy: [
          {
            name: '', // memory
            type: '',
            value: ''
          }
        ],
        cornPolicy: {
          data: [
            {
              desiredReplicas: '',
              start: '',
              end: '',
              timeZone: ''
            }
          ],
          switchCtl: false
        }
      },
      disableFormMode: false,
      disableKeda: true,
      disableWorkloadSelected: false,
      currentMode: 'formMode',
      cronPolicySwitch: false,
      workLoadNameList: ['xxxx']
    }
  },
  methods: {
    commitYamlOrForm() {
      if (this.isWorkloadMode) {
        this.editForm.scaleTargetRef.kind = 'StatefulSet'
        this.editForm.scaleTargetRef.name = this.workloadName
      }

      if (this.createOrUpdate === 'create') {
        if (this.currentMode === 'formMode') {
          this.handleResourcePolicyValue()
          this.$emit('createWithForm', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            hpaForm: this.editForm
          })
          return
        }
        if (this.currentMode === 'yamlMode') {
          this.$emit('createWithYaml', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            data: yaml2json(this.editYaml)
          })
          return
        }
      }

      if (this.createOrUpdate === 'update') {
        this.handleResourcePolicyValue()
        if (this.currentMode === 'formMode') {
          this.$emit('updateWithForm', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            kind: this.kind,
            hpaForm: this.editForm
          })
          return
        }
        if (this.currentMode === 'yamlMode') {
          this.$emit('updateWithYaml', {
            clusterId: parseInt(this.clusterId),
            namespace: this.namespace,
            data: yaml2json(this.editYaml)
          })
          return
        }
      }
      noticeError(this, '操作失败.')
    },
    handleModeChange(mode) {
      console.log(`current mode: ${mode}`)
      this.currentMode = mode
    },
    async getUpdateYaml() {
      let resource
      let group
      let version
      if (this.kind === '多维度HPA') {
        resource = 'scaledobjects'
        group = 'keda.sh'
        version = 'v1alpha1'
      } else {
        resource = 'horizontalpodautoscalers'
        group = 'autoscaling'
        // version = 'v2beta2'
        version = 'v2'
      }
      await ApiResourceYamlGet({
        resource: resource,
        cluster_id: this.clusterId,
        resource_name: this.yamlName,
        namespace: this.namespace,
        is_edit: true,
        version: version,
        group: group
      })
        .then((res) => {
          this.editYaml = res.data.data.data
          this.$nextTick(() => {
            this.$refs.refYaml.refresh()
          })
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    handleResourcePolicyValue(mode) {
      if (mode === 'get') {
        this.editForm.resourcePolicy.map((item) => {
          item.value = item.value.replace('m', '')
          item.value = item.value.replace('Mi', '')
          item.value = item.value.replace('Gi', '')
          item.value = item.value.replace('Ki', '')
        })
        return
      }
      this.editForm.resourcePolicy.map((item) => {
        if (item.name === 'cpu' && item.type === 'AverageValue') {
          item.value = `${item.value}m`
        }
        if (item.name === 'memory' && item.type === 'AverageValue') {
          item.value = `${item.value}Mi`
        }
      })
    },
    async getUpdateForm() {
      await ApiHpaFormGet({
        clusterId: this.clusterId,
        namespace: this.namespace,
        name: this.name,
        kind: this.kind
      })
        .then((res) => {
          this.fetchWorkloadList(res.data.data.data.scaleTargetRef.kind)
          this.editForm = res.data.data.data
          this.handleResourcePolicyValue('get')
          this.canUseKeda()
        })
        .catch((err) => {
          noticeError(this, `获取表单失败, ${errorMessage(err)}`)
        })
    },
    resetForm() {
      this.editForm = JSON.parse(JSON.stringify(this.originEditForm))
      this.setWorkloadName()
    },
    resetYaml() {
      this.$nextTick(() => {
        this.editYaml = 'apiVersion: keda.sh/v1alpha1\n' + 'kind: ScaledObject\n' + 'metadata:'
        this.$refs.refYaml.refresh()
      })
    },
    async canConvert() {
      this.disableFormMode = true
      ApiHpaFormCanConvert({
        clusterId: this.clusterId,
        namespace: this.namespace,
        name: this.name,
        kind: this.kind
      })
        .then((res) => {
          let c = res.data.data.isConvertible
          if (c) {
            this.disableFormMode = false
            this.currentMode = 'formMode'
            this.getUpdateForm()
          } else {
            this.currentMode = 'yamlMode'
          }
        })
        .catch((err) => {
          this.currentMode = 'yamlMode'
          noticeError(this, `查询能否开启表单失败; ${errorMessage(err)}`)
        })
    },
    async canUseKeda() {
      await ApiHpaKedaIsSupport({ clusterId: this.clusterId })
        .then((res) => {
          let c = res.data.data.isSupportive
          // let c = false
          if (c) {
            this.disableKeda = false
          } else {
            this.disableKeda = true
            this.editForm.cornPolicy.switchCtl = false
          }
        })
        .catch((err) => {
          this.disableKeda = true
          this.editForm.cornPolicy.switchCtl = false
          noticeError(this, `查询集群是否支持多维度HPA失败; ${errorMessage(err)}`)
        })
    },
    async fetchWorkloadList(workloadType) {
      this.editForm.scaleTargetRef.name = ''
      await ApiHpaWorkloadNameList({
        clusterId: this.clusterId,
        namespace: this.namespace,
        kind: workloadType
      })
        .then((res) => {
          this.workLoadNameList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, `获取工作负载列表失败, ${errorMessage(err)}`)
        })
    },
    async fetchTimezoneList() {
      await ApiHpaTimezoneList()
        .then((res) => {
          this.timezoneList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, `获取timezone失败, ${errorMessage(err)}`)
        })
    },
    addResourcePolicy() {
      this.editForm.resourcePolicy.push({
        name: '', // memory
        type: 'Utilization',
        value: ''
      })
    },
    deleteResourcePolicy(idx) {
      this.editForm.resourcePolicy.splice(idx, 1)
    },
    addCronPolicy() {
      this.editForm.cornPolicy.data.push({
        desiredReplicas: '',
        start: '',
        end: '',
        timeZone: ''
      })
    },
    deleteCronPolicy(idx) {
      this.editForm.cornPolicy.data.splice(idx, 1)
    },
    handleWorkloadNameChange(name) {
      if (name === undefined) {
        this.editForm.name = 'null'
        return
      }
      this.editForm.name = name
    },
    setWorkloadName() {
      if (this.isWorkloadMode) {
        this.editForm.name = this.name
      }
    },
    async init() {
      console.log(this.createOrUpdate)
      if (this.createOrUpdate === 'create') {
        this.disableWorkloadSelected = false
        this.canUseKeda()
        this.resetForm()
        this.resetYaml()
        return
      }
      // update mode
      this.getUpdateYaml()
      this.disableWorkloadSelected = true
      await this.canConvert()
    }
  },
  async mounted() {
    await this.init()
    this.fetchTimezoneList()
  }
}
</script>

<style scoped>
/deep/ .ivu-form-item-label {
  display: flex;
}
</style>
