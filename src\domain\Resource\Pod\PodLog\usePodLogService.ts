import { downloadFile, errorMessage, noticeError } from '@/libs/util'
import useWebSocketService from '@/libs/useWebSocketService'
import { ApiNsPodLogList } from '@/api/k8s/namespace/workloads'
import { cloneDeep, omit } from 'lodash-es'
import { getCurrentInstance, reactive, onMounted, onUnmounted, ref, watch, computed } from 'vue'
import { useGet, usePost, useDelete } from '@/libs/service.request'
import Config from '@/config'
import { getFullWidthText } from '@/components/route-map/util'
import { useObjTransferQueryStr } from '@/libs/useTools'

const initSearchObj = {
  mode: 'scroll',
  conditions: [],
  isMatch: false,
  contextLine: 0
}

const initFilterCondition = {
  matchMode: 'keywords',
  type: 'Inclusion',
  regex: '',
  keywords: []
}

const ruleMessageMap = {
  keywords: '请输入关键字并回车确认',
  regex: '请输入正确的正则表达式'
}

const POD_LOG_WS_URL = '/api/v1/ws/resource/pod/logs'
const POD_LOG_SEARCH_WS_URL = '/api/v2/ws/resource/pod/log/search'
const WORKLOAD_LOG_WS_URL = '/api/v1/ws/resource/workload/logs'
const WORKLOAD_LOG_SEARCH_WS_URL = '/api/v1/ws/resource/workload/log/search'

export default function usePodLogService() {
  const listenerMap: Record<string, any> = {}

  const { proxy } = getCurrentInstance()
  const logDomRef = ref(null)

  const loading = ref(false)

  const namespace = ref<string>()
  const deployment = ref<string>()
  const clusterID = ref<string>()
  const pod = ref<string>()
  const container = ref<string>()
  const workloadName = ref<string>()
  const workloadKind = ref<string>()

  const initLine = ref(100)
  const autoFollow = ref(true)
  const autoFollowInterval = ref<number>()
  const wsTimeout = ref<number>()

  const searchModalVisible = ref(false)
  const searchFormRef = ref()
  const pageStatus = ref<'staticSearch' | 'scrollSearch' | 'normal'>('normal')

  const searchObj = ref(cloneDeep(initSearchObj))
  const tempSearchObj = ref(cloneDeep(initSearchObj))

  const keywordList = ref([])

  const downloadModal = ref()
  const downloadLine = ref<number>(100)

  const autoWrap = ref(true)
  const isConnected = computed(() => isNormalConnected.value || isSearchConnected.value)

  const searchFilterModal = ref(false)
  const searchFilterErrorMsg = ref('')
  const searchFilterObj = ref(cloneDeep(initFilterCondition))
  const historyParamsList = ref([])
  const searchViewFormRef = ref(null)
  const searchViewModel = reactive({
    visible: false,
    name: ''
  })
  const showErrorMsg = ref(false)
  const { setQueryObj, getQueryStr } = useObjTransferQueryStr()

  let initFlag // 解决初始化时，监听器触发导致错误提示显示的问题
  const handleSearchFilterChange = (val) => {
    if (initFlag) {
      initFlag = false
      return
    }

    if ((Array.isArray(val) && val.length) || val.target?.value) {
      searchFilterErrorMsg.value = ''
      showErrorMsg.value = false
    } else {
      searchFilterErrorMsg.value = ruleMessageMap[searchFilterObj.value.matchMode]
      showErrorMsg.value = true
    }
  }

  const setLog = (log) => {
    const node = document.createElement('div')
    node.append(`${log}`)
    logDomRef.value.appendChild(node)
  }

  const handleStop = () => {
    normalWSRef.value.close()
    searchWSRef.value && searchWSRef.value.close()
  }

  const handleToggleConnected = (flag) => {
    handleStop()

    if (flag) {
      searchObj.value.conditions.length ? connectSearchWS() : connectNormalWS()
    }
  }

  // 创建过滤条件
  const handleCreateCondition = async () => {
    if (searchFilterObj.value.keywords.length || searchFilterObj.value.regex) {
      tempSearchObj.value.conditions.push(cloneDeep(searchFilterObj.value))
      searchFilterModal.value = false
    }
  }

  const transformSearchCondition = (data) => {
    return data.map((item) => ({
      ...omit(item, ['matchMode']),
      regex: btoa(item.regex)
    }))
  }

  const onOpenSearchFilterModal = () => {
    initFlag = true
    searchFilterObj.value = cloneDeep(initFilterCondition)
    keywordList.value = []
    showErrorMsg.value = false
    searchFilterModal.value = true
  }

  const onRemoveCondition = (index) => {
    tempSearchObj.value.conditions.splice(index, 1)
  }

  const onHistoryParamsListVisible = (visible) => {
    visible && getHistoryParamsList()
  }

  const onOpenSearchViewModal = () => {
    searchViewModel.name = ''
    searchViewFormRef.value?.resetFields()
    searchViewModel.visible = true
  }

  const onMatchModeChange = (key) => {
    showErrorMsg.value = false
    searchFilterErrorMsg.value = ''
    searchFilterObj.value.keywords = []
    searchFilterObj.value.regex = ''
  }

  const onSearchViewClick = (record) => {
    const { isMatch, conditions, contextLine, logType: mode } = record

    tempSearchObj.value = {
      mode,
      isMatch,
      conditions,
      contextLine
    }
  }

  const onSearchViewSave = async () => {
    const valid = await searchViewFormRef.value?.validate()

    if (valid) {
      proxy.$Modal.confirm({
        title: '提示',
        content: `是否确认保存视图 - ${searchViewModel.name} ？`,
        loading: true,
        onOk: async () => {
          const { mode, isMatch, conditions, contextLine } = tempSearchObj.value

          const payload = {
            logType: mode,
            name: searchViewModel.name,
            line: initLine.value,
            isMatch,
            contextLine,
            conditions: conditions.map((item) => ({ ...omit(item, ['matchMode']) }))
          }

          const res = await usePost(`${Config.Api.Base}${Config.Api.SavePodLogSearchHistory}`, payload)
          if (res.success) {
            proxy.$Message.success(`保存视图 - ${searchViewModel.name} 成功`)
            proxy.$Modal.remove()
            searchViewModel.visible = false
          }
        }
      })
    }
  }

  const onSearchViewDelete = (record) => {
    /** 强行修改dropdown层级，修复iview modal和dropdown 每次点击都叠加z-index，但叠加的数量不一致导致的覆盖问题 */
    const modal = document.getElementsByClassName('search-log-modal')?.[0] as HTMLElement
    const dropdown = document.getElementsByClassName('log-search-history-list')?.[0] as HTMLElement
    if (modal && dropdown) {
      dropdown.style.zIndex = (Number(modal.style.zIndex) + 1).toString()
    }

    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除视图 - ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeletePodLogSearchHistory}/${record.id}`)
        if (res.success) {
          proxy.$Message.success(`删除视图 - ${record.name} 成功`)
          getHistoryParamsList()
          proxy.$Modal.remove()
        }
      }
    })
  }

  const getHistoryParamsList = async () => {
    const res = await useGet(`${Config.Api.Base}${Config.Api.GetPodLogSearchHistoryList}`)
    if (res.success) {
      historyParamsList.value = res.data?.data ?? []
    }
  }

  const {
    initWebSocket: initNormalWS,
    webSocketRef: normalWSRef,
    isWebSocketConnected: isNormalConnected
  } = useWebSocketService({
    onWsMessage: (result) => {
      clearEmptyNoticeDom()
      setLog(result.data)
    },
    onRestart: () => {
      proxy.$router.go(0)
    }
  })
  const connectNormalWS = () => {
    setEmptyNoticeDom()
    const url = getWsUrl(pod.value ? 'pod' : 'workload', false)
    initNormalWS(url)
  }

  const getWsUrl = (type, isSearch) => {
    const token = proxy.$store.state.user.token
    let baseHost = process.env.VUE_APP_GLOB_API_URL
    baseHost = baseHost.replace('http', 'ws')
    baseHost = baseHost.replace('https', 'wss')
    let baseUrl = ''

    switch (type) {
      case 'pod':
        baseUrl = `${baseHost}${isSearch ? POD_LOG_SEARCH_WS_URL : POD_LOG_WS_URL}`
        if (isSearch) {
          setQueryObj({
            clusterId: clusterID.value,
            podName: pod.value,
            containerName: container.value,
            namespace: namespace.value,
            line: initLine.value
          })
        } else {
          setQueryObj({
            cluster_id: clusterID.value,
            pod: pod.value,
            container: container.value,
            namespace: namespace.value,
            tail: initLine.value
          })
        }
        break
      case 'workload':
        baseUrl = `${baseHost}${isSearch ? WORKLOAD_LOG_SEARCH_WS_URL : WORKLOAD_LOG_WS_URL}`
        if (isSearch) {
          setQueryObj({
            clusterId: clusterID.value,
            workloadName: workloadName.value,
            workloadKind: workloadKind.value,
            containerName: container.value,
            namespace: namespace.value,
            line: initLine.value
          })
        } else {
          setQueryObj({
            cluster_id: clusterID.value,
            workloadName: workloadName.value,
            workloadKind: workloadKind.value,
            container: container.value,
            namespace: namespace.value,
            tail: initLine.value
          })
        }
        break
    }

    return `${baseUrl}?${getQueryStr()}&token=${token}`
  }

  const {
    initWebSocket: initSearchWS,
    webSocketRef: searchWSRef,
    isWebSocketConnected: isSearchConnected
  } = useWebSocketService({
    onWsMessage: (result) => {
      clearEmptyNoticeDom()
      const { text, highLights } = JSON.parse(result.data) as { text: string; highLights: string[] }
      renderSearchDom(text, highLights)
    },
    onRestart: () => {
      proxy.$router.go(0)
    },
    onWsOpen: () => {
      searchWSRef.value.send(
        JSON.stringify({
          isMatch: searchObj.value.isMatch,
          line: initLine.value,
          conditions: transformSearchCondition(searchObj.value.conditions)
        })
      )
    }
  })
  const connectSearchWS = () => {
    setEmptyNoticeDom()
    const url = getWsUrl(pod.value ? 'pod' : 'workload', true)
    initSearchWS(url)
  }

  const handleLineChange = async (tail) => {
    switch (pageStatus.value) {
      case 'normal':
        normalWSRef.value && normalWSRef.value.close()
        connectNormalWS()

        break
      case 'scrollSearch':
        searchWSRef.value && searchWSRef.value.close()
        connectSearchWS()
        break
      case 'staticSearch':
      default:
        onLogSearch()
        break
    }
  }

  const onOpenSearchModal = () => {
    tempSearchObj.value = cloneDeep(searchObj.value)
    searchModalVisible.value = true
  }
  const onKeywordCreate = (val) => {
    keywordList.value.push({
      value: val,
      label: val
    })
  }
  const onSearch = () => {
    searchFormRef.value.validate().then(async (valid) => {
      if (valid) {
        searchObj.value = cloneDeep({ ...tempSearchObj.value, contextLine: tempSearchObj.value.contextLine ?? 1 })
        searchModalVisible.value = false
        onLogSearch()
      } else {
        searchModalVisible.value = true
      }
    })
  }
  const onLogSearch = async () => {
    setEmptyNoticeDom()
    if (searchObj.value.mode === 'static') {
      pageStatus.value = 'staticSearch'
      normalWSRef.value.close()
      searchWSRef.value && searchWSRef.value.close()
      // 静态输出
      const res = await usePost(
        `${Config.Api.Base}${pod.value ? Config.Api.PodLogSearchV2 : Config.Api.WorkloadLogSearch}`,
        {
          clusterId: clusterID.value,
          namespace: namespace.value,
          podName: pod.value,
          workloadName: workloadName.value,
          workloadKind: workloadKind.value,
          containerName: container.value,
          contextLine: searchObj.value.contextLine,
          line: initLine.value,
          conditions: transformSearchCondition(searchObj.value.conditions)
        }
      )
      if (res.success && res.data.data.length) {
        // 渲染页面
        clearEmptyNoticeDom()
        const width = logDomRef.value.clientWidth - 32
        const fullText = getFullWidthText('-', width, 16)
        res.data.data?.forEach((i) => {
          if (i.text === '-----') {
            i.text = fullText
          }
          renderSearchDom(i.text, i.highLights)
        })
      }
    } else {
      // 滚动输出
      pageStatus.value = 'scrollSearch'
      normalWSRef.value.close()
      if (!searchWSRef.value || !isSearchConnected.value) {
        connectSearchWS()
      } else {
        searchWSRef.value &&
          searchWSRef.value.send(
            JSON.stringify({
              isMatch: searchObj.value.isMatch,
              line: initLine.value,
              conditions: transformSearchCondition(searchObj.value.conditions)
            })
          )
      }
    }
  }
  /** 设置缺省状态的dom */
  const setEmptyNoticeDom = () => {
    logDomRef.value.innerHTML = '暂无数据……'
  }

  /** 如果dom是缺省状态，清空dom内容 */
  const clearEmptyNoticeDom = () => {
    logDomRef.value.innerHTML === '暂无数据……' && (logDomRef.value.innerHTML = '')
  }

  const renderSearchDom = (text: string, highLights: string[]) => {
    const node = document.createElement('div') as HTMLElement

    if (!!highLights?.length) {
      const reg = new RegExp(highLights.join('|'), 'gi')
      const textDom = text.replace(reg, function (x) {
        return `<span class='high-light'>${x}</span>`
      })
      node.innerHTML = textDom
    } else {
      node.append(`${text}`)
    }
    logDomRef.value.appendChild(node)
  }

  const handleAutoFollow = (isSetAutoFollow) => {
    autoFollowInterval.value && clearInterval(autoFollowInterval.value)
    autoFollow.value = isSetAutoFollow
    if (isSetAutoFollow) {
      autoFollowInterval.value = setInterval(() => {
        const sTop = logDomRef.value.scrollTop
        const sHeight = logDomRef.value.scrollHeight
        logDomRef.value.scrollTop = sTop + sHeight + 1000
      }, 100)
    }
  }

  const setWSTimeout = () => {
    if (wsTimeout.value) {
      clearTimeout(wsTimeout.value)
    }
    wsTimeout.value = setTimeout(() => {
      normalWSRef.value.close()
    }, 600000)
  }

  const onResetToNormalStatus = () => {
    pageStatus.value = 'normal'
    !isNormalConnected.value && connectNormalWS()
    searchWSRef.value && searchWSRef.value.close()
    searchObj.value = cloneDeep(initSearchObj)
  }

  const onDownloadLog = async () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认下载日志？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${pod.value ? Config.Api.PodLogSearchV2 : Config.Api.WorkloadLogSearch}`,
          {
            clusterId: clusterID.value,
            namespace: namespace.value,
            podName: pod.value,
            workloadName: workloadName.value,
            workloadKind: workloadKind.value,
            containerName: container.value,
            contextLine: searchObj.value.contextLine,
            line: downloadLine.value
          }
        )
        const logContent = res.data.data?.map((i) => i.text).join('\n')

        const fileName = `${pod.value}-${container.value}-${new Date().getTime()}.txt`
        downloadFile(fileName, logContent)
        proxy.$Modal.remove()
        downloadModal.value = false
      }
    })
  }

  const onClearLog = () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认清空屏幕`,
      loading: true,
      onOk: async () => {
        logDomRef.value.innerHTML = ''
        proxy.$Modal.remove()
      }
    })
  }

  const onAppendBlankRow = () => {
    const node = document.createElement('br') as HTMLElement
    logDomRef.value.appendChild(node)
  }

  watch(
    pageStatus,
    () => {
      switch (pageStatus.value) {
        case 'normal':
          handleAutoFollow(true)
          break
        case 'scrollSearch':
          handleAutoFollow(true)
          break
        case 'staticSearch':
        default:
          handleAutoFollow(false)
          break
      }
    },
    { immediate: true }
  )

  watch(
    () => tempSearchObj.value.conditions,
    () => {
      if (!tempSearchObj.value.conditions.some((i) => i.type === 'Inclusion')) {
        tempSearchObj.value.contextLine = 0
      }
    },
    {
      deep: true
    }
  )

  onMounted(() => {
    const query = proxy.$route.query as Record<string, string>
    clusterID.value = query.clusterId
    namespace.value = query.namespace
    deployment.value = query.deployment
    workloadName.value = query.workloadName
    workloadKind.value = query.workloadKind
    pod.value = query.pod
    container.value = query.container

    const title = document.getElementsByTagName('title')
    title[0].innerHTML = `日志 ${query.pod || query.workloadName}/${query.container}`

    connectNormalWS()
    // setWSTimeout()
    logDomRef.value.focus()

    // listenerMap.scroll = window.addEventListener('scroll', setWSTimeout)
    listenerMap.keyup = window.addEventListener('keyup', function (event) {
      //   setWSTimeout()
      event.preventDefault()
      if (event.target === document.body && event.keyCode === 13) {
        onAppendBlankRow()
      }
    })
  })

  onUnmounted(() => {
    if (autoFollowInterval.value) {
      clearInterval(autoFollowInterval.value)
    }
    if (wsTimeout.value) {
      clearTimeout(wsTimeout.value)
    }
    normalWSRef.value.close()
    searchWSRef.value && searchWSRef.value.close()
    for (const [type, listener] of Object.entries(listenerMap)) {
      removeEventListener(type, listener)
    }
  })

  return {
    loading,
    namespace,
    pod,
    workloadName,
    container,
    onOpenSearchModal,
    searchObj,
    isNormalConnected,
    setWSTimeout,
    handleLineChange,
    isConnected,
    handleToggleConnected,
    initLine,
    autoFollow,
    handleAutoFollow,
    autoWrap,
    handleStop,
    searchModalVisible,
    searchFormRef,
    tempSearchObj,
    searchFilterObj,
    searchFilterModal,
    historyParamsList,
    onHistoryParamsListVisible,
    searchViewFormRef,
    searchViewModel,
    showErrorMsg,
    searchFilterErrorMsg,
    handleSearchFilterChange,
    onMatchModeChange,
    onOpenSearchViewModal,
    onSearchViewSave,
    onSearchViewClick,
    onSearchViewDelete,
    onOpenSearchFilterModal,
    onRemoveCondition,
    handleCreateCondition,
    keywordList,
    onSearch,
    onKeywordCreate,
    logDomRef,
    isSearchConnected,
    onResetToNormalStatus,
    downloadModal,
    downloadLine,
    onDownloadLog,
    onClearLog
  }
}
