import expandTable from './ExpandTable/index.vue'
import { EnumTabType } from './type'
export const getTableColumns = (tabName: EnumTabType) => {
  switch (tabName) {
    case EnumTabType.backupStrategy:
    case EnumTabType.recoverStrategy:
      return [
        {
          width: 40,
          type: 'expand',
          render: (h, params) => {
            return h(expandTable, {
              props: {
                data: params.row,
                activeTab: tabName
              }
            })
          }
        },
        {
          title: 'Cluster',
          key: 'clusterName'
        },
        {
          title: 'Namespace',
          key: 'namespace'
        },
        {
          title: 'ApiGroup / ApiResource',
          key: 'group/resource',
          slot: 'groupResource'
        },

        {
          title: 'ObjectName',
          key: 'brObjectNames',
          slot: 'objectName'
        },
        {
          title: '生效期限',
          key: 'isPersistent',
          slot: 'isPersistent',
          width: 80,
          align: 'center'
        },
        {
          title: '备注',
          key: 'desc',
          render: (h, { row }) => h('span', row.desc ? row.desc : '-')
        },
        {
          title: '创建时间',
          key: 'createdAt'
        },
        {
          title: 'Ops',
          key: 'ops',
          slot: 'ops',
          width: 120,
          align: 'center'
        }
      ]

    case EnumTabType.backupList:
    default:
      return [
        {
          title: 'Cluster',
          key: 'clusterName'
        },
        {
          title: 'Namespace',
          key: 'namespace'
        },
        {
          title: 'ApiGroup / ApiResource',
          key: 'group/resource',
          slot: 'groupResource'
        },

        {
          title: 'ObjectName',
          key: 'objectName',
          render: (h, { row }) => h('span', row.objectName ? row.objectName : '-')
        },
        {
          title: '创建时间',
          key: 'createdAt'
        },
        {
          title: 'Ops',
          key: 'ops',
          slot: 'ops',
          width: 100,
          align: 'center'
        }
      ]
  }
}

export const OBJECT_NAME_COLUMNS = [
  {
    type: 'selection',
    width: 60,
    align: 'center'
  },
  {
    title: '对象名称',
    key: 'objectName'
  },
  {
    title: '版本信息',
    key: 'createdAt'
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 120,
    align: 'center'
  }
]
