<script lang="ts" setup>
import LinkButton from '@/components/link-button'
import { useGet } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, onMounted, ref } from 'vue'
import ProTable, { ActionType, TableRequest } from '@/components/pro-table'
import { GATEWAY_TABLE_COLUMNS } from './setting'
import { ViewYaml, YamlHistoryParams } from '@/components/yaml'

const SERVICE_ENTRY = { resource: 'gateways', group: 'networking.istio.io', version: 'v1beta1' }

interface Gateway {
  creationTimestamp: number
  hosts: string[]
  name: string
  namespace: string
  uid: string
}

const { proxy } = getCurrentInstance()
const refObject = {
  tableRef: ref<ActionType | null>(null)
}
const viewYamlVisible = ref(false)
const resourceEntry = ref()
const yamlHistoryParams = ref<YamlHistoryParams>()

const getTableData: TableRequest = async (params) => {
  const res = await useGet(
    `${Config.Api.Base}${Config.Api.GetRelatedGateway}?clusterId=${proxy.$route.query.clusterId}&namespace=${proxy.$route.query.namespace}&name=${proxy.$route.query.deployment}`
  )
  return {
    success: res.success,
    total: res.data?.data.length ?? 0,
    data: res.data?.data ?? []
  }
}

const onViewYaml = (record: Gateway) => {
  resourceEntry.value = {
    clusterId: proxy.$route.query.clusterId as string,
    namespace: record.namespace,
    ...SERVICE_ENTRY,
    resourceName: record.name
  }
  yamlHistoryParams.value = {
    kind: 'Gateway',
    uuid: record.uid
  }
  viewYamlVisible.value = true
}

onMounted(() => {
  refObject.tableRef.value?.reload()
})
</script>

<template>
  <div>
    <pro-table
      style="margin-top: 16px"
      :action-ref="refObject"
      manual-request
      :columns="GATEWAY_TABLE_COLUMNS"
      :request="getTableData"
      :pagination="false"
    >
      <template #ops="{ row }">
        <link-button @click="() => onViewYaml(row)" text="YAML" :disabled="row.namespace === 'mesh'" />
      </template>
    </pro-table>
    <view-yaml
      v-model="viewYamlVisible"
      :resource-entity="resourceEntry"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      resourceType="unified-gateway/gateway"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
  </div>
</template>

<style lang="less" scoped></style>
