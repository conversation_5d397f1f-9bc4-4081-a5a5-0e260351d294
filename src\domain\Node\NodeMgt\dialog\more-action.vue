<template>
  <div class="dialog-more-action">
    <Modal
      v-model="visible"
      width="850"
      title="更多操作"
      footer-hide
      @on-cancel="$refs.orderConfigFormRef.resetFields()"
    >
      <Card>
        <h4>基本功能</h4>
        <Alert style="margin-top: 16px" show-icon>
          <p style="margin-bottom: 8px">
            <b>调度开关</b>：只控制该污点 [<span style="color: #2a7cc3">node.kubernetes.io/unschedulable</span>] 和
            [<span style="color: #2a7cc3">unschedulable</span>] 字段.
          </p>
          <p>
            <b>节点驱逐</b>: 会进行节点 <span style="color: #2a7cc3">禁止调度</span> 操作和 发送
            <span style="color: #2a7cc3">驱逐指令</span>， 类似 kubectl 发送 cordon 和 drain 指令.
          </p>
        </Alert>
        <Row type="flex" align="middle" :gutter="24" class="card-row">
          <Col span="4">是否允许调度：</Col>
          <Col span="20"
            ><i-switch
              :value="isSchedulable"
              :loading="switchLoading"
              :before-change="handleBeforeSwitchChange"
            ></i-switch
          ></Col>
        </Row>
        <Row type="flex" align="middle" :gutter="24" class="card-row">
          <Col span="4">节点驱逐：</Col>
          <Col>
            <Button type="primary" @click="sendExpulsionOrder">发送驱逐指令</Button>
          </Col>
          <Col>
            <span class="instruction-config" @click="openOrderConfigModal">参数配置</span>
          </Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <h4>Label 操作</h4>
        <Row type="flex" justify="end" align="middle" :gutter="24" style="margin-bottom: 16px">
          <Col><Button type="primary" size="small" ghost @click="openAddLabelModal">创建 Label</Button></Col>
        </Row>
        <Table height="300" size="small" :columns="columns" :data="data" :loading="loading"></Table>
      </Card>
    </Modal>

    <Modal
      title="驱逐指令配置"
      v-model="showOrderConfig"
      class-name="vertical-center-modal"
      @on-ok="setOrderConfig"
      @on-cancel="unsetOrderConfig"
    >
      <Alert show-icon>Tips: 必选参数 ignore-daemonsets=true, delete-emptydir-data=true</Alert>
      <Alert type="info" show-icon
        ><b>参数解析：</b><br />
        <b>Timeout:</b> The length of time to wait before giving up, zero means infinite<br />
        <b>GracePeriod:</b> Period of time in seconds given to each pod to terminate gracefully. If negative, the
        default value specified in the pod will be used.
      </Alert>
      <Form ref="orderConfigFormRef" :model="orderConfigForm" :label-width="120">
        <FormItem label="timeout(s)" prop="timeout">
          <InputNumber
            v-model="orderConfigForm.timeout"
            style="width: 85%"
            :max="120"
            :min="1"
            placeholder="min: 1 max: 120 default: 120"
          ></InputNumber>
        </FormItem>
        <FormItem label="grace-period(s)" prop="gracePeriod">
          <InputNumber
            v-model="orderConfigForm.gracePeriod"
            style="width: 85%; height: 100%"
            :max="600"
            :min="-1"
            placeholder="min: -1 max: 600 default: 0"
          ></InputNumber>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      title="创建 Label"
      width="40"
      v-model="showAddLabel"
      class-name="vertical-center-modal"
      @on-cancel="resetLabelsForm"
    >
      <Form ref="addLabelFormRef" :label-width="60" :model="labelsForm">
        <template v-for="(item1, index1) in labelsForm.items">
          <Row :key="index1" :gutter="8">
            <template v-for="(item2, index2) in item1">
              <Col span="11" :key="index2">
                <FormItem
                  :label="item2.label"
                  :key="index2"
                  :prop="`items.${index1}.${index2}.value`"
                  :rules="{ required: true, message: 'can not be empty', trigger: 'blur' }"
                >
                  <Input v-model="item2.value" :placeholder="'Enter ' + item2.label"></Input>
                </FormItem>
              </Col>
            </template>
            <Col offset span="2">
              <div class="remove-label" @click="handleRemoveLabelClick(index1)">
                <Icon type="md-trash" :size="16" />
                <span>删除</span>
              </div>
            </Col>
          </Row>
        </template>
      </Form>
      <div class="add-label" style="margin-left: 20px" @click="handleAddLabelClick">
        <Icon type="md-add-circle" :size="16" />
        <span>添加</span>
      </div>
      <div slot="footer">
        <Button @click="resetLabelsForm">取消</Button>
        <Button type="primary" @click="handleLabelsSubmit">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { color } from '@/libs/consts'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import {
  ApiNodeLabelList,
  ApiNodeIsSchedulable,
  ApiNodeSchedule,
  ApiNodeLabelDelete,
  ApiNodeDrain,
  ApiNodeLabelAdd
} from '@/api/k8s/node'

export default {
  name: 'dialog-more-action',
  props: ['value', 'currentNode', 'currentClusterId'],
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  data() {
    return {
      data: [],
      loading: false,
      isSchedulable: false, // 是否允许调度

      switchLoading: false,
      showOrderConfig: false,
      orderConfigForm: {
        timeout: 120,
        gracePeriod: 0
      },
      preOrderConfigData: {
        timeout: 120,
        gracePeriod: 0
      },
      showAddLabel: false,
      labelsForm: {
        items: [
          [
            {
              label: 'Key',
              value: ''
            },
            {
              label: 'Value',
              value: ''
            }
          ]
        ]
      },
      columns: [
        {
          title: 'Key',
          key: 'key',

          tooltip: true
        },
        {
          title: 'Value',
          key: 'value',
          width: 250,

          tooltip: true
        },
        {
          title: '操作',
          key: 'ops',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h(
              'a',
              {
                style: {
                  color: color.error
                },
                on: {
                  click: async () => {
                    this.$Modal.confirm({
                      title: 'Tips',
                      content: `<p>确认删除 ${params.row.key} ?</p>`,
                      loading: true,
                      onOk: async () => {
                        try {
                          await ApiNodeLabelDelete({
                            clusterId: this.currentClusterId,
                            node: this.currentNode,
                            labelKey: params.row.key
                          })
                          this.refreshTable()
                          noticeSucceed(this, '删除成功')
                        } catch (error) {
                          noticeError(this, `删除失败, ${errorMessage(error)}`)
                        }
                        this.$Modal.remove()
                      }
                    })
                  }
                }
              },
              '删除'
            )
          }
        }
      ]
    }
  },
  watch: {
    visible: async function () {
      if (this.visible) {
        this.data = []

        await this.getNodeIsSchedulable(this.currentNode)

        await this.getNodeLabelList(this.currentNode)
      }
    }
  },
  methods: {
    // 新增label
    handleAddLabelClick() {
      this.labelsForm.items.push([
        {
          label: 'Key',
          value: ''
        },
        {
          label: 'Value',
          value: ''
        }
      ])
    },
    // 移除label
    handleRemoveLabelClick(index) {
      this.labelsForm.items.splice(index, 1)
    },
    resetLabelsForm() {
      this.showAddLabel = false
      this.$refs.addLabelFormRef.resetFields()
      this.labelsForm.items = [
        [
          {
            label: 'Key',
            value: ''
          },
          {
            label: 'Value',
            value: ''
          }
        ]
      ]
    },
    // 提交新建label
    handleLabelsSubmit() {
      this.$refs.addLabelFormRef.validate(async (valid) => {
        if (valid) {
          const label = {}
          this.labelsForm.items.forEach((item) => {
            label[item[0].value] = item[1].value
          })
          try {
            await ApiNodeLabelAdd({
              clusterId: this.currentClusterId,
              node: this.currentNode,
              label
            })
            this.showAddLabel = false
            this.refreshTable()
            this.resetLabelsForm()
            noticeSucceed(this, '新建成功')
          } catch (error) {
            noticeError(this, `新建失败, ${errorMessage(error)}`)
          }
        }
      })
    },
    openOrderConfigModal() {
      this.showOrderConfig = true
    },
    openAddLabelModal() {
      this.showAddLabel = true
    },
    // 设置指令配置
    setOrderConfig() {
      this.preOrderConfigData = { ...this.orderConfigForm }
      noticeSucceed(this, '配置成功')
    },
    // 取消指令设置时 恢复至之前的值
    unsetOrderConfig() {
      this.orderConfigForm = { ...this.preOrderConfigData }
    },
    // 切换开关
    async handleBeforeSwitchChange() {
      this.switchLoading = true
      try {
        await ApiNodeSchedule({
          clusterId: this.currentClusterId,
          node: this.currentNode,
          schedulable: !this.isSchedulable
        })
        this.getNodeIsSchedulable(this.currentNode, () => {
          this.switchLoading = false
          noticeSucceed(this, '切换成功')
          return Promise.resolve()
        })
      } catch (error) {
        this.switchLoading = false
        noticeError(this, `调度状态切换失败, ${errorMessage(error)}`)
        return Promise.reject(error)
      }
    },
    // 发送驱逐指令
    async sendExpulsionOrder() {
      this.$Modal.confirm({
        title: 'Tips',
        content: '<p>是否发起驱逐? 确认后请耐心等待结果返回。</p>',
        loading: true,
        onOk: async () => {
          try {
            await ApiNodeDrain({
              clusterId: this.currentClusterId,
              node: this.currentNode,
              ...this.orderConfigForm
            })
            noticeSucceed(this, '驱逐完成')
          } catch (error) {
            noticeError(this, `驱逐未完成, 请查看pod列表信息, ${errorMessage(error)}`)
          }
          this.getNodeIsSchedulable(this.currentNode)
          this.$Modal.remove()
        }
      })
    },
    refreshTable() {
      this.getNodeLabelList(this.currentNode)
    },
    // 获取调度状态
    async getNodeIsSchedulable(node, callback) {
      try {
        const res = await ApiNodeIsSchedulable({
          clusterId: this.currentClusterId,
          node
        })
        this.isSchedulable = res.data.data.isSchedulable
        if (callback) callback()
      } catch (error) {
        noticeError(this, `获取调度状态失败, ${errorMessage(error)}`)
      }
    }, // 获取node label列表
    async getNodeLabelList(node) {
      this.loading = true
      try {
        const res = await ApiNodeLabelList({
          clusterId: this.currentClusterId,
          node
        })
        const data = res.data.data.label

        const arr = []
        for (const key in data) {
          arr.push({
            key,
            value: data[key]
          })
        }
        this.data = arr
      } catch (error) {
        noticeError(this, `获取Node Label列表失败, ${errorMessage(error)}`)
      }
      this.loading = false
    }
  }
}
</script>

<style lang="less">
@primaryColor: #2a7cc3;

.card-row {
  margin-top: 20px;
  padding-left: 16px;
}

.instruction-config {
  color: @primaryColor;
  cursor: pointer;
}

.add-label {
  display: flex;
  align-items: center;
  width: 80px;
  color: @primaryColor;
  cursor: pointer;
}

.remove-label {
  position: absolute;
  top: 8px;
  display: flex;
  align-items: center;
  width: 80px;
  color: #ed4014;
  cursor: pointer;
}

.vertical-center-modal {
  display: flex;
  align16pxems: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
</style>
