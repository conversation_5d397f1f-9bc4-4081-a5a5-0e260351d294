import axios from '@/libs/api.request'

export const ApiDeployHpaList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiDeployHpaGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/get`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiDeployHpaDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/delete`,
    method: 'delete',
    data: data
  })
}

export const ApiDeployHpaYamlCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/yaml/create`,
    method: 'post',
    data: data
  })
}

export const ApiDeployHpaYamlUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/yaml/update`,
    method: 'post',
    data: data
  })
}

// ---

export const ApiDeployHpaFormGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/form/get`,
    method: 'get',
    params: params
  })
}
export const ApiDeployHpaFormCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/form/create`,
    method: 'post',
    data: data
  })
}
export const ApiDeployHpaFormUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/form/update`,
    method: 'post',
    data: data
  })
}
export const ApiDeployHpaFormCanConvert = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/form/is-convert`,
    method: 'get',
    params
  })
}
export const ApiDeployHpaKedaIsSupport = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/keda/is-support`,
    method: 'get',
    params
  })
}
export const ApiDeployHpaTimezoneList = () => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa/timezone/list`,
    method: 'get'
  })
}
