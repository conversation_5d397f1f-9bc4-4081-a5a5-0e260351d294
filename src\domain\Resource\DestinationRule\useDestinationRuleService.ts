import { ref, onMounted, getCurrentInstance, computed, nextTick, set, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList, usePost } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { DestinationRule } from './type'
import { EnumFormStatus, FormSubmitParams, ResourceEntity } from '@/components/resource-form'
import { EnumArrayObjectModalStatus } from '@/components/pro-form'
import { useRequest } from 'vue-request'
import { EnumComponentType } from './enum'
import { getTableColumns } from './setting'
import { isEmpty } from 'lodash-es'
import useForceUpdateNoticeService from '../useForceUpdateNoticeService'
import useSingleK8SService from '@/libs/useSingleK8SService'

export const DEFAULT_ENTITY = {
  resource: 'destinationrules',
  group: 'networking.istio.io',
  version: 'v1beta1'
}
export const POD_ENTITY = { resource: 'pods', group: '', version: 'v1' }

export default function useDestinationRuleService(props: {
  type: EnumComponentType.Associated | EnumComponentType.Independent
}) {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string; clusterName: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const viewDetailVisible = ref(false)
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const trafficPoliciesFormRef = ref()
  const modal = ref({
    visible: false,
    data: {} as DestinationRule,
    value: []
  })
  const tempHostOption = ref([])
  const formInitData = computed(() => ({
    name: undefined,
    namespace: K8SInstance.value?.namespace
  }))
  const currentResourceNamespace = ref()
  const showMoreNotice = ref(props.type === EnumComponentType.Independent)
  const batchCopyModalVisible = ref(false)

  // 防止多次注入同一个columns,解决table失焦不去除高亮效果的bug
  const columns = computed(() => getTableColumns(props.type === EnumComponentType.Associated))
  const isIndependentType = computed(() => props.type === EnumComponentType.Independent)

  const { onForceUpdateNotice } = useForceUpdateNoticeService()

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...DEFAULT_ENTITY,
      namespace: K8SInstance.value?.namespace
    }
    yamlInitData.value = `apiVersion: networking.istio.io/v1beta1 
kind: DestinationRule
metadata:
  name: 必须修改
  namespace: ${K8SInstance.value.namespace}
spec:`
    currentResourceNamespace.value = K8SInstance.value.namespace
  }
  const onDelete = (record: DestinationRule) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(
          `${Config.Api.Base}${
            props.type === EnumComponentType.Associated
              ? Config.Api.DeleteRelatedDestinationRule
              : Config.Api.DeleteDestinationRule
          }`,
          {
            params: {
              ...K8SInstance.value,
              resourceName: record.name
            }
          }
        )
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onEdit = (record: DestinationRule) => {
    const editFun = () => {
      console.log('onEdit')
      formVisible.value = true
      formStatus.value = EnumFormStatus.Edit
      formEntity.value = {
        ...K8SInstance.value,
        ...DEFAULT_ENTITY,
        resourceName: record.name,
        namespace: record.namespace
      }
      currentResourceNamespace.value = record.namespace
    }
    if (record.isAllowEdit) {
      editFun()
      return
    }
    onForceUpdateNotice(
      {
        namespace: record.namespace,
        clusterId: K8SInstance.value.clusterId,
        objectName: record.name,
        kind: 'DestinationRule'
      },
      editFun
    )
  }
  const { data: clusterCascaderList, run: getClusterCascaderList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetClusterCascaderList}`, {
        params: {
          clusterId: K8SInstance.value?.clusterId
        }
      })
    },
    {
      formatResult: (res) => res.data.data,
      ready: isIndependentType
    }
  )
  const onCopy = (record: DestinationRule) => {
    modal.value = {
      visible: true,
      data: record,
      value: []
    }
  }
  const onCopySubmit = async () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认复制 ${modal.value.data.name}？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(`${Config.Api.Base}${Config.Api.CopyDestinationRule}`, {
          newClusterId: modal.value.value[0],
          newNamespace: modal.value.value[1],
          originClusterId: K8SInstance.value.clusterId,
          originNamespace: K8SInstance.value.namespace,
          resourceName: modal.value.data.name
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
          modal.value.visible = false
          proxy.$Message.success(`复制成功`)
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onViewYaml = (record: DestinationRule, type: 'destinationRule' | 'pod' = 'destinationRule') => {
    formEntity.value = {
      ...K8SInstance.value,
      ...(type === 'destinationRule' ? DEFAULT_ENTITY : POD_ENTITY),
      namespace: record.namespace,
      resourceName: record.name
    }
    yamlHistoryParams.value = {
      kind: type === 'destinationRule' ? 'DestinationRule' : 'Pod',
      uuid: record.uid
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: DestinationRule) => {
    console.log('onViewDetail')
    viewDetailVisible.value = true
    formEntity.value = {
      ...K8SInstance.value,
      ...DEFAULT_ENTITY,
      resourceName: record.name,
      namespace: record.namespace
    }
  }

  const getTableData: TableRequest = async (params) => {
    const url =
      props.type === EnumComponentType.Associated
        ? `${Config.Api.Base}${Config.Api.GetRelatedDestinationRuleTableData}?&clusterId=${K8SInstance.value.clusterId}&namespace=${K8SInstance.value.namespace}&name=${proxy.$route.query.deployment}`
        : `${Config.Api.Base}${Config.Api.GetDestinationRuleTableData}?search=${params.searchValue ?? ''}&clusterId=${
            K8SInstance.value.clusterId
          }&namespace=${K8SInstance.value.namespace}&page=${params.pageIndex}&size=${params.pageSize}`
    const res = await useGet<PageList<DestinationRule[]>>(url)
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data:
        (props.type === EnumComponentType.Associated
          ? (res.data as unknown as { data: DestinationRule[] }).data
          : res.data?.list) ?? []
    }
  }

  const onEndpointsKeyChange = (record, key, index, val) => {
    if (!record.ports?.data?.length) {
      record.ports = Object.assign({}, record.ports, { data: [] })
    }
    if (!record.ports.data[index]) {
      record.ports.data[index] = {}
    }

    record.ports.data[index][key] = val
  }
  const onSubmitTrafficPolicies = (data, type, record, index) => {
    if (data.trafficPolicies?.length) {
      data.trafficPolicies.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.trafficPolicies = [record]
    }
  }

  const onInitFormat = (data) => {
    data.trafficPolicies?.forEach((i) => {
      i.loadBalancer?.data?.locality?.data?.distribute?.forEach((ie) => ie.from && (ie.from = [ie.from]))
      if (i.labels.data) {
        const arr = []
        for (const [key, value] of Object.entries(i.labels.data)) {
          arr.push(`${key}:${value}`)
        }
        i.labels.data = arr
      }
    })
    tempHostOption.value = [...hostOptions.value]
    if (data.host) {
      const keys = hostOptions.value?.map((i) => i.value)

      if (!keys.includes(data.host)) {
        tempHostOption.value.push({ value: data.host, label: data.host })
      }

      const labels = data.trafficPolicies
        ?.map((i) => i.labels.data)
        ?.flat()
        ?.filter((i, index, arr) => i && arr.indexOf(i) === index)
      getLabelOptions(data.host, labels)
    }
    return data
  }
  const onSubmitFormat = (params: FormSubmitParams) => {
    params.data.trafficPolicies?.forEach((i) => {
      i.loadBalancer?.data?.locality?.data?.distribute?.forEach(
        (item) => item.from && !isEmpty(item.from) && (item.from = String(item.from['0']))
      )
      if (i.labels?.data) {
        const obj = {}
        i.labels.data?.forEach((i) => {
          const [key, value] = i.split(':')
          obj[key] = value
        })
        i.labels.data = obj
      }
    })

    return params
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const { data: hostOptions, run: getHostOptions } = useRequest(
    () => {
      const url =
        props.type === EnumComponentType.Associated
          ? `${Config.Api.Base}${Config.Api.GetRelatedDestinationHostOptions}?clusterId=${K8SInstance.value?.clusterId}&namespace=${K8SInstance.value?.namespace}&name=${proxy.$route.query.deployment}`
          : `${Config.Api.Base}${Config.Api.GetDestinationHostOptions}?clusterId=${K8SInstance.value?.clusterId}&namespace=${K8SInstance.value?.namespace}`
      return useGet(url)
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data?.data?.map((i) => ({ value: i, label: i }))
    }
  )
  const { data: exportToList, run: getExportToList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetExportToList}?ClusterId=${
          props.type === EnumComponentType.Associated ? proxy.$route.query.clusterId : K8SInstance.value?.clusterId
        }`
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const {
    data: labelOptions,
    run: getLabelOptions,
    loading: labelOptionsLoading
  } = useRequest(
    (host, labels) => {
      return useGet(
        `${Config.Api.Base}${
          props.type === EnumComponentType.Associated
            ? Config.Api.GetRelatedDestinationLabelOptions
            : Config.Api.GetDestinationLabelOptions
        }?clusterId=${K8SInstance.value?.clusterId}&namespace=${currentResourceNamespace.value}&host=${host}`
      )
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => {
        if (!res.data.data) {
          return []
        }
        const arr = []
        for (const [key, value] of Object.entries(res.data.data)) {
          Object.keys(value)?.forEach((i) => {
            const item = `${key}:${i}`
            arr.push({ value: item, label: item })
          })
        }
        return arr
      },
      onSuccess: (data, params) => {
        const keys = data?.map((i) => i.value)
        params[1]?.forEach((i) => {
          if (!keys.includes(i)) {
            data.push({ value: String(i), label: i + '（已失效）', invalid: true })
          }
        })
      }
    }
  )

  const validateLabels = (rule, value, callback) => {
    const keys = value.data?.map((i) => i.split(':')[0])
    if (keys && new Set(keys).size < keys.length) {
      callback(new Error('不能同时存在 key 值相同的 label, 请重新选择'))
    } else {
      callback()
    }
  }

  const { data: clusterRegionZoneCascaderList, run: getClusterRegionZoneCascaderList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetUnifiedCascaderList}`, {
        params: { clusterId: K8SInstance.value?.clusterId }
      })
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const onSubmitDistributeFrom = (data, type, record, index, itemIndex) => {
    if (!record.loadBalancer.data.locality.data.distribute) {
      set(record.loadBalancer.data.locality.data, 'distribute', [])
    }
    if (!record?.loadBalancer?.data?.locality?.data?.distribute?.[index]) {
      set(record.loadBalancer.data.locality.data.distribute, index, {})
    }
    if (!record?.loadBalancer?.data?.locality?.data?.distribute?.[index].from) {
      set(record.loadBalancer.data.locality.data.distribute[index], 'from', [])
    }
    set(record.loadBalancer.data.locality.data.distribute[index].from, itemIndex, `${data.key[1]}/${data.key[2]}/*`)
  }
  const onDeleteDistributeFrom = (record, index, itemIndex) => {
    record.loadBalancer.data.locality.data.distribute[index].from.splice(itemIndex, 1)
  }
  const onSubmitDistributeTo = (data, type, record, index, itemIndex) => {
    if (!record.loadBalancer.data.locality.data.distribute) {
      set(record.loadBalancer.data.locality.data, 'distribute', [])
    }
    if (!record.loadBalancer.data.locality.data.distribute[index]) {
      set(record.loadBalancer.data.locality.data.distribute, index, {})
    }
    if (!record?.loadBalancer?.data?.locality?.data?.distribute?.[index].to) {
      set(record.loadBalancer.data.locality.data.distribute[index], 'to', {})
    }
    set(
      record.loadBalancer.data.locality.data.distribute[index].to,
      `${data.key[1]}/${data.key[2]}/*`,
      data.value ?? 100
    )
  }

  const onDeleteDistributeTo = (record, index, key) => {
    delete record.loadBalancer.data.locality.data.distribute[index].to[key]
    set(record.loadBalancer.data.locality.data.distribute[index], 'to', {
      ...(record.loadBalancer.data.locality.data.distribute[index]?.to ?? {})
    })
  }

  const initK8SInstance = () => {
    if (props.type === EnumComponentType.Associated) {
      K8SInstance.value = {
        namespace: proxy.$route.query.namespace as string,
        clusterId: proxy.$route.query.clusterId as string,
        clusterName: proxy.$route.query.cluster as string
      }
    } else {
      store.commit('getCurrentClusterID', store.state.user.userId)
      store.commit('getCurrentNamespace', store.state.user.userId)
      K8SInstance.value = {
        namespace: store.state.k8s.currentNamespace,
        clusterId: store.state.k8s.currentClusterId,
        clusterName: store.state.k8s.currentCluster
      }
    }
  }
  onMounted(() => {
    initK8SInstance()
    nextTick(() => {
      getHostOptions()
      getExportToList()
      getClusterRegionZoneCascaderList()
      getClusterCascaderList()
    })
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    nextTick(() => {
      getHostOptions()
      getExportToList()
      getClusterRegionZoneCascaderList()
      getClusterCascaderList()
    })
    refObject.tableRef.value.reload()
  })

  return {
    getTableData,
    refObject,
    onCreate,
    onDelete,
    onEdit,
    onCopy,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    formInitData,

    onInitFormat,

    onSubmitFormat,
    onEndpointsKeyChange,
    onSubmitTrafficPolicies,
    onSubmitSuccess,
    onViewDetail,
    viewDetailVisible,
    tempHostOption,
    exportToList,
    clusterRegionZoneCascaderList,

    labelOptions,
    getLabelOptions,
    validateLabels,
    trafficPoliciesFormRef,
    modal,
    onCopySubmit,
    clusterCascaderList,
    labelOptionsLoading,
    showMoreNotice,
    columns,
    onSubmitDistributeFrom,
    onDeleteDistributeFrom,
    onSubmitDistributeTo,
    onDeleteDistributeTo,
    batchCopyModalVisible
  }
}
