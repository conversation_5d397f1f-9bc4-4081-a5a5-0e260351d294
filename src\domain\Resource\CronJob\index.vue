<script lang="ts" setup>
import { Space, ViewYaml, ProTable, LinkButton, ResourceForm } from '@/components'
import { EventTable, BatchCopy } from '@/domain/Resource'
import { EnumResourceType } from '@/components/system-search/config'

import { TABLE_COLUMNS, RELATED_JOB_COLUMNS } from './setting'
import useCronJobService from './useCronJobService'
import { getCurrentInstance, ref } from 'vue'

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  onSubmitSuccess,
  onViewDetail,
  detailInfo,
  detailModal,
  K8SInstance,
  relatedJobInfo
} = useCronJobService()

const { proxy } = getCurrentInstance()
const batchCopyModalVisible = ref(false)
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>当前页面中您可以管理 CronJob 资源, 主要用于限制当前空间出口流量允许到达的空间。</p>
      </Space>
    </Alert>
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
      :on-create="onCreate"
    >
      <template #operate-buttons>
        <Button size="small" type="primary" ghost icon="md-copy" @click="() => (batchCopyModalVisible = true)">批量复制</Button>
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>

    <resource-form
      resourceType="cronjob"
      v-model="formVisible"
      resource-version="V1"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :onSubmitCallBack="onSubmitSuccess"
      notSynchronizeToUnifiedCluster
      forbiddenForm
      isSkipCheck
    />

    <Drawer title="查看详情" v-model="detailModal.visible" :closable="true" width="50">
      <Card>
        <h4>基本信息</h4>
        <Row type="flex" :gutter="20" style="margin-top: 20px">
          <Col span="10" class="info-key">Name</Col>
          <Col span="14" class="info-value">{{ detailInfo.name }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Namespace</Col>
          <Col span="14" class="info-value">{{ K8SInstance?.namespace }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Schedule</Col>
          <Col span="14" class="info-value">{{ detailInfo?.schedule }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Concurrency Policy</Col>
          <Col span="14" class="info-value">{{ detailInfo?.concurrencyPolicy }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Suspend</Col>
          <Col span="14" class="info-value">{{ detailInfo?.suspend }}</Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <h4>关联Job</h4>

        <Table style="margin-top: 16px" :columns="RELATED_JOB_COLUMNS" :data="relatedJobInfo">
          <template #ops="{ row }">
            <LinkButton @click="() => onViewYaml(row, 'job')" text="YAML" />
          </template>
        </Table>
      </Card>
      <Card style="margin-top: 16px">
        <EventTable :uuid="detailModal.data.uuid" :clusterId="K8SInstance?.clusterId" />
      </Card>
    </Drawer>

    <view-yaml
      resourceType="cronjob"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V1"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
    <BatchCopy v-model="batchCopyModalVisible" resourceType="CronJob" />
  </div>
</template>
