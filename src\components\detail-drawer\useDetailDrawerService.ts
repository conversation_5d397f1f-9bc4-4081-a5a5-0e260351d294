import { Ref, watch } from 'vue'
import { DetailDrawerProps } from './type'
import { useRequest } from 'vue-request'
import { useGet } from '@/libs/service.request'

import Config from '@/config'

export default function useDetailDrawerService(props: DetailDrawerProps, visible: Ref<Boolean>) {
  const {
    run: getResourceDetail,
    data: detailData,
    loading: detailLoading
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.Resource}${props.resourceType}/get`, {
        params: {
          clusterId: props.resourceEntity.clusterId,
          namespace: props.resourceEntity.namespace,
          resourceName: props.resourceEntity.resourceName
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => {
        return props.onInitFormat ? props.onInitFormat(res.data.data) : res.data.data
      }
    }
  )

  watch(
    () => props.value,
    () => {
      if (props.value) {
        getResourceDetail()
      }
    }
  )

  return {
    detailData,
    detailLoading
  }
}
