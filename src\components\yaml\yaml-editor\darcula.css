.cm-s-darcula {
  font-family: Consolas, Menlo, Monaco, '<PERSON><PERSON>', 'Liberation Mono', 'DejaVu Sans Mono',
    'Bitstream Vera Sans Mono', 'Courier New', monospace, serif;
}
.cm-s-darcula.CodeMirror {
  background: #1e1e1e;
  color: #cc7832;
  /*   color: #35b392; */
}

.cm-s-darcula span.cm-meta {
  color: #cc7832;
}
.cm-s-darcula span.cm-number {
  color: #cc7832;
}
.cm-s-darcula span.cm-keyword {
  color: #35b392;
  line-height: 1em;
  font-weight: bold;
}
.cm-s-darcula span.cm-def {
  color: #cc7832;
  font-style: italic;
}
.cm-s-darcula span.cm-variable {
  color: #cc7832;
}
.cm-s-darcula span.cm-variable-2 {
  color: #cc7832;
}
.cm-s-darcula span.cm-variable-3 {
  color: #cc7832;
}
.cm-s-darcula span.cm-type {
  color: #cc7832;
  font-weight: bold;
}
.cm-s-darcula span.cm-property {
  color: #35b392;
}
.cm-s-darcula span.cm-operator {
  color: #cc7832;
}
.cm-s-darcula span.cm-string {
  color: #cc7832;
}
.cm-s-darcula span.cm-string-2 {
  color: #cc7832;
}
.cm-s-darcula span.cm-comment {
  color: #cc7832;
  font-style: italic;
}
.cm-s-darcula span.cm-link {
  color: #35b392;
}
.cm-s-darcula span.cm-atom {
  color: #35b392;
}
.cm-s-darcula span.cm-error {
  color: #bc3f3c;
}
.cm-s-darcula span.cm-tag {
  color: #cc7832;
  font-weight: bold;
  font-style: italic;
  text-decoration: underline;
}
.cm-s-darcula span.cm-attribute {
  color: #cc7832;
}
.cm-s-darcula span.cm-qualifier {
  color: #cc7832;
}
.cm-s-darcula span.cm-bracket {
  color: #cc7832;
}

.cm-s-darcula span.cm-special {
  color: #ff9e59;
}
.cm-s-darcula span.cm-matchhighlight {
  color: #ffffff;
  background-color: rgba(50, 89, 48, 0.7);
  font-weight: normal;
}
.cm-s-darcula span.cm-searching {
  color: #ffffff;
  background-color: rgba(61, 115, 59, 0.7);
  font-weight: normal;
}

.cm-s-darcula .CodeMirror-cursor {
  border-left: 1px solid #a9b7c6;
}
.cm-s-darcula .CodeMirror-activeline-background {
  background: #323232;
}
.cm-s-darcula .CodeMirror-gutters {
  background: #1e1e1e;
  border-right: 1px solid #313335;
}
.cm-s-darcula .CodeMirror-guttermarker {
  color: #ffee80;
}
.cm-s-darcula .CodeMirror-guttermarker-subtle {
  color: #d0d0d0;
}
.cm-s-darcula .CodeMirrir-linenumber {
  color: #606366;
}
.cm-s-darcula .CodeMirror-matchingbracket {
  background-color: #3b514d;
  color: #ffef28 !important;
  font-weight: bold;
}

.cm-s-darcula div.CodeMirror-selected {
  background: red;
}

.CodeMirror-hints.darcula {
  font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
  color: #9c9e9e;
  background-color: #3b3e3f !important;
}

.CodeMirror-hints.darcula .CodeMirror-hint-active {
  background-color: #494d4e !important;
  color: #9c9e9e !important;
}

.cm-s-darcula div.CodeMirror-selected {
  background: rgb(0 116 254);
  color: #f9f9f9;
}
.cm-s-darcula .CodeMirror-line::selection,
.cm-s-darcula .CodeMirror-line > span::selection,
.cm-s-darcula .CodeMirror-line > span > span::selection {
  background: rgb(0 116 254);
  color: #f9f9f9;
}
.cm-s-darcula .CodeMirror-line::-moz-selection,
.cm-s-darcula .CodeMirror-line > span::-moz-selection,
.cm-s-darcula .CodeMirror-line > span > span::-moz-selection {
  background: rgb(0 116 254);
  color: #f9f9f9;
}

/* .CodeMirror-merge-r-chunk {
  background: #333333;
}
.CodeMirror-merge-r-chunk-end,
.CodeMirror-merge-r-chunk-start {
  border-color: #333333 !important;
} */
.CodeMirror-merge-r-chunk {
  background: rgba(30, 144, 255, 0.5) !important;
}
.CodeMirror-merge-r-chunk-start {
  border-top: 1px solid dodgerblue !important;
}
.CodeMirror-merge-r-chunk-end {
  border-bottom: 1px solid dodgerblue !important;
}
.CodeMirror-merge-r-connect {
  fill: rgba(30, 144, 255, 0.5) !important;
  stroke: rgba(30, 144, 255, 0.5) !important;
  stroke-width: 1px !important;
}

.CodeMirror-merge-l-chunk {
  background: rgba(30, 144, 255, 0.5) !important;
}
.CodeMirror-merge-l-chunk-start {
  border-top: 1px solid dodgerblue !important;
}
.CodeMirror-merge-l-chunk-end {
  border-bottom: 1px solid dodgerblue !important;
}
.CodeMirror-merge-l-connect {
  fill: rgba(30, 144, 255, 0.5) !important;
  stroke: rgba(30, 144, 255, 0.5) !important;
  stroke-width: 1px !important;
}

.CodeMirror-merge-l-chunk {
  background: rgba(30, 144, 255, 0.5) !important;
}
.CodeMirror-merge-r-chunk {
  background: rgba(30, 144, 255, 0.5) !important;
}
.CodeMirror-merge-l-chunk-start {
  border-top: 1px solid dodgerblue !important;
}
.CodeMirror-merge-r-chunk-start {
  border-top: 1px solid dodgerblue !important;
}
.CodeMirror-merge-l-chunk-end {
  border-bottom: 1px solid dodgerblue !important;
}
.CodeMirror-merge-r-chunk-end {
  border-bottom: 1px solid dodgerblue !important;
}

.CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
  height: 550px !important;
}

.CodeMirror-merge-2pane .CodeMirror-merge-gap {
  width: 8px !important;
}
.CodeMirror-merge-2pane .CodeMirror-merge-pane {
  width: calc(50% - 4px) !important;
}
