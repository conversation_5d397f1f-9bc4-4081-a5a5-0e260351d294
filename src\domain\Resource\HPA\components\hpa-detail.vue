<template>
  <div>
    <Card>
      <h4>基本信息</h4>
      <Row type="flex" :gutter="20" style="margin-top: 20px">
        <Col span="10" class="info-key">名称</Col>
        <Col span="14" class="info-value">{{ innerDetail.name }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">命名空间</Col>
        <Col span="14" class="info-value">{{ innerDetail.namespace }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">当前副本数</Col>
        <Col span="14" class="info-value">{{ innerDetail.replicas }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">最大副本数</Col>
        <Col span="14" class="info-value">{{ innerDetail.maxPods }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">最小副本数</Col>
        <Col span="14" class="info-value">{{ innerDetail.minPods }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">创建时间</Col>
        <Col span="14" class="info-value">{{ innerDetail.creationTimestamp | dateFormat }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">引用目标</Col>
        <Col span="14" class="info-value">
          <span>
            <template v-if="innerDetail.scaleTargetRef !== undefined">
              <a
                @click="handleGotoDeployment"
                v-if="innerDetail.scaleTargetRef.kind === 'Deployment'"
                style="cursor: pointer; color: #2a7cc3"
              >
                {{ innerDetail.scaleTargetRef.kind }} / {{ innerDetail.scaleTargetRef.name }}
                <Icon type="ios-share-alt" />
              </a>
              <a v-else>{{ innerDetail.scaleTargetRef.kind }} / {{ innerDetail.scaleTargetRef.name }}</a>
            </template>
          </span>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px">
      <h4>扩容指标</h4>
      <Table style="margin-top: 16px" :columns="metricsColumns" :data="metricsTableData"></Table>
    </Card>
    <Card style="margin-top: 16px">
      <EventTable :uuid="detail.uuid" :clusterId="clusterId" />
    </Card>
  </div>
</template>

<script>
import { TimeTrans } from '@/libs/tools'
import { EventTable } from '@/domain/Resource'
export default {
  name: 'hpa-detail',
  components: { EventTable },
  props: {
    detail: Object,
    clusterId: String,
    namespace: String,
    clusterName: String,
    cluster: String
  },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    }
  },
  data() {
    return {
      innerDetail: {},
      loading: false,
      metricsColumns: [
        {
          title: 'Type',
          key: 'type',
          tooltip: true,
          tooltipTheme: 'light',
          width: 100
        },
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'TargetType',
          key: 'targetType',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 140
        },
        {
          title: 'AverageUtilization',
          key: 'averageUtilization',
          tooltip: true,
          tooltipTheme: 'light',
          width: 160
        },
        {
          title: 'AverageValue',
          key: 'averageValue',
          tooltip: true,
          tooltipTheme: 'light',
          width: 140
        },
        {
          title: 'Value',
          key: 'value',
          tooltip: true,
          tooltipTheme: 'light',
          width: 100
        }
      ],
      metricsTableData: []
    }
  },
  methods: {
    handleGotoDeployment() {
      console.log(`cluster: ${this.cluster}`)
      let r = this.$router.resolve({
        path: '/kubernetes/namespace/deployment-detail',
        query: {
          clusterId: this.clusterId,
          namespace: this.namespace,
          deployment: this.detail.scaleTargetRef.name,
          cluster: this.clusterName,
          uuid: this.detail.scaleTargetRef.uuid
        }
      })
      window.open(r.href, '_blank')
    }
  },
  mounted() {
    this.innerDetail = this.detail
    this.metricsTableData = this.detail.targets
  }
}
</script>

<style scoped></style>
