import { getCurrentInstance, onMounted, ref } from 'vue'

import Config from '@/config'
import { useGet, usePost } from '@/libs/service.request'
import { useRoute, useRouter, useStore } from '@/libs/useVueInstance'
import { YamlGVR } from '../../config'
import { Pod, UrlParams } from './type'
import useReplicaCountService from '../../useReplicaCountService'
import useSubscribeService from '../../useSubscribeService'
import { genNonDuplicateID } from '@/libs/tools'

export enum EnumTabKey {
  meta = 'meta',
  runtime = 'runtime',
  service = 'service',
  hpa = 'hpa',
  configmap = 'configmap',
  secret = 'secret',
  pvc = 'pvc',
  serviceMesh = 'serviceMesh',
  monitor = 'monitor'
}

export default function useStatefulSetDetailService() {
  const { proxy } = getCurrentInstance()
  const route = useRoute()
  const router = useRouter()
  const store = useStore()

  const urlParams = ref<UrlParams>({} as UrlParams)
  const yamlModal = ref({ data: '', visible: false })

  const loading = ref(false)
  const chosenTab = ref('runtime')
  const currentPod = ref<Pod>({} as Pod)

  const logMergeModalVisible = ref(false)

  const tabKeyMap = ref<Record<EnumTabKey, string>>({
    [EnumTabKey.meta]: genNonDuplicateID(),
    [EnumTabKey.runtime]: genNonDuplicateID(),
    [EnumTabKey.service]: genNonDuplicateID(),
    [EnumTabKey.hpa]: genNonDuplicateID(),
    [EnumTabKey.configmap]: genNonDuplicateID(),
    [EnumTabKey.secret]: genNonDuplicateID(),
    [EnumTabKey.pvc]: genNonDuplicateID(),
    [EnumTabKey.serviceMesh]: genNonDuplicateID(),
    [EnumTabKey.monitor]: genNonDuplicateID()
  })

  // const {
  //   replicaCount,
  //   onOpenReplicaCountModal,
  //   onSetReplicaCount: onSetReplicaCountFunc,
  //   getReplicaCount
  // } = useReplicaCountService('statefulset', () => {
  //   onReloadResource(EnumTabKey.runtime)
  // })

  const { isSubscribe, onSubscribeOrNot: onSubscribeOrNotFunc, getSubscribeStatus } = useSubscribeService('statefulset')

  const gotoAppDeployment = () => {
    // 返回时，填充C/N
    // setClusterNamespace(urlParams.value.cluster, urlParams.value.clusterId, urlParams.value.namespace)
    router.push({ path: '/ai/resource/rayjob' })
  }

  const setClusterNamespace = (cluster, clusterId, namespace) => {
    store.commit('setCurrentCluster', { uid: store.state.user.userId, cluster })
    store.commit('setCurrentClusterId', { uid: store.state.user.userId, clusterId })
    store.commit('setCurrentNamespace', { uid: store.state.user.userId, namespace })
  }

  const onOpenYAML = async () => {
    const res = await useGet(`${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`, {
      params: {
        ...YamlGVR.RayJob,
        cluster_id: urlParams.value.clusterId,
        namespace: urlParams.value.namespace,
        resource_name: urlParams.value.name,
        is_edit: false
      }
    })
    if (res.success) {
      yamlModal.value = { visible: true, data: res.data.data }
    }
  }

  const onSubscribeOrNot = () =>
    onSubscribeOrNotFunc(urlParams.value.clusterId, urlParams.value.namespace, urlParams.value.name)

  // const onSetReplicaCount = () =>
  //   onSetReplicaCountFunc(urlParams.value.clusterId, urlParams.value.namespace, urlParams.value.name, 6000)

  const rolloutRestart = () => {
    proxy.$Modal.confirm({
      title: 'Tips',
      content: `<p>确认重建 ${urlParams.value.name} ?</p>`,
      loading: true,
      onOk: async () => {
        const res = await usePost(`${Config.Api.Base}${Config.Api.RolloutStartStatefulset}`, {
          clusterId: urlParams.value.clusterId,
          namespace: urlParams.value.namespace,
          name: urlParams.value.name
        })

        if (res.success) {
          proxy.$Message.success('重启成功！')
          onReloadResource()
        }
        proxy.$Modal.remove()
      }
    })
  }

  const onReloadResource = (resourceName?: EnumTabKey) => {
    if (!resourceName) {
      // 更新全部
      tabKeyMap.value = {
        [EnumTabKey.meta]: genNonDuplicateID(),
        [EnumTabKey.runtime]: genNonDuplicateID(),
        [EnumTabKey.service]: genNonDuplicateID(),
        [EnumTabKey.hpa]: genNonDuplicateID(),
        [EnumTabKey.configmap]: genNonDuplicateID(),
        [EnumTabKey.secret]: genNonDuplicateID(),
        [EnumTabKey.pvc]: genNonDuplicateID(),
        [EnumTabKey.serviceMesh]: genNonDuplicateID(),
        [EnumTabKey.monitor]: genNonDuplicateID()
      }
    } else {
      // 更新指定
      tabKeyMap.value = {
        ...tabKeyMap.value,
        [resourceName]: genNonDuplicateID()
      }
    }
  }

  const onResourceCardClick = (resourceName: string) => {
    if (resourceName === 'servicemesh') {
      proxy.$Message.warning('暂不支持查看服务网格详情')
      return
    }

    chosenTab.value = resourceName
  }

  onMounted(() => {
    urlParams.value = {
      clusterId: route.query.clusterId as string,
      cluster: route.query.cluster as string,
      namespace: route.query.namespace as string,
      name: route.query.name as string,
      uuid: route.query.uuid as string
    }
    getSubscribeStatus(urlParams.value.clusterId, urlParams.value.namespace, urlParams.value.name)
    // getReplicaCount(urlParams.value.clusterId, urlParams.value.namespace, urlParams.value.name)
  })
  return {
    urlParams,
    gotoAppDeployment,

    onOpenYAML,
    yamlModal,

    isSubscribe,
    onSubscribeOrNot,

    rolloutRestart,
    loading,

    // replicaCount,
    // onOpenReplicaCountModal,
    // onSetReplicaCount,

    chosenTab,
    onResourceCardClick,
    currentPod,

    tabKeyMap,
    onReloadResource,

    logMergeModalVisible
  }
}
