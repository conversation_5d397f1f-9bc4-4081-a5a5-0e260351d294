import Config from '@/config'
import { useGet } from '@/libs/service.request'
import { InjectionKey, ref } from 'vue'
import { useRequest } from 'vue-request'

export const UserInfoServiceProvide = Symbol('UserInfoServiceProvide') as InjectionKey<UserInfoServiceReturn>

export type UserInfoServiceReturn = ReturnType<typeof useUserInfoService>

export default function useUserInfoService() {
  const {
    data: userInfo,
    loading,
    run: getUserInfo
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.UserProfile}`)
    },
    {
      initialData: {},
      formatResult(res) {
        return res.data
      }
    }
  )
  return {
    userInfo,
    loading,
    getUserInfo
  }
}
