import { relativeTime } from '@/libs/tools'

export const COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Namespace',
    key: 'namespace',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Status',
    key: 'status',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Volume',
    key: 'volume',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Capacity',
    key: 'capacity',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'AccessModes',
    key: 'accessModes',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'StorageClass',
    key: 'storageClass',
    tooltip: true,
    tooltipTheme: 'light'
  },

  {
    title: 'Age',
    key: 'creationTimestamp',

    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 160,
    align: 'center'
  }
]
