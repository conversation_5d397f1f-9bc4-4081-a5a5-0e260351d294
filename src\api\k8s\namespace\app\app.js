import axios from '@/libs/api.request'


export const ApiV2DeploymentList = (data) => {
  return axios.request({
    url: `/api/v2/resource/deployment/list`,
    method: 'post',
    data: data
  })
}

export const ApiV2DeploymentRestart = (data) => {
  return axios.request({
    url: `/api/v2/resource/deployment/restart`,
    method: 'post',
    data: data
  })
}

export const ApiStatefulSetList = (data) => {
  return axios.request({
    url: `/api/v1/resource/statefulset/list`,
    method: 'post',
    data: data
  })
}

export const ApiStatefulSetGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/statefulset/get`,
    method: 'get',
    params: params
  })
}

export const ApiStatefulSetRestart = (data) => {
  return axios.request({
    url: `/api/v1/resource/statefulset/restart`,
    method: 'post',
    data: data
  })
}

export const ApiStatefulSetPodDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/statefulset/pod/delete`,
    method: 'delete',
    data: data
  })
}

export const ApiStatefulSetPodList = (params) => {
  return axios.request({
    url: `/api/v1/resource/statefulset/pod/list`,
    method: 'get',
    params: params
  })
}



