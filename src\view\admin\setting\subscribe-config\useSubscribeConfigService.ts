import { ActionType, TableRequest } from '@/components/pro-table'
import { PageList, useDelete, useGet, usePost } from '@/libs/service.request'
import { getCurrentInstance, ref } from 'vue'
import Config from '@/config'

export interface SubscribeConfig {
  created_at?: string
  deleted_at?: string
  id?: number
  kind?: string
  same_event_interval?: number
  send_interval?: number
  updated_at?: string
}

interface Modal {
  visible: boolean
  data?: SubscribeConfig
  status: 'edit' | 'blank'
}

const columns = [
  {
    title: 'Id',
    key: 'id',
    width: 50,
    tooltip: true
  },
  {
    title: 'Kind',
    key: 'kind',
    tooltip: true
  },
  {
    title: 'SameEventInterval (s)',
    key: 'same_event_interval'
  },
  {
    title: 'SendInterval  (s)',
    key: 'send_interval'
  },
  {
    title: 'CreatedAt',
    key: 'created_at',
    tooltip: true
  },
  {
    title: 'UpdatedAt',
    key: 'updated_at',
    tooltip: true
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]
export default function useSubscribeConfigService() {
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const formRef = ref()
  const { proxy } = getCurrentInstance()
  const modal = ref<Modal>({ visible: false, status: 'blank', data: {} })

  const getTableData: TableRequest = async () => {
    const res = await useGet<PageList<SubscribeConfig[]>>(`${Config.Api.Base}${Config.Api.GetSubscribeConfig}`)
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onCreate = () => {
    modal.value = {
      visible: true,
      status: 'blank',
      data: {
        kind: 'Deployment',
        same_event_interval: 30,
        send_interval: 30
      }
    }
  }
  const onEdit = (record: SubscribeConfig) => {
    modal.value = {
      visible: true,
      status: 'edit',
      data: record
    }
  }
  const onDelete = (record: SubscribeConfig) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.kind}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteSubscribeConfig}`, {
          params: {
            pid: record.id
          }
        })
        if (res.success) {
          proxy.$Message.success('删除成功！')
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onSubmit = async () => {
    formRef.value.validate().then(async (valid) => {
      if (valid) {
        const { kind, same_event_interval, send_interval } = modal.value.data
        const res = await usePost(
          `${Config.Api.Base}${
            modal.value.status === 'blank' ? Config.Api.CreateSubscribeConfig : Config.Api.EditSubscribeConfig
          }`,
          {
            ...(modal.value.status === 'blank' ? {} : { pid: modal.value.data.id }),
            kind,
            same_event_interval,
            send_interval
          }
        )
        if (res.success) {
          proxy.$Message.success(`${modal.value.status === 'blank' ? '创建' : '编辑'}成功！`)
          refObject.tableRef.value.reload()
          modal.value = {
            visible: false,
            data: {},
            status: 'blank'
          }
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      } else {
        modal.value.visible = true
      }
    })
  }

  return { columns, getTableData, refObject, onCreate, onEdit, onDelete, onSubmit, modal, formRef }
}
