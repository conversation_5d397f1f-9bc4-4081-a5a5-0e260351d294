<template>
  <Container>
    <Alert show-icon>
      在当前页面，您可以管理系统使用的在线配置。所做的配置更改会实时影响系统的运行, 请谨慎配置。
    </Alert>
    <Card :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            ref="input"
            v-model="page.s_key"
            clearable
            placeholder="搜索 key"
            search
            style="width: 100%"
            @on-search="handleSearch"
          />
        </Col>
      </Row>
      <Row type="flex">
        <Col span="24">
          <Button type="primary" size="small" icon="ios-add" @click="handleCreate">创建</Button>
          <Button size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable" style="margin-left: 16px"
            >刷新</Button
          >
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px" :bordered="false">
      <Table
        size="small"
        :loading="loading.table"
        :columns="columns"
        :data="data"
        @on-filter-change="tableFilterChange"
      ></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <Modal v-model="editModal" :title="editModalTitle" :loading="loading.edit" @on-ok="editTable">
      <Form :model="editModalForm" ref="editModalForm" label-position="top">
        <FormItem label="Key" prop="key">
          <Input
            v-if="editModalState === 'create'"
            v-model="editModalForm.key"
            placeholder="Enter something..."
          ></Input>
          <Tag v-else color="primary">{{ editModalForm.key }} </Tag>
        </FormItem>
        <FormItem label="Value" prop="value">
          <Input v-model="editModalForm.value" placeholder="Enter something..."></Input>
        </FormItem>
        <FormItem label="分类 (Category)" prop="category">
          <Input v-model="editModalForm.category" placeholder="Enter something..."></Input>
        </FormItem>
        <FormItem label="描述 (Desc)" prop="desc">
          <Input v-model="editModalForm.desc" placeholder="Enter something..."></Input>
        </FormItem>
      </Form>
    </Modal>
  </Container>
</template>

<script>
import { Container } from '@/components'
import { ApiUserList } from '@/api/user'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { color } from '@/libs/consts'
import { apiConfigCreate, apiConfigDelete, apiConfigList, apiConfigUpdate } from '@/api/setting/config'

import LinkButton from '@/components/link-button'
import Space from '@/components/space'

export default {
  name: 'settingConfig',
  components: { Container },
  data() {
    return {
      editModal: false,
      editModalTitle: '创建',
      editModalState: 'create',
      editModalForm: {
        id: 0,
        key: '',
        value: '',
        category: '',
        desc: ''
      },
      loading: {
        table: false,
        edit: true
      },
      columns: [
        {
          title: 'ID',
          key: 'id',
          tooltip: true,
          tooltipTheme: 'light',
          width: 90
        },
        {
          title: 'Key',
          key: 'key',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'Value',
          key: 'value',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'Category',
          key: 'category',
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center',
          width: 120,
          filters: [],
          filterMultiple: false,
          filterRemote: async (values, row) => {
            console.log(values)
            this.page.page = 1
            if (values.length === 0) {
              delete this.page.f_category
              this.reloadTable()
              return
            }
            this.page.f_category = values[0]
            this.reloadTable()
          }
        },
        {
          title: 'Desc',
          key: 'desc',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'UpdatedAt',
          key: 'updated_at',
          width: 180,
          align: 'center'
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h(Space, { props: { justify: true } }, [
              h(LinkButton, {
                props: {
                  text: '配置'
                },
                on: {
                  click: () => {
                    this.editModalState = 'update'
                    this.editModal = true
                    this.editModalTitle = '更新'
                    this.editModalForm = {
                      id: params.row.id,
                      key: params.row.key,
                      value: params.row.value,
                      category: params.row.category,
                      desc: params.row.desc
                    }
                  }
                }
              }),
              h(
                LinkButton,
                {
                  props: {
                    text: '删除',
                    type: 'danger'
                  },
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>Confirm delete key: ${params.row.key}</p>`,
                        loading: true,
                        onOk: async () => {
                          await this.deleteKV(params.row.id)
                          this.reloadTable()
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        s_key: '',
        total: 0
      }
    }
  },
  methods: {
    tableFilterChange(row) {
      console.log('row', row)
      // delete this.page.s_category
      // this.reloadTable()
    },
    async createKV() {
      await apiConfigCreate({
        key: this.editModalForm.key,
        value: this.editModalForm.value,
        category: this.editModalForm.category,
        desc: this.editModalForm.desc
      })
        .then((res) => {
          noticeSucceed(this, 'succeed')
          this.reloadTable()
          this.editModal = false
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async editKV() {
      await apiConfigUpdate({
        pid: this.editModalForm.id,
        value: this.editModalForm.value,
        category: this.editModalForm.category,
        desc: this.editModalForm.desc
      })
        .then((res) => {
          noticeSucceed(this, 'succeed')
          this.reloadTable()
          this.editModal = false
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async editTable() {
      if (this.editModalState === 'create') {
        await this.createKV()
        return
      }
      await this.editKV()
    },
    async deleteKV(pid) {
      await apiConfigDelete({ pid: pid })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    handleCreate() {
      console.log(this.$refs)
      this.$refs['editModalForm'].resetFields()
      this.editModal = true
      this.editModalState = 'create'
    },
    async reloadTable() {
      this.loading.table = true
      await apiConfigList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
          var filters = data.list.map((item) => {
            return item.category
          })
          var filters2 = filters.filter((x, index, self) => self.indexOf(x) === index)
          this.columns[3].filters = filters2.map((item) => {
            return {
              label: item,
              value: item
            }
          })
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
      this.loading.table = false
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    }
  },
  mounted() {
    this.reloadTable()
    this.$refs.input.focus({
      cursor: 'start'
    })
  }
}
</script>

<style scoped></style>
