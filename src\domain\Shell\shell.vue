<script lang="ts" setup>
import { Space, Empty } from '@/components'
import useShellService from './useShellService'
const {
  urlParams,
  split,
  namespace,
  namespaceList,

  shellList,
  onCloseShell,
  onContextMenu,
  onCopyShell,
  onReloadShell,
  currentShellKey,
  filterPodName,
  clusterEnvSetting,
  loading,
  data,
  onContentScroll,
  noMore,
  loadingMore,
  refresh,
  onNamespaceChange
} = useShellService()
</script>

<template>
  <div class="wrapper">
    <Space class="top">
      <img src="@/assets/images/logo-tiny.png" alt="logo" />
      <span>{{ urlParams?.cluster }}</span>
      <Tag :color="clusterEnvSetting?.color">{{ clusterEnvSetting?.title }}</Tag>
    </Space>
    <div class="content-wrapper">
      <Split v-model="split" min="300px" max="450px">
        <div slot="left" class="split-pane left">
          <Select v-model="namespace" class="namespace-selection" @on-change="onNamespaceChange" filterable>
            <Option v-for="item in namespaceList" :value="item.name" :key="item.name">{{ item.name }}</Option>
          </Select>
          <Input
            class="pod-name-input"
            placeholder="请输入关键词搜索"
            size="small"
            clearable
            search
            v-model="filterPodName"
            @on-enter="refresh"
          />
          <Icon type="md-refresh" @click="refresh" class="refresh-icon" />

          <Space
            v-if="!!data?.list?.length || loading"
            class="tree-wrapper"
            direction="vertical"
            @scroll="onContentScroll"
            :size="8"
          >
            <Tree :data="data?.list" expand-node></Tree>
            <div class="tree-wrapper-footer" v-if="noMore">全部数据加载完毕 ~</div>
            <div class="tree-wrapper-footer" v-else-if="loadingMore || loading">
              <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>加载中...
            </div>
            <div class="tree-wrapper-footer" v-else>滚动到底，加载更多数据 ~</div>
          </Space>
          <Empty v-else />
        </div>

        <div slot="right" class="split-pane right">
          <Tabs
            v-if="shellList.length > 0"
            v-model="currentShellKey"
            type="card"
            size="small"
            :animated="false"
            closable
            @on-contextmenu="onContextMenu"
            @on-tab-remove="onCloseShell"
          >
            <TabPane
              v-for="(item, index) in shellList"
              :key="item.key"
              :index="index + 1"
              :label="item.label"
              :name="item.key"
              context-menu
              icon="md-desktop"
            >
              <div class="shell-windows-wrapper" :key="item.key">
                <iframe :src="item.url" class="shell-windows" :key="item.key"></iframe>
              </div>
            </TabPane>
            <template slot="contextMenu">
              <DropdownItem @click.native="onCopyShell">复制窗口</DropdownItem>
              <DropdownItem @click.native="onReloadShell">重新连接</DropdownItem>
            </template>
          </Tabs>
        </div>
      </Split>
    </div>
  </div>
</template>

<style lang="less" scoped>
.wrapper {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  .top {
    height: 40px;
    align-items: center;
    background: #463e3e;
    color: #f0f0f1;
    padding-left: 16px;
    span {
      font-weight: 600;
    }
    img {
      height: 30px;
    }
  }

  .content-wrapper {
    width: 100vw;
    height: ~'calc(100vh - 40px)';
    /deep/.ivu-split-trigger-vertical {
      background: #1f1b1b;
      border-color: #1f1b1b;
      width: 0px;
      .ivu-split-trigger-bar-con {
        display: none;
      }
      .ivu-split-trigger-bar {
        background-color: #8f8f8f;
      }
    }
    .split-pane {
      background-color: #3a3333;
      color: #fff;
      position: relative;
      height: 100%;
      .box-title {
        font-weight: 600;
        font-size: 14px;
      }
    }

    .split-pane.left {
      display: flex;
      flex-direction: column;
      > :not(:last-child) {
        margin-bottom: 8px;
      }
      .namespace-selection {
        width: 100%;
        background: transparent;
        color: #fff;
        position: relative;
        &::before {
          content: '命名空间';
          position: absolute;
          width: 56px;
          height: 12px;
          left: 4px;
          top: 6px;
          color: #d1d1d1;
          font-size: 12px;
        }
        /deep/input {
          color: #fff;
        }
        /deep/.ivu-select-selection {
          background: transparent;
          border-width: 0 0 1px 0;
          border-radius: 0;
          box-shadow: unset;
          padding-left: 56px;
          .ivu-select-arrow {
            color: #fff;
          }
        }
      }
      .pod-name-input {
        &::before {
          content: 'PodName';
          position: absolute;
          width: 48px;
          height: 12px;
          left: 4px;
          top: 6px;
          color: #d1d1d1;
          font-size: 12px;
        }
        /deep/.ivu-input {
          background: transparent;
          border-width: 0 0 1px 0;
          border-radius: 0;
          box-shadow: unset;
          color: #fff;
          padding-left: 64px;
          padding-right: 48px;
        }
        /deep/.ivu-icon {
          color: #fff;
        }
        /deep/.ivu-icon-ios-close-circle,
        /deep/.ivu-icon-ios-search {
          position: absolute;
          right: 24px;
        }
        /deep/.ivu-input-suffix {
          right: 20px;
        }
      }
      .refresh-icon {
        position: absolute;
        right: 8px;
        top: 45px;
        cursor: pointer;
        &:hover {
          color: #66bdff;
        }
      }
      .tree-wrapper {
        height: ~'calc(100% - 32px - 24px - 20px)';
        overflow: auto;
        position: relative;
        &::-webkit-scrollbar-track-piece {
          background-color: #3a3333;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #8f8f8f;
          background-clip: padding-box;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
        &::-webkit-scrollbar-corner {
          background-color: #8f8f8f;
        }

        .tree-wrapper-footer {
          height: 24px;
          color: #999;
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          font-size: 12px;

          .spin-icon-load {
            margin-right: 8px;
            color: #2a7cc3;
            animation: ani-demo-spin 1s linear infinite;
          }
        }
        .ivu-tree {
          /deep/.ivu-tree-title {
            width: ~'calc(100% - 24px)';
            color: #fff;
            &:hover {
              background-color: #777777;
            }
          }
          /deep/.tree-title {
            align-items: center;
            font-size: 12px;
            > span {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 90%;
            }
          }
          /deep/.ivu-tree-title .selected {
            & {
              background: #777777;
              border-radius: 2px;
              margin-left: -4px;
              padding-left: 4px;
              width: ~'calc(100% + 8px)';
            }
            .ivu-icon {
              color: #5babff;
            }
          }
        }
      }
    }
    .split-pane.right {
      background-color: #1f1b1b;
      /deep/.ivu-tabs-bar {
        margin: 0;
        border: none;
        background: #3b3434;
      }
      /deep/.ivu-tabs-tab {
        background: transparent;
        color: #e9e9e9;
        font-size: 12px;
        border-width: 0 1px 0 0;
        border-radius: 0;
        border-color: #9a9a9a;
        font-weight: 400;
        padding: 4px 12px;
        margin: 0;
        position: relative;
        &.ivu-tabs-tab-active::before {
          content: '';
          width: 100%;
          height: 2px;
          position: absolute;
          background: #3ca9fb;
          bottom: 0;
          left: 0;
        }
        .ivu-tabs-close {
          color: #e9e9e9 !important;
          &:hover {
            color: #66bdff !important;
          }
        }
      }

      .shell-windows-wrapper {
        height: ~'calc(100vh - 30px - 32px - 16px)';
        margin-top: 8px;
        margin-left: 8px;
        .shell-windows {
          width: 100%;
          height: 100%;
          border: 0;
        }
      }
    }
  }
}
</style>
