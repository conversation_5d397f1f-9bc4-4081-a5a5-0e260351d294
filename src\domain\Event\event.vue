<script lang="ts" setup>
import useEventService from './useEventService'
import { Space, ProTable, Container, K8SSwitch } from '@/components'
import dayjs from 'dayjs'

const {
  refObject,
  getTableData,
  COLUMNS,
  onSearch,
  cluster,
  clusterId,
  clusterList,

  namespace,
  eventType,
  resourceName,
  resourceKind,
  timeRange,
  message,
  namespaceTypeKindData,
  resourceNameList,
  getResourceName,
  onClusterChange,
  namespaceList
} = useEventService()
</script>

<template>
  <Container>
    <div class="event-mgt-wrapper">
      <Alert show-icon>记录了所有集群的k8s事件,可以通过集群命名空间和资源维度快速检索历史事件。</Alert>
      <Space class="search-wrapper">
        <Select
          v-model="clusterId"
          :label-in-value="true"
          class="search-item"
          filterable
          clearable
          placeholder="请选择集群"
          @on-change="onClusterChange"
        >
          <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
        </Select>

        <Select
          v-model="resourceKind"
          class="search-item"
          filterable
          clearable
          :disabled="!cluster"
          placeholder="请选择资源类型"
          @on-change="(val) => getResourceName()"
        >
          <Option v-for="item in namespaceTypeKindData?.resourceKinds" :value="item" :key="item">{{ item }}</Option>
        </Select>

        <Select
          v-model="namespace"
          class="search-item"
          filterable
          clearable
          :disabled="!cluster"
          placeholder="请选择命名空间"
          @on-change="(val) => getResourceName()"
        >
          <Option v-for="item in namespaceList" :value="item.name" :key="item.name">{{ item.name }}</Option>
        </Select>

        <Select
          v-model="resourceName"
          class="search-item resource-name"
          filterable
          clearable
          :disabled="!resourceKind"
          placeholder="请选择资源对象"
        >
          <Option v-for="item in resourceNameList" :value="item" :key="item">{{ item }}</Option>
        </Select>

        <Select
          v-model="eventType"
          class="search-item"
          filterable
          clearable
          :disabled="!cluster"
          placeholder="请选择事件等级"
        >
          <Option v-for="item in namespaceTypeKindData?.eventTypes" :value="item" :key="item">{{ item }}</Option>
        </Select>
        <Input v-model="message" placeholder="请输入Message" style="width: 180px" clearable />
        <DatePicker
          v-model="timeRange"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm"
          placeholder="请选择时间范围"
          class="search-item time-range"
          @on-change="(val) => getResourceName()"
        />

        <Button type="primary" icon="ios-search" @click="onSearch()">搜索</Button>
      </Space>

      <div class="table-wrapper">
        <pro-table
          :columns="COLUMNS"
          :request="getTableData"
          :action-ref="refObject"
          hideSearchOperation
          :pagination="{ defaultPageSize: 50, simple: true }"
          :height="550"
        >
          <template #age="{ row }">
            {{ dayjs(row.age * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template #type="{ row }">
            <span :class="`type-item ${row.type?.toLowerCase()}`">{{ row.type }}</span>
          </template>
        </pro-table>
      </div>
    </div>
  </Container>
</template>

<style lang="less" scoped>
.event-mgt-wrapper {
  .search-wrapper,
  .table-wrapper {
    padding: 16px;
    background: #fff;
    align-items: center;
    &:hover {
      box-shadow: 0px 0px 4px 0px #ddd;
    }
  }
  .search-wrapper {
    margin-bottom: 16px;

    .search-item {
      width: 140px;
      &.resource-name {
        width: 300px;
      }
      &.time-range {
        width: 240px;
      }
    }
  }
  .type-item {
    font-weight: 600;

    &.normal {
      color: #2a7cc3;
    }
    &.warning {
      color: #ff9800;
    }
  }
  .table-wrapper /deep/div div div {
    box-shadow: none !important;
  }
}
</style>
