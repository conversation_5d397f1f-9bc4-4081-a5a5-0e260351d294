import axios from '@/libs/api.request'

export const ApiOpenAuthTokenList = () => {
  return axios.request({
    url: `/api/v1/auth-token/list`,
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiOpenAuthTokenCreate = (name, desc) => {
  return axios.request({
    url: `/api/v1/auth-token/create`,
    method: 'post',
    data: {
      "name": name,
      "desc": desc
    },
    params: {}
  })
}

export const ApiOpenAuthTokenDelete  = (token_id) => {
  return axios.request({
    url: `/api/v1/auth-token/delete/${token_id}`,
    method: 'delete',
    data: {},
    params: {}
  })
}

export const ApiOpenDocList = () => {
  return axios.request({
    url: `/api/v1/openapi/doc/list`,
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiOpenClusterList = (is_ready) => {
  return axios.request({
    url: `/api/v1/openapi/resource/cluster/list`,
    method: 'get',
    data: {},
    params: {
      is_ready
    }
  })
}

export const ApiOpenNamespaceList = (cluster_id) => {
  return axios.request({
    url: `/api/v1/openapi/resource/namespace/list`,
    method: 'get',
    data: {},
    params: {
      cluster_id
    }
  })
}

export const ApiOpenPodList = (cluster_id, namespace) => {
  return axios.request({
    url: `/api/v1/openapi/resource/pod/list`,
    method: 'get',
    data: {},
    params: {
      cluster_id,
      namespace,
    }
  })
}

export const ApiOpenContainerList = (cluster_id, namespace, pod) => {
  return axios.request({
    url: `/api/v1/openapi/resource/container/list`,
    method: 'get',
    data: {},
    params: {
      cluster_id, namespace, pod
    }
  })
}


export const ApiOpenResourceMetricsClusters = () => {
  return axios.request({
    url: `/api/v1/openapi/resource/metrics/cluster/list`,
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiOpenResourceMetricsNodes = (cluster_id) => {
  return axios.request({
    url: `/api/v1/openapi/resource/metrics/node/list`,
    method: 'get',
    data: {},
    params: {
      cluster_id
    }
  })
}


export const ApiOpenResourceMetricsClustersAmount = () => {
  return axios.request({
    url: `/api/v1/openapi/resource/metrics/cluster/amount`,
    method: 'get',
    data: {},
    params: {}
  })
}

