<script lang="ts" setup>
import { PropType } from 'vue'
import { Space } from '@/components'
import useContainerChartService, { Entity, TIME_PICKER_LIST } from './useContainerChartService'

const props = defineProps({
  reloadFlag: { type: [String, Boolean] },
  entity: { type: Object as PropType<Entity>, default: () => ({}) },
  prefix: String
})
defineEmits(['input'])

const { timePicker, containerIdMap, jumpToTelemetryDashboard, renderChart, loading } = useContainerChartService(props)
</script>

<template>
  <div>
    <Space class="operation-wrapper">
      <slot name="operation" />
      <Select v-model="timePicker">
        <Option v-for="item in TIME_PICKER_LIST" :key="item.value" :value="item.value">{{ item.label }}</Option>
      </Select>
      <Button type="primary" size="small" icon="ios-refresh" ghost @click="renderChart"> 刷新</Button>
      <Button type="primary" size="small" ghost @click="jumpToTelemetryDashboard">
        <div class="button-wrapper">
          <img src="@/assets/telemetry-logo-mini.png" alt="" />
          详细信息
        </div>
      </Button>
    </Space>
    <div class="chart-list-wrapper">
      <Spin fix v-if="loading" />
      <div class="chart-wrapper split">
        <div class="chart-title">CPU Usage（core）</div>
        <div class="chart" :id="containerIdMap['cpu usage']"></div>
      </div>
      <div class="chart-wrapper">
        <div class="chart-title">CPU Throttling</div>
        <div class="chart" :id="containerIdMap['cpu throttling']"></div>
      </div>
      <div class="chart-wrapper split">
        <div class="chart-title">Memory Usage</div>
        <div class="chart" :id="containerIdMap['mem usage']"></div>
      </div>

      <div class="chart-wrapper">
        <div class="chart-title">CPU Usage（Workload Stdvar）</div>
        <div class="chart" :id="containerIdMap['cpu usage(workload stdvar)']"></div>
      </div>
      <div class="chart-wrapper split">
        <div class="chart-title">Receive Bandwidth</div>
        <div class="chart" :id="containerIdMap['receive bandwidth']"></div>
      </div>

      <div class="chart-wrapper">
        <div class="chart-title">Transmit Bandwidth</div>
        <div class="chart" :id="containerIdMap['transmit bandwidth']"></div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.operation-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  .ivu-select {
    width: 200px;
  }
  .button-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 16px;
      margin-right: 4px;
    }
  }
  /deep/.ivu-select-selection {
    &,
    .ivu-select-selected-value {
      height: 26px;
      line-height: 26px;
    }
  }
}
.chart-list-wrapper {
  position: relative;
  max-height: 65vh;
  overflow: auto;
  padding: 16px 16px 0;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  .chart-wrapper {
    margin-bottom: 16px;
    padding: 8px 16px 0;
    height: 30vh;
    width: ~'calc(100%/2 - 8px)';
    border-radius: 4px;
    border: 1px dashed #e8e8e8;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: #fff;

    &:hover {
      box-shadow: 0px 0px 4px 0px #e8e8e8;
    }
    &.split {
      margin-right: 16px;
    }
    .chart-title {
      font-size: 14px;
      font-weight: bold;
    }
    .chart {
      height: ~'calc(100% - 24px)';
      width: 100%;
    }
  }
}
</style>
