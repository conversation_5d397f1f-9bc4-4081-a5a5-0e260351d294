import axios from '@/libs/api.request'
// 获取node的pod列表
export const ApiNodePodList = (params) => {
  return axios.request({
    url: '/api/v1/resource/node/pod/list',
    method: 'get',
    params
  })
}
// 获取调度状态
export const ApiNodeIsSchedulable = (params) => {
  return axios.request({
    url: '/api/v1/resource/node/is-schedulable',
    method: 'get',
    params
  })
}

// 修改调度状态
export const ApiNodeSchedule = (data) => {
  return axios.request({
    url: '/api/v1/resource/node/schedule',
    method: 'post',
    data
  })
}

// 获取label列表
export const ApiNodeLabelList = (params) => {
  return axios.request({
    url: '/api/v1/resource/node/label/list',
    method: 'get',
    params
  })
}

// 创建label
export const ApiNodeLabelAdd = (data) => {
  return axios.request({
    url: '/api/v1/resource/node/label/add',
    method: 'post',
    data
  })
}

// 删除label
export const ApiNodeLabelDelete = (data) => {
  return axios.request({
    url: '/api/v1/resource/node/label/delete',
    method: 'delete',
    data
  })
}

// 发送驱逐指令
export const ApiNodeDrain = (data) => {
  return axios.request({
    url: '/api/v1/resource/node/drain',
    method: 'post',
    data
  })
}


// 删除 node 下 pod
export const ApiNodePodsDelete = (data) => {
  return axios.request({
    url: '/api/v1/resource/node/pod/delete',
    method: 'delete',
    data
  })
}

// node pod metrics
export const ApiNodePodMetrics = (clusterId, node) => {
  return axios.request({
    url: '/api/v1/resource/node/pod-metrics/list',
    method: 'post',
    data: {
      clusterId: clusterId,
      node: node,
    }
  })
}
