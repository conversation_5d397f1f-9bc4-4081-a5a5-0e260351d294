import { ref, onMounted, getCurrentInstance, PropType, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import { YamlGVR } from '../config'
import { EnumComponentType, Service, ServiceYaml } from './type'
import { TimeTrans } from '@/libs/tools'
import { useRequest } from 'vue-request'
import useSingleK8SService from '@/libs/useSingleK8SService'

export default function useSVCService(props: { type: EnumComponentType }) {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string; relativeName?: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const yamlInitData = ref<string>()

  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>({ ...YamlGVR['Service'] })

  const detailModalVisible = ref(false)
  const portData = ref()

  const onDelete = (record: Service) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteService}`, {
          params: {
            ...K8SInstance.value,
            resourceName: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Message.success('删除成功！')
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...YamlGVR['Service'],
      ...K8SInstance.value
    }
    yamlInitData.value = `apiVersion: v1
kind: Service
metadata:
  name: 必须修改
  namespace: ${K8SInstance.value.namespace}
spec:`
  }
  const onEdit = (record: Service) => {
    console.log('onEdit')

    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...K8SInstance.value,
      ...YamlGVR['Service'],
      resourceName: record.name
    }
  }

  const onViewYaml = (record: Service) => {
    formEntity.value = {
      ...K8SInstance.value,
      ...YamlGVR['Service'],
      resourceName: record.name
    }
    yamlHistoryParams.value = {
      kind: 'Service',
      uuid: record.uuid
    }
    viewYamlVisible.value = true
  }

  const getTableData: TableRequest = async (params) => {
    let path = ''
    let query = {}
    switch (props.type) {
      case EnumComponentType.Independent:
        path = Config.Api.GetServiceList
        query = {
          cluster_id: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          page: params.pageIndex,
          size: params.pageSize,
          search: params.searchValue ?? ''
        }
        break

      case EnumComponentType.AssociatedDeployment:
        path = Config.Api.GetServiceListWidthDeployment
        query = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          name: K8SInstance.value.relativeName
        }
        break
      case EnumComponentType.Rayjob:
          path = Config.Api.GetServiceListWidthRayjob
          query = {
            clusterId: K8SInstance.value.clusterId,
            namespace: K8SInstance.value.namespace,
            name: K8SInstance.value.relativeName
          }
          break
      default:
        path = Config.Api.GetServiceListWidthStatefulset
        query = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          name: K8SInstance.value.relativeName
        }
        break
    }

    const res = await useGet<PageList<ServiceYaml[]>>(`${Config.Api.Base}${path}`, {
      params: query
    })

    const data =
      props.type === EnumComponentType.Independent
        ? res.data?.list
        : (res.data as unknown as { data: ServiceYaml[] }).data
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data:
        data?.map((item) => {
          return {
            uuid: item.metadata.uid,
            name: item.metadata.name,
            type: item.spec.type,
            cluster_ip: item.spec.clusterIP,
            lb_ip: item.status?.loadBalancer?.ingress?.map((i) => i.ip)?.join(',') ?? '-',

            ports: item.spec.ports,
            created: TimeTrans(item.metadata.creationTimestamp)
          }
        }) ?? []
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const {
    data: detail,
    run: getDetail,
    loading: detailLoading
  } = useRequest(
    (uuid) => {
      return useGet(`${Config.Api.Base}${Config.Api.GetServiceDetail}/${uuid}`, {
        params: {
          cluster_id: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace
        }
      })
    },
    {
      manual: true,
      initialData: {
        metadata: {
          namespace: '',
          name: '',
          creationTimestamp: null
        },
        spec: {
          type: '',
          clusterIP: '',
          ports: []
        }
      },
      formatResult: (res) => res.data.data,
      onSuccess: (data, [uuid]) => {
        portData.value =
          data?.spec?.ports?.map((item) => {
            return {
              containerPort: item.port,
              protocol: item.protocol,
              hostPort: item.nodePort,
              name: item.name
            }
          }) ?? []

        if (props.type === EnumComponentType.Independent) {
          getPodData(uuid)
        }
      }
    }
  )

  const {
    data: podData,
    run: getPodData,
    loading: podDataLoading
  } = useRequest(
    (uuid) => {
      return useGet(`${Config.Api.Base}${Config.Api.GetServiceRelativePod}`, {
        params: {
          uuid,
          cluster_id: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace
        }
      })
    },
    {
      manual: true,

      formatResult: (res) =>
        res.data.data?.map((item) => {
          let owner = ''
          if (item.metadata.ownerReferences.length !== 0) {
            owner = `${item.metadata.ownerReferences[0].kind} / ${item.metadata.ownerReferences[0].name}`
          }

          return {
            uid: item.metadata.uid,
            name: item.metadata.name,
            owner: owner,
            created: TimeTrans(item.metadata.creationTimestamp)
          }
        })
    }
  )

  const onOpenDetail = (record: Service) => {
    getDetail(record.uuid)
    detailModalVisible.value = true
  }

  const initK8SInstance = () => {
    if (props.type === EnumComponentType.Independent) {
      K8SInstance.value = {
        namespace: store.state.k8s.currentNamespace,
        clusterId: store.state.k8s.currentClusterId,
        relativeName: undefined
      }
    } else {
      K8SInstance.value = {
        namespace: proxy.$route.query.namespace as string,
        clusterId: proxy.$route.query.clusterId as string,
        relativeName:
          props.type === EnumComponentType.AssociatedDeployment
            ? (proxy.$route.query.deployment as string)
            : (proxy.$route.query.name as string)
      }
    }
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value?.reload()
  })

  const batchCopyModalVisible = ref(false)

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })
  return {
    getTableData,
    refObject,

    onEdit,
    onDelete,

    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,

    onSubmitSuccess,
    onCreate,
    yamlInitData,
    detailModalVisible,
    detail,
    detailLoading,
    onOpenDetail,
    K8SInstance,
    podData,
    podDataLoading,
    getPodData,
    portData,
    batchCopyModalVisible
  }
}
