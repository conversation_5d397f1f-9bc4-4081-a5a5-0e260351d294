<script>
import { Select, Option } from 'view-design'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SearchComponent',
  components: {
    Select,
    Option
  },
  props: {
    options: { type: Array, default: () => [] },
    placeholder: String,
    isFillValue: { type: Boolean, default: false },
    relativeValue: String,
    initValue: String
  },
  data() {
    return {
      selectedValue: this.$props.initValue ?? ''
    }
  },

  watch: {
    selectedValue(newValue) {
      console.log('Selected value:', newValue)
      this.$emit('change', newValue)
    },
    relativeValue: {
      handler(newVal, oldVal) {
        this.handleChange()
      }
    }
  },
  methods: {
    handleChange(value) {
      this.selectedValue = value
    }
  },
  mounted() {
    if (this.isFillValue) {
      this.handleChange(this.options?.[0]?.value ?? '')
    }
  }
})
</script>

<template>
  <Select v-model="selectedValue" @on-change="handleChange" :placeholder="placeholder" filterable>
    <Option v-for="(option, index) in options" :value="option.value" :key="index">{{ option.label }}</Option>
  </Select>
</template>
