import { ref } from 'vue'
import dayjs from 'dayjs'

export const columns = ref([
  {
    title: '容器名称',
    key: 'name',
    width: 150
  },
  {
    title: '镜像',
    key: 'imageUrl',
    ellipsis: true,
    tooltip: true,
    minWidth: 200
  },
  {
    title: '过期时间',
    key: 'expireTime',
    align: 'center',
    width: 150,
    render: (h, { row }) => {
      return h('span', {}, row.expireTime ? dayjs(row.expireTime * 1000).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  },
  {
    title: '操作',
    key: 'ops',
    slot: 'ops',
    align: 'center',
    width: 140
  }
])

export const expireTimeArr = [
  {
    label: '3小时',
    value: 3 * 60 * 60
  },
  {
    label: '6小时',
    value: 6 * 60 * 60
  },
  {
    label: '12小时',
    value: 12 * 60 * 60
  }
]
