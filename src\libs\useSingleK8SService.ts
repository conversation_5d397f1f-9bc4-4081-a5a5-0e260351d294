import { computed, getCurrentInstance, onBeforeMount, onUnmounted, ref, watch } from 'vue'
import { useStore } from '@/libs/useVueInstance'

// const skipNoticePage = [
//   '/login',
//   '/401',
//   '/500',
//   '/pure-yaml',
//   '/pod-console',
//   '/gateway-route-map',
//   '/kubernetes/namespace/deployment-detail',
//   '/kubernetes/logs',
//   '/kubernetes/pods/console'
// ]

/** 单一 K8S 的服务，每次聚焦回页面中时监听页面的 K8S 数据，变更时发出提醒，并返回最新的K8SKey给业务处理交互 */
export default function useSingleK8SService(setting?: {
  setClusterOnly?: boolean
  skipNamespaceNotice?: boolean
  skipNotice?: boolean
}) {
  const store = useStore()
  const listener = ref()

  const { proxy } = getCurrentInstance()

  const K8SKey = computed(() => {
    if (setting?.setClusterOnly) {
      return `${store.state.k8s.currentClusterId ?? 'none'}/${store.state.k8s.currentCluster ?? 'none'}`
    } else {
      return `${store.state.k8s.currentClusterId ?? 'none'}/${store.state.k8s.currentCluster ?? 'none'}/${
        store.state.k8s.currentNamespace ?? 'none'
      }`
    }
  })

  watch(K8SKey, (value, oldValue) => {
    if (setting?.skipNotice) return
    if (oldValue.includes('none')) {
      // 原始为空/页面刚挂载导致原始为空，不提醒
      return
    }

    if (setting?.skipNamespaceNotice || setting?.setClusterOnly) {
      proxy.$Message.success({
        render: (h) => {
          return h('span', ['已切换至 ', h('b', `${store.state.k8s.currentCluster}`)])
        }
      })
    } else {
      proxy.$Message.success({
        render: (h) => {
          return h('span', [
            '已切换至 ',
            h(
              'b',
              `${store.state.k8s.currentCluster}
            ${!setting?.skipNamespaceNotice ? ` / ${store.state.k8s.currentNamespace}` : ''}`
            )
          ])
        }
      })
    }
  })

  onBeforeMount(() => {
    listener.value = document.addEventListener('visibilitychange', function () {
      if (document.visibilityState === 'visible') {
        if (!setting?.setClusterOnly) {
          store.commit('getCurrentNamespace', store.state.user.userId)
        }
        store.commit('getCurrentCluster', store.state.user.userId)
        store.commit('getCurrentClusterID', store.state.user.userId)
        console.log('页面激活,检测pageKey：', K8SKey.value)
      }
    })
  })
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', listener.value)
  })

  return { K8SKey }
}
