export const Api = {
  Base: process.env.VUE_APP_GLOB_API_URL,
  MonitorAPI: process.env.VUE_APP_MONITOR_API_URL,
  MockBase: 'https://mock.apifox.cn/m1/2177889-0-default',

  GetMonitorData: '/api/v1/query_range',

  GetServiceEntryTableData: '/api/v2/resource/serviceentry/list',
  GetServiceEntry: '/api/v2/resource/serviceentry/get',
  DeleteServiceEntry: '/api/v2/resource/serviceentry/delete',
  GetYamlHistoryList: '/api/v1/k8s-op-record/resource/list',
  GetSideCarTableData: '/api/v2/resource/sidecar/list',
  GetSideCar: '/api/v2/resource/sidecar/get',
  DeleteSideCar: '/api/v2/resource/sidecar/delete',

  ResourceV1: '/api/v1/resource/',
  Resource: '/api/v2/resource/',
  RelatedResource: '/api/v1/resource/deployment/related/',

  IsFormConvertible: '/form/is-convert',
  CreateFormData: '/form/create',
  GetFormData: '/form/get',
  EditFormData: '/form/update',
  CheckFormData: '/form/check',
  ReviewFormData: '/form/preview',
  CreateYamlData: '/yaml/create',
  EditYamlData: '/yaml/update',
  CheckYamlData: '/yaml/check',
  RolloutYamlData: '/versioned/yaml/update',
  GetLatestYaml: 'yaml/get',

  GetExportToList: '/api/v1/cluster/namespace/export-to/list',
  GetUnifiedCascaderList: '/api/v1/cluster/unified/cascader/list',
  GetClusterCascaderList: '/api/v1/cluster/cascader/list',
  GetIsUnifiedCluster: '/api/v1/cluster/unified/is-unified',
  GetUnifiedClusterList: '/api/v1/cluster/unified/list',

  GetDestinationRuleTableData: '/api/v2/resource/destinationrule/list',
  GetDestinationRule: '/api/v2/resource/destinationrule/get',
  DeleteDestinationRule: '/api/v2/resource/destinationrule/delete',
  GetDestinationHostOptions: '/api/v2/resource/destinationrule/service/list',
  GetDestinationLabelOptions: '/api/v2/resource/destinationrule/related/pod/label/list',
  CopyDestinationRule: '/api/v2/resource/destinationrule/copy/create',

  GetRelatedDestinationRuleTableData: '/api/v1/resource/deployment/related/destinationrule/list',
  GetRelatedDestinationRule: '/api/v1/resource/deployment/related/destinationrule/get',
  DeleteRelatedDestinationRule: '/api/v1/resource/deployment/related/destinationrule/delete',
  GetRelatedDestinationHostOptions: '/api/v1/resource/deployment/related/destinationrule/service/list',
  GetRelatedDestinationLabelOptions: '/api/v1/resource/deployment/related/destinationrule/related/pod/label/list',

  UpdateEgress: '/api/v1/cluster/namespace/sidecar/form/apply',
  GetEgressTableList: '/api/v1/cluster/namespace/sidecar/form/get',
  GetIstioInjectStatus: '/api/v1/cluster/namespace/istio-inject/status/get',
  UpdateIstioInjectStatus: '/api/v1/cluster/namespace/istio-inject/status/set',
  GetIstioInjectStatusList: '/api/v1/cluster/istio-inject/status/list',

  GetVirtualServicePortOptions: '/api/v1/resource/service/fqdn/port-number/get',
  GetVirtualServiceSubsetOptions: '/api/v1/resource/service/fqdn/related/destinationrule/subset/get',
  GetGatewaysOptions: '/api/v2/resource/gateway/fqdn/mesh/list',

  GetVirtualServiceTableData: '/api/v2/resource/virtualservice/list',
  DeleteVirtualService: '/api/v2/resource/virtualservice/delete',
  CopyVirtualService: '/api/v2/resource/virtualservice/copy/create',
  GetDelegateCascader: '/api/v2/resource/virtualservice/delegate/cascader/list',
  GetVirtualServiceHostOptions: '/api/v1/resource/service/fqdn/list',

  GetRelatedVirtualServiceTableData: '/api/v1/resource/deployment/related/virtualservice/list',
  DeleteRelatedVirtualService: '/api/v1/resource/deployment/related/virtualservice/delete',
  GetRelatedDelegateCascader: '/api/v1/resource/deployment/related/virtualservice/delegate/cascader/list',
  GetRelatedVirtualServiceHostOptions: '/api/v1/resource/deployment/related/service/fqdn',

  GetRelatedGatewayVirtualServiceTableData: '/api/v2/resource/unified-gateway/virtualservice/list',
  DeleteRelatedGatewayVirtualService: '/api/v2/resource/unified-gateway/virtualservice/delete',
  CopyRelatedGatewayVirtualService: '/api/v2/resource/unified-gateway/virtualservice/copy/create',
  GetRelatedGatewayDelegateCascader: '/api/v2/resource/unified-gateway/virtualservice/delegate/cascader/list',

  GetGatewayManageTableData: '/api/v2/resource/unified-gateway/list',
  GetGatewayManageRelativeLogTableData: '/api/v2/resource/unified-gateway/related-pod/list',
  GetClusterSecList: '/api/v1/cluster-sec/list',

  GetGatewayTableData: '/api/v2/resource/unified-gateway/gateway/list',
  GetGateway: '/api/v2/resource/unified-gateway/gateway/get',
  DeleteGateway: '/api/v2/resource/unified-gateway/gateway/delete',
  GetGatewayHost: '/api/v2/resource/unified-gateway/gateway/host/list',
  GetTlsModeList: '/api/v1/resource/secret/tls/list',
  GenerateCredentialName: '/api/v2/resource/unified-gateway/gateway/certificate/name/create',
  CreateCredentialName: '/api/v2/resource/unified-gateway/gateway/certificate/create',

  GetRelatedGateway: '/api/v1/resource/deployment/related/gateway/list',
  GetDomainList: '/api/v2/meshroutemap/domain/list',
  GetDomainRouterTree: '/api/v2/meshroutemap/domaintree/get',
  GetDomainVirtualServiceList: '/api/v2/meshroutemap/domaintree/vs/list',
  GetGatewayRouterTree: '/api/v2/meshroutemap/gatewaytree/get',
  GetGatewayVirtualServiceList: '/api/v2/meshroutemap/gatewaytree/vs/list',

  GetSubscribeList: '/api/v1/user/resource-subscribe/list',

  SubscribeDeployment: '/api/v2/resource/deployment/subscribe/create',
  UnsubscribeDeployment: '/api/v2/resource/deployment/subscribe/delete',
  GetDeploymentSubscribeStatus: '/api/v2/resource/deployment/subscribe/get',

  onSubscribeStatefulsetOrNot: '/api/v1/resource/statefulset/subscribe',
  GetStatefulsetSubscribeStatus: '/api/v1/resource/statefulset/subscribe/get',

  GetPodCrashLog: '/api/v1/resource/pod/crash-logs/list',

  DeleteDeployment: '/api/v2/resource/deployment/delete',
  DeleteStatefulset: '/api/v2/resource/statefulset/delete',
  DeleteService: '/api/v2/resource/service/delete',

  GetSubscribeConfig: '/api/v1/resource-notify-policy/setting/list',
  CreateSubscribeConfig: '/api/v1/resource-notify-policy/setting/create',
  EditSubscribeConfig: '/api/v1/resource-notify-policy/setting/update',
  DeleteSubscribeConfig: '/api/v1/resource-notify-policy/setting/delete',

  SetDeployPodSidecarLogLevel: '/api/v1/resource/deployment/related/pod/sidecar/log-level/set',

  GetNodeList: '/api/v1/cluster/get',
  GetNodeMetricsList: '/api/v1/openapi/resource/metrics/node/list',
  GetPodList: '/api/v1/resource/node/pod/list',
  PodLogSearch: '/api/v1/resource/pod/log/search',
  PodLogSearchV2: '/api/v2/resource/pod/log/search',

  UserProfile: '/api/v1/user/profile',
  GetAllWorkload: '/api/v1/workspace/user/app/list',

  GetClusterList: '/api/v1/cluster/auth-resource/cluster/list',
  GetNamespaceList: '/api/v1/cluster/auth-resource/namespace/list',

  GetUserProfile: '/api/v1/workspace/user/profile',
  GetUserK8SList: '/api/v1/workspace/user/auth/k8s/list',
  GetUserKubectlProxyList: '/api/v1/workspace/user/auth/kubectl-proxy/list',
  GetUserKubectlWebShellList: '/api/v1/workspace/user/auth/kubectl-webshell/list',
  DownloadKubectlProxyConfig: '/api/v1/workspace/user/auth/kubeconfig/download',
  RebuildKubectlProxyConfigAuthRole: '/api/v1/kubeconfig/auth-role/rebuild',
  DownloadKubectlProxyMergeConfig: '/api/v1/workspace/user/auth/kubeconfig/merge-download',

  GetWorkspaceSubscribeList: '/api/v1/workspace/user/subscribe-app/list',
  SubscribeWorkspaceResource: '/api/v1/workspace/user/app/subscribe',
  UnsubscribeWorkspaceResource: '/api/v1/workspace/user/app/unsubscribe',

  GetResourceFavorId: '/api/v1/workspace/collection/resource/get',
  GetResourceFavorClusterNamespaceCascader: '/api/v1/workspace/collection/cluster-ns/list',
  GetResourceFavorList: '/api/v1/workspace/collection/resource/list',
  AddResourceFavor: '/api/v1/workspace/collection/resource/add',
  RemoveResourceFavor: '/api/v1/workspace/collection/resource/delete',

  GetExecPod: '/api/v1/kubectl/auth/exec-pod/get',

  /** 多维搜索 */
  GetMultiClusterSearchLicenseInfo: '/api/v1/multi-cluster-search/license-info/list',
  SearchMultiClusterList: '/api/v1/multi-cluster-search/list',
  GetMultiClusterSearchHistoryParams: '/api/v1/multi-cluster-search/history/list',
  SaveMultiClusterSearchHistoryParams: '/api/v1/multi-cluster-search/history/save',
  DeleteMultiClusterSearchHistoryParams: '/api/v1/multi-cluster-search/history/delete',
  GetMultiClusterSearchRelateResource: '/api/v1/multi-cluster-search/resource/relate',
  GetPodContainers: '/api/v1/multi-cluster-search/pod/container/relate',

  GetEventList: '/api/v1/event-manager/list',
  GetEventClusterList: '/api/v1/event-manager/cluster/list',
  GetEventNamespaceTypeKindList: '/api/v1/event-manager/namespace-type-kind/list',
  GetEventObjectList: '/api/v1/event-manager/event-obj/list',

  GetPodRelativeContainerList: '/api/v1/resource/pod/container/relate',

  GetAccessOperateLogFilterList: '/api/v1/op-record/opt/list',
  GetRelativeEvent: '/api/v1/resource/related/event',

  GetContainerDirList: '/api/v1/resource/pod/container/dir/list',
  GetContainerFileList: '/api/v1/resource/pod/container/file/list',
  DownloadContainerFile: '/api/v1/transparent/container/file/download',
  UploadContainerFile: '/api/v1/transparent/container/file/upload',

  ForceUpdateNotice: '/api/v2/resource/force-update/notify',

  GetSimpleResourceList: '/api/v1/resource/name/list',
  BatchCopy: '/batch-copy',
  GetWorkloadPodList: '/api/v1/resource/workload/related/pod-name/list',

  DeletePVC: '/api/v1/resource/pvc/delete',

  GetEphemeralContainerList: '/api/v1/resource/pod/ephemeral-container/running/list',
  GetImageList: '/api/v1/image/list',
  CreateEphemeralContainer: '/api/v1/resource/pod/ephemeral-container/create',

  SetDeploymentReplicasCount: '/api/v2/resource/deployment/replicas-count/set',
  GetDeploymentReplicasCount: '/api/v2/resource/deployment/replicas-count/get',
  SetStatefulsetReplicasCount: '/api/v1/resource/statefulset/replicas-count/set',
  GetStatefulsetReplicasCount: '/api/v1/resource/statefulset/replicas-count/get',

  GetPodLog: '/api/v1/resource/pod/logs/list',

  GetPodContainerList: '/api/v1/cluster/namespace/related/pod/list',
  GetClusterEnvSetting: '/api/v1/cluster/env/get',

  GetStatefulsetSummary: '/api/v1/resource/statefulset/related/brief-statistics/list',
  GetDeploymentSummary: '/api/v1/resource/deployment/related/brief-statistics/list',
  RolloutStartStatefulset: '/api/v1/resource/statefulset/restart',

  GetDeploymentMeta: '/api/v1/cluster/auth-resource/deployment/get',
  GetStatefulsetMeta: '/api/v1/resource/statefulset/native-json/get',

  GetServiceList: '/api/v1/resource/service/list',
  GetServiceListWidthDeployment: '/api/v1/resource/deployment/related/service',
  GetServiceListWidthStatefulset: '/api/v1/resource/statefulset/related/native-json/service',

  GetServiceDetail: '/api/v1/resource/service',
  GetServiceRelativePod: '/api/v1/resource/related/service/pod',

  GetSecretList: '/api/v1/resource/secret/list',
  GetSecretListWidthDeployment: '/api/v1/resource/deployment/related/secret/list',
  GetSecretListWidthStatefulset: '/api/v1/resource/statefulset/related/secret',

  GetPVCList: '/api/v1/resource/pvc/list',
  GetPVCListWidthDeployment: '/api/v1/resource/deployment/related/pvc',
  GetPVCListWidthStatefulset: '/api/v1/resource/statefulset/related/pvc',

  GetConfigMapList: '/api/v1/resource/configmap/list',
  GetConfigMapListWidthDeployment: '/api/v1/resource/deployment/related/configmap/list',
  GetConfigMapListWidthStatefulset: '/api/v1/resource/statefulset/related/configmap',

  GetPodListWidthDeployment: '/api/v1/resource/deployment/related/rs',
  GetPodListWidthStatefulset: '/api/v1/resource/statefulset/related/pod',

  GetPodAnalyze: '/api/v1/resource/pod/analyze',

  DeletePodWidthDeployment: '/api/v1/resource/deployment/related/pod/delete',
  DeletePodWidthStatefulset: '/api/v1/resource/statefulset/pod/delete',

  GetContainerMetrics: '/api/v2/resource/pod/metrics/get',

  GetPodLogSearchHistoryList: '/api/v1/resource/pod/log-search/history/list',
  SavePodLogSearchHistory: '/api/v1/resource/pod/log-search/history/save',
  DeletePodLogSearchHistory: '/api/v1/resource/pod/log-search/history/delete',

  GetPortForwardList: '/api/v1/portforward/management/list',
  GetPortForwardListWithDeployment: '/api/v1/portforward/deployment/list',
  GetPortForwardListWithStatefulset: '/api/v1/portforward/statefulset/list',
  GetPortForwardListWithPod: '/api/v1/portforward/pod/list',

  CreatePortForwardWithDeployment: '/api/v1/portforward/deployment/create',
  CreatePortForwardWithStatefulset: '/api/v1/portforward/statefulset/create',
  CreatePortForwardWithPod: '/api/v1/portforward/pod/create',

  DeletePortForward: '/api/v1/portforward/management/delete',
  DeletePortForwardWithDeployment: '/api/v1/portforward/deployment/delete',
  DeletePortForwardWithStatefulset: '/api/v1/portforward/statefulset/delete',
  DeletePortForwardWithPod: '/api/v1/portforward/pod/delete',

  RebuildPortForward: '/api/v1/portforward/management/rebuild',
  RebuildPortForwardWithDeployment: '/api/v1/portforward/deployment/rebuild',
  RebuildPortForwardWithStatefulset: '/api/v1/portforward/statefulset/rebuild',
  RebuildPortForwardWithPod: '/api/v1/portforward/pod/rebuild',

  GetPodListWithDeployment: '/api/v1/resource/deployment/related/pod/list',
  GetPodListWithStatefulset: '/api/v1/resource/statefulset/related/pod',

  GetPortListWithDeployment: '/api/v1/resource/deployment/related/svc-port/list',
  GetPortListWithStatefulset: '/api/v1/resource/statefulset/related/svc-port/list',
  GetPortListWithPod: '/api/v1/resource/pod/related/svc-port/list',

  GetDeploymentContainerRelate: '/api/v1/resource/deployment/container/relate',
  GetStatefulsetContainerRelate: '/api/v1/resource/statefulset/container/relate',
  WorkloadLogSearch: '/api/v1/resource/workload/logs/search',

  SyncUnifiedCluster: '/unified-cluster/sync',

  GetCRDList: '/api/v1/resource/crd/list',
  GetCRDObjectTableColumns: '/api/v1/resource/crd/table',
  GetCRDObjectList: '/api/v1/resource/crd/object/list',
  DeleteCRDObject: '/api/v1/resource/crd/object/delete',

  GetEndPointTableData: '/api/v1/resource/endpoint/list',
  DeleteEndPoint: '/api/v1/resource/endpoint/delete',

  GetCronJobTableData: '/api/v1/resource/cronjob/list',
  DeleteCronJob: '/api/v1/resource/cronjob/delete',
  GetCronJobDetailData: '/api/v1/resource/cronjob/detail',
  GetCronJobRelativeJob: '/api/v1/resource/cronjob/related/job',

  GetJobTableData: '/api/v1/resource/job/list',
  GetJobDetailData: '/api/v1/resource/job/detail',
  GetJobRelativePod: '/api/v1/resource/job/related/pod',
  DeleteJob: '/api/v1/resource/job/delete',

  GetTelemetryTableData: '/api/v2/resource/telemetry/list',
  GetTelemetryDetailData: '/api/v2/resource/telemetry/detail',
  GetTelemetryRelativePod: '/api/v2/resource/telemetry/related/pod',
  DeleteTelemetry: '/api/v2/resource/telemetry/delete',

  GetBackupStrategyClusterList: '/api/v1/cluster/auth-resource/cluster/list',
  GetBackupStrategyNamespaceList: '/api/v1/cluster/namespace/list',
  GetBackupStrategyGroupResourceList: '/api/v1/cluster/gvrk-namespaced/list',
  GetStrategyTaskList: '/api/v1/backup-manager/strategy/task/list',
  GetBackupOrRecoverStrategyList: '/api/v1/backup-manager/strategy/list',
  GetBackUpObjectList: '/api/v1/backup-manager/resource/object/list',
  CreateBackupStrategy: '/api/v1/backup-manager/backup-strategy/create',
  ExecBackupStrategy: '/api/v1/backup-manager/backup-strategy/execute',
  DeleteBackupStrategy: '/api/v1/backup-manager/backup-strategy/delete/',
  CreateRecoverStrategy: '/api/v1/backup-manager/recover-strategy/create',
  ExecRecoverStrategy: '/api/v1/backup-manager/recover-strategy/execute',
  DeleteRecoverStrategy: '/api/v1/backup-manager/recover-strategy/delete/',
  GetStrategyTaskObjectList: '/api/v1/backup-manager/strategy/task/object/list',
  GetRecoverStrategyClusterList: '/api/v1/backup-manager/recover-strategy/cluster/list',
  GetRecoverStrategyGroupResourceList: '/api/v1/backup-manager/recover-strategy/gr/list',
  GetRecoverStrategyNamespaceList: '/api/v1/backup-manager/recover-strategy/namespace/list',
  GetBackupStrategyObjectName: '/api/v1/cluster/any/resource-name/list',
  GetRecoverStrategyObjectNameList: '/api/v1/backup-manager/recover-strategy/resource/list',

  DeleteRayJob: '/api/v1/mlops/ray/job/delete',
  GetRayJobTableData: '/api/v1/mlops/ray/job/list',
  GetPodListWidthRayjob: '/api/v1/mlops/ray/job/related/pod/list',
  GetRayJobSummary: '/api/v1/mlops/ray/job/related/brief-statistics/list',
  GetServiceListWidthRayjob: '/api/v1/mlops/ray/job/related/service/list'
}
export default Api
