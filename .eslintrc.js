module.exports = {
  parser: 'vue-eslint-parser',
  root: true,
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2017,
    ecmaFeatures: {
      implied: true,
      jsx: true,
      modules: true
    },
    sourceType: 'module',
    extraFileExtensions: ['.vue']
  },
  env: {
    browser: true,
    node: true,
    es6: true
  },
  plugins: ['@typescript-eslint', 'vue'],
  extends: [
    'plugin:vue/essential',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript'
  ],
  rules: {
    'import/no-unresolved': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/ban-types': 'off',
    'import/no-named-as-default': 'off',
    'vue/multi-word-component-names': 'off',

    '@typescript-eslint/ban-ts-comment': 'off'
  }
}
