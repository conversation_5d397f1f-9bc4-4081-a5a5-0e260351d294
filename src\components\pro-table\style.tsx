import { Page as IViewPage } from 'view-design'
import styled from 'vue-styled-components'

export const Operation = styled('div', { border: Boolean })`
  &:hover {
    box-shadow: ${(props) => (props.border ? '0px 0px 4px 0px #dddddd' : 'none')};
  }
  margin-bottom: 16px;
`

export const BlockStyleOperation = Operation.extend`
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: ${(props) => (props.border ? '1px solid #e8eaec' : 'none')};
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  > :not(:last-child) {
    margin-bottom: 16px;
  }
`

export const OperateSearch = styled('div')`
  display: flex;
  flex: 1 0 0%;
`
export const OperateBtn = styled('div')`
  flex: 1 0 0%;
  > :not(:last-child) {
    margin-right: 16px;
  }
`
export const Table = styled('div', { border: Boolean })`
  > :not(:last-child) {
    margin-bottom: 16px;
  }

  &:hover {
    box-shadow: ${(props) => (props.border ? '0px 0px 4px 0px #dddddd' : 'none')};
    /* box-shadow: 0px 0px 4px 0px #dddddd; */
  }
  th > .ivu-table-cell {
    white-space: nowrap;
  }
`
export const BlockStyleTable = styled('div', { border: Boolean })`
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: ${(props) => (props.border ? '1px solid #e8eaec' : 'none')};
  &:hover {
    box-shadow: ${(props) => (props.border ? '0px 0px 4px 0px #dddddd' : 'none')};
    /* box-shadow: 0px 0px 4px 0px #dddddd; */
  }
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  > :not(:last-child) {
    margin-bottom: 16px;
  }

  th > .ivu-table-cell {
    white-space: nowrap;
  }
`

export const Page = styled(IViewPage)`
  .ivu-page-simple-pager input {
    pointer-events: none;
    border: none;
    width: 48px;
    margin: 0;
  }
`
