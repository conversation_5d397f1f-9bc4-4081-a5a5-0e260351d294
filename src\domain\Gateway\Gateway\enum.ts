export function formatEnumToLabelValue(enumObj) {
  const arr: { value: string; label: string }[] = []
  for (const [value, label] of Object.entries(enumObj)) {
    arr.push({ value, label: label.toString() })
  }
  return arr
}

export enum EnumProtocol {
  HTTP = 'HTTP',
  HTTPS = 'HTTPS',
  GRPC = 'GRPC',
  HTTP2 = 'HTTP2',
  MONGO = 'MONGO',
  TCP = 'TCP',
  TLS = 'TLS'
}

export enum EnumMode {
  PASSTHROUGH = 'PASSTHROUGH',
  SIMPLE = 'SIMPLE'
}
