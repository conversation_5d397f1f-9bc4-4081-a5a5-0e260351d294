import axios from '@/libs/api.request'

export const ApiNsDeploymentList = (page, data) => {
  return axios.request({
    url: `/api/v1/resource/deployment/list`,
    method: 'post',
    data: { label_selector: data },
    params: page
  })
}

export const ApiNsDeploySvcList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/service`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiNsDeployCmList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/configmap`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiNsDeployHPAList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/hpa`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiNsDeployUpdateHPA = (cluster_id, namespace, deployment, yaml) => {
  return axios.request({
    url: `/api/v1/resource/deployment/hpa/update`,
    method: 'post',
    data: {
      cluster_id,
      namespace,
      deployment,
      yaml
    },
    params: {}
  })
}

export const ApiNsDeployPVCList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/pvc`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiNsDeployRsList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/rs`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiNsDeployRsPodList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/rs/pod`,
    method: 'get',
    data: {},
    params
  })
}

export const ApiResourceJson = ({ cluster_id, namespace, resource_name, kind }) => {
  return axios.request({
    url: `/api/v1/resource/json/get`,
    method: 'get',
    data: {},
    params: {
      cluster_id,
      namespace,
      resource_name,
      kind
    }
  })
}

export const ApiNsPodLogList = ({ cluster_id, namespace, pod, container, tail }) => {
  return axios.request({
    url: `/api/v1/resource/pod/logs/list`,
    method: 'get',
    data: {},
    params: {
      cluster_id,
      namespace,
      pod,
      container,
      tail
    }
  })
}

export const ApiNsPodMetrics = ({ cluster_id, namespace, pod_name }) => {
  return axios.request({
    url: `/api/v1/resource/pod/metrics/get`,
    method: 'get',
    data: {},
    params: {
      cluster_id,
      namespace,
      pod_name
    }
  })
}
