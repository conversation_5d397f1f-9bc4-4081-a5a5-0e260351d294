<template>
  <div class="log-editor">
    <textarea ref="textarea" />
  </div>
</template>

<script>
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import './darcula.css'
import 'codemirror/mode/markdown/markdown'

export default {
  name: 'TxtEditor',
  props: ['value', 'forbiddenEdit'],
  data() {
    return {
      editor: false
    }
  },
  watch: {
    value(value) {
      const editorValue = this.editor.getValue()
      if (value !== editorValue) {
        this.editor.setValue(this.value ?? '')
        this.refresh()
      }
    }
  },
  mounted() {
    this.editor = CodeMirror.fromTextArea(this.$refs.textarea, {
      lineNumbers: true, // 显示行号
      theme: 'darcula', // 编辑器主题
      inputStyle: 'contenteditable', // 让文本处于可编辑状态(该模式兼容性好)
      ...(!!this.forbiddenEdit ? { readOnly: 'nocursor' } : null)
    })

    this.editor.setValue(this.value ?? '')
    this.editor.on('change', (cm) => {
      this.$emit('changed', cm.getValue())
      this.$emit('input', cm.getValue())
    })
  },
  methods: {
    getValue() {
      return this.editor.getValue()
    },
    refresh() {
      this.editor.refresh()
    }
  }
}
</script>

<style lang="less">
.log-editor {
  height: 100%;
  position: relative;
}
.log-editor .CodeMirror {
  height: 100%;
  min-height: 100px;
  font-family: Consolas, Monaco, monospace;
  font-size: 14px;
  *::-webkit-scrollbar-track-piece {
    background-color: #f1f1f1;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }
  *::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }
  *::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }
  *::-webkit-scrollbar-corner {
    background-color: #f1f1f1;
  }
}

.log-editor .CodeMirror-scroll {
  min-height: 100px;
}

/deep/.CodeMirror-selected {
  background: #b36539;
}
</style>
