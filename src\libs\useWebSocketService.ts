import { ref, getCurrentInstance } from 'vue'
import { noticeError } from './util'

interface WebSocketServiceProps {
  onWsMessage: (result) => void
  onRestart?: () => void
  onWsOpen?: (result) => void
}

export default function useWebSocketService(props: WebSocketServiceProps) {
  const webSocketRef = ref<WebSocket>()
  const isWebSocketConnected = ref(false)
  const { proxy } = getCurrentInstance()

  const initWebSocket = (url) => {
    const webSocket = new WebSocket(url)
    webSocket.onopen = onWebSocketOpen
    webSocket.onclose = onWebSocketClose
    webSocket.onerror = onWebSocketError
    webSocket.onmessage = onWebSocketMessage
    webSocketRef.value = webSocket
  }

  const onWebSocketOpen = (result) => {
    isWebSocketConnected.value = true
    props.onWsOpen?.(result)
  }
  const onWebSocketClose = () => {
    isWebSocketConnected.value = false
  }
  const onWebSocketError = () => {
    if (webSocketRef.value) {
      webSocketRef.value.close()
    }
    noticeError(proxy, `连接错误，已关闭连接。`)
    proxy.$Modal.confirm({
      title: 'Tips',
      content: '连接已断开，是否进行重连 ?',
      onOk: () => {
        if (props.onRestart) {
          props.onRestart?.()
        } else {
        }
      }
    })
  }
  const onWebSocketMessage = (result) => {
    props.onWsMessage(result)
  }

  return { initWebSocket, webSocketRef, isWebSocketConnected }
}
