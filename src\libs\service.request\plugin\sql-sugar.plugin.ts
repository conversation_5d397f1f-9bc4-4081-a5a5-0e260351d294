export enum EnumConditionalType {
  Equal = 0,
  Like = 1,
  GreaterThan = 2,
  GreaterThanOrEqual = 3,
  LessThan = 4,
  LessThanOrEqual = 5,
  In = 6,
  NotIn = 7,
  LikeLeft = 8,
  LikeRight = 9,
  NoEqual = 10,
  IsNullOrEmpty = 11,
  IsNot = 12,
  NoLike = 13,
  EqualNull = 14,
}

export interface Condition {
  fieldName: string;
  fieldValue: string;
  conditionalType: EnumConditionalType;
}

export interface QueryParams {
  orderField?: string;
  orderType?: 'asc' | 'desc';
  conditions?: Condition[];
  pageIndex?: number;
  pageSize?: number;
}

export const formatCondition = (
  fieldName: any,
  fieldValue: any,
  conditionalType?: EnumConditionalType,
) => {
  // FieldValue为0或非空字符串或Boolean类型时不过滤数据
  return fieldValue === 0 || typeof fieldValue === 'boolean' || !!fieldValue
    ? {
        fieldName,
        fieldValue: String(fieldValue),
        conditionalType: conditionalType ?? EnumConditionalType.Equal,
      }
    : null;
};
