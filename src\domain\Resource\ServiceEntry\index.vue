<script lang="ts" setup>
import { ResourceContainer, BatchCopy } from '@/domain/Resource'
import {
  Space,
  LinkButton,
  ViewYaml,
  ProTable,
  ProFormSelect,
  ProFormText,
  ProFormItem,
  ArrayObject,
  EnumFormItemControllerType,
  MultiInput
} from '@/components'
import { ResourceForm, EnumFormStatus } from '@/components/resource-form'
import { DetailDrawer, DetailCard } from '@/components/detail-drawer'
import { genNonDuplicateArr } from '@/libs/tools'
import useServiceEntryService from './useServiceEntryService'
import { formatEnumToLabelValue, EnumLocation, EnumWorkload, EnumResolution, EnumPorts } from './enum'
import { TABLE_COLUMNS, PORTS_TABLE_COLUMNS, DETAIL_CONFIG } from './setting'
import { getCurrentInstance,ref } from 'vue'
const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  formInitData,
  onPortKeyChange,
  onWorkloadSelectorKeyChange,
  onEndpointsKeyChange,
  onInitFormat,
  onSubmitFormat,
  onSubmitWorkloadEndpoints,
  onSubmitSuccess,
  onViewDetail,
  viewDetailVisible,
  onInitDetailFormat,
  exportToList
} = useServiceEntryService()
const { proxy } = getCurrentInstance()
const batchCopyModalVisible = ref(false)
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>
          当前页面中您可以管理 ServiceEntry 资源, 主要用于让外部服务统一接入到网格内方便统一管理,
          默认情况对所有命名空间生效。
        </p>
      </Space>
    </Alert>
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
      :on-create="onCreate"
    >
      <template #operate-buttons>
        <Button size="small" type="primary" ghost icon="md-copy" @click="() => (batchCopyModalVisible = true)">批量复制</Button>
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>
    <view-yaml
      resourceType="serviceentry"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      isCheckYaml
    />
    <resource-form
      resourceType="serviceentry"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      :onInitFormat="onInitFormat"
      :onSubmitFormat="onSubmitFormat"
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" :readonly="true" />
        <ProFormText name="name" label="Name（名称）" :data="data" :readonly="formStatus === EnumFormStatus.Edit" />
        <ProFormSelect
          name="location"
          label="Location（来自）"
          :data="data"
          :options="formatEnumToLabelValue(EnumLocation)"
          desc="指定服务是在服务网格外（MESH_EXTERNAL）还是在服务网格内（MESH_INTERNAL）。"
          url="https://istio.io/latest/docs/reference/config/networking/service-entry/#ServiceEntry-Location:~:text=Yes-,location,-Location"
        />
        <ProFormItem
          name="exportTo"
          label="ExportTo（暴露到目标空间）"
          :data="data"
          :dataReloadFlag="dataReloadFlag"
          contentStyleMode="background"
          :mode="EnumFormItemControllerType.Switch"
          desc="指定该virtualservice在哪些namespace上可被gateway, virtualservice等资源使用。'.' 表示在当前virtualservice的namespace， '*' 表示所有的namespace。"
          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest:~:text=No-,exportTo,-string%5B%5D"
        >
          <MultiInput
            key="exportTo"
            addLabel="添加输出空间"
            :data="data"
            :on-delete="(index) => data.exportTo.data.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data?.exportTo?.data?.length || 1)"
          >
            <template #default="{ index }">
              <Select
                placeholder="目标空间"
                filterable
                transfer
                :value="data?.exportTo?.data?.[index]"
                @on-change="
                  (value) => {
                    if (data.exportTo?.data?.length) {
                      data.exportTo.data.splice(index, 1, value)
                    } else {
                      data.exportTo.data = [value]
                    }
                  }
                "
              >
                <Option v-for="item in exportToList" :key="item" :value="item">{{ item }}</Option>
              </Select>
            </template>
          </MultiInput>
        </ProFormItem>
        <ProFormItem
          name="hosts"
          label="Hosts（服务主机地址）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          desc="与serviceentry关联的主机。"
          url="https://istio.io/latest/docs/reference/config/networking/service-entry/#ServiceEntry-Resolution:~:text=Required-,hosts,-string%5B%5D"
        >
          <MultiInput
            key="hosts"
            addLabel="添加主机"
            :data="data"
            :on-delete="(index) => data.hosts.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data.hosts?.length || 1)"
          >
            <template #default="{ index }">
              <Input
                placeholder="服务主机 www.baidu.com"
                :value="data.hosts?.[index]"
                @on-change="
                  (e) => {
                    !data.hosts?.length && (data.hosts = [])
                    data.hosts[index] = e.target.value
                  }
                "
              />
            </template>
          </MultiInput>
        </ProFormItem>
        <ProFormItem
          name="ports"
          label="Ports（服务端口）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          desc="与外部服务关联的端口。"
          url="https://istio.io/latest/docs/reference/config/networking/service-entry/#ServiceEntry-Resolution:~:text=No-,ports,-ServicePort%5B%5D"
        >
          <MultiInput
            key="ports"
            addLabel="添加端口"
            :data="data"
            :on-delete="(index) => data.ports.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data.ports?.length || 1)"
          >
            <template #default="{ index }">
              <div class="multi-item-wrapper">
                <Input
                  placeholder="端口名称"
                  :value="data.ports?.[index]?.name"
                  @on-change="(e) => onPortKeyChange(data, 'name', index, e.target.value)"
                />
                <Input
                  placeholder="端口号"
                  :value="data.ports?.[index]?.number"
                  @on-change="(e) => onPortKeyChange(data, 'number', index, e.target.value)"
                />
                <Input
                  placeholder="目标端口"
                  :value="data.ports?.[index]?.targetPort"
                  @on-change="(e) => onPortKeyChange(data, 'targetPort', index, e.target.value)"
                />
                <Select
                  placeholder="端口协议"
                  filterable
                  transfer
                  :value="data.ports?.[index]?.protocol"
                  @on-change="(val) => onPortKeyChange(data, 'protocol', index, val)"
                >
                  <Option v-for="item in formatEnumToLabelValue(EnumPorts)" :key="item.value" :value="item.value">{{
                    item.label
                  }}</Option>
                </Select>
              </div>
            </template>
          </MultiInput>
        </ProFormItem>
        <ProFormItem
          v-if="formStatus === EnumFormStatus.Edit"
          name="address"
          label="Addresses（虚拟IP地址, 该字段仅支持查看不支持修改）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          desc="与服务关联的虚IP。"
          url="https://istio.io/latest/docs/reference/config/networking/service-entry/#ServiceEntry-Resolution:~:text=Yes-,addresses,-string%5B%5D"
        >
          <div>{{ data?.address ?? '-' }}</div>
        </ProFormItem>
        <ProFormItem
          name="resolution"
          label="Resolution（解析方式）"
          :data="data"
          contentStyleMode="background"
          desc="主机的解析方式。目前支持NONE,STATIC,DNS这三种解析方式。"
          url="https://istio.io/latest/docs/reference/config/networking/service-entry/#ServiceEntry:~:text=No-,resolution,-Resolution"
        >
          <RadioGroup v-model="data.resolution">
            <Radio v-for="item in formatEnumToLabelValue(EnumResolution)" :key="item.value" :label="item.value">
              {{ item.label }}
            </Radio>
          </RadioGroup>
        </ProFormItem>
        <ProFormItem
          v-show="data.resolution === EnumResolution.STATIC"
          name="workload"
          label="WorkLoad（工作负载选择, 通常配合解析方式是 STATIC 的时候）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          :mode="EnumFormItemControllerType.Chosen"
          :chosenOptions="formatEnumToLabelValue(EnumWorkload)"
        >
          <template #endpoints>
            <ArrayObject
              v-if="data.workload"
              label="Endpoints"
              :data="data.workload"
              name="endpoints"
              canEdit
              :columns="PORTS_TABLE_COLUMNS"
              @on-ok="(type, record, index) => onSubmitWorkloadEndpoints(data, type, record, index)"
              @on-delete="(index) => data.workload?.endpoints?.splice(index, 1)"
            >
              <template #default="{ record, visible }">
                <Form>
                  <ProFormText
                    name="address"
                    label="Address（Socket地址不需要配置 Port）"
                    :data="record"
                    placeholder="IP / SOCKET"
                    :dataReloadFlag="visible"
                    desc="外部服务的地址。"
                    url="https://istio.io/latest/docs/reference/config/networking/workload-entry/#WorkloadEntry:~:text=Required-,address,-string"
                  />
                  <ProFormItem
                    name="ports"
                    label="Ports"
                    :data="record"
                    contentStyleMode="background"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="与端点相关的端口。"
                    url="https://istio.io/latest/docs/reference/config/networking/workload-entry/#WorkloadEntry:~:text=Yes-,ports,-map%3Cstring%2C%C2%A0uint32"
                  >
                    <MultiInput
                      key="ports"
                      addLabel="添加端口"
                      :data="record"
                      :on-delete="(index) => record?.ports?.data.splice(index, 1)"
                      :on-init="() => genNonDuplicateArr(record?.ports?.data?.length || 1)"
                    >
                      <template #default="{ index }">
                        <div class="multi-item-wrapper">
                          <Input
                            placeholder="端口名称"
                            :value="record?.ports?.data?.[index]?.key"
                            @on-change="(e) => onEndpointsKeyChange(record, 'key', index, e.target.value)"
                          />
                          <Input
                            placeholder="端口号"
                            :value="record?.ports?.data?.[index]?.value"
                            @on-change="(e) => onEndpointsKeyChange(record, 'value', index, e.target.value)"
                          />
                        </div>
                      </template>
                    </MultiInput>
                  </ProFormItem>
                </Form>
              </template>
            </ArrayObject>
          </template>
          <template #workloadSelector>
            <MultiInput
              addLabel="添加label"
              :data="data"
              :on-delete="(index) => data.workload?.workloadSelector.splice(index, 1)"
              :on-init="() => genNonDuplicateArr(data.workload?.workloadSelector?.length || 1)"
            >
              <template #default="{ index }">
                <div class="multi-item-wrapper">
                  <Input
                    placeholder="Key"
                    :value="data.workload?.workloadSelector?.[index]?.key"
                    @on-change="(e) => onWorkloadSelectorKeyChange(data, 'key', index, e.target.value)"
                  />
                  <Input
                    placeholder="Value"
                    :value="data.workload.workloadSelector?.[index]?.value"
                    @on-change="(e) => onWorkloadSelectorKeyChange(data, 'value', index, e.target.value)"
                  />
                </div>
              </template>
            </MultiInput>
          </template>
        </ProFormItem>
      </template>
    </resource-form>

    <detail-drawer
      resourceType="serviceentry"
      :title="'ServiceEntry 详情'"
      v-model="viewDetailVisible"
      :resource-entity="formEntity"
      :onInitFormat="onInitDetailFormat"
    >
      <template #default="{ data }">
        <detail-card :title="'基本信息'" :data="data" :config="DETAIL_CONFIG.BASE_INFO">
          <template #labels="{ data }">
            <template v-for="(label, index) in data.labels">
              <Tag :key="index" color="#2a7cc3"> {{ label }} </Tag>
            </template>
          </template>
        </detail-card>
        <detail-card :title="'服务入口信息'" :data="data" :config="DETAIL_CONFIG.SERVICE_ENTRY">
          <template #dynamics>
            <template v-if="data?.resolution === 'STATIC'">
              <Row v-if="data?.endPoints" type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">Endpoint</Col>
                <Col span="14" class="info-value">
                  <span v-for="({ address, ports }, index) in data.endPoints" :key="index">
                    <b>{{ address }}</b>
                    （{{ ports.join('，') }}）
                  </span>
                </Col>
              </Row>
              <Row v-if="data?.workloadSelector?.length" type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">WorkloadSelector</Col>
                <Col span="14" class="info-value">
                  <space>
                    <span v-for="item in data?.workloadSelector" :key="item.key">
                      <b>{{ item }}</b>
                    </span>
                  </space>
                </Col>
              </Row>
            </template>
          </template>
        </detail-card>
      </template>
    </detail-drawer>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="ServiceEntry" />
  </div>
</template>

<style lang="less" scoped>
.multi-item-wrapper {
  display: flex;
  flex: 1 0 0%;
  > div {
    flex: 1 0 0%;
    &:not(:last-child) {
      margin-right: 16px;
    }
  }
}
</style>
<style lang="less">
.input-suffix-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-left: 1px solid #cccc;
}
</style>
