<script lang="ts" setup>
import { formItemProps } from './pro-from-item/type'

import { ProFormItem } from '@/components/pro-form'
import { computed, PropType, ref, set, watch } from 'vue'

const props = defineProps({
  ...formItemProps(),
  options: {
    type: Array as PropType<{ value: string; label: string }[]>
  },
  afterChange: Function as PropType<(val) => void>,
  /** 为Boolean类型的值进行处理（RadioGroup 组件并不支持直接将值设置为布尔类型） */
  isBoolean: Boolean
})
const options = ref([])
const value = computed({
  get: () => {
    const tempValue = !props.mode ? props.data[props.name] : props.data[props.name]?.data
    return props.isBoolean ? tempValue?.toString() : tempValue
  },
  set: (value) => {
    const tempValue = props.isBoolean ? value === 'true' : value
    !props.mode ? set(props.data, props.name, tempValue) : set(props.data[props.name], 'data', tempValue)
    props.afterChange && props.afterChange?.(tempValue)
  }
})

watch(
  () => props.options,
  () => {
    options.value = props.options
      ? props.options
      : props.isBoolean
      ? [
          { label: '是', value: 'true' },
          { label: '否', value: 'false' }
        ]
      : []
  },
  { immediate: true }
)
</script>

<template>
  <ProFormItem v-bind="props">
    <template #default>
      <RadioGroup v-model="value">
        <Radio v-for="item in options" :key="item.value" :label="item.value" :disabled="item.disabled">{{
          item.label
        }}</Radio>
      </RadioGroup>
    </template>
  </ProFormItem>
</template>

<style lang="less" scope>
.ivu-radio-group {
  display: flex;
  height: 22px;
  align-items: center;
}
</style>
