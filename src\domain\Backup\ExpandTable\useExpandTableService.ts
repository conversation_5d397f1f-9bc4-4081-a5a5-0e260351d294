import { PageList, useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { computed, getCurrentInstance, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import { EnumTabType } from '../type'
import { color } from '@/libs/consts'
import dayjs from 'dayjs'
import { ActionType, TableRequest } from '@/components/pro-table'

export const TASK_OBJECT_COLUMNS = [
  {
    title: '名称',
    key: 'objectName'
  },

  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 120,
    align: 'center'
  }
]
export const STATUS_MAP = {
  succeed: {
    text: '成功',
    color: color.success
  },
  failed: {
    text: '失败',
    color: color.error
  },
  waiting: {
    text: '等待',
    color: color.primary
  },
  running: {
    text: '进行中',
    color: color.primary
  },
  部分成功: {
    text: '部分成功',
    color: color.warning
  }
}

export default function useExpandTableService(props) {
  const { proxy } = getCurrentInstance()
  const taskObject = ref({ visible: false, data: {} as { id: string } })
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const keyword = ref()

  const staticData = ref({ total: 0, succeed: 0, failed: 0 })
  const yamlModal = ref({ visible: false, data: '' })
  const columns = computed(() => [
    {
      title: '版本',
      key: 'version',
      render: (h, { row }) => h('span', dayjs(row.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm:ss')),
      width: 220
    },
    {
      title: '状态',
      key: 'status',
      slot: 'status',
      align: 'center',
      width: 120
    },
    {
      title: props.activeTab === EnumTabType.backupStrategy ? '备份量' : '恢复进度',
      key: 'count',
      render: (h, { row }) => h('span', `${row.readyBackupCount ?? 0} / ${row.backupCount ?? 0}`),
      align: 'center'
    },
    {
      title: '错误信息',
      key: 'message',
      slot: 'message',
      minWidth: 90
    },
    {
      title: 'Ops',
      key: 'ops',
      slot: 'ops',
      width: 120,
      align: 'center'
    }
  ])
  const { data, run, loading } = useRequest(
    () => {
      return useGet<{ list: any[] }>(`${Config.Api.Base}${Config.Api.GetStrategyTaskList}`, {
        params: {
          pid: props.data.id,
          page: 1,
          size: 9999
        }
      })
    },
    {
      manual: true,
      formatResult: (res) =>
        res.data?.list?.sort((a, b) => dayjs(b.updated).valueOf() - dayjs(a.updated).valueOf()) ?? []
    }
  )

  const onOpenTaskModal = async (record) => {
    taskObject.value = { visible: true, data: record }
    refObject.tableRef.value?.reload()
  }
  const onExec = () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认重新执行此备份？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${
            props.activeTab === EnumTabType.backupStrategy
              ? Config.Api.ExecBackupStrategy
              : Config.Api.ExecRecoverStrategy
          }`,
          {
            pid: props.data.id
          }
        )
        if (res.success) {
          proxy.$Message.success('执行成功！')
          run()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<
      PageList<[]> & {
        count: number
        successCount: number
        failCount: number
      }
    >(`${Config.Api.Base}${Config.Api.GetStrategyTaskObjectList}`, {
      params: {
        page: params.pageIndex,
        size: params.pageSize,
        taskId: taskObject.value.data?.id,
        search: keyword.value
      }
    })
    staticData.value = { total: res.data.count, succeed: res.data.successCount, failed: res.data.failCount }
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onOpenYaml = (record) => {
    yamlModal.value = {
      visible: true,
      data: record.yaml
    }
  }

  const onSearch = () => {
    refObject.tableRef.value.reload()
  }

  watch(
    () => props.data,
    () => {
      run()
    },
    {
      immediate: true
    }
  )

  return {
    columns,
    taskObject,
    refObject,
    keyword,

    data,
    loading,
    onOpenTaskModal,
    onExec,
    getTableData,
    staticData,
    onOpenYaml,
    onSearch,
    yamlModal
  }
}
