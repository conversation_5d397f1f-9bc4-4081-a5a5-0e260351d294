import { Ellipsis } from '@/components'
import dayjs from 'dayjs'

/** 事件列表的列定义 */
export const EVENT_TABLE_COLUMNS = [
  {
    title: 'Time',
    key: 'age',
    width: 160,
    render: (h, params) => {
      return h('span', params.row.age ? dayjs(params.row.age * 1000).format('YYYY/MM/DD HH:mm:ss') : '-')
    }
  },
  {
    title: 'Reason',
    key: 'reason',
    width: 120,
    tooltip: true,
    tooltipTheme: 'light'
  },

  {
    title: 'Message',
    key: 'message',
    minWidth: 300,
    render: (h, params) => {
      return h(
        Ellipsis,
        {
          scopedSlots: {
            content: () => {
              return h('div', { style: { 'white-space': 'pre-wrap', 'max-width': '480px' } }, params.row.message)
            }
          }
        },
        params.row.message
      )
    }
  }
]
