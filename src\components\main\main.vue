<template>
  <Layout style="height: 100%" class="main">
    <Sider hide-trigger collapsible :width="220" class="left-sider" :style="{ overflow: 'hidden' }">
      <side-menu
        accordion
        ref="sideMenu"
        :active-name="$route.name"
        :collapsed="false"
        @on-select="turnToPage"
        :menu-list="menuList"
      >
        <!-- 需要放在菜单上面的内容，如Logo，写在side-menu标签内部，如下 -->
        <div class="logo-wrapper">
          <img :src="Logo" key="min-logo" />
        </div>
        <Dropdown placement="left" transfer transfer-class-name="sub-site-switch-dropdown">
          <Space class="sub-site-switch-wrapper" :size="8">
            <div class="sub-site-title">容器云平台</div>
            <Icon type="md-swap" title="切换子网站" :size="16" />
          </Space>
          <DropdownMenu slot="list">
            <DropdownItem selected>容器云平台</DropdownItem>
            <DropdownItem disabled>多云任务调度</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </side-menu>
    </Sider>
    <Layout>
      <Header class="header-wrapper">
        <SystemSearch />
        <Space class="right-box">
          <Tooltip content="版本更新" theme="light">
            <common-icon type="_info2" @click="onOpenVersionModal" class="header-icon" :size="24" color="#ffffff" />
            <!-- <Icon type="ios-alert-outline"  @click="onOpenVersionModal" class="header-icon" /> -->
          </Tooltip>
          <Divider type="vertical" />
          <Tooltip content="使用文档" theme="light">
            <common-icon type="_icon-question" @click="openDoc" class="header-icon" :size="24" color="#ffffff" />
            <!-- <img class="header-icon" src="@/assets/help.svg" @click="openDoc" /> -->
          </Tooltip>
          <Divider type="vertical" />
          <Tooltip content="联系我们" theme="light">
            <!-- <common-icon type="_conversation1" @click="openFeiShu" class="header-icon" :size="24" color="#ffffff" /> -->
            <img class="header-icon" src="@/assets/message.svg" @click="openFeiShu" />
          </Tooltip>

          <!-- <Divider type="vertical" />
          <img class="header-icon" src="@/assets/bell.svg" /> -->

          <Divider type="vertical" />
          <Dropdown transfer @on-click="logout">
            <img class="header-icon" src="@/assets/setting.svg" />
            <DropdownMenu slot="list">
              <DropdownItem>退出登录</DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <Divider type="vertical" />
          <Avatar class="avatar">Hi</Avatar>
          <div class="user-name">{{ userName }}</div>
        </Space>
        <!-- </header-bar> -->
      </Header>
      <Content class="main-content-con">
        <Layout class="main-layout-con">
          <Content class="content-wrapper">
            <main>
              <keep-alive :include="cacheList">
                <router-view v-if="isShow" />
              </keep-alive>
            </main>
            <footer class="content-footer">
              <div class="footer-links">
                <a
                  target="_blank"
                  href="https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=1eas73cf-b77c-43ec-95d1-926c1ed492c8"
                  >联系我们</a
                >
                <span>牵星</span>
              </div>
              <div class="footer-copyright">©2023 技术保障部 基础架构组出品 版本 {{ $config.version }}</div>
            </footer>
          </Content>
        </Layout>
      </Content>
    </Layout>
  </Layout>
</template>
<script>
import SideMenu from './components/side-menu'
import CommonIcon from '@/components/common-icon'

import { mapMutations, mapActions, mapGetters } from 'vuex'
import { getNewTagList, routeEqual } from '@/libs/util'
import routers from '@/router/routers'
// import Logo from '@/assets/images/logo.png'
// import Logo from '@/assets/images/logo-white.png'
import Logo from '@/assets/images/logo-pure-white.png'
import './main.less'

import { Space, SystemSearch } from '@/components'

export default {
  name: 'Main',
  provide() {
    return {
      reloadRouter: this.reloadRouter
    }
  },
  //   inject: ['VersionNoticeServiceProvide'],
  components: {
    SideMenu,
    Space,
    SystemSearch,
    CommonIcon
  },
  data() {
    return {
      Logo,
      isFullscreen: false,
      showConnect: false,
      isShow: true // 控制路由的显隐
    }
  },
  computed: {
    ...mapGetters(['errorCount']),
    tagNavList() {
      return this.$store.state.app.tagNavList
    },
    tagRouter() {
      return this.$store.state.app.tagRouter
    },

    cacheList() {
      const list = [
        ...(this.tagNavList.length
          ? this.tagNavList.filter((item) => !(item.meta && item.meta.notCache)).map((item) => item.name)
          : [])
      ]
      return list
    },
    menuList() {
      return this.$store.getters.menuList
    },
    local() {
      return this.$store.state.app.local
    },
    hasReadErrorPage() {
      return this.$store.state.app.hasReadErrorPage
    },
    userName() {
      return this.$store.state.user.userName
    }
  },
  methods: {
    ...mapMutations(['setBreadCrumb', 'setTagNavList', 'addTag', 'setLocal', 'setHomeRoute', 'closeTag']),
    ...mapActions(['handleLogin', 'getUnreadMessageCount', 'handleLogOut']),
    onOpenVersionModal() {
      window.open('https://q9jvw0u5f5.feishu.cn/docx/SY1gdzSvpoH4DdxlhqQcKJ8xnyd', '_blank')
      //   this.VersionNoticeServiceProvide.renderVersionNotice(true)
    },
    // 刷新当前路由
    reloadRouter() {
      this.isShow = false
      this.$nextTick(() => {
        this.isShow = true
      })
    },
    turnToPage(route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$router.push({
        name,
        params,
        query
      })
    },

    handleCloseTag(res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          this.turnToPage(this.$config.homeName)
        } else {
          if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    handleClick(item) {
      this.turnToPage(item)
    },
    openDoc() {
      window.open('https://q9jvw0u5f5.feishu.cn/wiki/wikcnR2o35nWutWWZmcArIMX7dr', '_blank')
    },
    openFeiShu() {
      window.open(
        'https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=1eas73cf-b77c-43ec-95d1-926c1ed492c8',
        '_blank'
      )
    },
    logout() {
      this.handleLogOut().then(() => {
        window.location.href = process.env.VUE_APP_OPERATION_PLATFORM
        // this.$router.push({
        //   name: 'login'
        // })
      })
    }
  },
  watch: {
    $route(newRoute) {
      const { name, query, params, meta } = newRoute
      this.setBreadCrumb(newRoute)
      this.addTag({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.setTagNavList(getNewTagList(this.tagNavList, newRoute))
      this.$refs.sideMenu.updateOpenName(newRoute.name)
    }
  },
  mounted() {
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
    this.setTagNavList()
    this.setHomeRoute(routers)
    const { name, params, query, meta } = this.$route
    this.addTag({
      route: { name, params, query, meta }
    })
    this.setBreadCrumb(this.$route)
    // 如果当前打开页面不在标签栏中，跳到homeName页
    if (!this.tagNavList.find((item) => item.name === this.$route.name)) {
      this.$router.push({
        name: this.$config.homeName
      })
    }
    // 获取未读消息条数
    // this.getUnreadMessageCount()
  }
}
</script>
<style scope lang="less">
.main {
  /deep/.ivu-table-wrapper {
    position: relative;
    border: 1px solid #dcdee2;
    border-bottom: 0;
    border-right: 0;
    overflow: hidden;
    border-radius: 5px;
  }

  .show-connect {
    padding: 5px 7px 5px 7px;
  }

  .show-connect:hover {
    padding: 7px 15px 7px 15px;
  }
  .content-wrapper main {
    min-height: calc(100vh - 64px - 40px - 16px * 2 - 132px);
  }
  .content-footer {
    margin: 48px 0 24px;
    padding: 0 16px;
    text-align: center;
  }
  .footer-links {
    margin-bottom: 16px;
  }
  .footer-links a,
  .footer-links span {
    color: #00000073;
    transition: all 0.3s;
    font-size: 14px;
  }
  .footer-links > :not(:last-child) {
    margin-right: 40px;
  }
  .footer-copyright {
    color: #00000073;
    font-size: 14px;
  }

  .header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    line-height: 48px;
    padding: 0px 16px 0 0;
    background: #2a7cc3;
    // background: linear-gradient(90deg, #2a7cc3 0%, #2f76b1 25%, #4eb9eb 60%, #087b95 100%);
    .right-box {
      align-items: center;
      justify-content: flex-end;
      .ivu-divider {
        height: 24px;
      }
      .ivu-tooltip .ivu-tooltip-rel {
        display: flex;
      }
    }
    .header-icon,
    .ivu-dropdown {
      height: 20px;
      line-height: 20px;
      cursor: pointer;
      font-size: 24px !important;
      color: #fff;
    }
  }

  .logo-wrapper {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 8px 32px;
    background: #2a7cc3;
    // background: linear-gradient(90deg, #385576 0%, #336492 65%, #3172a9 100%);
    z-index: 899;
    position: relative;
    img {
      height: 34px;
    }
  }
  .sub-site-switch-wrapper {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0 8px 0 24px;
    cursor: pointer;
    z-index: 888;
    width: ~'calc(13vw - 24px)';
    position: relative;
    justify-content: space-between;
    .sub-site-icon {
      // color: #2a7cc3;
    }
    .sub-site-title {
      font-size: 16px;
      //   font-weight: 600;
      white-space: nowrap;
    }
  }
  .avatar {
    color: #2a7cc3;
    background-color: #f0faff;
    margin-right: 16px;
  }
  .user-name {
    font-weight: 600;
    color: #fff;
  }
}
</style>

<style lang="less">
.sub-site-switch-dropdown {
  .ivu-dropdown-item-selected {
    border-left: 4px solid;
  }
  .ivu-dropdown-item {
    font-size: 14px;
  }
}
</style>
