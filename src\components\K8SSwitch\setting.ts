const statusSetting = {
  0: { type: 'error', message: '异常' },
  1: { type: 'success', message: '就绪' }
}

export const clusterColumns = [
  {
    title: '集群名',
    key: 'name',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: 'APIServer',
    key: 'apiServer',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: '状态',
    key: 'isReady',
    align: 'center',
    width: 120,
    render: (h, params) => {
      const status = params.row.isReady

      return h(
        'Tag',
        {
          props: {
            color: statusSetting[status].type
          }
        },
        statusSetting[status].message
      )
    }
  },
  {
    title: '描述',
    key: 'desc',
    tooltipTheme: 'light',
    tooltip: true
  }
]

export const namespaceColumns = [
  {
    title: '命名空间',
    key: 'name',
    tooltipTheme: 'light',
    tooltip: true
  },
  {
    title: '状态',
    key: 'phase',
    width: 100,
    align: 'center',
    render: (h, params) => {
      return h(
        'Tag',
        {
          props: {
            color: params.row.phase === 'Active' ? 'success' : 'error'
          }
        },
        params.row.phase
      )
    }
  },
  {
    title: '环境标识',
    key: 'env',
    width: 100,
    render: (h, params) => {
      return h(
        'span',
        params.row.env === '子环境'
          ? {}
          : {
              style: {
                color: '#1890ff'
              }
            },
        params.row.env
      )
    }
  }
]
