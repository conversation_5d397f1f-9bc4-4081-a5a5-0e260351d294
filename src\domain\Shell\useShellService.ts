import { PageList, useGet } from '@/libs/service.request'
import Config from '@/config'
import { useRoute } from '@/libs/useVueInstance'
import { getCurrentInstance, nextTick, onBeforeMount, onMounted, onUnmounted, ref, set } from 'vue'
import { useLoadMore, useRequest } from 'vue-request'
import { Space, CommonIcon } from '@/components'
import { debounce } from 'lodash-es'

const CLUSTER_ENV_SETTING = {
  dev: {
    color: 'success',
    title: '开发环境'
  },
  testing: {
    color: 'primary',
    title: '测试环境'
  },
  preview: {
    color: 'error',
    title: '灰度环境'
  },
  production: {
    color: 'error',
    title: '生产环境'
  }
}

interface PodDataTreeItem {
  podName: string
  containers: string[]
}

export default function useShellService() {
  const route = useRoute()
  const { proxy } = getCurrentInstance()

  const split = ref(0.15)
  const namespace = ref()
  const urlParams = ref()
  const selectContainer = ref()

  const shellList = ref<{ label: string; key: string; url: string }[]>([])
  const contextMenuItem = ref()
  const currentShellKey = ref()
  const filterPodName = ref()
  const isFilterPod = ref(false)
  const mode = ref<'loadMore' | 'refresh'>()

  const renderItem = (h, { root, node, data }, isContainer) => {
    return h(
      Space,
      {
        class: `tree-title`,
        on: {
          click: () => isContainer && onSelectTreeNode(data)
        },
        props: {
          size: 8
        }
      },
      [
        h(CommonIcon, {
          props: {
            type: isContainer ? '_docker' : '_rongqizu',
            size: 14
          }
        }),
        h('span', { attrs: { title: data.title } }, data.title)
      ]
    )
  }
  const onSelectTreeNode = (item) => {
    console.log(111, 'onSelectTreeNode', item)
    selectContainer.value = item.key
    const key = item.key + '@' + Math.random().toString(36)
    shellList.value.push({
      label: item.key,
      key,
      url:
        `/webshell.html?host=${process.env.VUE_APP_GLOB_API_URL}&cluster_id=${urlParams.value.clusterId}&namespace=${
          namespace.value
        }&pod=${item.podName}&container=${item.title}&exec=${'/bin/bash'}` +
        `&token=${proxy.$store.state.user.token}&priority=${urlParams.value.priority}`
    })
    currentShellKey.value = key
  }

  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet<{ data: { name: string }[] }>(`${Config.Api.Base}${Config.Api.GetNamespaceList}`, {
        params: {
          clusterName: urlParams.value.cluster
        }
      })
    },
    {
      manual: true,
      formatResult(res) {
        // if (namespace.value && !res.data.data.find((i) => i.name === namespace.value)) {
        //   proxy.$Message.warning('您没有当前命名空间的权限，请切换其他命名空间查看。')
        // }
        return res.data.data
      }
    }
  )

  const { data: clusterEnvSetting, run: getClusterEnv } = useRequest(
    () => {
      return useGet<{ data: string }>(`${Config.Api.Base}${Config.Api.GetClusterEnvSetting}`, {
        params: { clusterId: urlParams.value.clusterId }
      })
    },
    {
      manual: true,
      formatResult(res) {
        const clusterEnv = res.data.data

        return CLUSTER_ENV_SETTING[clusterEnv]
      }
    }
  )

  const onCloseShell = (name) => {
    shellList.value = shellList.value.filter((i) => i.key !== name)
  }
  const onContextMenu = (data) => {
    contextMenuItem.value = data
  }
  const onCopyShell = () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: '是否确认复制窗口？',
      loading: true,
      onOk: () => {
        const key = contextMenuItem.value.name
        const url = shellList.value?.filter((i) => i.key === key)?.[0]?.url
        const newKey = key.split('@')[0] + '@' + Math.random().toString(36)

        shellList.value.push({
          label: contextMenuItem.value.label,
          key: newKey,
          url
        })
        currentShellKey.value = newKey
        proxy.$Modal.remove()
      }
    })
  }
  const onReloadShell = () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: '是否确认重新连接？',
      loading: true,
      onOk: () => {
        const key = contextMenuItem.value.name

        shellList.value?.forEach((i, index) => {
          if (i.key === key) {
            const newKey = key.split('@')[0] + '@' + Math.random().toString(36)
            currentShellKey.value = newKey
            set(shellList.value, index, { ...i, key: newKey })
          }
        })
        proxy.$Modal.remove()
      }
    })
  }
  const {
    data,
    loading,
    run: getPodTreeData,
    loadingMore,
    noMore
  } = useLoadMore(
    (param?: { data }) => {
      return useGet<PageList<PodDataTreeItem[]>>(`${Config.Api.Base}${Config.Api.GetPodContainerList}`, {
        params: {
          clusterId: urlParams.value.clusterId,
          namespace: namespace.value,
          size: 30,
          page: mode.value === 'loadMore' ? (data.value?.page ?? 1) + 1 : 1,
          search: filterPodName.value
        }
      })
    },
    {
      manual: true,
      isNoMore: () => {
        const { page, size, total } = data?.value ?? {}

        return page * size >= total
      },
      pagination: {
        currentKey: 'page'
      },
      formatResult: (res: { data: PageList<PodDataTreeItem[]> }) => {
        if (mode.value === 'refresh') return { ...res.data, list: formatTree(res.data?.list) }
        return { ...res.data, list: [...(data.value?.list ?? []), ...formatTree(res.data?.list)] }
      },
      onError: (err) => {
        console.log(55, err)
      }
    }
  )

  const formatTree = (pods) => {
    const tree = (
      pods?.map((i) => {
        if (isFilterPod.value && i.podName !== urlParams.value.pod) return null
        return {
          title: i.podName,
          key: i.podName,
          expand: i.podName === urlParams.value.pod,
          render: (h, { root, node, data }) => renderItem(h, { root, node, data }, false),
          children:
            i.containers?.map((container) => ({
              title: container,
              podName: i.podName,
              namespace: namespace.value,
              key: i.podName + ' / ' + container + ' / ' + namespace.value,
              render: (h, { root, node, data }) => renderItem(h, { root, node, data }, true)
            })) ?? []
        }
      }) ?? []
    )?.filter((i) => i !== null)
    return tree
  }
  const refresh = async () => {
    mode.value = 'refresh'

    getPodTreeData()
    const container = document.getElementsByClassName('tree-wrapper')?.[0]
    container?.scrollTo({
      top: 0
    })
  }
  const loadMore = async (scrollTop) => {
    mode.value = 'loadMore'
    await getPodTreeData()
    await nextTick()
    const container = document.getElementsByClassName('tree-wrapper')?.[0]
    container?.scrollTo({
      top: scrollTop
    })
  }

  const onContentScroll = debounce((e) => {
    const { scrollTop, clientHeight, scrollHeight } = e.target
    if (!noMore.value && scrollHeight - (scrollTop + clientHeight) <= 1) {
      loadMore(scrollTop)
    }
  }, 150)

  const onNamespaceChange = () => {
    filterPodName.value = undefined
    isFilterPod.value = false
    refresh()
  }

  const onLeaveAlert = (e) => {
    e.returnValue = '是否确定关闭页面'
    return '是否确定关闭页面'
  }

  onMounted(() => {
    window.addEventListener('beforeunload', onLeaveAlert)
    window.addEventListener('unload', onLeaveAlert)

    namespace.value = route.query.namespace as string
    urlParams.value = {
      clusterId: route.query.clusterId as string,
      cluster: route.query.cluster as string,
      namespace: route.query.namespace as string,
      pod: route.query.pod as string,
      container: route.query.container as string,
      priority: route.query.priority ? JSON.parse(route.query.priority as string) : false
    }
    getPodTreeData()
    isFilterPod.value = route.query.type === 'kubectl'
    getClusterEnv()
    getNamespaceList()
    onSelectTreeNode({
      podName: urlParams.value.pod,
      namespace: urlParams.value.namespace,
      title: urlParams.value.container,
      key: urlParams.value.pod + ' / ' + urlParams.value.container + ' / ' + urlParams.value.namespace
    })
  })

  onUnmounted(() => {
    window.removeEventListener('beforeunload', onLeaveAlert)
    window.removeEventListener('unload', onLeaveAlert)
  })

  return {
    urlParams,
    split,
    namespace,
    namespaceList,
    shellList,
    onCloseShell,
    onContextMenu,
    onCopyShell,
    onReloadShell,
    currentShellKey,
    filterPodName,
    clusterEnvSetting,

    loading,
    data,
    onContentScroll,
    noMore,
    loadingMore,
    refresh,
    onNamespaceChange
  }
}
