<script setup lang="ts">
import { Space, Ellipsis } from '@/components'
import useK8SSwitchService from './useK8SSwitchService'
import { PropType, useSlots } from 'vue'

const props = defineProps({
  onClusterChange: {
    type: Function as PropType<(clusterId, cluster) => void>,
    default: () => {
      console.error('onClusterChange is empty')
    }
  },
  /** 只受到集群变化控制的特殊资源（如网格路由）控制开关，选择cluster时，默认选择第一个namespace */
  setClusterOnly: { type: Boolean, default: false },

  /** 网关管理专供，用来过滤出特定的namespace数据 */
  filterNamespace: {
    type: Function as PropType<(namespace) => boolean>,
    default: () => true
  },

  /** 网关管理专供，当前namespace不在命名空间列表中时，默认取命名空间列表首位 */
  setFirstNamespaceWhileIllegal: { type: Boolean, default: false }
})

const {
  visible,
  currentCluster,
  currentNamespace,
  currentClusterID,
  clusterList,
  namespaceList,
  tempCluster,
  tempClusterID,
  tempNamespace,
  onSearchCluster,
  onSearchNamespace,
  onSelectCluster,
  onCancel,
  onNamespaceChange,
  onOpenPopperShow,
  searchKeyword,
  clusterEnv,
  onClusterEnvChange,
  clusterEnvList,
  namespaceBusiness,
  namespaceBusinessList,
  onNamespaceBusinessChange
} = useK8SSwitchService(props)
const slots = useSlots()
</script>

<template>
  <div>
    <Poptip
      trigger="hover"
      :popper-class="`cluster-switch-poptip${props.setClusterOnly ? ' switch-cluster-only' : ' '}`"
      placement="bottom-end"
      @on-popper-show="onOpenPopperShow"
    >
      <Space class="enter-btn" :size="8">
        <Icon type="md-menu" />
        <b>{{ currentCluster ?? 'none' }}</b>
        <span v-show="!setClusterOnly">/</span>
        <b v-show="!setClusterOnly">{{ currentNamespace ?? 'none' }}</b>
        <!-- 强制引用，触发computed执行 -->
        <b v-show="false"> {{ currentClusterID }}</b>
        <slot name="default" />
      </Space>

      <template slot="content">
        <Alert
          >{{
            props.setClusterOnly ? '选择要操作的集群进行操作。' : '选择要操作的集群和命名空间进行操作。'
          }}
          若无数据，请先<a
            target="_blank"
            href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnvPxbpYWECGpJVo3ZnBf2hd"
            style="color: #2a7cc3"
            >申请权限</a
          >。</Alert
        >
        <div class="filter-wrapper">
          <div class="cluster-env">
            集群环境：
            <Select :value="clusterEnv" clearable @on-change="onClusterEnvChange">
              <Option v-for="item in clusterEnvList" :value="item" :key="item">{{ item }}</Option>
            </Select>
          </div>
          <div>
            业务归属：
            <Select :value="namespaceBusiness" clearable @on-change="onNamespaceBusinessChange">
              <Option v-for="item in namespaceBusinessList" :value="item" :key="item">{{ item }}</Option>
            </Select>
          </div>
        </div>
        <div class="header-content-wrapper">
          <div class="box left">
            <Input
              v-model="searchKeyword.cluster"
              placeholder="请搜索集群"
              style="margin-bottom: 8px"
              search
              clearable
              @on-search="() => onSearchCluster(clusterEnv)"
            />
            <RadioGroup v-model="tempClusterID" vertical @on-change="onSelectCluster">
              <template v-for="item in clusterList">
                <Radio :label="item.id" :key="item.id" :id="item.id">
                  <Ellipsis type="text">{{ item.name }}</Ellipsis>
                </Radio>
              </template>
            </RadioGroup>
          </div>
          <div v-if="!slots['namespace']">
            <div class="box right" v-show="!setClusterOnly">
              <Input
                v-model="searchKeyword.namespace"
                placeholder="请搜索命名空间"
                style="margin-bottom: 8px"
                search
                clearable
                @on-search="() => onSearchNamespace(namespaceBusiness)"
              />
              <RadioGroup v-model="tempNamespace" vertical @on-change="onNamespaceChange">
                <template v-for="item in namespaceList">
                  <Radio :label="item.name" :key="tempClusterID + item.name" :id="tempClusterID + item.name">
                    <Ellipsis type="text">{{ item.name }}</Ellipsis>
                  </Radio>
                </template>
              </RadioGroup>
            </div>
          </div>
          <div v-else class="box right">
            <slot name="namespace" />
          </div>
        </div>
      </template>
    </Poptip>

    <Modal :value="visible" title="选择 Cluster / Namespace" @on-cancel="onCancel" :mask-closable="false" footer-hide>
      <div slot="close">
        <Icon type="ios-close" @click.stop="onCancel" />
      </div>
      <Alert
        >选择要操作的集群和命名空间进行操作。 若无数据，请先<a
          target="_blank"
          href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnvPxbpYWECGpJVo3ZnBf2hd"
          style="color: #2a7cc3"
          >申请权限</a
        ></Alert
      >

      <Space class="enter-btn" :size="8" style="margin-bottom: 16px">
        <span>当前选择：</span>
        <b :class="tempCluster ?? 'empty'">{{ tempCluster ?? 'none' }}</b>
        <span>/</span>
        <b :class="tempNamespace ?? 'empty'">{{ tempNamespace ?? 'none' }}</b>
      </Space>

      <div class="header-content-wrapper">
        <div class="box left">
          <Input
            v-model="searchKeyword.cluster"
            placeholder="请搜索集群"
            style="margin-bottom: 8px"
            search
            clearable
            @on-search="() => onSearchCluster()"
          />
          <RadioGroup v-model="tempClusterID" vertical @on-change="onSelectCluster">
            <template v-for="item in clusterList">
              <Radio :label="item.id" :key="item.id">
                <Ellipsis type="text">{{ item.name }}</Ellipsis>
              </Radio>
            </template>
          </RadioGroup>
        </div>
        <div class="box right">
          <Input
            v-model="searchKeyword.namespace"
            placeholder="请搜索命名空间"
            style="margin-bottom: 8px"
            search
            clearable
            @on-search="() => onSearchNamespace()"
          />
          <RadioGroup v-model="tempNamespace" vertical @on-change="onNamespaceChange">
            <template v-for="item in namespaceList">
              <Radio :label="item.name" :key="tempClusterID + item.name">
                <Ellipsis type="text">{{ item.name }}</Ellipsis>
              </Radio>
            </template>
          </RadioGroup>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.ivu-table .demo-table-info-row td {
  background-color: #ebf7ff;
  /*color: #fff;*/
}
.enter-btn {
  cursor: pointer;
  color: #2a7cc3;
  white-space: nowrap;
  align-items: center;
  .empty {
    color: #ed4014;
  }
}

/deep/.cluster-switch-poptip {
  .ivu-poptip-title {
    display: none;
  }
  .ivu-poptip-body {
    padding: 16px;
  }
  &.switch-cluster-only .header-content-wrapper {
    width: 360px;
  }
}

.filter-wrapper {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
  > div {
    flex: 1;
    .ivu-select {
      width: ~'calc(100% - 65px)';
    }
  }
  .cluster-env {
    margin-right: 16px;
  }
}

.header-content-wrapper {
  width: 480px;
  display: flex;
  justify-content: space-between;

  .box {
    flex: 1 0 0%;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    &.right {
      margin-left: 16px;
    }
    .ivu-radio-group {
      flex-direction: column;
      align-items: flex-start;
      height: 240px;
      overflow-y: auto;
      overflow-x: hidden;
      .ivu-radio-wrapper {
        display: flex;
        align-items: center;
        .ellipsis-text-wrapper {
          width: 160px;
        }
      }
    }
  }
}
</style>
