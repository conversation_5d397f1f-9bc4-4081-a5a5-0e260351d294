import Config from '@/config'
import { PropType, defineComponent } from 'vue'
import styled from 'vue-styled-components'
import Ellipsis from './ellipsis'

const Button = styled('a', {
  type: { type: String as PropType<'default' | 'danger'> },
  ellipsis: { type: <PERSON><PERSON><PERSON> },
  disabled: { type: <PERSON><PERSON>an }
})`
  color: ${(props) => (props.type === 'default' ? Config.Theme.Color.primary : Config.Theme.Color.error)};
  height: 14px;
  display: block;
  line-height: 14px;
  width: ${(props) => (props.ellipsis ? '100%' : 'auto')};
  overflow: ${(props) => (props.ellipsis ? 'hidden' : 'inherit')};
  text-overflow: ${(props) => (props.ellipsis ? 'ellipsis' : 'unset')};
  white-space: nowrap;
  &:hover {
    color: ${(props) =>
      props.disabled ? '#9e9e9e' : props.type === 'default' ? Config.Theme.Color.primary : Config.Theme.Color.error};
  }
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  color: ${(props) => (props.disabled ? '#9e9e9e' : '')};
`
export default defineComponent({
  name: 'LinkButton',
  props: {
    text: { type: String, default: '' },
    type: { type: String as PropType<'default' | 'danger'>, default: 'default' },
    ellipsis: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    tooltip: { type: Boolean, default: false }
  },
  components: { Ellipsis },
  setup(props, { attrs, emit }) {
    const onClick = (e) => {
      emit('click', e)
    }
    return () => (
      <Button {...attrs} onClick={(e) => !props.disabled && onClick(e)} type={props.type} disabled={props.disabled}>
        {props.ellipsis ? <Ellipsis type={props.tooltip ? 'tooltip' : 'text'}>{props.text}</Ellipsis> : props.text}
      </Button>
    )
  }
})
