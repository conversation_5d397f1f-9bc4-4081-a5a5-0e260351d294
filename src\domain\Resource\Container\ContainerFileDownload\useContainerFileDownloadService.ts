import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { useRequest } from 'vue-request'
import { getCurrentInstance, ref, watch } from 'vue'
import { cloneDeep } from 'lodash-es'
import { Space } from '@/components'
import { Message } from 'view-design'

interface Entity {
  container: string
  clusterId: number
  namespace: string
  pod: string
}

type File = {
  name: string
  size: number
  lastModifyTime: string
}

interface TreeData {
  title: string
  loading: boolean
  children?: TreeData[]
  expand?: boolean
  selected?: boolean
  dir: string
}

const rootFileTree: TreeData[] = [
  {
    title: '/',
    dir: '',
    loading: false,
    expand: true,
    children: []
  }
]
export default function useContainerFileDownloadService(props: { entity: Entity; value: boolean }) {
  const selectDirPath = ref()
  const split = ref(0.3)
  const fileTreeData = ref()
  const tempFileList = ref()
  const { proxy } = getCurrentInstance()

  const renderItem = (h, { root, node, data }) => {
    return h(
      Space,
      {
        class: `dir-title ${data.dir === selectDirPath.value ? 'selected' : ''}`,
        on: {
          click: () => onSelectTreeNode(data)
        }
      },
      [
        h('Icon', {
          props: {
            type: 'ios-folder'
          }
        }),
        h('span', { attrs: { title: data.title } }, data.title)
      ]
    )
  }

  const {
    data: fileList,
    loading: fileListLoading,
    run: getFileList
  } = useRequest(
    () => {
      return useGet<{ data: File[] }>(
        `${Config.Api.Base}${Config.Api.GetContainerFileList}?clusterId=${props.entity.clusterId}&namespace=${
          props.entity.namespace
        }&pod=${props.entity.pod}&container=${props.entity.container}&dirPath=${selectDirPath.value + '/'}`
      )
    },
    {
      manual: true,
      formatResult(res) {
        tempFileList.value = res.data?.data ?? []
        return res.data?.data ?? []
      }
    }
  )

  const onSelectTreeNode = (item: TreeData) => {
    console.log(111, 'onSelectTreeNode', item)
    selectDirPath.value = item.dir
  }
  const getDirChildren = async (dir) => {
    const res = await useGet<{ data: File[] }>(
      `${Config.Api.Base}${Config.Api.GetContainerDirList}?clusterId=${props.entity.clusterId}&namespace=${
        props.entity.namespace
      }&pod=${props.entity.pod}&container=${props.entity.container}&dirPath=${dir + '/'}`
    )

    return (
      res.data?.data?.map((file) => ({
        title: file.name,
        dir: `${dir + '/'}${file.name}`,
        loading: false,
        expand: false,
        children: [],
        render: renderItem
      })) ?? []
    )
  }

  const loadTreeChildren = async (item, callback) => {
    const children = await getDirChildren(item.dir)
    if (!children?.length) {
      callback([])
      fileTreeData.value = deleteItemFormTreeData(cloneDeep(fileTreeData.value), item.dir)
    } else {
      callback(children)
    }
  }

  const deleteItemFormTreeData = (tree: TreeData[], dir: string): TreeData[] => {
    tree?.forEach((i, index, arr) => {
      if (i.dir !== dir) {
        deleteItemFormTreeData(i.children, dir)
      } else {
        delete arr[index].loading
        delete arr[index].children
      }
    })
    return tree
  }

  const onDownloadFile = async (file: File) => {
    console.log('file', file)
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认下载${file.name}？`,
      loading: true,
      onOk: async () => {
        const url = `${Config.Api.Base}${Config.Api.DownloadContainerFile}?clusterId=${
          props.entity.clusterId
        }&namespace=${props.entity.namespace}&pod=${props.entity.pod}&container=${props.entity.container}&filePath=${
          selectDirPath.value + '/' + file.name
        }`
        window.open(url, '_blank')
        proxy.$Modal.remove()

        return
      }
    })
  }

  const onSearchFileName = (value) => {
    if (value) {
      tempFileList.value = fileList.value?.filter((file) => file.name.includes(value))
    } else {
      tempFileList.value = fileList.value
    }
  }

  const onUpload = async (file) => {
    const formData = new FormData()

    formData.append('clusterId', String(props.entity.clusterId))
    formData.append('namespace', props.entity.namespace)
    formData.append('pod', props.entity.pod)
    formData.append('container', props.entity.container)
    formData.append('destFilePath', selectDirPath.value + '/' + file.name)
    formData.append('uploadFile', file, file.name)

    const res = await usePost(`${Config.Api.Base}${Config.Api.UploadContainerFile}`, formData)

    if (res.success) {
      proxy.$Message.success('上传成功')
      getFileList()
    }

    return false
  }

  watch(
    () => props.value,
    async () => {
      if (props.value) {
        const children = await getDirChildren('')
        fileTreeData.value = [{ ...rootFileTree[0], children, render: renderItem }]
        selectDirPath.value = ''
      }
    }
  )

  watch(selectDirPath, () => {
    getFileList()
  })

  return {
    split,
    fileTreeData,
    loadTreeChildren,
    selectDirPath,
    tempFileList,
    fileListLoading,
    onDownloadFile,
    onSearchFileName,
    onUpload
  }
}
