export enum EnumFavorResourceTitle {
  Deployment = 'Deployment',
  StatefulSet = 'StatefulSet',
  Service = 'Service',
  Pod = 'Pod',
  HPA = 'HPA',
  Configmap = 'Configmap',
  Secrets = 'Secrets',

  GW = 'Gateway',
  VS = 'VirtualService',
  DR = 'DestinationRule',
  SC = 'SideCar',
  SE = 'ServiceEntry',
  EP = 'EndPoint',
  PVC = 'PVC',
  CRD = 'CRD',
  Job = 'Job',
  CronJob = 'CronJob',
  Telemetry = 'Telemetry'
}

export enum EnumFavorResourcePath {
  Deployment = '/kubernetes/resource/namespace/workloads-deployment-list',
  StatefulSet = '/kubernetes/resource/namespace/workloads-stateful-set-list',
  Service = '/kubernetes/resource/namespace/services',
  Pod = '/kubernetes/resource/namespace/pod',
  HPA = '/kubernetes/resource/namespace/hpa',
  Configmap = '/kubernetes/resource/namespace/configmap',
  Secrets = '/kubernetes/resource/namespace/secrets',

  GW = '/kubernetes/resource/mesh/route-map',
  VS = '/kubernetes/resource/mesh/virtualservice',
  DR = '/kubernetes/resource/mesh/destinationrule',
  SC = '/kubernetes/resource/mesh/serviceentry',
  SE = '/kubernetes/resource/mesh/sidecar',
  EP = '/kubernetes/resource/namespace/ep',
  CRD = '/kubernetes/resource/namespace/crd',
  Job = '/kubernetes/resource/namespace/job',
  CronJob = '/kubernetes/resource/namespace/cron-job',
  Telemetry = '/kubernetes/resource/mesh/telemetry'
}

export enum EnumFavorResourceName {
  Deployment = 'deployments',
  StatefulSet = 'statefulsets',
  Service = 'services',
  Pod = 'pods',
  HPA = 'horizontalpodautoscalers',
  Configmap = 'configmaps',
  Secrets = 'secrets',

  GW = 'gateways',
  VS = 'virtualservices',
  DR = 'destinationrules',
  SC = 'sidecars',
  SE = 'serviceentries',

  EP = 'endpoints',
  PVC = 'persistentvolumeclaims',

  CRD = 'customresourcedefinitions',
  Job = 'jobs',
  CronJob = 'cronjobs',
  Telemetry = 'telemetries',

  RayJob = 'rayjobs',
  //   bindings = 'bindings',
  //   endpoints = 'endpoints',
  //   nodes = 'nodes',
  //   jobs = 'jobs',
  //   challenges = 'challenges',
  //   orders = 'orders',
  //   daemonsets = 'daemonsets',
  //   cronjobs = 'cronjobs',
  //   componentstatuses = 'componentstatuses',
  //   limitranges = 'limitranges',
  //   namespaces = 'namespaces',
  //   persistentvolumeclaims = 'persistentvolumeclaims',
  //   persistentvolumes = 'persistentvolumes',
  //   podtemplates = 'podtemplates',
  //   replicationcontrollers = 'replicationcontrollers',
  //   resourcequotas = 'resourcequotas',
  //   serviceaccounts = 'serviceaccounts',
  //   mutatingwebhookconfigurations = 'mutatingwebhookconfigurations',
  //   validatingwebhookconfigurations = 'validatingwebhookconfigurations',
  //   customresourcedefinitions = 'customresourcedefinitions',
  //   apiservices = 'apiservices',
  //   controllerrevisions = 'controllerrevisions',
  //   replicasets = 'replicasets',
  //   tokenreviews = 'tokenreviews',
  //   localsubjectaccessreviews = 'localsubjectaccessreviews',
  //   selfsubjectaccessreviews = 'selfsubjectaccessreviews',
  //   selfsubjectrulesreviews = 'selfsubjectrulesreviews',
  //   subjectaccessreviews = 'subjectaccessreviews',
  //   certificaterequests = 'certificaterequests',
  //   certificates = 'certificates',
  //   clusterissuers = 'clusterissuers',
  //   issuers = 'issuers',
  //   certificatesigningrequests = 'certificatesigningrequests',
  //   logconfigs = 'logconfigs',
  //   pediaclusters = 'pediaclusters',
  //   envoyproxies = 'envoyproxies',
  //   leases = 'leases',
  //   endpointslices = 'endpointslices',
  //   events = 'events',
  //   wasmplugins = 'wasmplugins',
  //   's0-cron-Asia-Shanghai-10xxxx-25xxxx' = 's0-cron-Asia-Shanghai-10xxxx-25xxxx',
  //   's0-cron-Asia-Shanghai-30xxxx-50xxxx' = 's0-cron-Asia-Shanghai-30xxxx-50xxxx',
  //   's0-cron-Asia-Shanghai-45xxxx-50xxxx' = 's0-cron-Asia-Shanghai-45xxxx-50xxxx',
  //   flowschemas = 'flowschemas',
  //   prioritylevelconfigurations = 'prioritylevelconfigurations',
  //   authenticationfilters = 'authenticationfilters',
  //   ratelimitfilters = 'ratelimitfilters',
  //   gatewayclasses = 'gatewayclasses',
  //   grpcroutes = 'grpcroutes',
  //   httproutes = 'httproutes',
  //   referencegrants = 'referencegrants',
  //   tcproutes = 'tcproutes',
  //   tlsroutes = 'tlsroutes',
  //   udproutes = 'udproutes',
  //   istiooperators = 'istiooperators',
  //   clustertriggerauthentications = 'clustertriggerauthentications',
  //   scaledjobs = 'scaledjobs',
  //   scaledobjects = 'scaledobjects',
  //   triggerauthentications = 'triggerauthentications',
  //   envoyfilters = 'envoyfilters',
  //   proxyconfigs = 'proxyconfigs',
  //   workloadentries = 'workloadentries',
  //   workloadgroups = 'workloadgroups',
  //   ingressclasses = 'ingressclasses',
  //   ingresses = 'ingresses',
  //   networkpolicies = 'networkpolicies',
  //   runtimeclasses = 'runtimeclasses',
  //   poddisruptionbudgets = 'poddisruptionbudgets',
  //   pediaclusterlifecycles = 'pediaclusterlifecycles',
  //   clusterrolebindings = 'clusterrolebindings',
  //   clusterroles = 'clusterroles',
  //   rolebindings = 'rolebindings',
  //   roles = 'roles',
  //   priorityclasses = 'priorityclasses',
  //   authorizationpolicies = 'authorizationpolicies',
  //   peerauthentications = 'peerauthentications',
  //   requestauthentications = 'requestauthentications',
  //   csidrivers = 'csidrivers',
  //   csinodes = 'csinodes',
  //   csistoragecapacities = 'csistoragecapacities',
  //   storageclasses = 'storageclasses',
  //   volumeattachments = 'volumeattachments',
}

const getEnumFavorResourceNameToKey = () => {
  const EnumFavorResourceNameToKey = {}
  for (const [key, value] of Object.entries(EnumFavorResourceName)) {
    EnumFavorResourceNameToKey[value] = key
  }
  return EnumFavorResourceNameToKey
}

export const EnumFavorResourceNameToKey = getEnumFavorResourceNameToKey()

const getEnumFavorResourceTitleToName = () => {
  const EnumFavorResourceTitleToName = {}
  for (const [key, value] of Object.entries(EnumFavorResourceTitle)) {
    EnumFavorResourceTitleToName[value] = key
  }
  return EnumFavorResourceTitleToName
}

export const EnumFavorResourceTitleToName = getEnumFavorResourceTitleToName()
