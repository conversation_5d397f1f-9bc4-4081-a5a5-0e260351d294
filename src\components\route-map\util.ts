const canvas = document.createElement('canvas')
const context = canvas.getContext('2d')

/**
 * 根据文本宽度，返回文本长度
 * @param text 文本
 * @param fontSize 文本大小
 */
export const getStringWidth = (text: string, fontSize: number) => {
  context.font = `normal ${fontSize}px "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif`
  return Math.floor(context.measureText(text).width)
}

/**
 * 根据文本宽度，返回超长省略的字符串
 * @param text 文本
 * @param width 文本宽度
 * @param fontSize 文本大小
 */
export const getEllipsisText = (text: string, width: number, fontSize: number): string => {
  const ellipsisText = '...'
  if (width < 16) {
    return text.slice(0, 1) + ellipsisText
  }
  if (context && text) {
    const labelWidth = getStringWidth(text, fontSize)
    if (labelWidth > width) {
      const ellipsisWidth = getStringWidth(ellipsisText, fontSize)
      let tempWidth = labelWidth
      while (tempWidth > width - ellipsisWidth) {
        text = text.slice(0, -1)
        tempWidth = getStringWidth(text, fontSize)
      }
      return text + ellipsisText
    } else {
      return text
    }
  }
  return text
}

/**
 * 根据文本宽度，返回文本切片
 * @param text 文本
 * @param width 文本宽度
 * @param fontSize 文本大小
 */
export const getStringArrayByWidth = (text: string, width: number, fontSize: number): string[] => {
  if (context && text) {
    const labelWidth = getStringWidth(text, fontSize)
    if (labelWidth > width) {
      const textArr = []

      while (text.length > 0) {
        let tempText = ''
        let tempWidth = 0
        let tempIndex = 0
        while (tempWidth < width && tempText.length !== text.length) {
          tempIndex++
          tempText = text.slice(0, tempIndex)
          tempWidth = getStringWidth(tempText, fontSize)
        }
        textArr.push(tempText)
        text = text.slice(tempIndex)
      }
      return textArr
    } else {
      return [text]
    }
  }
  return [text]
}

/**
 * 根据文本和日期宽度，返回填充的文本
 * @param text 文本
 * @param width 文本宽度
 * @param fontSize 文本大小
 */
export const getFullWidthText = (text: string, width: number, fontSize: number): string => {
  if (context && text) {
    const labelWidth = getStringWidth(text, fontSize)
    const counter = Math.floor(width / labelWidth)
    if (counter > 1) {
      return new Array(counter).fill(text).join('')
    } else {
      return text
    }
  }
  return text
}

/**
 * 获取节点的下属所有节点
 * @param node
 */
export const getChildrenNodes = (node) => {
  const nodes = node.getNeighbors('target')
  const children = nodes
    ?.map((i) => getChildrenNodes(i))
    ?.flat()
    ?.filter((i) => i)
  return [...nodes, ...children]
}
