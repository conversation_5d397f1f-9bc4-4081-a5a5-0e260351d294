<script lang="ts" setup>
import { set, PropType } from 'vue'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import { DetailDrawer, DetailCard } from '@/components/detail-drawer'
import { ResourceForm, EnumFormStatus } from '@/components/resource-form'
import { Ellipsis, Space } from '@/components'
import {
  ProFormSelect,
  ProFormText,
  ProFormItem,
  ArrayObject,
  ProFormRadio,
  EnumFormItemControllerType,
  MultiInput
} from '@/components/pro-form'
import useDestinationRuleService from './useDestinationRuleService'
import {
  formatEnumToLabelValue,
  EnumComponentType,
  EnumBasic,
  EnumLbPolicySimple,
  EnumConsistentHash,
  EnumHTTP2UpgradePolicy
} from './enum'
import { TRAFFIC_POLICIES_TABLE_COLUMNS, RELATED_POD_COLUMNS, DETAIL_BASE_INFO } from './setting'
import { genNonDuplicateArr } from '@/libs/tools'
import { BatchCopy, EnumIstioResourceType } from '@/domain/Resource'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType.Associated | EnumComponentType.Independent>,
    default: EnumComponentType.Independent
  },
  // 初始化请求的参数
  name: { type: String }
})

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onCopy,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  formInitData,
  onInitFormat,
  onSubmitFormat,
  onSubmitTrafficPolicies,
  onSubmitSuccess,
  onViewDetail,
  viewDetailVisible,
  tempHostOption,
  exportToList,
  clusterRegionZoneCascaderList,

  labelOptions,
  getLabelOptions,
  validateLabels,
  trafficPoliciesFormRef,
  modal,
  onCopySubmit,
  clusterCascaderList,
  labelOptionsLoading,
  showMoreNotice,
  columns,
  onSubmitDistributeFrom,
  onDeleteDistributeFrom,
  onSubmitDistributeTo,
  onDeleteDistributeTo,
  batchCopyModalVisible
} = useDestinationRuleService(props)
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p style="margin-bottom: 16px">
          当前页面中您可以管理 DestinationRule 资源, 主要用于对目标路由的负载均衡, 连接池, 熔断配置等,
          默认情况对所有空间生效；
        </p>
        <div v-show="showMoreNotice">
          <p style="font-weight: 600">&nbsp;&nbsp;&nbsp;&nbsp;配置技巧：</p>
          <p>
            &nbsp;&nbsp;&nbsp;&nbsp; <Badge status="default" />
            <span style="color: #2a7cc3">跨空间配置DR：</span>
            当您需要跨空间配置DR规则时，请前往上游服务的空间进行DR规则配置，并将规则的ExportTo设置为调用（下游）空间。
          </p>
          <p v-if="props.type === EnumComponentType.Associated" style="font-weight: 600">
            &nbsp;&nbsp;&nbsp;&nbsp;配置限制：
          </p>
          <p v-if="props.type === EnumComponentType.Associated">
            &nbsp;&nbsp;&nbsp;&nbsp;<Badge status="default" /><span style="color: #2a7cc3">Host：</span>
            只允许选择当前应用关联的 Service.
          </p>
        </div>
        <LinkButton
          v-if="props.type === EnumComponentType.Associated && !showMoreNotice"
          text="查看更多"
          @click="showMoreNotice = true"
        />
        <LinkButton
          v-if="props.type === EnumComponentType.Associated && showMoreNotice"
          text="收起信息"
          @click="showMoreNotice = false"
        />
      </Space>
    </Alert>
    <pro-table
      :columns="columns"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :on-create="onCreate"
      v-bind="{
        ...(props.type === EnumComponentType.Independent
          ? {
              search: [{ value: 'keyword', label: '名称', initData: props.name }]
            }
          : { pagination: false })
      }"
    >
      <template #operate-buttons>
        <Button
          v-if="props.type === EnumComponentType.Independent"
          size="small"
          type="primary"
          ghost
          icon="md-copy"
          @click="() => (batchCopyModalVisible = true)"
          >批量复制</Button
        >
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button v-if="props.type === EnumComponentType.Independent" @click="() => onCopy(row)" text="复制" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>
    <resource-form
      :type="props.type"
      resourceType="destinationrule"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      :onInitFormat="onInitFormat"
      :onSubmitFormat="onSubmitFormat"
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" :readonly="true" />
        <ProFormText name="name" label="Name（名称）" :data="data" :readonly="formStatus === EnumFormStatus.Edit" />
        <ProFormSelect
          name="host"
          label="Host（关联主机）"
          :data="data"
          :options="tempHostOption"
          allow-create
          :dataReloadFlag="formVisible"
          :afterChange="(host) => host && getLabelOptions(host)"
          desc="主机名称，可以是k8s service，service entry，或者在服务注册中心中存在的服务名。"
          url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LoadBalancerSettings:~:text=Required-,host,-string"
        />
        <ProFormItem
          name="trafficPolicies"
          label="TrafficPolicies（传输策略表）"
          :data="data"
          contentStyleMode="background"
          desc="配置流量策略，例如：负载均衡策略、连接池大小、异常值检测。目前只支持负载均衡策略。"
          url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#TrafficPolicy"
        >
          <ArrayObject
            label="策略"
            :data="data"
            :modalWidth="40"
            name="trafficPolicies"
            addButtonPosition="top"
            canEdit
            :columns="TRAFFIC_POLICIES_TABLE_COLUMNS"
            :getAddData="() => (!!data.trafficPolicies?.find((i) => i.type === 'default') ? { type: 'subset' } : {})"
            @on-ok="
              (type, record, index, callback) => {
                trafficPoliciesFormRef.validate((valid) => {
                  valid && onSubmitTrafficPolicies(data, type, record, index)
                  callback(!valid)
                })
              }
            "
            @on-delete="(index) => data.trafficPolicies?.splice(index, 1)"
          >
            <template #alter>如果标签为红色，表示此标签已失效，请进行处理</template>
            <template #table-labels="{ row }">
              <template v-for="item in row.labels?.data">
                <div :key="item">
                  <Spin v-if="labelOptionsLoading" fix />
                  <Ellipsis
                    v-else
                    :style="`color:${
                      labelOptions?.filter((i) => i.value === item)?.[0]?.invalid ? 'red' : 'rgb(81, 90, 110)'
                    }`"
                  >
                    {{ item ?? '-' }}
                  </Ellipsis>
                </div>
              </template>
            </template>
            <template #default="{ record, visible, index }">
              <Form
                @submit.native.prevent
                :model="record"
                :rules="{ labels: [{ validator: validateLabels, trigger: 'blur' }] }"
                ref="trafficPoliciesFormRef"
              >
                <Alert show-icon>默认(Default) 策略只允许存在一个。</Alert>
                <ProFormRadio
                  name="type"
                  label="Type（策略类型）"
                  :data="record"
                  :options="[
                    {
                      label: 'Default（默认策略）',
                      value: 'default',
                      disabled:
                        data.trafficPolicies?.[index]?.type !== 'default' &&
                        !!data.trafficPolicies?.find((i) => i.type === 'default')
                    },
                    {
                      label: 'Subset（子策略）',
                      value: 'subset'
                    }
                  ]"
                  :afterChange="
                    (type) => {
                      set(record, 'name', type === 'default' ? '#default#' : undefined)
                    }
                  "
                />
                <ProFormText
                  :disabled="record?.type === 'default'"
                  name="name"
                  label="Name（名称）"
                  :data="record"
                  placeholder="v1"
                />
                <ProFormSelect
                  v-if="record?.type === 'subset'"
                  name="labels"
                  label="Label（标签，匹配Pod标签）"
                  :data="record"
                  :dataReloadFlag="record.type"
                  :mode="EnumFormItemControllerType.Switch"
                  :options="labelOptions"
                  multiple
                  desc="通过配置label来将service的endpoint划分为不同的子集。"
                  url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#Subset:~:text=Yes-,labels,-map%3Cstring%2C%C2%A0string"
                  :after-change="trafficPoliciesFormRef?.validate"
                />
                <ProFormItem
                  name="loadBalancer"
                  label="LoadBalancer（负载均衡）"
                  :data="record"
                  :dataReloadFlag="visible"
                  contentStyleMode="border"
                  :mode="EnumFormItemControllerType.Switch"
                  desc="配置负载均衡策略。"
                  url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LoadBalancerSettings"
                  :initData="{
                    lbPolicy: {},
                    locality: {}
                  }"
                  contentClassName="load-balancer-content"
                >
                  <ProFormItem
                    name="lbPolicy"
                    label="基础策略"
                    :data="record?.loadBalancer?.data"
                    :dataReloadFlag="visible"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    contentClassName="lb-policy-content"
                  >
                    <template>
                      <ProFormItem
                        name="data"
                        :label="null"
                        :data="record.loadBalancer?.data.lbPolicy"
                        :dataReloadFlag="visible"
                        contentStyleMode="dashBorder"
                        :mode="EnumFormItemControllerType.Chosen"
                        :chosenOptions="formatEnumToLabelValue(EnumBasic)"
                        contentClassName="lb-policy-data-content"
                      >
                        <template #simple>
                          <ProFormSelect
                            name="simple"
                            label="负载均衡算法"
                            :data="record.loadBalancer?.data.lbPolicy.data"
                            :options="formatEnumToLabelValue(EnumLbPolicySimple)"
                            desc="标准的负载均衡算法。"
                            url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LoadBalancerSettings-SimpleLB"
                          />
                        </template>
                        <template #consistentHash>
                          <ProFormItem
                            name="consistentHash"
                            label="会话保持方式"
                            :data="record.loadBalancer?.data.lbPolicy.data"
                            :mode="EnumFormItemControllerType.Chosen"
                            :chosenOptions="formatEnumToLabelValue(EnumConsistentHash)"
                            :dataReloadFlag="record.loadBalancer?.data.lbPolicy.data.chosenCtl"
                            :init-data="{ useSourceIp: 'true', chosenCtl: 'useSourceIp' }"
                            desc="一致性hash算法。"
                            url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LoadBalancerSettings-ConsistentHashLB"
                          >
                            <template #useSourceIp>
                              <ProFormRadio
                                name="useSourceIp"
                                label="根据源IP"
                                :data="record.loadBalancer?.data.lbPolicy.data.consistentHash"
                                :options="[{ label: '是', value: 'true' }]"
                              />
                            </template>
                            <template #httpHeaderName>
                              <ProFormText
                                name="httpHeaderName"
                                label="请求头部名"
                                :data="record.loadBalancer?.data.lbPolicy.data.consistentHash"
                              />
                            </template>
                            <template #httpQueryParameterName>
                              <ProFormText
                                name="httpQueryParameterName"
                                label="请求参数名"
                                :data="record.loadBalancer?.data.lbPolicy.data.consistentHash"
                              />
                            </template>
                          </ProFormItem>
                        </template>
                      </ProFormItem>
                    </template>
                  </ProFormItem>
                  <ProFormItem
                    name="locality"
                    label="跨地域负载均衡"
                    :data="record?.loadBalancer?.data"
                    :dataReloadFlag="visible"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="跨地域的流量配置，例如 跨地域负载均衡，故障转移。目前只支持跨地域负载均衡"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LocalityLoadBalancerSetting"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <template>
                      <ProFormRadio
                        name="enabled"
                        label="Enabled（是否启动本地负载均衡）"
                        :data="record.loadBalancer?.data.locality.data"
                        :mode="EnumFormItemControllerType.Switch"
                        isBoolean
                        :dataReloadFlag="visible"
                        desc="这里是目标规则级别,并将完全覆盖网格范围设置. 如果选了true,表示无论网格宽度设置成什么,都为此DestinationRule开启局部负载平衡。"
                        url="https://istio.io/latest/zh/docs/reference/config/networking/destination-rule/#LocalityLoadBalancerSetting"
                      />
                      <ProFormItem
                        name="data"
                        :label="null"
                        :data="record.loadBalancer?.data.locality"
                        :dataReloadFlag="visible"
                        :mode="EnumFormItemControllerType.Chosen"
                        :chosenOptions="[
                          {
                            label: 'Distribute（流量分发）',
                            value: 'distribute'
                          },
                          {
                            label: 'Failover（故障转移）',
                            value: 'failover',
                            disabled: true
                          }
                        ]"
                        :initData="{ distribute: [{}] }"
                      >
                        <template #distribute>
                          <Alert show-icon>
                            跨地域的流量配置（
                            <a
                              href="https://istio.io/latest/docs/reference/config/networking/destination-rule/#LocalityLoadBalancerSetting-Distribute"
                              target="_blank"
                            >
                              官方文档
                            </a>
                            ）
                          </Alert>
                          <MultiInput
                            addLabel="添加分发策略"
                            hidden-default-delete-btn
                            add-button-position="top"
                            :data="record?.loadBalancer?.data?.locality?.data"
                            name="distribute"
                            :on-delete="
                              (index) => record?.loadBalancer?.data?.locality?.data?.distribute.splice(index, 1)
                            "
                            :on-add="(index) => record?.loadBalancer?.data?.locality?.data?.distribute.splice(0, 0, {})"
                            :on-init="
                              () =>
                                genNonDuplicateArr(record?.loadBalancer?.data?.locality?.data?.distribute?.length || 0)
                            "
                          >
                            <template #default="{ index, onDelete }">
                              <div class="multi-item-wrapper">
                                <Card :bordered="false">
                                  <Icon type="md-close" @click="() => onDelete()" />

                                  <ProFormItem
                                    name="from"
                                    label="From（流量来源）"
                                    :data="record?.loadBalancer?.data?.locality?.data?.distribute?.[index]"
                                    contentStyleMode="background"
                                  >
                                    <ArrayObject
                                      label="流量来源"
                                      add-button-position="top"
                                      name="from"
                                      :max="1"
                                      :initData="{}"
                                      :data="record?.loadBalancer?.data?.locality?.data?.distribute?.[index]"
                                      @on-ok="
                                        (type, data, itemIndex) =>
                                          onSubmitDistributeFrom(data, type, record, index, itemIndex)
                                      "
                                      @on-delete="
                                        (itemIndex) => {
                                          onDeleteDistributeFrom(record, index, itemIndex)
                                        }
                                      "
                                    >
                                      <template #custom-data="{ data, onDelete }">
                                        <div v-for="(row, index) in data" :key="index" class="distribute-to-item">
                                          <span>{{ row }}</span>
                                          <LinkButton @click="() => onDelete(index)" type="danger" text="删除" />
                                        </div>
                                      </template>
                                      <template #default="{ record }">
                                        <Cascader
                                          :data="clusterRegionZoneCascaderList"
                                          v-model="record.key"
                                          filterable
                                        />
                                      </template>
                                    </ArrayObject>
                                  </ProFormItem>
                                  <ProFormItem
                                    name="to"
                                    label="To（流量目标）"
                                    :data="record?.loadBalancer?.data?.locality?.data?.distribute?.[index]"
                                    contentStyleMode="background"
                                  >
                                    <ArrayObject
                                      label="流量目标"
                                      add-button-position="top"
                                      name="to"
                                      :initData="{}"
                                      :data="record?.loadBalancer?.data?.locality?.data?.distribute?.[index]"
                                      @on-ok="
                                        (type, data, itemIndex) =>
                                          onSubmitDistributeTo(data, type, record, index, itemIndex)
                                      "
                                      @on-delete="(key) => onDeleteDistributeTo(record, index, key)"
                                    >
                                      <template #custom-data="{ data, onDelete }">
                                        <div
                                          v-for="[key, value] in Object.entries(data)"
                                          :key="key"
                                          class="distribute-to-item"
                                        >
                                          <span>{{ key }} 权重：{{ value }}</span>
                                          <LinkButton @click="() => onDelete(key)" type="danger" text="删除" />
                                        </div>
                                      </template>
                                      <template #default="{ record }">
                                        <div class="to-modal-wrapper">
                                          <Cascader
                                            :data="clusterRegionZoneCascaderList"
                                            v-model="record.key"
                                            filterable
                                          />
                                          <InputNumber
                                            :max="100"
                                            :min="0"
                                            :value="record.value ?? 100"
                                            @on-change="(val) => (record.value = val)"
                                            placeholder="请输入权重"
                                            :formatter="(value) => parseInt(`${value}`)"
                                          />
                                        </div>
                                      </template>
                                    </ArrayObject>
                                  </ProFormItem>
                                </Card>
                              </div>
                            </template>
                          </MultiInput>
                        </template>
                      </ProFormItem>
                    </template>
                  </ProFormItem>
                </ProFormItem>
                <ProFormItem
                  name="connectionPool"
                  label="ConnectionPool（连接池）"
                  :data="record"
                  :dataReloadFlag="visible"
                  contentStyleMode="border"
                  :mode="EnumFormItemControllerType.Switch"
                  desc="控制与上游服务的连接量的设置"
                  url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings"
                  contentClassName="filter-last-from-item-margin"
                  :init-data="{
                    tcp: {},
                    http: {}
                  }"
                >
                  <ProFormItem
                    name="tcp"
                    label="TCP"
                    :data="record?.connectionPool?.data"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    :dataReloadFlag="visible"
                    :init-data="{}"
                    desc="HTTP 和 TCP 上游连接通用的设置"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-TCPSettings"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <ProFormText
                      name="maxConnections"
                      label="MaxConnections（最大连接数）"
                      :dataReloadFlag="visible"
                      :data="record?.connectionPool?.data?.tcp?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      type="number"
                      number
                      desc="到目标主机的最大 HTTP1 /TCP 连接数。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-TCPSettings:~:text=Required-,maxConnections,-int32"
                    />
                    <ProFormText
                      name="connectTimeout"
                      label="ConnectTimeout（超时时间） "
                      :dataReloadFlag="visible"
                      :data="record?.connectionPool?.data?.tcp?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      desc="TCP 连接超时。格式：1小时/1分钟/1秒/1毫秒，必须为>=1毫秒。默认值为 10 秒。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-TCPSettings:~:text=No-,connectTimeout,-Duration"
                    />
                    <ProFormText
                      name="maxConnectionDuration"
                      label="MaxConnectionDuration（最长连接时间） "
                      :dataReloadFlag="visible"
                      :data="record?.connectionPool?.data?.tcp?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      desc="连接的最长持续时间。持续时间定义为自建立连接以来的时间段。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-TCPSettings:~:text=maxConnectionDuration"
                    />
                  </ProFormItem>
                  <ProFormItem
                    name="http"
                    label="HTTP"
                    :data="record?.connectionPool?.data"
                    :dataReloadFlag="visible"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    :init-data="{}"
                    desc="HTTP 连接池设置。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <ProFormText
                      name="http1MaxPendingRequests"
                      label="Http1MaxPendingRequests（http1最大请求数）"
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      type="number"
                      number
                      desc="等待就绪连接池连接时将排队的最大请求数。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=http1MaxPendingRequests"
                    />
                    <ProFormText
                      name="http2MaxRequests"
                      label="Http2MaxRequests（http2最大请求数）"
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      type="number"
                      number
                      desc="等待就绪连接池连接时将排队的最大请求数。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,http2MaxRequests,-int32"
                    />
                    <ProFormText
                      name="maxRequestsPerConnection"
                      label="MaxRequestsPerConnection（每个连接最大请求数）"
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      type="number"
                      number
                      desc="等待就绪连接池连接时将排队的最大请求数。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,maxRequestsPerConnection,-int32"
                    />
                    <ProFormText
                      name="maxRetries"
                      label="MaxRetries（最大重试次数）"
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      type="number"
                      number
                      desc="在给定时间，群集中所有主机可以完成的最大重试次数。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,maxRetries,-int32"
                    />
                    <ProFormText
                      name="idleTimeout"
                      label="IdleTimeout（空闲超时） "
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      desc="上游连接池连接的空闲超时。空闲超时定义为没有活动请求的时间段。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,idleTimeout,-Duration"
                    />
                    <ProFormSelect
                      name="h2UpgradePolicy"
                      label="H2UpgradePolicy（http2升级） "
                      :data="record?.connectionPool?.data?.http?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      :options="formatEnumToLabelValue(EnumHTTP2UpgradePolicy)"
                      desc="指定是否应将关联目标的 http1.1 连接升级到 http2。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,h2UpgradePolicy,-H2UpgradePolicy"
                    />
                    <ProFormRadio
                      name="useClientProtocol"
                      label="UseClientProtocol （保留客户端协议）"
                      :data="record?.connectionPool?.data?.http?.data"
                      isBoolean
                      :dataReloadFlag="visible"
                      desc="如果设置为 true，则在启动与后端的连接时将保留客户端协议。"
                      url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#ConnectionPoolSettings-HTTPSettings:~:text=No-,useClientProtocol,-bool"
                    />
                  </ProFormItem>
                </ProFormItem>
                <ProFormItem
                  name="outlierDetection"
                  label="OutlierDetection（离群检测）"
                  :data="record"
                  contentStyleMode="border"
                  :mode="EnumFormItemControllerType.Switch"
                  :dataReloadFlag="visible"
                  contentClassName="filter-last-from-item-margin"
                  :init-data="{}"
                  desc="控制从负载平衡池中逐出不正常主机的设置"
                  url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection"
                >
                  <ProFormRadio
                    name="splitExternalLocalOriginErrors"
                    label="SplitExternalLocalOriginErrors（区分本地错误和外部错误）"
                    :data="record?.outlierDetection?.data"
                    isBoolean
                    :dataReloadFlag="visible"
                    desc="确定是否区分本地源故障和外部错误。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=splitExternalLocalOriginErrors"
                  />
                  <ProFormText
                    name="consecutiveLocalOriginFailures"
                    label="ConsecutiveLocalOriginFailures（连续本地错误次数, 上方参数需要开启）"
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    type="number"
                    number
                    desc="发生弹出之前连续本地发起的故障数"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=consecutiveLocalOriginFailures"
                  />
                  <ProFormText
                    name="consecutiveGatewayErrors"
                    label="ConsecutiveGatewayErrors（连网关错误次数） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    type="number"
                    number
                    desc="从连接池中弹出主机之前的网关错误数。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=consecutiveGatewayErrors"
                  />
                  <ProFormText
                    name="consecutive5xxErrors"
                    label="Consecutive5xxErrors（连续5xx错误次数） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    type="number"
                    number
                    desc="​从连接池中弹出主机之前的5xx错误数。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=consecutive5xxErrors"
                  />
                  <ProFormText
                    name="interval"
                    label="Interval（检测时间） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="驱逐分析之间的时间间隔。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=No-,interval,format%3A%201h/1m/1s/1ms.%20MUST%20BE%20%3E%3D1ms.%20Default%20is%2010s.,-No"
                  />
                  <ProFormText
                    name="baseEjectionTime"
                    label="BaseEjectionTime（离群时间） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="最小驱逐持续时间。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=No-,baseEjectionTime,-Duration"
                  />
                  <ProFormText
                    name="maxEjectionPercent"
                    label="MaxEjectionPercent（最大离群百分比） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="可以弹出的上游服务的负载平衡池中主机的最大百分比。默认值为 10%。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=No-,maxEjectionPercent,-int32"
                    type="number"
                    number
                  >
                    <template #suffix>
                      <span class="input-suffix-wrapper">%</span>
                    </template>
                  </ProFormText>
                  <ProFormText
                    name="minHealthPercent"
                    label="MinHealthPercent（最少健康百分比） "
                    :dataReloadFlag="visible"
                    :data="record?.outlierDetection?.data"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="只要关联的负载平衡池至少有 min_health_percent 台主机处于正常模式，就会启用异常值检测。"
                    url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#OutlierDetection:~:text=No-,minHealthPercent,-int32"
                    type="number"
                    number
                  >
                    <template #suffix>
                      <span class="input-suffix-wrapper">%</span>
                    </template>
                  </ProFormText>
                </ProFormItem>
              </Form>
            </template>
          </ArrayObject>
        </ProFormItem>
        <ProFormItem
          name="exportTo"
          label="ExportTo（暴露到目标空间）"
          :data="data"
          :dataReloadFlag="dataReloadFlag"
          contentStyleMode="background"
          :mode="EnumFormItemControllerType.Switch"
          desc="指定该destinationrule在哪些namespace上可被virtualservice等资源使用。'.' 表示在当前destinationrule的namespace， '*' 表示所有的namespace。"
          url="https://istio.io/latest/docs/reference/config/networking/destination-rule/#:~:text=exportTo,string%5B%5D"
        >
          <MultiInput
            key="exportTo"
            addLabel="添加输出空间"
            :data="data"
            :on-delete="(index) => data.exportTo.data.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data?.exportTo?.data?.length || 1)"
          >
            <template #default="{ index }">
              <Select
                transfer
                filterable
                placeholder="目标空间"
                :value="data?.exportTo?.data?.[index]"
                @on-change="
                  (value) => {
                    if (data.exportTo?.data?.length) {
                      data.exportTo.data.splice(index, 1, value)
                    } else {
                      data.exportTo.data = [value]
                    }
                  }
                "
              >
                <Option v-for="item in exportToList" :key="item" :value="item">{{ item }}</Option>
              </Select>
            </template>
          </MultiInput>
        </ProFormItem>
      </template>
    </resource-form>
    <detail-drawer
      resourceType="destinationrule"
      :title="'DestinationRule 详情'"
      v-model="viewDetailVisible"
      :resource-entity="formEntity"
    >
      <template #default="{ data }">
        <detail-card title="基本信息" :data="data" :config="DETAIL_BASE_INFO">
          <template #labels="{ data }">
            <template v-for="(key, value) in data.labels">
              <Tag :key="key" color="#2a7cc3"> {{ `${value}:${key}` }} </Tag>
            </template>
          </template>
        </detail-card>
        <detail-card title="关联Pod" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="RELATED_POD_COLUMNS" :data="data.relatedPod">
              <template #ops="{ row }">
                <LinkButton @click="() => onViewYaml(row, 'pod')" text="YAML" />
              </template>
            </Table>
          </template>
        </detail-card>
      </template>
    </detail-drawer>
    <Modal title="复制目标规则（DR）" v-model="modal.visible">
      <Alert show-icon>将当前资源对象复制到选中的目标集群和目标空间</Alert>
      <Row type="flex" justify="start" align="middle" style="margin-bottom: 12px">
        <Col>复制对象：</Col>
        <Col>
          <Tag color="#2a7cc3"> {{ modal.data.name }} </Tag>
        </Col>
      </Row>
      <Row type="flex" justify="start" align="middle">
        <Col>复制位置：</Col>
        <Col flex="1">
          <Cascader :data="clusterCascaderList" v-model="modal.value" filterable />
        </Col>
      </Row>
      <template #footer>
        <Button type="text" @click="() => (modal.visible = false)">取消</Button>
        <Button type="primary" @click="onCopySubmit">确定</Button>
      </template>
    </Modal>
    <view-yaml
      resourceType="destinationrule"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      isCheckYaml
    />
    <BatchCopy v-model="batchCopyModalVisible" :resource-type="EnumIstioResourceType.DestinationRule" />
  </div>
</template>

<style lang="less" scoped>
.multi-item-wrapper {
  display: flex;
  flex: 1 0 0%;
  > div {
    flex: 1 0 0%;
    &:not(:last-child) {
      margin-right: 16px;
    }
    /deep/ .ivu-card-body {
      padding: 16px;
      display: flex;
      position: relativeS;
      flex-direction: column;
      > .ivu-icon-md-close {
        position: absolute;
        right: 4px;
        top: 4px;
      }
      > .ivu-form-item:last-child {
        margin: 0;
      }
    }
  }
}

.info-key {
  color: #808695;
}

.info-value {
  color: #515a6e;
  font-family: Consolas, Menlo, Bitstream Vera Sans Mono, Monaco, 微软雅黑, monospace !important;
}
/deep/.load-balancer-content {
  > .ivu-form-item:last-child {
    margin: 0;
  }
}
/deep/.lb-policy-content {
  margin: 0;
  .ivu-form-item {
    margin: 0;
  }
}

/deep/.lb-policy-data-content {
  padding: 0;
}
.distribute-to-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/deep/.lb-locality-content {
  > .ivu-form-item {
    margin: 0;
  }
}

.to-modal-wrapper {
  display: flex;
  align-items: center;
  > .ivu-cascader {
    flex: 1 0 0%;
  }
  > .ivu-input-number {
    width: 100px;
  }
}

.input-suffix-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-left: 1px solid #cccc;
}

.filter-last-from-item-margin > .ivu-form-item:last-child {
  margin-bottom: 0;
}
</style>
