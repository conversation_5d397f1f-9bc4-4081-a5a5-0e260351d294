<script lang="ts" setup>
import useRouteMapService, { CONTAINER_ID } from './useRouteMapService'
import Space from '@/components/space'
import LinkButton from '@/components/link-button'
import { PropType } from 'vue/types/v3-component-props'
import { RouterItem, EnumStatus } from './type'
import { relativeTime } from '@/libs/tools'

const props = defineProps({
  containerHeight: String,
  routeData: Object as PropType<RouterItem>,
  loading: Boolean
})
const { modal, onSearch, onOpenYamlFromTable, showType, onShowTypeChange, routeData, activeNodesCount } =
  useRouteMapService(props)
</script>

<template>
  <div class="wrapper">
    <slot name="alert" />
    <div class="toolbar">
      <Space class="operation">
        <slot name="operation" />
        <Space class="search-wrapper" :size="8">
          <Input search placeholder="检索图中内容" enter-button @on-search="onSearch" />
          <div class="counter">匹配数量: {{ activeNodesCount }}</div>
        </Space>
      </Space>
      <CheckboxGroup class="legend-wrapper" :value="showType" @on-change="onShowTypeChange">
        <Checkbox :label="EnumStatus.succeed">
          <span class="legend success">正常</span>
        </Checkbox>
        <Checkbox :label="EnumStatus.warning">
          <span class="legend warning">警告</span>
        </Checkbox>
        <Checkbox :label="EnumStatus.error">
          <span class="legend error">错误</span>
        </Checkbox>
      </CheckboxGroup>
    </div>
    <div class="content">
      <div v-show="!routeData" :style="`height:${props.containerHeight}`" class="empty">
        <img src="./assets/empty.svg" />
        暂无数据
      </div>
      <div :id="CONTAINER_ID" :style="`height:${props.containerHeight};z-index:${routeData ? 1 : -1}`" />
      <Spin size="large" fix v-if="props.loading"></Spin>
    </div>

    <Modal v-model="modal.visible" title="数据详情" footer-hide width="50" class="vertical-center-modal">
      <Table v-if="modal.columns" height="300" size="small" :columns="modal.columns" :data="modal.data">
        <template #name="{ row }">
          <LinkButton @click="() => onOpenYamlFromTable(row)" :text="row.name" class="ellipsis-text" ellipsis />
        </template>
        <template #age="{ row }">
          <div>{{ relativeTime(row.age) }}</div>
        </template>
      </Table>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.wrapper {
  background: #fff;
  border-radius: 4px;
  position: relative;
}
.toolbar {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 16px;
  height: 32px;
  .operation {
    width: 300px;
    display: flex;
    align-items: center;
  }
  .legend-wrapper .ivu-checkbox-wrapper {
    display: inline-flex;
    align-items: center;
  }
  .legend {
    // width: 80px;
    // display: flex;
    // align-items: center;
    // line-height: 20px;
    font-size: 14px;
    margin: 0 8px;
    // &::before {
    //   display: inline-block;
    //   height: 16px;
    //   width: 16px;
    //   margin-right: 8px;
    //   border-radius: 50%;
    //   content: '';
    // }
  }
  .success {
    color: #19be6b;
    &::before {
      background: #19be6b;
    }
  }
  .warning {
    color: #f59b22;
    &::before {
      background: #f59b22;
    }
  }
  .error {
    color: #d8001b;
    &::before {
      background: #d8001b;
    }
  }
}
.content {
  position: relative;
}
#container {
  border: 1px solid #d7d7d7;
  border-radius: 4px;
  overflow: hidden;
  position: relative;

  /deep/.g6-minimap {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #fff;
    width: auto !important;
    height: auto !important;
    box-shadow: 0px 0px 4px 3px #eeeeee;

    /deep/.g6-minimap-viewport {
      border: 1px solid #009dff;
    }
  }
}
.empty {
  position: absolute;
  width: ~'calc(100% - 16px * 2)';

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  > img {
    height: 160px;
  }
}

.vertical-center-modal {
  /deep/.ivu-modal-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    .ivu-modal {
      top: 0;
      // top: 50%;
      // transform: translateY(-50%);
    }
  }
}

.ellipsis-text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.search-wrapper {
  align-items: flex-end;
  .counter {
    flex-shrink: 0;
  }
}
</style>
