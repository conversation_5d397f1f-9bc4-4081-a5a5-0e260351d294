import Vue, { VueConstructor } from 'vue'
import directive from './directives'

const importDirective = (
  Vue: VueConstructor<
    Vue<
      Record<string, any>,
      Record<string, any>,
      never,
      never,
      (event: string, ...args: any[]) => Vue<Record<string, any>, Record<string, any>, never, never, any>
    >
  >
) => {
  /**
   * 拖拽指令 v-draggable="options"
   * options = {
   *  trigger: /这里传入作为拖拽触发器的CSS选择器/,
   *  body:    /这里传入需要移动容器的CSS选择器/,
   *  recover: /拖动结束之后是否恢复到原来的位置/
   * }
   */
  Vue.directive('draggable', directive.draggable)
  /**
   * clipboard指令 v-draggable="options"
   * options = {
   *  value:    /在输入框中使用v-model绑定的值/,
   *  success:  /复制成功后的回调/,
   *  error:    /复制失败后的回调/
   * }
   */
  Vue.directive('clipboard', directive.clipboard)
}

export default importDirective
