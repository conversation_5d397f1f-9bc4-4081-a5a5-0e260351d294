<script lang="ts" setup>
import { PropType, set } from 'vue'
import Space from '@/components/space'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import { Alter } from './index'
import { DetailDrawer, DetailCard } from '@/components/detail-drawer'
import { ResourceForm, EnumFormStatus } from '@/components/resource-form'
import {
  ProFormSelect,
  ProFormText,
  ProFormItem,
  ArrayObject,
  ProFormRadio,
  EnumFormItemControllerType,
  MultiInput
} from '@/components/pro-form'
import useVirtualServiceService from './useVirtualServiceService'
import {
  formatEnumToLabelValue,
  EnumComponentType,
  EnumRetryOn,
  EnumMatchType,
  EnumRoute,
  EnumAllowMethods,
  EnumHeadersMethod,
  EnumHeadersType
} from './enum'
import {
  HTTP_COLUMNS,
  DETAIL_HTTP_COLUMNS,
  DETAIL_BASE_INFO,
  DETAIL_RELATED_SERVICE_COLUMNS,
  DETAIL_RELATED_DESTINATION_RULE_COLUMNS,
  MATCH_COLUMNS,
  ROUTE_COLUMNS
} from './setting'
import { genNonDuplicateArr } from '@/libs/tools'
import { BatchCopy, EnumIstioResourceType } from '@/domain/Resource'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  },
  K8SInstance: {
    type: Object as PropType<{
      clusterId: string
      clusterName: string
      namespace: string
      relativeGatewayName: string
    }>
  },
  // 初始化请求的参数
  name: { type: String }
})

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onCopy,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  formInitData,
  onInitFormat,
  onSubmitFormat,
  onSubmitSuccess,
  onViewDetail,
  viewDetailVisible,

  modal,
  onCopySubmit,
  clusterCascaderList,
  onMatchKeyChange,
  onAddMatch,
  delegateCascaderList,

  onAddRouteRoute,
  onAllowOriginsKeyChange,
  exportToList,
  onHttpTableDragDrop,
  httpFormRef,
  validateName,
  onCheckHttpData,
  tempGatewaysOption,

  tempHostOption,
  initTempHostOption,
  isHostInit,
  afterHostChange,
  portOptions,
  subsetOptions,

  tempMirrorHostOption,
  initTempMirrorHostOption,
  isMirrorHostInit,
  afterMirrorHostChange,
  portMirrorOptions,
  subsetMirrorOptions,
  columns,
  batchCopyModalVisible
} = useVirtualServiceService(props)
</script>

<template>
  <div>
    <alter :type="props.type" />
    <pro-table
      :columns="columns"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :on-create="onCreate"
      :border="!!(props.type === EnumComponentType.AssociatedGateway)"
      v-bind="{
        ...(props.type === EnumComponentType.AssociatedDeployment
          ? {
              pagination: false
            }
          : props.type === EnumComponentType.AssociatedGateway
          ? {
              search: [{ value: 'keyword', label: '名称' }],
              height: 300
            }
          : { search: [{ value: 'keyword', label: '名称', initData: props.name }] })
      }"
    >
      <template #operate-buttons>
        <Button
          v-if="props.type !== EnumComponentType.AssociatedDeployment"
          size="small"
          type="primary"
          ghost
          icon="md-copy"
          @click="() => (batchCopyModalVisible = true)"
          >批量复制</Button
        >
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button v-if="props.type === EnumComponentType.Independent" @click="() => onCopy(row)" text="复制" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>
    <resource-form
      :type="props.type"
      :resourceType="`${
        props.type === EnumComponentType.AssociatedGateway ? 'unified-gateway/virtualservice' : 'virtualservice'
      }`"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      :onInitFormat="onInitFormat"
      :onSubmitFormat="onSubmitFormat"
      width="1200"
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" :readonly="true" />
        <ProFormText
          name="name"
          label="Name（名称）"
          :data="data"
          :readonly="formStatus === EnumFormStatus.Edit"
          size="md"
        />
        <ProFormItem
          name="hosts"
          label="Hosts（匹配主机）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          :mode="EnumFormItemControllerType.Switch"
          desc="主机名称，可以是DNS名称，ip，k8s service(使用完全限定名称以避免不必要的错误。)"
          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#:~:text=Required-,hosts,-string%5B%5D"
          size="md"
        >
          <MultiInput
            key="hosts"
            addLabel="添加主机"
            :data="data"
            :on-delete="(index) => data.hosts.data.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data?.hosts?.data?.length || 1)"
          >
            <template #default="{ index }">
              <Input
                placeholder="服务主机 www.baidu.com"
                :value="data.hosts?.data?.[index]"
                @on-change="
                  (e) => {
                    !data.hosts.data?.length && (data.hosts.data = [])
                    data.hosts.data[index] = e.target.value
                  }
                "
              />
            </template>
          </MultiInput>
        </ProFormItem>
        <ProFormSelect
          name="gateways"
          label="Gateways（网关）"
          :data="data"
          :readonly="props.type === EnumComponentType.AssociatedGateway"
          :mode="EnumFormItemControllerType.Switch"
          multiple
          :dataReloadFlag="dataReloadFlag"
          :options="tempGatewaysOption"
          desc="当前virtualservice作用在哪些gateway(使用<gateway namespace>/<gateway name>以避免不必要的错误)和sidecar上。"
          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#:~:text=No-,gateways,string%5B%5D,-The%20names%20of"
          size="md"
        />
        <ProFormItem
          name="HTTP"
          label="HttpRoute（HTTP路由配置）"
          :data="data"
          contentStyleMode="background"
          :mode="EnumFormItemControllerType.Switch"
          :dataReloadFlag="dataReloadFlag"
          desc="配置HTTP流量的路由规则。注意该路由规则是一个有序列表。"
          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRoute"
        >
          <ArrayObject
            label="路由"
            :data="data.HTTP"
            name="data"
            addButtonPosition="top"
            canEdit
            :modalWidth="1200"
            :columns="HTTP_COLUMNS(data.HTTP?.data)"
            :opsWidth="100"
            draggable
            @on-drag-drop="(index1, index2) => onHttpTableDragDrop(data, index1, index2)"
            @on-ok="(type, record, index, callback) => onCheckHttpData(data, type, record, index, callback)"
            @on-delete="(index) => data.HTTP.data?.splice(index, 1)"
            @on-add="(data) => initTempMirrorHostOption(data)"
            @on-edit="(data) => initTempMirrorHostOption(data)"
          >
            <template #alter>拖动表格行可调整顺序, 顺序会影响匹配优先级。</template>
            <template #default="{ record, visible }">
              <Form
                @submit.native.prevent
                :model="record"
                :rules="{ name: [{ validator: validateName, trigger: 'blur' }] }"
                ref="httpFormRef"
              >
                <ProFormText name="name" label="Name（路由名称）" :data="record" required />

                <ProFormItem name="" label="HTTP相关配置" contentStyleMode="border">
                  <ProFormItem
                    name="headers"
                    label="Headers（添加请求/响应头）"
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    contentStyleMode="background"
                    :init-data="[]"
                    desc="配置header控制规则"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Headers"
                  >
                    <MultiInput
                      addLabel="添加头部设置"
                      name="data"
                      :data="record?.headers"
                      key="headers"
                      :on-delete="(index) => record?.headers?.data.splice(index, 1)"
                      :on-init="() => genNonDuplicateArr(record?.headers?.data?.length || 1)"
                    >
                      <template #default="{ index }">
                        <Select
                          placeholder="类型"
                          filterable
                          transfer
                          :value="record?.headers?.data?.[index]?.type"
                          @on-change="(value) => onMatchKeyChange(record, 'headers', 'type', index, value)"
                        >
                          <Option
                            v-for="item in formatEnumToLabelValue(EnumHeadersType)"
                            :key="item.value"
                            :value="item.value"
                            >{{ item.label }}</Option
                          >
                        </Select>
                        <Select
                          placeholder="方法"
                          filterable
                          transfer
                          :value="record?.headers?.data?.[index]?.method"
                          @on-change="
                            (value) => {
                              onMatchKeyChange(record, 'headers', 'method', index, value)
                              if (value === EnumHeadersMethod.remove) {
                                onMatchKeyChange(record, 'headers', 'value', index, undefined)
                              }
                            }
                          "
                        >
                          <Option
                            v-for="item in formatEnumToLabelValue(EnumHeadersMethod)"
                            :key="item.value"
                            :value="item.value"
                            >{{ item.label }}</Option
                          >
                        </Select>
                        <Input
                          placeholder="Key"
                          :value="record?.headers?.data?.[index]?.key"
                          @on-change="(e) => onMatchKeyChange(record, 'headers', 'key', index, e.target.value)"
                        />

                        <Input
                          :disabled="record?.headers?.data?.[index]?.method === EnumHeadersMethod.remove"
                          placeholder="Value"
                          :value="record?.headers?.data?.[index]?.value"
                          @on-change="(e) => onMatchKeyChange(record, 'headers', 'value', index, e.target.value)"
                        />
                      </template>
                    </MultiInput>
                  </ProFormItem>
                  <ProFormItem
                    name="match"
                    label="Match（匹配项）"
                    :data="record"
                    :dataReloadFlag="visible"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="配置当前规则需要满足的匹配条件。单个匹配块中的所有条件都具有 AND 语义，而匹配块列表具有 OR 语义。如果任何一个匹配块成功，则匹配规则。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest"
                  >
                    <ArrayObject
                      label="匹配条目"
                      name="data"
                      :data="record.match"
                      canEdit
                      canCopy
                      addButtonPosition="top"
                      :columns="MATCH_COLUMNS"
                      :dataReloadFlag="dataReloadFlag"
                      resizable
                      @on-ok="(type, matchRecord, index) => onAddMatch(record, type, matchRecord, index)"
                      @on-delete="(index) => record.match.data?.splice(index, 1)"
                    >
                      <template #default="{ record: matchRecord, visible }">
                        <ProFormItem
                          name="uri"
                          label="URI"
                          :data="matchRecord"
                          :dataReloadFlag="visible"
                          :mode="EnumFormItemControllerType.Switch"
                          contentStyleMode="background"
                          :init-data="{}"
                          desc="对http uri应用匹配规则。注意是大小写敏感。"
                          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest:~:text=No-,uri,-StringMatch"
                        >
                          <div class="single-line">
                            <ProFormSelect
                              name="type"
                              :label="null"
                              :data="matchRecord?.uri?.data"
                              :options="formatEnumToLabelValue(EnumMatchType)"
                              placeholder="请选择匹配方式"
                            />
                            <ProFormText name="value" :label="null" :data="matchRecord?.uri?.data" placeholder="URI" />
                          </div>
                        </ProFormItem>
                        <ProFormItem
                          name="headers"
                          label="Headers"
                          :data="matchRecord"
                          :dataReloadFlag="visible"
                          :mode="EnumFormItemControllerType.Switch"
                          contentStyleMode="background"
                          :init-data="[]"
                          desc="对http header应用匹配规则。注意header的key必须是小写，并使用连字符作为分隔符，例如：x-request-id。header的value是大小写敏感。"
                          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest:~:text=No-,headers,-map%3Cstring%2C%C2%A0"
                        >
                          <MultiInput
                            addLabel="添加匹配"
                            name="data"
                            :data="matchRecord?.headers"
                            key="headers"
                            :on-delete="(index) => matchRecord?.headers?.data.splice(index, 1)"
                            :on-init="() => genNonDuplicateArr(matchRecord?.headers?.data?.length || 1)"
                          >
                            <template #default="{ index }">
                              <Input
                                placeholder="Key"
                                :value="matchRecord?.headers?.data?.[index]?.key"
                                @on-change="
                                  (e) => onMatchKeyChange(matchRecord, 'headers', 'key', index, e.target.value)
                                "
                              />
                              <Select
                                placeholder="匹配方式"
                                filterable
                                transfer
                                :value="matchRecord?.headers?.data?.[index]?.type"
                                @on-change="(value) => onMatchKeyChange(matchRecord, 'headers', 'type', index, value)"
                              >
                                <Option
                                  v-for="item in formatEnumToLabelValue(EnumMatchType)"
                                  :key="item.value"
                                  :value="item.value"
                                  >{{ item.label }}</Option
                                >
                              </Select>
                              <Input
                                placeholder="Value"
                                :value="matchRecord?.headers?.data?.[index]?.value"
                                @on-change="
                                  (e) => onMatchKeyChange(matchRecord, 'headers', 'value', index, e.target.value)
                                "
                              />
                            </template>
                          </MultiInput>
                        </ProFormItem>
                        <ProFormItem
                          name="queryParams"
                          label="QueryParams"
                          :data="matchRecord"
                          :dataReloadFlag="visible"
                          :mode="EnumFormItemControllerType.Switch"
                          contentStyleMode="background"
                          :init-data="[]"
                          desc="对http queryParams应用匹配规则。"
                          url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest:~:text=No-,queryParams,-map%3Cstring%2C%C2%A0"
                        >
                          <MultiInput
                            addLabel="添加匹配"
                            name="data"
                            :data="matchRecord?.queryParams"
                            key="queryParams"
                            :on-delete="(index) => matchRecord?.queryParams?.data.splice(index, 1)"
                            :on-init="() => genNonDuplicateArr(matchRecord?.queryParams?.data?.length || 1)"
                          >
                            <template #default="{ index }">
                              <Input
                                placeholder="Key"
                                :value="matchRecord?.queryParams?.data?.[index]?.key"
                                @on-change="
                                  (e) => onMatchKeyChange(matchRecord, 'queryParams', 'key', index, e.target.value)
                                "
                              />
                              <Select
                                placeholder="匹配方式"
                                filterable
                                transfer
                                :value="matchRecord?.queryParams?.data?.[index]?.type"
                                @on-change="
                                  (value) => onMatchKeyChange(matchRecord, 'queryParams', 'type', index, value)
                                "
                              >
                                <Option
                                  v-for="item in formatEnumToLabelValue(EnumMatchType)"
                                  :key="item.value"
                                  :value="item.value"
                                  >{{ item.label }}</Option
                                >
                              </Select>
                              <Input
                                placeholder="Value"
                                :value="matchRecord?.queryParams?.data?.[index]?.value"
                                @on-change="
                                  (e) => onMatchKeyChange(matchRecord, 'queryParams', 'value', index, e.target.value)
                                "
                              />
                            </template>
                          </MultiInput>
                        </ProFormItem>
                        <ProFormText
                          name="port"
                          label="Port"
                          :data="matchRecord"
                          :dataReloadFlag="visible"
                          :mode="EnumFormItemControllerType.Switch"
                          type="number"
                          number
                        />
                      </template>
                    </ArrayObject>
                  </ProFormItem>
                  <ProFormItem
                    name="route"
                    label="Route（路由分发）"
                    :data="record"
                    :dataReloadFlag="visible"
                    contentStyleMode="background"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="规则匹配后，流量最终可以通过direct_response(目前不支持)、redirect或route来转发流量。也可以通过配置delegate来完成。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRoute:~:text=No-,route,-HTTPRouteDestination%5B%5D"
                    contentClassName="filter-from-item-margin"
                  >
                    <ProFormItem
                      name="data"
                      :label="null"
                      :data="record.route"
                      :dataReloadFlag="visible"
                      :mode="EnumFormItemControllerType.Chosen"
                      :chosenOptions="formatEnumToLabelValue(EnumRoute)"
                      :initData="{
                        redirect: {}
                      }"
                    >
                      <template #route>
                        <ArrayObject
                          label="目标主机"
                          name="route"
                          :data="record.route?.data"
                          addButtonPosition="top"
                          :columns="ROUTE_COLUMNS"
                          :opsWidth="100"
                          canEdit
                          @on-ok="(type, routeRecord, index) => onAddRouteRoute(record, type, routeRecord, index)"
                          @on-delete="(index) => record.route.data.route?.splice(index, 1)"
                          @on-add="(data) => initTempHostOption(data)"
                          @on-edit="(data) => initTempHostOption(data)"
                        >
                          <template #default="{ record: routeRecord, visible }">
                            <ProFormSelect
                              name="host"
                              label="Host（关联主机）"
                              :data="routeRecord"
                              :options="tempHostOption"
                              allow-create
                              :dataReloadFlag="visible"
                              :afterChange="(host, isInit) => afterHostChange(host, routeRecord, isInit)"
                              desc="服务注册表（kubernetes，serviceentry）中的服务名称。注意请使用FQDN名称（reviews.default.svc.cluster.local），而不是reviews。"
                              url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=Required-,host,-string"
                            />
                            <ProFormSelect
                              name="port"
                              label="Port（端口）"
                              :data="routeRecord"
                              :options="portOptions"
                              :mode="EnumFormItemControllerType.Switch"
                              :dataReloadFlag="visible || routeRecord.host"
                              :allow-create="isHostInit"
                              desc="通常只有服务存在多个端口时才需要明确指定匹配端口。"
                              url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=No-,port,-PortSelector"
                            />
                            <ProFormSelect
                              name="subset"
                              label="Subset（关联目标规则的子集名称）"
                              :data="routeRecord"
                              :options="subsetOptions"
                              :mode="EnumFormItemControllerType.Switch"
                              :dataReloadFlag="visible || routeRecord.host"
                              :allow-create="isHostInit"
                              desc="服务中子集的名称。仅适用于网格内的服务。必须在相应的目标规则（destinationrule）中定义子集（subset）。"
                              url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=Yes-,subset,-string"
                            />
                            <ProFormText
                              name="weight"
                              label="Weight（权重）"
                              :data="routeRecord"
                              :dataReloadFlag="visible"
                              :mode="EnumFormItemControllerType.Switch"
                              type="number"
                              number
                              desc="权重指定要转发到目标的流量的相对比例。目标将接收权重/（所有权重的总和）请求。如果规则中只有一个目标，它将接收所有流量。否则，如果权重为 0，则目标将不会收到任何流量。"
                              url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#RouteDestination"
                            />
                          </template>
                        </ArrayObject>
                      </template>
                      <template #delegate>
                        <Alert show-icon>
                          若数据未正常显示在选择器中，说明当前集群没有匹配的数据，请酌情处理废弃数据
                        </Alert>
                        <Cascader
                          :data="delegateCascaderList"
                          v-model="record.route.data.delegate"
                          filterable
                          placeholder="可搜索选项后进行选择"
                        />
                      </template>
                      <template #redirect>
                        <div class="dashed-wrapper">
                          <ProFormText
                            name="authority"
                            label="Authority（主机）"
                            :dataReloadFlag="record.route.data.chosenCtl"
                            :data="record.route.data.redirect"
                            :mode="EnumFormItemControllerType.Switch"
                            size="md"
                            desc="在重定向时，使用此值覆盖 URL 的authority/host部分。"
                            url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRedirect"
                          />
                          <ProFormText
                            name="uri"
                            label="URI（URI地址）"
                            :data="record.route.data.redirect"
                            :dataReloadFlag="record.route.data.chosenCtl"
                            :mode="EnumFormItemControllerType.Switch"
                            size="md"
                            desc="在重定向中，使用此值覆盖 URL 的“Path”部分。请注意，无论请求 URI 是否匹配为确切的路径或前缀，都将替换整个路径。"
                            url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRedirect:~:text=Required-,uri,string,-On%20a%20redirect"
                          />
                          <ProFormText
                            name="redirectPort"
                            label="Port（端口）"
                            :data="record.route.data.redirect"
                            :dataReloadFlag="record.route.data.chosenCtl"
                            :mode="EnumFormItemControllerType.Switch"
                            size="md"
                            type="number"
                            number
                            desc="在重定向时，使用此值覆盖 URL 的端口部分。"
                            url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRedirect"
                          />
                          <ProFormText
                            name="scheme"
                            label="Scheme（协议）"
                            :data="record.route.data.redirect"
                            :dataReloadFlag="record.route.data.chosenCtl"
                            :mode="EnumFormItemControllerType.Switch"
                            size="md"
                            desc="在重定向时，使用此值覆盖 URL 的协议。例如：http，https，ftp等等。如果未设置，将使用原始方案。如果 derivePort 设置为 FROM_PROTOCOL_DEFAULT，这也会影响使用的端口"
                            url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRedirect"
                          />
                        </div>
                      </template>
                    </ProFormItem>
                  </ProFormItem>
                  <ProFormItem
                    name="rewrite"
                    label="Rewrite（URI 重写, 匹配项命中后进行URI重写）"
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="重写 HTTP URI 和 Host header(不支持)。重写不能与重定向原语一起使用。重写将在转发之前执行。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRewrite"
                    :init-data="{}"
                    contentClassName="filter-from-item-margin"
                  >
                    <ProFormText
                      name="uri"
                      :label="null"
                      :data="record?.rewrite?.data"
                      :dataReloadFlag="record?.rewrite?.switchCtl"
                      placeholder="URI"
                      size="md"
                    />
                  </ProFormItem>
                  <ProFormText
                    name="timeout"
                    label="Timeout（请求超时）"
                    :data="record"
                    type="number"
                    :min="0"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    desc="配置HTTP超时时间。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRoute:~:text=No-,timeout,-Duration"
                    size="md"
                  />
                  <ProFormItem
                    name="retries"
                    label="Retries（请求重试次数）"
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    contentStyleMode="background"
                    desc="配置 HTTP 请求的重试策略。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry"
                    :init-data="{}"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <ProFormText
                      name="attempts"
                      label="Attempts（重试次数）"
                      :data="record.retries?.data"
                      :dataReloadFlag="visible"
                      type="number"
                      number
                      desc="给定请求允许的重试次数。重试间隔将自动确定 （25ms+）。配置 HTTP 路由或perTryTimeout的请求超时时，尝试的实际重试次数还取决于指定的请求超时和perTryTimeout值。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=Required-,attempts,-int32"
                      size="md"
                    />
                    <ProFormText
                      name="perTryTimeout"
                      label="PerTryTimeout（重试间隔时间）"
                      :data="record.retries?.data"
                      :dataReloadFlag="visible"
                      :mode="EnumFormItemControllerType.Switch"
                      type="number"
                      number
                      desc="给定请求的每次尝试超时时间，包括初始调用和任何重试。单位是秒。默认值与 HTTP 路由的请求超时值相同，这意味着没有超时。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=Yes-,perTryTimeout,-Duration"
                      size="md"
                    />
                    <ProFormSelect
                      name="retryOn"
                      label="RetryOn（重试条件）"
                      :data="record.retries?.data"
                      :options="formatEnumToLabelValue(EnumRetryOn)"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      desc="指定进行重试的条件。可以使用“，”分隔的列表指定一个或多个策略。如果retry_on指定有效的 HTTP 状态，则会将其添加到retriable_status_codes重试策略中。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=No-,retryOn,-string"
                      size="md"
                    />
                    <ProFormRadio
                      name="retryRemoteLocalities"
                      label="RetryRemoteLocalities（跨地域重试）"
                      :data="record.retries?.data"
                      isBoolean
                      :dataReloadFlag="visible"
                      desc="用于指定重试是否应重试到其他位置的标志。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=retryRemoteLocalities"
                      size="md"
                    />
                  </ProFormItem>
                  <ProFormItem
                    name="corsPolicy"
                    label="CorsPolicy (跨域配置): "
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    contentStyleMode="background"
                    desc="配置跨域策略。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#CorsPolicy"
                    :init-data="{}"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <ProFormItem
                      name="allowOrigins"
                      label="AllowOrigins （允许的主机）"
                      :data="record.corsPolicy"
                      :dataReloadFlag="record.corsPolicy?.switchCtl"
                      desc="与允许的origin匹配的字符串模式。如果任何字符串匹配器匹配，则允许origin。如果找到匹配项，则传出访问控制允许origin将设置为客户端提供的origin。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=Required-,allowOrigins,-StringMatch%5B%5D"
                      size="md"
                    >
                      <MultiInput
                        addLabel="添加主机"
                        name="allowOrigins"
                        :data="record.corsPolicy?.data"
                        :on-delete="(index) => record.corsPolicy?.data.allowOrigins.splice(index, 1)"
                        :on-init="() => genNonDuplicateArr(record.corsPolicy?.data?.allowOrigins?.length || 1)"
                      >
                        <template #default="{ index }">
                          <Select
                            placeholder="匹配方式"
                            filterable
                            transfer
                            :value="record.corsPolicy?.data?.allowOrigins?.[index]?.type"
                            @on-change="(value) => onAllowOriginsKeyChange(record, 'type', index, value)"
                          >
                            <Option
                              v-for="item in formatEnumToLabelValue(EnumMatchType)"
                              :key="item.value"
                              :value="item.value"
                              >{{ item.label }}</Option
                            >
                          </Select>
                          <Input
                            placeholder="*"
                            :value="record.corsPolicy?.data?.allowOrigins?.[index]?.value"
                            @on-change="(e) => onAllowOriginsKeyChange(record, 'value', index, e.target.value)"
                          />
                        </template>
                      </MultiInput>
                    </ProFormItem>
                    <ProFormSelect
                      name="allowMethods"
                      label="AllowMethods （允许的方法）"
                      :data="record.corsPolicy?.data"
                      :options="formatEnumToLabelValue(EnumAllowMethods)"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible"
                      multiple
                      desc="允许访问资源的 HTTP 方法列表。内容将被序列化到 Access-Control-Allow-Headers header中。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=No-,allowMethods,-string%5B%5D"
                      size="md"
                    />
                    <ProFormItem
                      name="allowHeaders"
                      label="AllowHeaders （允许的头部）"
                      :data="record.corsPolicy?.data"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="record.corsPolicy?.switchCtl"
                      desc="请求资源时可以使用的 HTTP header列表。序列化为Access-Control-Allow-Headers header"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=No-,allowHeaders,-string%5B%5D"
                      size="md"
                    >
                      <MultiInput
                        addLabel="添加头部Key"
                        name="data"
                        :data="record.corsPolicy?.data?.allowHeaders"
                        :on-delete="(index) => record.corsPolicy?.data.allowHeaders.data.splice(index, 1)"
                        :on-init="() => genNonDuplicateArr(record.corsPolicy?.data?.allowHeaders?.data?.length || 1)"
                      >
                        <template #default="{ index }">
                          <Input
                            placeholder="填写 Header 的 KEY"
                            :value="record.corsPolicy?.data?.allowHeaders?.data?.[index]"
                            @on-change="
                              (e) => {
                                !record.corsPolicy?.data?.allowHeaders?.data?.length &&
                                  (record.corsPolicy.data.allowHeaders.data = [])
                                set(record.corsPolicy.data.allowHeaders.data, index, e.target.value)
                              }
                            "
                          />
                        </template>
                      </MultiInput>
                    </ProFormItem>
                    <ProFormText
                      name="maxAge"
                      label="MaxAge（缓存时间）"
                      :data="record.corsPolicy?.data"
                      :dataReloadFlag="visible"
                      :mode="EnumFormItemControllerType.Switch"
                      desc="指定请求的结果缓存的时间，单位为秒。转换为Access-Control-Max-Age header。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=No-,maxAge,-Duration"
                      size="md"
                    />

                    <ProFormRadio
                      name="allowCredentials"
                      label="AllowCredentials（允许携带验证信息）"
                      :data="record.corsPolicy?.data"
                      isBoolean
                      :dataReloadFlag="visible"
                      desc="指示是否允许调用方使用证书发送实际请求。转换为Access-Control-Allow-Credentials header。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPRetry:~:text=No-,allowCredentials,-BoolValue"
                      size="md"
                    />
                  </ProFormItem>
                  <ProFormItem
                    name="mirror"
                    label="Mirror（流量镜像）"
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    contentStyleMode="background"
                    :init-data="{}"
                    desc="除了将请求转发到预期目标之外，还将 HTTP 流量镜像到另一个目标。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=No-,mirror,-Destination"
                    contentClassName="filter-last-from-item-margin"
                  >
                    <ProFormSelect
                      name="host"
                      label="Host（关联主机）"
                      :data="record.mirror?.data"
                      :options="tempMirrorHostOption"
                      allow-create
                      :dataReloadFlag="visible"
                      :afterChange="(host, isInit) => afterMirrorHostChange(host, record, isInit)"
                      desc="服务注册表（kubernetes，serviceentry）中的服务名称。注意请使用FQDN名称（reviews.default.svc.cluster.local），而不是reviews。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=Required-,host,-string"
                      size="md"
                    />
                    <ProFormSelect
                      name="port"
                      label="Port（端口）"
                      :data="record.mirror?.data"
                      :options="portMirrorOptions"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible || record.mirror?.data.host"
                      :allow-create="isMirrorHostInit"
                      desc="对http header应用匹配规则。注意header的key必须是小写，并使用连字符作为分隔符，例如：x-request-id。header的value是大小写敏感。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=No-,port,-PortSelector"
                      size="md"
                    />
                    <ProFormSelect
                      name="subset"
                      label="Subset（关联目标规则的子集名称）"
                      :data="record.mirror?.data"
                      :options="subsetMirrorOptions"
                      :mode="EnumFormItemControllerType.Switch"
                      :dataReloadFlag="visible || record.mirror?.data.host"
                      :allow-create="isMirrorHostInit"
                      desc="服务中子集的名称。仅适用于网格内的服务。必须在相应的目标规则（destinationrule）中定义子集（subset）。"
                      url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=Yes-,subset,-string"
                      size="md"
                    />
                  </ProFormItem>
                  <ProFormText
                    name="mirrorPercentage"
                    label="MirrorPercentage（流量百分比）"
                    :data="record"
                    :dataReloadFlag="visible"
                    :mode="EnumFormItemControllerType.Switch"
                    type="number"
                    number
                    desc="镜像字段要镜像的流量百分比。"
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=No-,mirrorPercentage,-Percent"
                    size="md"
                  >
                    <template #suffix>
                      <span class="input-suffix-wrapper">%</span>
                    </template>
                  </ProFormText>
                </ProFormItem>
              </Form>
            </template>
          </ArrayObject>
        </ProFormItem>
        <ProFormItem
          name="exportTo"
          label="ExportTo（暴露到目标空间）"
          :data="data"
          :dataReloadFlag="dataReloadFlag"
          contentStyleMode="background"
          :mode="EnumFormItemControllerType.Switch"
          desc="指定该virtualservice在哪些namespace上可被 sidecars, gateways使用。'.' 表示在当前virtualservice的namespace， '*' 表示所有的namespace。"
          url="https://istio.io/latest/zh/docs/reference/config/networking/virtual-service/#VirtualService"
          size="md"
        >
          <MultiInput
            key="exportTo"
            addLabel="添加输出空间"
            :data="data"
            :on-delete="(index) => data.exportTo.data.splice(index, 1)"
            :on-init="() => genNonDuplicateArr(data?.exportTo?.data?.length || 1)"
          >
            <template #default="{ index }">
              <Select
                placeholder="目标空间"
                transfer
                filterable
                :value="data?.exportTo?.data?.[index]"
                @on-change="
                  (value) => {
                    if (data.exportTo?.data?.length) {
                      data.exportTo.data.splice(index, 1, value)
                    } else {
                      data.exportTo.data = [value]
                    }
                  }
                "
              >
                <Option v-for="item in exportToList" :key="item" :value="item">{{ item }}</Option>
              </Select>
            </template>
          </MultiInput>
        </ProFormItem>
      </template>
    </resource-form>
    <detail-drawer
      :resourceType="`${
        props.type === EnumComponentType.AssociatedGateway ? 'unified-gateway/virtualservice' : 'virtualservice'
      }`"
      title="VirtualService 详情"
      v-model="viewDetailVisible"
      :resource-entity="formEntity"
    >
      <template #default="{ data }">
        <detail-card title="基本信息" :data="data" :config="DETAIL_BASE_INFO">
          <template #labels="{ data }">
            <template v-for="(key, value) in data.labels">
              <Tag :key="key" color="#2a7cc3"> {{ `${value}:${key}` }} </Tag>
            </template>
          </template>
        </detail-card>
        <detail-card title="HTTP路由" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="DETAIL_HTTP_COLUMNS" :data="data.HTTPRoute" />
          </template>
        </detail-card>
        <detail-card title="关联Service" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="DETAIL_RELATED_SERVICE_COLUMNS" :data="data.relatedService">
              <template #ops="{ row }">
                <LinkButton @click="() => onViewYaml(row, 'services')" text="YAML" />
              </template>
            </Table>
          </template>
        </detail-card>
        <detail-card title="关联DestinationRule" :data="data">
          <template #default="{ data }">
            <Table
              style="margin-top: 16px"
              :columns="DETAIL_RELATED_DESTINATION_RULE_COLUMNS"
              :data="data.relatedDestinationRule"
            >
              <template #ops="{ row }">
                <LinkButton @click="() => onViewYaml(row, 'destinationRule')" text="YAML" />
              </template>
            </Table>
          </template>
        </detail-card>
      </template>
    </detail-drawer>
    <Modal title="复制目标规则（VS）" v-model="modal.visible">
      <Alert show-icon>将当前资源对象复制到选中的目标集群和目标空间</Alert>
      <Row type="flex" justify="start" align="middle" style="margin-bottom: 12px">
        <Col>复制对象：</Col>
        <Col>
          <Tag color="#2a7cc3"> {{ modal.data.name }} </Tag>
        </Col>
      </Row>
      <Row type="flex" justify="start" align="middle">
        <Col>复制位置：</Col>
        <Col flex="1">
          <Cascader :data="clusterCascaderList" v-model="modal.value" filterable placeholder="可搜索选项后进行选择" />
        </Col>
      </Row>
      <template #footer>
        <Button type="text" @click="() => (modal.visible = false)">取消</Button>
        <Button type="primary" @click="onCopySubmit">确定</Button>
      </template>
    </Modal>
    <view-yaml
      resourceType="virtualservice"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      isCheckYaml
    />
    <BatchCopy v-model="batchCopyModalVisible" :resource-type="EnumIstioResourceType.VirtualService" />
  </div>
</template>

<style lang="less" scoped>
.dashed-wrapper {
  padding: 16px 16px 0 16px;
  border: 1px dashed#dcdee2;
  background: #fff;
  border-radius: 4px;
}

.single-line {
  display: flex;
  align-items: center;

  > div {
    margin: 0;

    &:last-child {
      flex: 1 0 0%;
    }
  }
}
.filter-from-item-margin > .ivu-form-item {
  margin-bottom: 0;
}
.filter-last-from-item-margin > .ivu-form-item:last-child {
  margin-bottom: 0;
}
</style>

<style lang="less">
.text-ellipsis {
  display: flex;
  margin: 0;
  > .ivu-tooltip-rel {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ivu-tooltip-inner {
    max-width: unset;
  }
}
.input-suffix-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-left: 1px solid #cccc;
}
</style>
