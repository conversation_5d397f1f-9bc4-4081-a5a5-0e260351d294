import { getCurrentInstance, ref } from 'vue'

import Config from '@/config'
import { useGet, usePost } from '@/libs/service.request'

export default function useReplicaCountService(
  relativeType: 'deployment' | 'statefulset',
  onUpdateCallback: () => void
) {
  const { proxy } = getCurrentInstance()

  const replicaCount = ref({ visible: false, tempCount: 0, count: 0 })

  const onOpenReplicaCountModal = () => {
    replicaCount.value = {
      ...replicaCount.value,
      visible: true,
      tempCount: replicaCount.value.count
    }
  }

  const onSetReplicaCount = (clusterId, namespace, name, timeout?: number) => {
    if (replicaCount.value.tempCount === undefined) {
      proxy.$Message.warning('请先输入副本数')
      return
    }
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认调整副本数？`,
      loading: true,
      onOk: async () => {
        const msg = proxy.$Message.info({
          content: '正在调整副本数，耗时较长，请耐心等待~',
          duration: 0
        }) as unknown as TimerHandler
        const res = await usePost(
          `${Config.Api.Base}${
            relativeType === 'deployment'
              ? Config.Api.SetDeploymentReplicasCount
              : Config.Api.SetStatefulsetReplicasCount
          }`,
          {
            clusterId: clusterId,
            namespace: namespace,
            name: name,
            ...(relativeType === 'statefulset'
              ? { replicas: replicaCount.value.tempCount }
              : { count: replicaCount.value.tempCount })
          }
        )
        if (res.success) {
          setTimeout(msg, timeout ?? 3000)
          setTimeout(async () => {
            getReplicaCount(clusterId, namespace, name)
            proxy.$Message.success('调整副本数成功！')
            replicaCount.value = {
              ...replicaCount.value,
              visible: false,
              tempCount: 0
            }
            proxy.$Modal.remove()
            onUpdateCallback()
          }, timeout ?? 3000)
        }
      }
    })
  }

  const getReplicaCount = async (clusterId, namespace, name) => {
    const res = await useGet(
      `${Config.Api.Base}${
        relativeType === 'deployment' ? Config.Api.GetDeploymentReplicasCount : Config.Api.GetStatefulsetReplicasCount
      }`,
      {
        params: { clusterId, namespace, name }
      }
    )
    if (res.success) {
      replicaCount.value.count = res.data.data
    }
  }

  return {
    replicaCount,
    onOpenReplicaCountModal,
    onSetReplicaCount,
    getReplicaCount
  }
}
