import Main from '@/components/main'
import parentView from '@/components/parent-view'

/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: 'Login - 登录',
      hideInMenu: true
    },
    component: () => import('@/view/login/login.vue')
  },
  {
    path: '/pure-yaml',
    name: 'pure-yaml',
    meta: {
      title: 'YAML - 查看YAML',
      hideInMenu: true
    },
    component: () => import('@/view/pure-yaml.vue')
  },
  {
    path: '/pod-console',
    name: 'pod-console',
    meta: {
      title: '终端',
      hideInMenu: true
    },
    component: () => import('@/view/admin/pod-console.vue')
  },
  {
    path: '/gateway-route-map',
    name: 'gateway-route-map',
    meta: {
      title: 'Gateway 路由图',
      hideInMenu: true
    },
    component: () => import('@/view/admin/gateway-route-map.vue')
  },
  {
    path: '/',
    name: '_workspace',
    redirect: '/workspace',
    component: Main,
    meta: {},
    children: [
      {
        path: '/workspace',
        name: 'workspace',
        meta: {
          title: '工作台',
          icon: 'md-desktop'
        },
        component: () => import('@/view/admin/workspace.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource',
    name: '_overview',
    component: Main,
    meta: {
      hideInBread: true
    },
    children: [
      {
        path: 'overview',
        name: 'namespace-overview',
        meta: {
          title: '资源概览',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-resourceOverview-r'],
          icon: '_ziyuan'
        },
        component: () => import('@/view/admin/resource-overview.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource',
    name: '_deployment',
    component: Main,
    meta: {
      hideInBread: true
    },
    children: [
      {
        path: 'deployment-list',
        name: 'deployment-list',
        meta: {
          title: '无状态应用',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-applicationList-r'],
          icon: '_Deployment'
        },
        component: () => import('@/view/admin/deployment-list.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource',
    name: '_stateful-set-list',
    component: Main,
    meta: {
      hideInBread: true
    },
    children: [
      {
        path: 'statefulset-list',
        name: 'statefulset-list',
        meta: {
          title: '有状态应用',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-applicationList-r'],
          icon: 'md-cube'
        },
        component: () => import('@/view/admin/statefulset-list.vue')
      }
    ]
  },
  {
    path: '/ai/resource',
    name: 'ai-resource',
    component: Main,
    meta: {
      title: 'AI 应用',
      icon: '_kubernetes1'
    },
    children: [
      {
        path: 'rayjob',
        name: 'ai-rayjob',
        meta: {
          // title: 'Rayjob',
          title: 'AI 应用',
          notCache: true,
          icon: '_kubernetes1',
          access: ['admin', 'cluster-admin', 'k8s-namespaceJob-r']
        },
        component: () => import('@/view/admin/ray-job.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource',
    name: '_mesh',
    component: Main,
    meta: {
      hideInBread: true
    },
    children: [
      {
        path: 'route-map',
        name: 'namespace-mesh-route-map',
        meta: {
          notCache: true,
          title: '网格路由图',
          access: ['admin', 'cluster-admin', 'k8s-meshroutemap-r'],
          icon: '_map'
        },
        component: () => import('@/view/admin/route-map.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource/namespace',
    name: 'k8s-resource-namespace',
    component: Main,
    meta: {
      title: 'Kubernetes 资源',
      icon: '_kubernetes1'
    },
    children: [
      {
        path: 'workloads-deployment-list',
        name: 'workloads-deployment-list',
        meta: {
          title: 'Deployment',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-applicationList-r']
        },
        component: () => import('@/view/admin/deployment-list.vue')
      },
      {
        path: 'workloads-stateful-set-list',
        name: 'workloads-stateful-set-list',
        meta: {
          title: 'StatefulSet',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-applicationList-r']
        },
        component: () => import('@/view/admin/statefulset-list.vue')
      },
      {
        path: 'services',
        name: 'namespace-services',
        meta: {
          title: 'Service',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceService-r']
        },
        component: () => import('@/view/admin/service.vue')
      },
      {
        path: 'pod',
        name: 'namespace-pod',
        meta: {
          title: 'Pod',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespacePods-r']
        },
        component: () => import('@/view/admin/pod.vue')
      },
      {
        path: 'hpa',
        name: 'namespace-hpa',
        meta: {
          title: 'HPA',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceHpa-r']
        },
        component: () => import('@/view/admin/hpa.vue')
      },
      {
        path: 'configmap',
        name: 'namespace-configmap',
        meta: {
          title: 'ConfigMap',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceConfigmap-r']
        },
        component: () => import('@/view/admin/configmap.vue')
      },
      {
        path: 'secrets',
        name: 'namespace-secrets',
        meta: {
          title: 'Secrets',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceSecret-r']
        },
        component: () => import('@/view/admin/secrets.vue')
      },
      {
        path: 'pvc',
        name: 'namespace-pvc',
        meta: {
          title: 'PVC',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespacePVC-r']
        },
        component: () => import('@/view/admin/pvc.vue')
      },

      {
        path: 'end-point',
        name: 'namespace-end-point',
        meta: {
          title: 'EndPoint',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceEndpoint-r']
        },
        component: () => import('@/view/admin/end-point.vue')
      },
      {
        path: 'cron-job',
        name: 'namespace-cron-job',
        meta: {
          title: 'CronJob',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceCronjob-r']
        },
        component: () => import('@/view/admin/cron-job.vue')
      },
      {
        path: 'job',
        name: 'namespace-job',
        meta: {
          title: 'Job',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceJob-r']
        },
        component: () => import('@/view/admin/job.vue')
      },
      {
        path: 'crd',
        name: 'namespace-crd',
        meta: {
          title: 'CRD',
          notCache: true,
          access: ['admin', 'cluster-admin', 'k8s-namespaceCrd-r']
        },
        component: () => import('@/view/admin/crd.vue')
      }
    ]
  },
  {
    path: '/kubernetes/resource/mesh',
    name: 'mesh',
    component: Main,
    meta: {
      title: 'Istio 资源',
      icon: '_istio'
    },
    children: [
      {
        path: 'virtualservice',
        name: 'namespace-mesh-virtualservice',
        meta: {
          notCache: true,
          title: 'VirtualService',
          access: ['admin', 'cluster-admin', 'k8s-namespaceMeshVS-r']
        },
        component: () => import('@/view/admin/virtual-service.vue')
      },
      {
        path: 'destinationrule',
        name: 'namespace-mesh-destinationRule',
        meta: {
          notCache: true,
          title: 'DestinationRule',
          access: ['admin', 'cluster-admin', 'k8s-namespaceMeshDR-r']
        },
        component: () => import('@/view/admin/destination-rule.vue')
      },
      {
        path: 'serviceentry',
        name: 'namespace-mesh-serviceEntry',
        meta: {
          notCache: true,
          title: 'ServiceEntry',
          access: ['admin', 'cluster-admin', 'k8s-namespaceMeshSE-r']
        },
        component: () => import('@/view/admin/service-entry.vue')
      },
      {
        path: 'sidecar',
        name: 'namespace-mesh-sideCar',
        meta: {
          notCache: true,
          title: 'Sidecar',
          access: ['admin', 'cluster-admin', 'k8s-namespaceMeshSidecar-r']
        },
        component: () => import('@/view/admin/side-car.vue')
      },
      {
        path: 'telemetry',
        name: 'namespace-mesh-telemetry',
        meta: {
          notCache: true,
          title: 'Telemetry',
          access: ['admin', 'cluster-admin', 'k8s-namespaceMeshTelemetry-r']
        },
        component: () => import('@/view/admin/telemetry.vue')
      }
    ]
  },

  {
    path: '/gateway',
    name: 'gateway',
    meta: {
      hideInBread: true
    },
    component: Main,
    children: [
      {
        path: 'gateway-management',
        name: 'gateway-management',
        meta: {
          notCache: true,
          title: '网关管理',
          icon: 'md-git-network',
          access: ['admin', 'cluster-admin', 'k8s-unifiedGateway-r']
        },
        component: () => import('@/view/admin/gateway-management.vue')
      }
    ]
  },
  {
    path: '/operation',
    name: 'operation',
    component: Main,
    meta: {
      title: '运维管理',
      icon: 'ios-calendar'
    },
    children: [
      {
        path: 'node-management',
        name: 'node-management',
        meta: {
          notCache: true,
          title: '节点管理',
          access: ['admin', 'cluster-admin', 'k8s-opsManagement-r', 'k8s-opsManagement-w']
        },
        component: () => import('@/view/admin/node-mgt.vue')
      },
      {
        path: 'event-management',
        name: 'event-management',
        meta: {
          title: '事件管理',
          access: ['admin', 'cluster-admin', 'k8s-opsManagement-r', 'k8s-opsManagement-w']
        },
        component: () => import('@/view/admin/event-mgt.vue')
      },
      {
        path: 'port-forward',
        name: 'port-forward',
        meta: {
          title: 'PortForward',
          access: ['admin', 'cluster-admin', 'k8s-opsManagement-r', 'k8s-opsManagement-w']
        },
        component: () => import('@/view/admin/port-forward.vue')
      },
      {
        path: 'backup',
        name: 'backup',
        meta: {
          title: '备份管理',
          access: ['admin', 'cluster-admin', 'k8s-opsManagement-r', 'k8s-opsManagement-w']
        },
        component: () => import('@/view/admin/backup-mgt.vue')
      }
    ]
  },

  {
    path: '/setting',
    name: 'setting',
    component: Main,
    meta: {
      icon: 'md-build',
      title: '后台管理',
      access: ['admin']
    },
    children: [
      {
        path: 'user-manager',
        name: 'user-manager',
        meta: {
          title: '用户管理',
          access: ['admin']
        },
        component: () => import('@/view/admin/setting/user-manager.vue')
      },
      {
        path: 'config',
        name: 'config',
        meta: {
          title: '系统配置',
          access: ['admin']
        },
        component: () => import('@/view/admin/setting/config.vue')
      },

      {
        path: 'subscribe-config',
        name: 'subscribeConfig',
        meta: {
          title: '订阅配置',
          access: ['admin']
        },
        component: () => import('@/view/admin/setting/subscribe-config/subscribe-config.vue')
      },

      {
        path: '/op-audit',
        name: 'operation-audit',
        component: parentView,
        meta: {
          title: '操作审计',
          hideInBread: true
        },
        children: [
          {
            path: 'access-op-log',
            name: 'accessOperationLog',
            meta: {
              title: '平台审计',
              access: ['admin']
            },
            component: () => import('@/view/admin/setting/audit/accessOpLog.vue')
          },
          {
            path: 'pod-op-log',
            name: 'settingKubernetesPodOperationLog',
            meta: {
              title: 'Pod 审计',
              access: ['admin']
            },
            component: () => import('@/view/admin/setting/audit/podOperationLog.vue')
          }
        ]
      },
      {
        path: '/openapi-token',
        name: 'openapi-token',
        meta: {
          title: 'OpenApiToken',
          access: ['admin']
        },
        component: () => import('@/view/admin/setting/openapiToken.vue')
      },
      {
        path: '/cluster-mgt',
        name: 'cluster-mgt',
        component: parentView,
        meta: {
          title: '集群管理',
          access: ['admin'],
          hideInBread: true
        },
        children: [
          {
            path: 'cluster-overview',
            name: 'cluster-overview',
            meta: {
              title: '信息统计',
              access: ['admin']
            },
            component: () => import('@/view/admin/cluster-overview.vue')
          },
          {
            path: 'cluster-config',
            name: 'cluster-config',
            meta: {
              title: '集群配置',
              access: ['admin']
            },
            component: () => import('@/view/admin/cluster-config.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/kubernetes/namespace/deployment-detail',
    name: 'deployment-detail',
    meta: {
      // title: 'Deployment 详情',
      title: (route) => `${route.query.deployment} / ${route.query.namespace} / ${route.query.cluster}`,
      notCache: true,
      hideInMenu: true
    },
    component: () => import('@/view/admin/deployment-detail.vue')
  },
  {
    path: '/kubernetes/namespace/statefulset-detail',
    name: 'statefulset-detail',
    meta: {
      // title: 'Deployment 详情',
      title: (route) => `${route.query.name} / ${route.query.namespace} / ${route.query.cluster}`,
      notCache: true,
      hideInMenu: true
    },
    component: () => import('@/view/admin/statefulset-detail.vue')
  },
  {
    path: '/kubernetes/namespace/rayjob-detail',
    name: 'rayjob-detail',
    meta: {
      // title: 'Deployment 详情',
      title: (route) => `${route.query.name} / ${route.query.namespace} / ${route.query.cluster}`,
      notCache: true,
      hideInMenu: true
    },
    component: () => import('@/domain/Resource/RayJob/RayjobDetail/index.vue')
  },
  {
    path: '/kubernetes/logs',
    name: 'k8s-logs',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/admin/pod-log.vue')
  },
  {
    path: '/kubernetes/pods/console/:id',
    name: 'k8s-pod-exec',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/admin/podExec.vue')
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/500.vue')
  },
  {
    path: '*',
    name: 'error_404',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/404.vue')
  }
]
