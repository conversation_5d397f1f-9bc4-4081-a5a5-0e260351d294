<script lang="ts" setup>
import { PropType } from 'vue'
import { Resource } from '../type'
import useRelativeResourceModalService from './useRelativeResourceModalService'
import { Space, Empty } from '@/components'
import { ResourceCard } from '../index'

const props = defineProps({
  value: { type: Boolean, default: false },
  data: { type: Object as PropType<Resource>, default: () => ({}) }
})

const { data, loading } = useRelativeResourceModalService(props)

defineEmits(['input'])
</script>

<template>
  <Modal
    :title="`关联资源 - ${props.data.name}`"
    width="60"
    :value="props.value"
    footer-hide
    @on-visible-change="(val) => $emit('input', val)"
    class-name="relative-resource-modal"
  >
    <Spin fix v-if="loading" />
    <Space class="search-modal-content-right" v-if="!!data?.length" direction="vertical">
      <template v-for="(item, index) in data">
        <ResourceCard :data="item" :key="item.name + item.createdAt + index" isRelative />
      </template>
    </Space>
    <Empty v-else />
  </Modal>
</template>

<style lang="less" scoped>
/deep/.relative-resource-modal {
  .ivu-modal-body {
    height: 50vh;
    overflow: auto;
  }
}
</style>
