import { ref, onMounted, getCurrentInstance, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'

import { YamlGVR } from '../config'
import { YamlHistoryParams } from '@/components/yaml'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList } from '@/libs/service.request'
import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import useSingleK8SService from '@/libs/useSingleK8SService'

interface EndPoint {
  name: string
  uuid: string
}

export default function useEndPointService() {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const detail = ref({
    visible: false,
    data: {}
  })
  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...YamlGVR['EndPoint']
    }

    yamlInitData.value = `apiVersion: v1 
kind: Endpoints
metadata:
    name: 必须修改
    namespace: ${K8SInstance.value.namespace}
spec:`
  }

  const onDelete = (record: EndPoint) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteEndPoint}`, {
          params: {
            ...K8SInstance.value,
            name: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onEdit = (record: EndPoint) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...YamlGVR['EndPoint'],
      resourceName: record.name
    }
  }

  const onViewYaml = (record: EndPoint) => {
    yamlHistoryParams.value = {
      ...K8SInstance.value,
      kind: 'EndPoints',
      uuid: record.uuid
    }
    formEntity.value = {
      ...K8SInstance.value,
      ...YamlGVR['EndPoint'],
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: EndPoint) => {
    console.log('onViewDetail')
    detail.value = {
      visible: true,
      data: record
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<EndPoint[]>>(
      `${Config.Api.Base}${Config.Api.GetEndPointTableData}?search=${params.searchValue ?? ''}&clusterId=${
        K8SInstance.value.clusterId
      }&namespace=${K8SInstance.value.namespace}&page=${params.pageIndex}&size=${params.pageSize}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const initK8SInstance = () => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    store.commit('getCurrentNamespace', store.state.user.userId)
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId
    }
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })
  return {
    getTableData,
    refObject,
    onCreate,
    onEdit,
    onDelete,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    onSubmitSuccess,
    onViewDetail,
    detail,
    K8SInstance
  }
}
