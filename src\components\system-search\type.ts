import { EnumToUnion } from '@/libs/service.request/type'
import { EnumResourceType } from './config'

export interface Resource {
  uuid: string
  name: string
  resource: EnumToUnion<EnumResourceType>
  clusterName: string
  clusterId: number
  namespace: string
  createdAt: number
  descriptions: Record<string, string | number>
  isAuthorized: boolean // 是否经过此用户授权
  gvr: {
    resource: string
    group: string
    version: string
  }
}

export interface Container {
  name: string
  startedAt: string
  state: string
}

interface Cluster {
  clusterId: string
  clusterName: string
}

export interface LicenseInfo {
  clusters: Cluster[]
  namespaces: string[]
  resources: {
    group: string
    resource: string
  }[]
}

export interface HistoryParam extends Partial<LicenseInfo> {
  search?: string
}
