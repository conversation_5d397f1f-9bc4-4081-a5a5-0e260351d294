<script setup lang="ts">
import useLogMergeService from './useLogMergeService'
import { EnumWorkloadKind } from './useLogMergeService'

export interface LogMergeModalProps {
  value: boolean
  clusterId: string
  namespace: string
  cluster: string
  workloadName: string
  workloadKind: keyof typeof EnumWorkloadKind
  title?: string
}

const props = withDefaults(defineProps<LogMergeModalProps>(), {
  title: '需合并的容器'
})

const emit = defineEmits(['input'])

const { visible, columns, loading, tableData, handleMergeCheckChange, onLogMergeSubmit } = useLogMergeService(
  props,
  emit
)
</script>

<template>
  <Modal v-model="visible" :title="props.title" width="480px" class-name="log-merge-modal">
    <Table :columns="columns" :loading="loading" :data="tableData" border>
      <template #isMerge="{ index }">
        是
        <Checkbox v-model="tableData[index].isMerge" @on-change="() => handleMergeCheckChange(index)" />
      </template>
    </Table>

    <template #footer>
      <Button type="text" @click="() => (visible = false)">取消</Button>
      <Button type="primary" @click="onLogMergeSubmit">确认</Button>
    </template>
  </Modal>
</template>

<style lang="less" scoped></style>
