<script lang="ts" setup>
import { PropType } from 'vue'
import { Space, LinkButton, ProTable, Yaml } from '@/components'
import { EnumTabType } from '../type'
import dayjs from 'dayjs'
import useExpandTableService, { TASK_OBJECT_COLUMNS, STATUS_MAP } from './useExpandTableService'
import { color } from '@/libs/consts'

const props = defineProps({
  data: { type: Object as PropType<{ id: string }> },
  activeTab: { type: String as PropType<EnumTabType> }
})

const {
  columns,
  taskObject,
  refObject,
  keyword,
  data,
  loading,
  onOpenTaskModal,
  onExec,
  getTableData,
  staticData,
  onOpenYaml,
  onSearch,
  yamlModal
} = useExpandTableService(props)
</script>

<template>
  <div class="expand-row">
    <Table :data="data" :columns="columns" size="small" :loading="loading" :max-height="220">
      <template #status="{ row }">
        <span :style="`font-weight: 600; color: ${STATUS_MAP[row.status]?.color}`">{{
          STATUS_MAP[row.status]?.text
        }}</span>
      </template>
      <template #message="{ row }">{{ row.errorMessage ? row.errorMessage : '-' }}</template>
      <template #ops="{ row }">
        <Space justify>
          <LinkButton @click="() => onOpenTaskModal(row)" text="任务详情" />

          <LinkButton @click="() => onExec()" text="重试" />
        </Space>
      </template>
    </Table>
    <Modal title="查看任务列表" v-model="taskObject.visible" footer-hide width="60">
      <Card>
        <Row>
          <Col span="3" class="detail-title">Cluster：</Col>
          <Col span="9">{{ props.data.clusterName }}</Col>
          <Col span="3" class="detail-title">ApiResource：</Col>
          <Col span="9">{{
            `${props.data.group ? props.data.group : '-'} / ${
              props.data.resource ? props.data.resource : '-'
            } / ${dayjs(taskObject.data.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm:ss')}`
          }}</Col>
        </Row>
        <Row>
          <Col span="3" class="detail-title">Namespace：</Col>
          <Col span="9">{{ props.data.namespace }}</Col>
          <Col span="3" class="detail-title">生效期限：</Col>
          <Col span="9">
            <span :style="`font-weight: 600; color: ${props.data.isPersistent ? color.success : color.warning}`">{{
              props.data.isPersistent ? '永久' : '临时'
            }}</span></Col
          >
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <Space direction="vertical">
          <Input v-model="keyword" placeholder="请输入关键字" clearable search @on-search="onSearch" />

          <Space>
            <span class="statistic-item" :style="`color:${color.primary}`">总数：{{ staticData.total }}</span>
            <span class="statistic-item" :style="`color:${color.success}`">成功：{{ staticData.succeed }}</span>
            <span class="statistic-item" :style="`color:${color.error}`">失败：{{ staticData.failed }}</span>
          </Space>
          <pro-table
            :columns="TASK_OBJECT_COLUMNS"
            :request="getTableData"
            :action-ref="refObject"
            manual-request
            hideSearchOperation
            :pagination="{ defaultPageSize: 10, simple: true }"
            :height="300"
          >
            <template #ops="{ row }">
              <LinkButton @click="() => onOpenYaml(row)" text="YAML" />
            </template>
          </pro-table>
        </Space>
      </Card>
    </Modal>

    <Modal v-model="yamlModal.visible" title="查看YAML" footer-hide :width="60">
      <yaml v-if="yamlModal.data" :value="yamlModal.data" :forbiddenEdit="true" style="height: 60vh" />
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.detail-title {
  font-weight: 600;
}
.statistic-item {
  font-size: 12px;
  font-weight: 600;
}
</style>
