import axios from '@/libs/api.request'

export const ApiAuthClusterList = () => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/cluster/list',
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiAuthNamespaceList = (clusterName) => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/namespace/list',
    method: 'get',
    data: {},
    params: {
      clusterName
    }
  })
}

export const ApiResourceDeploymentSearch = (page) => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/deployment/list',
    method: 'get',
    params: page
  })
}

export const ApiResourcePermissionRefresh = () => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/permission/refresh',
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiResourcePermissionList = () => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/permission/list',
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiResourceGetDeployment = (cluster_id, namespace, deployment) => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/deployment/yaml/get',
    method: 'get',
    data: {},
    params: {
      cluster_id: cluster_id,
      namespace: namespace,
      deployment: deployment
    }
  })
}

export const ApiResourceGetDeploymentJson = (
  cluster_id,
  namespace,
  deployment
) => {
  return axios.request({
    url: '/api/v1/cluster/auth-resource/deployment/get',
    method: 'get',
    data: {},
    params: {
      cluster_id: cluster_id,
      namespace: namespace,
      deployment: deployment
    }
  })
}
