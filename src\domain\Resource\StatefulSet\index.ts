export { default as StatefulSetDetail } from './StatefulSetDetail/index.vue'
export { default as StatefulSetList } from './StatefulSetList/index.vue'
export const openStatefulSetDetail = (clusterId, cluster, namespace, name, uuid) => {
  const href = `${window.location.origin}/kubernetes/namespace/statefulset-detail?clusterId=${clusterId}&cluster=${cluster}&namespace=${namespace}&name=${name}&uuid=${uuid}`

  window.open(href, '_blank')
}
