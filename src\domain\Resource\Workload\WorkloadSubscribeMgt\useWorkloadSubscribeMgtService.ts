import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import { TableColumn } from 'view-design'
const COLUMNS = [
  {
    title: '资源名',
    key: 'name',
    slot: 'name',
    minWidth: 100
  },
  {
    title: '集群',
    key: 'cluster',
    tooltip: true,

    minWidth: 80
  },
  {
    title: '命名空间',
    key: 'namespace',
    tooltip: true,

    minWidth: 80
  },
  {
    title: '资源种类',
    key: 'kind',
    tooltip: true,
    width: 100
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 80
  }
]

export default function useWorkloadSubscribeMgtService() {
  const { proxy } = getCurrentInstance()
  const tabKey = ref('subscribe')
  const allDataKeyword = ref()
  const subscribeDataKeyword = ref()
  const allDataKind = ref()
  const subscribeDataKind = ref()

  const allColumns = ref(COLUMNS)
  const subscribeColumns = ref<TableColumn[]>(COLUMNS)

  const {
    data: allData,
    loading: allDataLoading,
    run: getAllData
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetAllWorkload}`, {
        params: {
          ...(allDataKeyword.value ? { search: allDataKeyword.value } : {}),
          ...(allDataKind.value ? { kind: allDataKind.value } : {})
        }
      })
    },
    {
      initialData: [],
      formatResult(res) {
        return res.data.data
      }
    }
  )

  const {
    data: subscribeData,
    run: getSubscribeList,
    loading: subscribeDataLoading
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetWorkspaceSubscribeList}`, {
        params: {
          ...(subscribeDataKeyword.value ? { search: subscribeDataKeyword.value } : {}),
          ...(subscribeDataKind.value ? { kind: subscribeDataKind.value } : {})
        }
      })
    },
    {
      //   manual: true,
      initialData: [],
      formatResult(res) {
        formatSubscribeColumns(res.data.data)
        return res.data.data
      }
    }
  )

  const formatSubscribeColumns = (data) => {
    const clusterList: { value: string; label: string }[] = Array.from(new Set(data?.map((item) => item.cluster)))?.map(
      (i) => ({
        value: i as string,
        label: i as string
      })
    )

    const namespaceList: { value: string; label: string }[] = Array.from(
      new Set(data?.map((item) => item.namespace))
    )?.map((i) => ({
      value: i as string,
      label: i as string
    }))

    subscribeColumns.value = [
      {
        title: '资源名',
        key: 'name',
        slot: 'name',
        minWidth: 100
      },
      {
        title: '集群',
        key: 'cluster',
        tooltip: true,
        minWidth: 80,
        ...(clusterList.length
          ? {
              filters: clusterList,
              filterMultiple: false,
              filterMethod: ((value, row) => {
                return row?.cluster === value
              }) as unknown as () => void //  filterMethod ts 源码定义有问题
            }
          : null)
      },
      {
        title: '命名空间',
        key: 'namespace',
        tooltip: true,
        minWidth: 80,
        ...(namespaceList.length
          ? {
              filters: namespaceList,
              filterMultiple: false,
              filterMethod: ((value, row) => {
                return row?.namespace === value
              }) as unknown as () => void //  filterMethod ts 源码定义有问题
            }
          : null)
      },
      {
        title: '资源种类',
        key: 'kind',
        tooltip: true,
        width: 100
      },
      {
        title: 'Ops',
        key: 'ops',
        slot: 'ops',
        width: 80
      }
    ]
  }

  const onUnsubscribe = async (record) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认退订 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(`${Config.Api.Base}${Config.Api.UnsubscribeWorkspaceResource}`, {
          clusterId: record.clusterId,
          namespace: record.namespace,
          name: record.name,
          kind: record.kind
        })
        if (res.success) {
          proxy.$Message.success('退订成功！')
          getAllData()
          getSubscribeList()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const jumpToDetail = (record) => {
    if (record.kind === 'Deployment') {
      const router = proxy.$router.resolve({
        path: '/kubernetes/namespace/deployment-detail',
        query: {
          clusterId: record.clusterId,
          namespace: record.namespace,
          deployment: record.name,
          cluster: record.cluster,
          uuid: record.uuid
        }
      })
      window.open(router.href, '_blank')
    } else {
      const router = proxy.$router.resolve({
        path: '/kubernetes/namespace/statefulset-detail',
        query: {
          clusterId: record.clusterId,
          namespace: record.namespace,
          name: record.name,
          cluster: record.cluster,
          uuid: record.uuid
        }
      })
      window.open(router.href, '_blank')
    }
  }

  const onSubscribeOrNot = async (record) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认${record.subscribeStatus ? '退订' : '订阅'} ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${
            record.subscribeStatus ? Config.Api.UnsubscribeWorkspaceResource : Config.Api.SubscribeWorkspaceResource
          }`,
          {
            clusterId: record.clusterId,
            namespace: record.namespace,
            name: record.name,
            kind: record.kind
          }
        )
        if (res.success) {
          proxy.$Message.success(`${record.subscribeStatus ? '退订' : '订阅'}成功！`)
          getAllData()
          getSubscribeList()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  //   watch(allDataKind, () => {
  //     getAllData()
  //   })

  //   watch(subscribeDataKind, () => {
  //     getSubscribeList()
  //   })

  return {
    tabKey,
    allDataKeyword,
    getAllData,
    allData,
    allDataLoading,
    subscribeDataKeyword,
    getSubscribeList,
    subscribeData,
    subscribeDataLoading,
    onUnsubscribe,
    jumpToDetail,
    onSubscribeOrNot,
    allColumns,
    subscribeColumns,
    allDataKind,
    subscribeDataKind
  }
}
