<script lang="ts" setup>
import { PropType } from 'vue'
import useCrashLogModalService from './useCrashLogModalService'
import { TxtEditor, Space } from '@/components'

const props = defineProps({
  value: <PERSON><PERSON><PERSON>,
  entity: Object as PropType<{
    clusterId: string
    cluster: string
    namespace: string
    name: string
    podName: string
    container: string
  }>
})
const emit = defineEmits(['input'])

const { visible, crashLogData, crashLogLine, crashLogLoading, getCrashLog, onDownloadCrashLog } =
  useCrashLogModalService(props, emit)
</script>

<template>
  <Modal v-model="visible" footer-hide width="90" class-name="crash-log-modal-wrapper">
    <template #header>
      <Space class="crash-log-modal-title">
        <span class="title">查看崩溃日志</span>
        <Divider type="vertical" />
        <Space :size="8">
          <span>查看日志行数</span>
          <InputNumber
            size="small"
            :min="1"
            v-model="crashLogLine"
            :step="1000"
            placeholder="请输入查看行数"
            :formatter="(value) => parseInt(`${value}`)"
          />
        </Space>
        <Divider type="vertical" />
        <div class="header-btn" @click="getCrashLog">
          <Icon class="header-icon" type="ios-search" />
          查看
        </div>
        <Divider type="vertical" />
        <div class="header-btn" @click="onDownloadCrashLog">
          <Icon class="header-icon" type="md-download" />
          下载
        </div>
      </Space>
    </template>
    <Spin fix v-if="crashLogLoading">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
    </Spin>
    <TxtEditor :value="crashLogData" :forbiddenEdit="true" class="text-editor" />
  </Modal>
</template>

<style lang="less" scoped>
/deep/.crash-log-modal-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    position: relative;
    height: 84vh;
    overflow: auto;
  }
}

.crash-log-modal-title,
.crash-log-modal-title span {
  display: flex;
  align-items: center;
  .title {
    font-weight: 600;
  }
  .ivu-input-number {
    width: 120px;
  }
  .header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #2a7cc3;
    cursor: pointer;
    > .header-icon {
      margin-right: 8px;
    }
  }
  .space-component {
    display: inline-flex;
    width: auto;
    align-items: center;
  }
}
</style>
