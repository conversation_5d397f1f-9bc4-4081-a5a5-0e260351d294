<template>
  <div class="menu-title">
    {{ title }}
  </div>
</template>

<script>
export default {
  name: 'menu-title',
  props: {
    title: {
      type: String,
      default: () => {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="css">
.menu-title {
  padding-left: 16px;
  font-size: 16px;
  color: #2a7cc3;
  height: 16px;
  line-height: 16px;
  margin-bottom: 16px;
}
.menu-title:after {
  content: '';
  width: 0;
  height: 15px;
  position: relative;
  border: 2px solid #2a7cc3;
  left: -15px;
  top: -16px;
  border-left-color: #2a7cc3;
  display: block;
  border-radius: 3px;
}
</style>
