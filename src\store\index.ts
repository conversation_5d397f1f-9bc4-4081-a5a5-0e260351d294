import Vue from 'vue'
import Vuex from 'vuex'

import user from './module/user'
import app from './module/app'
import k8s from './module/k8s'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    user: { userId: '', access: '', hasGetInfo: '' },
    k8s: { currentCluster: '', currentNamespace: '' }
  },
  mutations: {
    //
  },
  actions: {
    //
  },
  modules: {
    user,
    app,
    k8s
  }
})
