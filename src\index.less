@import '~iview/src/styles/index.less';
@primary-color: #2a7cc3;
@info-color: #2a7cc3;
@success-color: #1abe6b;
@warning-color: #ff9800;
@error-color: #ed4014;

@menu-dark-title: #001529;
@menu-dark-active-bg: #000c17;

@layout-sider-background: #ffffff;

body {
  font-size: 14px;
}
//
//@menu-dark-title: #2c4f88;
//@menu-dark-active-bg: #2c4f88;
//@layout-sider-background: #2c4f88;

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.ivu-spin-fullscreen {
  background-color: transparent;
}

.ivu-alert {
  margin-bottom: 16px;
}

/* 滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
  background-color: #f8f8f8;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条的宽度 */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条的设置 */
*::-webkit-scrollbar-thumb {
  background-color: #ddd;
  background-clip: padding-box;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条鼠标移上去 */
*::-webkit-scrollbar-thumb:hover {
  background-color: #bbb;
}

a {
  color: #2a7cc3;
}

.ivu-tag-primary {
  background: #2a7cc3;
}


.p-0 {
  padding: 0 !important;
}
