<template>
  <Container>
    <Alert show-icon>
      在当前页面，您可以管理平台的用户，进行角色设置、资源授权和Kubectl授权操作。这样，您可以有效地对用户进行权限管理和资源访问控制。
      <p style="margin-top: 16px; margin-left: 16px">
        <b>用户: </b>
        平台的用户分为两种，一种是普通用户，另一种是App用户。App用户会带有
        <span style="color: #ed4014">[app]</span>标识，以便于在平台上区分不同类型的用户。
      </p>
      <p style="margin-top: 16px; margin-left: 16px">
        <b>资源授权: </b>
        资源授权分为 <span style="color: #2a7cc3">页面权限</span> 和
        <span style="color: #2a7cc3">K8S资源权限</span>。页面权限主要针对平台页面的访问和操作进行授权.
        K8S资源权限则按集群和命名空间进行资源授权，使用户只能访问和操作被授权的Kubernetes资源。
      </p>
      <p style="margin-top: 16px; margin-left: 16px">
        <b>Kubectl授权: </b>
        Kubectl授权分为 <span style="color: #2a7cc3">代理模式</span> 和
        <span style="color: #2a7cc3">WEB模式</span
        >。代理模式允许用户下载kubeconfig配置到本地，然后使用本地的kubectl工具管理Kubernetes集群。而WEB模式则通过平台提供的在线console进行操作。
      </p>
    </Alert>
    <Card :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            ref="input"
            v-model="page.s_username"
            clearable
            placeholder="搜索 用户名(英文)"
            search
            enter-button
            style="width: 100%"
            @on-search="handleSearch"
          />
        </Col>
      </Row>
      <Row type="flex">
        <Col span="24">
          <Button type="primary" ghost icon="ios-refresh" size="small" @click="reloadTable"> 刷新 </Button>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px" :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data">
        <template slot-scope="{ row }" slot="is_active">
          <Checkbox
            v-model="row.is_active"
            :true-value="1"
            :false-value="0"
            @on-change="
              () => {
                setActive(row, row.is_active === 1)
              }
            "
          ></Checkbox>
        </template>
        <template slot-scope="{ row }" slot="is_cluster_admin">
          <Checkbox
            v-model="row.is_cluster_admin"
            :true-value="1"
            :false-value="0"
            @on-change="
              () => {
                setClusterAdmin(row, row.is_cluster_admin === 1)
              }
            "
          ></Checkbox>
        </template>
        <template slot-scope="{ row }" slot="is_admin">
          <Checkbox
            v-model="row.is_admin"
            :true-value="1"
            :false-value="0"
            @on-change="
              () => {
                setAdmin(row, row.is_admin === 1)
              }
            "
          ></Checkbox>
        </template>
      </Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        show-total
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
      >
      </Page>
    </Card>
    <Modal v-model="nsModal" title="Namespace scope" :loading="loading.ns" width="50" @on-ok="handleNs">
      <Form :model="nsForm" label-position="top">
        <FormItem label="Select namespace">
          <Select v-model="nsForm.select" multiple filterable>
            <template v-for="(ns, k) in allNamespaceList">
              <Option :key="k" :value="ns">{{ ns }}</Option>
            </template>
          </Select>
        </FormItem>
      </Form>
    </Modal>
    <Modal v-model="raModal" :title="raModalTitle" width="60">
      <Alert show-icon>点击 【读】、【写】、【执行】授权立即生效， 无需额外点击确定保存.</Alert>
      <Tabs size="small" type="card">
        <TabPane label="页面权限">
          <Table size="small" :columns="viewerColumns" :data="viewerData" height="496">
            <template slot="read" slot-scope="{ row }">
              <Checkbox
                v-model="row.read[1]"
                @on-change="
                  (b) => {
                    if (b) {
                      createUserViewerAuth(currentUserId, row.read[0])
                    } else {
                      deleteUserViewerAuth(currentUserId, row.read[0])
                    }
                  }
                "
                >Read
              </Checkbox>
            </template>
            <template slot="write" slot-scope="{ row }">
              <Checkbox
                v-model="row.write[1]"
                @on-change="
                  (b) => {
                    if (b) {
                      createUserViewerAuth(currentUserId, row.write[0])
                    } else {
                      deleteUserViewerAuth(currentUserId, row.write[0])
                    }
                  }
                "
                >Write
              </Checkbox>
            </template>
          </Table>
        </TabPane>
        <TabPane label="K8S资源权限">
          <Alert show-icon
            >需要先选择 <b style="color: #ed4014">【集群】</b>
            <b style="color: #ed4014">【命名空间】</b>
          </Alert>
          <div style="margin-bottom: 20px">
            <Select
              v-model="currentCluster"
              filterable
              clearable
              style="width: 49%"
              placeholder="集群"
              @on-change="
                (clusterId) => {
                  currentCluster = clusterId
                  currentNamespace = ''
                  getClusterWithNsList(clusterId)
                }
              "
            >
              <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>

            <Select
              v-model="currentNamespace"
              style="width: 49%; margin-left: 16px"
              filterable
              clearable
              placeholder="命名空间 (需先选择集群)"
              @on-change="
                (namespace) => {
                  currentNamespace = namespace
                  getK8sAuthList(currentCluster, namespace)
                  getNamespaceAuths()
                }
              "
            >
              <Option v-for="item in clusterNamespaceList" :value="item.metadata.name" :key="item.metadata.name">{{
                item.metadata.name
              }}</Option>
            </Select>
          </div>
          <Card>
            <Spin fix v-if="loading.ResourcePRSwitchNs">
              <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
              <div>Loading</div>
            </Spin>

            <span>为空间下所有资源进行授权：&nbsp;&nbsp;</span>
            <Checkbox
              style="margin-right: 16px"
              v-model="currentNsRead"
              @on-change="
                (b) => {
                  if (b) {
                    createNamespaceAuth('r')
                  } else {
                    deleteNamespaceAuth('r')
                  }
                }
              "
              ><b>读</b></Checkbox
            >
            <Checkbox
              style="margin-right: 16px"
              v-model="currentNsWrite"
              @on-change="
                (b) => {
                  if (b) {
                    createNamespaceAuth('w')
                  } else {
                    deleteNamespaceAuth('w')
                  }
                }
              "
              ><b>写</b></Checkbox
            >
            <Checkbox
              style="font-weight: 400"
              v-model="currentNsExec"
              @on-change="
                (b) => {
                  if (b) {
                    createNamespaceAuth('x')
                  } else {
                    deleteNamespaceAuth('x')
                  }
                }
              "
              ><b>执行</b></Checkbox
            >

            <br /><br />
            <div class="ra-collapse-wrapper">
              <Collapse accordion simple>
                <Panel name="service">
                  <b>Service 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="svcCols" :data="svcData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Pod">
                  <b>Pod 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="podCols" :data="podData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Deployment">
                  <b>Deployment 授权</b>
                  <div slot="content" style="padding: 16px">
                    <span style="font-size: 12px; color: #ed4014; margin-bottom: 16px">
                      授权 deploy/* 相当于对所有deployment 资源授权对应权限;
                    </span>
                    <Input
                      style="margin-bottom: 16px"
                      clearable
                      @on-clear="
                        () => {
                          deployData = originDeployData
                        }
                      "
                      @on-search="searchDeployment"
                      search
                      placeholder="检索需要授权的 deployment"
                    />
                    <Table size="small" height="230" :columns="deployCols" :data="deployData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="HPA">
                  <b>HPA 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="hpaCols" :data="hpaData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Configmap">
                  <b>Configmap 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="configmapCols" :data="configmapData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Secrets">
                  <b>Secrets 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="secretCols" :data="secretData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="VirtualService">
                  <b>VirtualService 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="vsCols" :data="vsData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="DestinationRule">
                  <b>DestinationRule 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="drCols" :data="drData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Gateway">
                  <b>Gateway 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="gatewayCols" :data="gatewayData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="ServiceEntry">
                  <b>ServiceEntry 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="seCols" :data="seData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="SideCar">
                  <b>SideCar 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="sidecarData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="EndPoint">
                  <b>EndPoint 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="endpointData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Job">
                  <b>Job 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="jobData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="CronJob">
                  <b>CronJob 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="cronJobData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="Telemetry">
                  <b>Telemetry 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="telemetryData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
                <Panel name="CRD">
                  <b>CRD 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="crdData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>

                <Panel name="PVC">
                  <b>PVC 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="pvcData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>

                <Panel name="StatefulSet">
                  <b>StatefulSet 授权</b>
                  <div slot="content" style="padding: 16px">
                    <Table size="small" :columns="sidecarCols" :data="statefulsetData"
                      >`
                      <template slot="read" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.read[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.read[0])
                              } else {
                                deleteUserK8sAuth(row.read[0])
                              }
                            }
                          "
                          >Read
                        </Checkbox>
                      </template>
                      <template slot="write" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.write[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.write[0])
                              } else {
                                deleteUserK8sAuth(row.write[0])
                              }
                            }
                          "
                          >Write
                        </Checkbox>
                      </template>
                      <template slot="exec" slot-scope="{ row }">
                        <Checkbox
                          v-model="row.exec[1]"
                          @on-change="
                            (b) => {
                              if (b) {
                                createUserK8sAuth(row.exec[0])
                              } else {
                                deleteUserK8sAuth(row.exec[0])
                              }
                            }
                          "
                          >Exec
                        </Checkbox>
                      </template>
                    </Table>
                  </div>
                </Panel>
              </Collapse>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </Modal>

    <Modal v-model="kubectlModal" title="Kubectl 授权" width="60" :footer-hide="true">
      <Tabs size="small" type="card">
        <TabPane label="代理模式" name="proxy_pattern">
         
          <Divider orientation="left" size="small">基础操作</Divider>
          <Row type="flex" align="middle">
            <Col flex="auto"><b>激活状态</b></Col>
            <Col flex="0 1 80px">
              <i-switch v-model="activationStatus" @on-change="changeActivationStatus">激活</i-switch>
            </Col>
          </Row>
          <Divider orientation="left" size="small">权限配置</Divider>
          <Alert show-icon>【选择集群】后可展示集群授权信息。</Alert>
          <Row type="flex" align="middle">
            <Col flex="auto">
              <span style="font-weight: bold; margin-right: 16px">集群: </span>
              <Select
                v-model="kubeconfigSelectCluster"
                style="width: 300px"
                filterable
                placeholder="选择集群"
                @on-change="onSelectKubeConfigCluster"
              >
                <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </Col>
            <Col flex="0 1 170px" style="display: flex; justify-content: flex-end">
              <Button type="info" @click="rebuildKubeConfigRole">重建权限</Button>
              <Button style="margin-left: 8px" @click="handleKubeConfigDownload">下载</Button>
            </Col>
          </Row>
          <Card style="margin-top: 20px">
            <h4>全局范围</h4>
            <Row style="margin-top: 20px">
              <Button
                type="primary"
                size="small"
                ghost
                @click="
                  () => {
                    kubeconfigEditMode = 'global'
                    handleOpenCreateKubeConfigRole()
                  }
                "
                >+ 创建角色
              </Button>
              <Alert show-icon>会自动同时创建【WEB模式】角色。</Alert>
            </Row>
            <div style="margin-top: 16px">
              <Table size="small" :columns="kubeconfigGlobalCols" :data="kubeconfigGlobalData"></Table>
            </div>
          </Card>
          <Card style="margin-top: 20px">
            <h4>命名空间范围</h4>
            <Row style="margin-top: 20px">
              <Button
                type="primary"
                size="small"
                ghost
                @click="
                  () => {
                    handleOpenCreateKubeConfigRole()
                    kubeconfigEditMode = 'namespace'
                  }
                "
                >+ 创建角色
              </Button>
              <Alert show-icon>会自动同时创建【WEB模式】角色。</Alert>
            </Row>
            <Table
              style="margin-top: 16px"
              size="small"
              :columns="kubeconfigNamespaceCols"
              :data="kubeconfigNamespaceData"
            ></Table>
          </Card>
        </TabPane>

        <TabPane label="WEB模式" name="web_pattern">
          <Alert show-icon>【选择集群】后可展示集群授权信息。</Alert>
          <div>
            <span style="font-weight: bold; margin-right: 16px">集群: </span>
            <Select
              v-model="kubectlSelectCluster"
              style="width: 300px"
              filterable
              placeholder="选择集群"
              @on-change="onSelectKubectlCluster"
            >
              <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
          </div>
          <Card style="margin-top: 20px">
            <h4>全局范围</h4>
            <Row style="margin-top: 20px">
              <Button
                type="primary"
                size="small"
                ghost
                @click="
                  () => {
                    kubectlEditMode = 'global'
                    handleOpenCreateKubectlRole()
                  }
                "
                >+ 创建角色
              </Button>
            </Row>
            <div style="margin-top: 16px">
              <Table size="small" :columns="kubectlGlobalCols" :data="kubectlGlobalData"></Table>
            </div>
          </Card>
          <Card style="margin-top: 20px">
            <h4>命名空间范围</h4>
            <Row style="margin-top: 20px">
              <Button
                type="primary"
                size="small"
                ghost
                @click="
                  () => {
                    handleOpenCreateKubectlRole()
                    kubectlEditMode = 'namespace'
                  }
                "
                >+ 创建角色
              </Button>
            </Row>
            <Table
              style="margin-top: 16px"
              size="small"
              :columns="kubectlNamespaceCols"
              :data="kubectlNamespaceData"
            ></Table>
          </Card>
        </TabPane>
      </Tabs>
      <!-- <div slot="footer"></div> -->
    </Modal>

    <!-- kubeconfig编辑框 -->
    <Modal
      v-model="kubeconfigEditModal"
      :loading="kubeconfigEditLoading"
      width="50"
      :title="kubeconfigCommitMode === 'create' ? '创建角色' : '编辑角色'"
      ok-text="提交"
      @on-ok="handleKubeConfigRoleCommit"
      :mask-closable="false"
    >
      <Form label-position="top">
        <FormItem label="权限模版" v-if="kubeconfigCommitMode === 'create'">
          <Select v-model="kubeconfigForm.templateName" filterable @on-change="getKubeConfigTemplateYaml">
            <Option v-for="(item, idx) in kubeconfigTemplateList" :key="idx" :value="item.name">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem v-if="kubeconfigEditMode === 'namespace'" label="命名空间 (namespace)">
          <Select v-model="kubeconfigForm.namespace" filterable :disabled="kubeconfigCommitMode === 'update'">
            <Option v-for="item in clusterNamespaceList" :value="item.metadata.name" :key="item.metadata.name">{{
              item.metadata.name
            }}</Option>
          </Select>
        </FormItem>
        <FormItem label="角色名称">
          <Input v-model="kubeconfigForm.roleName"></Input>
        </FormItem>
        <FormItem label="过期时间">
          <Date-picker :options="dateOptions" type="datetime" placeholder="选择日期"  v-model="kubeconfigForm.expiredAt" style="width: 100%"></Date-picker>
        </FormItem>

        <FormItem label="权限内容">
          <div style="height: 350px; overflow: auto">
            <yaml ref="kubeconfigRoleYaml" style="font-size: 12px; height: 350px" v-model="kubeconfigForm.yaml"></yaml>
          </div>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="kubectlEditModal"
      :loading="kubectlEditLoading"
      width="50"
      :title="kubectlCommitMode === 'create' ? '创建角色' : '编辑角色'"
      ok-text="提交"
      @on-ok="handleKubectlRoleCommit"
      :mask-closable="false"
    >
      <Form label-position="top">
        <FormItem label="权限模版" v-if="kubectlCommitMode === 'create'">
          <Select v-model="kubectlForm.template_name" filterable @on-change="getKubectlTemplateYaml">
            <Option v-for="(item, idx) in kubectlTemplateList" :key="idx" :value="item.name">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem v-if="kubectlEditMode === 'namespace'" label="命名空间 (namespace)">
          <Select v-model="kubectlForm.namespace" filterable :disabled="kubectlCommitMode === 'update'">
            <Option v-for="item in clusterNamespaceList" :value="item.metadata.name" :key="item.metadata.name">{{
              item.metadata.name
            }}</Option>
          </Select>
        </FormItem>
        <FormItem label="角色名称">
          <Input v-model="kubectlForm.role_name"></Input>
        </FormItem>

        <FormItem label="权限内容">
          <div style="height: 350px; overflow: auto">
            <yaml ref="roleYaml" style="font-size: 12px; height: 350px" v-model="kubectlForm.yaml"></yaml>
          </div>
        </FormItem>
      </Form>
    </Modal>

    <Modal v-model="profileModal" title="用户信息" width="50" class-name="info-modal">
      <Spin fix v-if="profileLoading">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <Card>
        <Row type="flex" v-for="(v, k) in userInfo" :key="k" style="margin-bottom: 16px">
          <Col span="10" class="custom-align-left">
            <b>{{ k }}</b>
          </Col>
          <Col span="14">
            <template v-if="k === 'isActive' || k === 'isAdmin' || k === 'isClusterAdmin'">
              <Icon v-if="v === 1" type="ios-checkmark-circle" style="color: #19be6b" />
              <Icon v-else type="ios-close-circle" style="color: crimson" />
            </template>
            <template v-else>
              {{ v }}
            </template>
          </Col>
        </Row>
      </Card>
      <div style="margin-top: 20px">
        <Alert show-icon>
          权限信息
          <div slot="desc">
            <p>
              1.
              <span style="color: #ed4014">Admin 权限</span>
              ：拥有平台所有权限，所有操作都无限制 (可忽略下面的权限显示)。
            </p>
            <p>
              2.
              <span style="color: #ed4014">ClusterAdmin 权限</span>：几乎拥有所有权限除了一下后台管理相关的权限。
            </p>
            <p>
              3.
              <span style="color: #2a7cc3">视图权限</span>：针对平台页面的读写权限；
            </p>
            <p>
              4.
              <span style="color: #2a7cc3">K8S 资源权限</span
              >：针对平台k8s资源的权限进行<b>读</b>、<b>写</b>和<b>操作</b>权限。
              如需要修改k8s资源权限则同时需要拥有页面<b>写</b>权限和资源<b>写</b>权限；
            </p>
            <p>
              5.
              <span style="color: #2a7cc3">Kubectl 权限</span>：针对用户对k8s集群进行kubectl的操作进行授权；
            </p>
          </div>
        </Alert>
      </div>
      <div style="margin-top: 20px">
        <Tabs :value="authMode" type="card">
          <TabPane label="视图权限" name="viewer">
            <Table height="300" size="small" :columns="viewerTableCols" :data="viewerTableData"></Table>
          </TabPane>
          <TabPane label="K8S 资源权限" name="k8s">
            <div style="height: 300px; overflow: auto">
              <Collapse accordion simple @on-change="handleSelectK8sCluster">
                <Panel v-for="(_, cluster) in originK8sAuthData" :key="cluster" :name="cluster">
                  集群:<b style="margin-left: 20px">{{ cluster }}</b>
                  <div style="margin-top: 16px" slot="content">
                    <Table size="small" :columns="k8sTableCols" :data="k8sTableData"></Table>
                  </div>
                </Panel>
              </Collapse>
            </div>
          </TabPane>
          <TabPane label="Kubectl 代理权限" name="kubeconfig">
            <div style="height: 300px; overflow: auto">
              <Collapse accordion simple @on-change="handleSelectKubeConfigCluster">
                <Panel v-for="(cluster, idx) in originKubeconfigClusters" :key="idx" :name="cluster">
                  集群:<b style="margin-left: 20px">{{ cluster }}</b>
                  <div style="margin-top: 16px" slot="content">
                    <b>Global 作用范围</b>
                    <Table
                      style="margin-top: 16px"
                      size="small"
                      :columns="kubeconfigTableCols"
                      :data="kubeconfigGlobalTableData"
                    ></Table>
                  </div>
                  <div style="margin-top: 20px" slot="content">
                    <b>Namespace 作用范围</b>
                    <Table
                      style="margin-top: 16px"
                      size="small"
                      :columns="kubeconfigTableCols"
                      :data="kubeconfigNsTableData"
                    ></Table>
                  </div>
                </Panel>
              </Collapse>
            </div>
          </TabPane>
          <TabPane label="Kubectl WEB权限" name="kubectl">
            <div style="height: 300px; overflow: auto">
              <Collapse accordion simple @on-change="handleSelectKubectlCluster">
                <Panel v-for="(cluster, idx) in originKubectlClusters" :key="idx" :name="cluster">
                  集群:<b style="margin-left: 20px">{{ cluster }}</b>
                  <div style="margin-top: 16px" slot="content">
                    <b>Global 作用范围</b>
                    <Table
                      style="margin-top: 16px"
                      size="small"
                      :columns="kubectlTableCols"
                      :data="kubectlGlobalTableData"
                    ></Table>
                  </div>
                  <div style="margin-top: 20px" slot="content">
                    <b>Namespace 作用范围</b>
                    <Table
                      style="margin-top: 16px"
                      size="small"
                      :columns="kubectlTableCols"
                      :data="kubectlNsTableData"
                    ></Table>
                  </div>
                </Panel>
              </Collapse>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </Modal>
    <Modal v-model="openUserInfoYAML" title="查看 YAML">
      <Input v-model="currentUserInfoYAML" type="textarea" :rows="10" />
    </Modal>
  </Container>
</template>

<script>
import dayjs from 'dayjs'
import { Container, Ellipsis, LinkButton, Yaml } from '@/components'
import { color } from '@/libs/consts'
import {
  ApiSetAdmin,
  ApiSetActive,
  ApiUserList,
  ApiSetClusterAdmin,
  ApiSetUserNamespace,
  getUserInfoByUserId
} from '@/api/user'
import {
  ApiPRAAuthK8sNamespaceCreate,
  ApiPRAAuthK8sNamespaceDelete,
  ApiPRAAuthK8sNamespaceGet,
  ApiPRAuthK8sAuthList,
  ApiPRAuthK8sWithUserDelete,
  ApiPRAuthK8sWithUserGet,
  ApiPRAuthK8sWithUserUpdate,
  ApiPRAuthViewerAuthList,
  ApiPRAuthViewerWithUserDelete,
  ApiPRAuthViewerWithUserGet,
  ApiPRAuthViewerWithUserUpdate
} from '@/api/platformRsAuh'
import { errorMessage, noticeError, noticeSucceed, noticeWarn, toUnderLine, downloadFile } from '@/libs/util'
import { ApiListAllClusterNs } from '@/api/k8s/cluster'
import { ApiOpenClusterList, ApiOpenNamespaceList } from '@/api/k8s/openapi'

import {
  ApiKubectlAuthRoleWithUserList,
  ApiKubectlGlobalRoleCreate,
  ApiKubectlGlobalRoleDelete,
  ApiKubectlGlobalRoleUpdate,
  ApiKubectlNsRoleCreate,
  ApiKubectlNsRoleDelete,
  ApiKubectlNsRoleUpdate,
  ApiKubectlRoleTemplateGet,
  ApiKubectlRoleTemplateNameList
} from '@/api/k8s/kubectl'

import {
  ApiGetActivationStatus,
  ApiDownloadKubeConfig,
  ApiGetKubeConfigList,
  ApiUpdateActivationStatus,
  ApiCreateGlobalKubeConfigRole,
  ApiCreateNsKubeConfigRole,
  ApiDeleteKubeConfigRole,
  ApiUpdateKubeConfigRole,
  ApiRebuildKubeConfigRole
} from '@/api/k8s/kubeconfig'

export default {
  name: 'userManager',
  components: { Container, Yaml },
  data() {
    return {
      dateOptions: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000
        }
      },
      openUserInfoYAML: false,
      currentUserInfoYAML: '',
      profileModal: false,
      profileLoading: false,
      userInfo: {
        id: '',
        'uid (sso)': '',
        username: '',
        nickName: '',
        isActive: 0,
        isAdmin: 0,
        importTime: ''
      },
      authMode: 'viewer',
      viewerTableCols: [
        {
          title: '权限名称',
          key: 'auth'
        }
      ],
      viewerTableData: [],
      k8sTableCols: [
        {
          title: '命名空间',
          key: 'namespace',
          tooltip: true
        },
        {
          title: '资源种类',
          key: 'kind',
          tooltip: true
        },
        {
          title: '授权',
          key: 'authority'
        }
      ],
      k8sTableData: [
        {
          kind: 'Pod',
          namespace: '',
          authority: 'rw'
        }
      ],
      kubectlTableCols: [
        {
          title: '角色名称',
          key: 'role_name',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '集群',
          tooltipTheme: 'light',
          key: 'cluster_name',
          tooltip: true
        },
        {
          title: '名称空间',
          key: 'namespace',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '查看权限',
          key: 'yaml',
          render: (h, params) => {
            return h(linkButton, {
              props: {
                text: 'YAML'
              },
              style: {
                fontWeight: 600
              },
              on: {
                click: () => {
                  this.currentUserInfoYAML = ''
                  this.openUserInfoYAML = true
                  this.currentUserInfoYAML = params.row.yaml
                }
              }
            })
          }
        }
      ],
      kubectlGlobalTableData: [],
      kubectlNsTableData: [],
      originKubectlAuthData: {},
      originKubectlClusters: [],
      originK8sAuthData: [],

      kubeconfigTableCols: [
        {
          title: '角色名称',
          key: 'role_name',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '集群',
          key: 'cluster_name',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '名称空间',
          key: 'namespace',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '查看权限',
          key: 'yaml',
          render: (h, params) => {
            return h(linkButton, {
              props: {
                text: 'YAML'
              },
              style: {
                fontWeight: 600
              },
              on: {
                click: () => {
                  this.currentUserInfoYAML = ''
                  this.openUserInfoYAML = true
                  this.currentUserInfoYAML = params.row.yaml
                }
              }
            })
          }
        }
      ],
      kubeconfigGlobalTableData: [],
      kubeconfigNsTableData: [],
      originKubeconfigAuthData: {},
      originKubeconfigClusters: [],

      kubectlModal: false,
      kubectlEditModal: false,
      kubectlEditLoading: true,
      kubectlEditMode: 'global', // global | namespace
      kubectlCommitMode: 'create', // create | update
      kubectlSelectCluster: null,
      kubectlSelectNamespace: '',
      kubectlTemplateList: [],
      kubectlForm: {
        cluster_id: null,
        role_id: null,
        role_name: null,
        namespace: null,
        user_id: null,
        template_name: null,
        yaml: ''
      },
      kubectlGlobalCols: [
        {
          title: '角色名称',
          key: 'role_name',
          tooltip: true
        },
        {
          title: '创建时间',
          key: 'created_at',
          tooltip: true,
          width: 200
        },
        {
          title: '修改时间',
          key: 'updated_at',
          tooltip: true,
          width: 200
        },
        {
          title: '操作',
          key: 'ops',
          tooltip: true,
          align: 'center',
          width: 150,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    fontSize: '12px'
                  },
                  on: {
                    click: () => {
                      this.kubectlCommitMode = 'update'
                      this.kubectlEditMode = 'global'
                      this.kubectlEditModal = true
                      this.kubectlForm.yaml = params.row.yaml
                      this.$nextTick(() => {
                        this.$refs.roleYaml.refresh()
                      })
                      this.kubectlForm.role_name = params.row.role_name
                      this.kubectlForm.role_id = params.row.id
                    }
                  }
                },
                '修改'
              ),
              h(
                'a',
                {
                  style: {
                    color: color.error,
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: () => {
                      this.kubectlEditMode = 'global'
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除 ${params.row.role_name} ?</p>`,
                        loading: true,
                        onOk: async () => {
                          await this.handleKubectlRoleDelete(params.row.id)
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      kubectlGlobalData: [
        // {
        //   "role_name": "infra命名空间的tt-edit",
        //   "cluster_name": "k8s-hw-gz-cloud-02",
        //   "updated_at": "2022-12-07 11:14:56",
        // }
      ],
      kubectlNamespaceCols: [
        {
          title: '角色名称',
          key: 'role_name',
          tooltip: true
        },
        {
          title: '命名空间',
          key: 'namespace',
          tooltip: true,
          width: 200
        },
        {
          title: '修改时间',
          key: 'updated_at',
          tooltip: true,
          width: 200
        },
        {
          title: '操作',
          key: 'ops',
          tooltip: true,
          align: 'center',
          width: 150,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    fontSize: '12px'
                  },
                  on: {
                    click: () => {
                      this.kubectlCommitMode = 'update'
                      this.kubectlEditMode = 'namespace'
                      this.kubectlEditModal = true
                      this.kubectlForm.yaml = params.row.yaml
                      this.$nextTick(() => {
                        this.$refs.roleYaml.refresh()
                      })
                      this.kubectlForm.role_name = params.row.role_name
                      this.kubectlForm.role_id = params.row.id
                      this.kubectlForm.namespace = params.row.namespace
                    }
                  }
                },
                '修改'
              ),
              h(
                'a',
                {
                  style: {
                    color: color.error,
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: () => {
                      this.kubectlEditMode = 'namespace'
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除 ${params.row.role_name} ?</p>`,
                        loading: true,
                        onOk: async () => {
                          await this.handleKubectlRoleDelete(params.row.id)
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      kubectlNamespaceData: [
        // {
        //   "role_name": "infra命名空间的tt-edit",
        //   "updated_at": "2022-12-07 11:14:56",
        //   "namespace": "default",
        // }
      ],

      kubeconfigEditModal: false,
      kubeconfigEditLoading: true,
      kubeconfigEditMode: 'global', // global | namespace
      kubeconfigCommitMode: 'create', // create | update
      kubeconfigSelectCluster: null,
      kubeconfigSelectNamespace: '',
      kubeconfigTemplateList: [],
      kubeconfigForm: {
        clusterId: null,
        roleId: null,
        roleName: null,
        namespace: null,
        userId: null,
        templateName: null,
        yaml: ''
      },
      kubeconfigGlobalCols: [
        {
          title: '角色名称',
          key: 'roleName',
          tooltip: true
        },
        {
          title: '创建时间',
          key: 'createdAt',
          tooltip: true,
          width: 200
        },
        {
          title: '修改时间',
          key: 'updatedAt',
          tooltip: true,
          width: 200
        },
        {
          title: '过期时间',
          key: 'expiredAt',
          tooltip: true,
          width: 200,
          render: (h, { row }) => {
            return h('span', row.expiredAt || '-')
          }
        },
        {
          title: '操作',
          key: 'ops',
          tooltip: true,
          align: 'center',
          width: 150,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    fontSize: '12px'
                  },
                  on: {
                    click: () => {
                      this.kubeconfigCommitMode = 'update'
                      this.kubeconfigEditMode = 'global'
                      this.kubeconfigEditModal = true
                      this.kubeconfigForm.yaml = params.row.yaml
                      this.$nextTick(() => {
                        this.$refs.kubeconfigRoleYaml.refresh()
                      })
                      this.kubeconfigForm.roleName = params.row.roleName
                      this.kubeconfigForm.roleId = params.row.id
                      this.kubeconfigForm.expiredAt = params.row.expiredAt
                    }
                  }
                },
                '修改'
              ),
              h(
                'a',
                {
                  style: {
                    color: color.error,
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: () => {
                      this.kubeconfigEditMode = 'global'
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除 ${params.row.roleName} ?</p>`,
                        loading: true,
                        onOk: async () => {
                          await this.handleKubeConfigRoleDelete(params.row.id)
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      kubeconfigGlobalData: [],
      kubeconfigNamespaceCols: [
        {
          title: '角色名称',
          key: 'roleName',
          tooltip: true
        },
        {
          title: '命名空间',
          key: 'namespace',
          tooltip: true,
          width: 200
        },
        {
          title: '修改时间',
          key: 'updatedAt',
          tooltip: true,
          width: 200
        },
        {
          title: '过期时间',
          key: 'expiredAt',
          tooltip: true,
          width: 200,
          render: (h, { row }) => {
            return h('span', row.expiredAt || '-')
          }
        },
        {
          title: '操作',
          key: 'ops',
          tooltip: true,
          align: 'center',
          width: 150,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    fontSize: '12px'
                  },
                  on: {
                    click: () => {
                      this.kubeconfigCommitMode = 'update'
                      this.kubeconfigEditMode = 'namespace'
                      this.kubeconfigEditModal = true
                      this.kubeconfigForm.yaml = params.row.yaml
                      this.$nextTick(() => {
                        this.$refs.kubeconfigRoleYaml.refresh()
                      })
                      this.kubeconfigForm.roleName = params.row.roleName
                      this.kubeconfigForm.roleId = params.row.id
                      this.kubeconfigForm.namespace = params.row.namespace
                      this.kubeconfigForm.expiredAt = params.row.expiredAt
                    }
                  }
                },
                '修改'
              ),
              h(
                'a',
                {
                  style: {
                    color: color.error,
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: () => {
                      this.kubeconfigEditMode = 'namespace'
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除 ${params.row.roleName} ?</p>`,
                        loading: true,
                        onOk: async () => {
                          await this.handleKubeConfigRoleDelete(params.row.id)
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      kubeconfigNamespaceData: [],
      activationStatus: false, // 激活状态

      currentCluster: 0,
      currentNamespace: '',
      currentUserId: 0,
      clusterList: [],
      clusterNamespaceList: [],
      currentNsRead: false,
      currentNsWrite: false,
      currentNsExec: false,

      raModal: false,
      raModalTitle: '资源授权',
      svcCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      svcData: [],
      podCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      podData: [],
      deployCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      deployData: [],
      vsCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      vsData: [],
      drCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      drData: [],
      hpaCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      hpaData: [],
      configmapCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      configmapData: [],
      secretCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      secretData: [],
      gatewayCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      gatewayData: [],
      seCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      seData: [],
      sidecarCols: [
        {
          title: '权限名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        },
        {
          title: '执行',
          key: 'exec',
          slot: 'exec',
          width: 100,
          align: 'center'
        }
      ],
      sidecarData: [],
      endpointData: [],
      jobData: [],
      cronJobData: [],
      telemetryData: [],
      crdData: [],
      pvcData: [],
      statefulsetData: [],
      viewerColumns: [
        {
          title: '页面名称',
          key: 'auth_name',
          tooltip: true,
          align: 'left'
        },
        {
          title: '读',
          key: 'read',
          slot: 'read',
          width: 100,
          align: 'center'
        },
        {
          title: '写',
          key: 'write',
          slot: 'write',
          width: 100,
          align: 'center'
        }
      ],
      viewerData: [
        // {
        //   "auth_name": "后台管理-用户管理",
        //   "read": [3, true],
        //   "write": [2, false],
        // }
      ],
      search: '',
      nsModal: false,
      nsForm: {
        userId: null,
        select: []
      },
      loading: {
        table: false,
        ns: true,
        ResourcePRSwitchNs: false
      },
      columns: [
        {
          title: 'ID',
          key: 'id',
          tooltip: true,
          tooltipTheme: 'light',
          width: 80
        },
        {
          title: 'UUID',
          key: 'uuid',
          width: 100,
          render: (h, params) => h(Ellipsis, params.row.uuid ?? '-')
        },
        {
          title: 'UserName',
          key: 'username',
          render: (h, params) => {
            return h(
              'a',
              {
                style: {
                  color: color.primary,
                  cursor: 'pointer',
                  whiteSpace: 'nowrap'
                },
                on: {
                  click: () => this.getUserProfileInfo(params.row.id)
                }
              },
              [
                params.row.username ?? '-',
                params.row.is_service_account === 1 &&
                  h(
                    'span',
                    {
                      style: {
                        marginLeft: '8px',
                        color: color.error
                      }
                    },
                    '[app]'
                  )
              ]
            )
          }
        },
        {
          title: 'RealName',
          key: 'nick_name',
          render: (h, params) => h(Ellipsis, params.row.nick_name ?? '-')
        },
        {
          title: 'IsActive',
          key: 'is_active',
          slot: 'is_active',
          width: 100,
          align: 'center'
        },
        {
          title: 'IsClusterAdmin',
          key: 'is_cluster_admin',
          slot: 'is_cluster_admin',
          width: 150,
          align: 'center'
        },
        {
          title: 'IsAdmin',
          key: 'is_admin',
          slot: 'is_admin',
          width: 100,
          align: 'center'
        },
        {
          title: 'ImportTime',
          key: 'created_at',
          width: 180,
          render: (h, params) => h(Ellipsis, params.row.created_at ?? '-')
        },
        {
          title: 'Ops',
          key: 'ops',
          align: 'center',
          width: 200,
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    fontSize: '12px'
                  },
                  on: {
                    click: async () => {
                      // viewer
                      await this.getViewerAuthList()
                      this.getUserViewerAuthList(params.row.id)
                      this.raModalTitle = `资源授权 - ${params.row.username}(${params.row.nick_name})`
                      this.raModal = true
                      this.currentUserId = params.row.id

                      // k8s
                      this.getClusterList()
                      this.currentNamespace = ''
                      this.clusterNamespaceList = []
                      this.currentCluster = 0
                      let rsDataMap = {
                        svc: 'svcData',
                        pod: 'podData',
                        vs: 'vsData',
                        dr: 'drData',
                        deploy: 'deployData',
                        hpa: 'hpaData',
                        configmap: 'configmapData',
                        secret: 'secretData',
                        gateway: 'gatewayData',
                        se: 'seData',
                        sidecar: 'sidecarData',
                        endpoint: 'endpointData',
                        job: 'jobData',
                        cronJob: 'cronJobData',
                        telemetry: 'telemetryData',
                        crd: 'crdData',
                        pvc: 'pvcData',
                        statefulset: 'statefulsetData'
                      }
                      this.k8sResourceList.forEach((kind) => {
                        this[rsDataMap[kind]] = []
                      })
                    }
                  }
                },
                '资源授权'
              ),
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: async () => {
                      this.getActivationStatus(params.row.id)
                      this.kubectlModal = true
                      this.currentUserId = params.row.id
                      this.kubectlSelectCluster = null
                      this.kubectlGlobalData = []
                      this.kubectlNamespaceData = []

                      this.kubeconfigGlobalData = []
                      this.kubeconfigNamespaceData = []
                      this.kubeconfigSelectCluster = null
                      this.getClusterList()
                      this.getKubectlTemplateNameList()
                    }
                  }
                },
                'Kubectl授权'
              )
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        s_username: '',
        total: 0
      },
      allNamespaceList: [],
      viewerAuthList: [],
      userViewerAuthList: [],
      k8sResourceList: [
        'svc',
        'pod',
        'vs',
        'dr',
        'deploy',
        'hpa',
        'configmap',
        'secret',
        'gateway',
        'se',
        'sidecar',
        'endpoint',
        'job',
        'cronJob',
        'telemetry',
        'crd',
        'pvc',
        'statefulset'
      ],
      originDeployData: []
    }
  },
  methods: {
    // 选择集群后获取kubeconfig列表
    async onSelectKubeConfigCluster(cluster_id) {
      if (!cluster_id) {
        return
      }
      // 获取namespace
      this.getKubectlClusterWithNsList(cluster_id)

      const params = {
        user_id: this.currentUserId,
        cluster_id
      }
      const res = await ApiGetKubeConfigList(params)
      this.kubeconfigGlobalData = []
      this.kubeconfigNamespaceData = []
      const list = res.data.data.data
      if (list && list.length) {
        list.forEach((item) => {
          if (item.isClusterRole === 1) {
            this.kubeconfigGlobalData.push(item)
          } else {
            this.kubeconfigNamespaceData.push(item)
          }
        })
      }
    },
    // 获取用户的kubeconfig状态
    async getActivationStatus(user_id) {
      const res = await ApiGetActivationStatus(user_id)
      this.activationStatus = res.data.data.data
    },
    // 下载kubeconfig信息
    async handleKubeConfigDownload() {
      if (!this.kubeconfigSelectCluster) {
        noticeWarn(this, '需要先选择集群')
        return
      }
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>确认下载 ${this.clusterList.find((item) => item.id === this.kubeconfigSelectCluster).name} ?</p>`,
        loading: true,
        onOk: async () => {
          try {
            const payload = {
              user_id: this.currentUserId,
              cluster_id: this.kubeconfigSelectCluster
            }
            const res = await ApiDownloadKubeConfig(payload)
            let fileName = ''
            if (res.headers['content-disposition']) {
              fileName = res.headers['content-disposition'].split(';')[1].split('=')[1]
            }
            downloadFile(fileName, res.data)
          } catch (error) {
            noticeError(this, `下载失败, ${errorMessage(error)}`)
          }
          this.$Modal.remove()
        }
      })
    },
    // 变更激活状态
    async changeActivationStatus() {
      const payload = { user_id: this.currentUserId, is_active: this.activationStatus }
      try {
        await ApiUpdateActivationStatus(payload)
        noticeSucceed(this, '状态变更成功')
      } catch (error) {
        noticeError(this, `状态变更失败, ${errorMessage(error)}`)
      }
    },
    // 处理提交类型
    handleKubeConfigRoleCommit() {
      if (this.kubeconfigCommitMode === 'create') {
        this.handleKubeConfigRoleCreate()
      }

      if (this.kubeconfigCommitMode === 'update') {
        this.handleKubeConfigRoleUpdate()
      }
    },
    // 处理创建kubeconfig角色
    async handleKubeConfigRoleCreate() {
      this.kubeconfigForm.clusterId = this.kubeconfigSelectCluster
      this.kubeconfigForm.userId = this.currentUserId
      this.kubeconfigForm.expiredTs = Math.round(new Date(this.kubeconfigForm.expiredAt).getTime() / 1000)
      try {
        if (this.kubeconfigEditMode === 'global') {
          await ApiCreateGlobalKubeConfigRole(toUnderLine(this.kubeconfigForm))
        } else {
          await ApiCreateNsKubeConfigRole(toUnderLine(this.kubeconfigForm))
        }
        // 刷新列表
        this.onSelectKubeConfigCluster(this.kubeconfigSelectCluster)
        this.kubeconfigEditModal = false
        noticeSucceed(this, '创建成功')
      } catch (error) {
        noticeError(this, `创建失败, ${errorMessage(error)}`)
        this.kubeconfigEditLoading = false
        this.$nextTick(() => {
          // 这个组件库要用loading拦截modal关闭，有被奇葩到
          this.kubeconfigEditLoading = true
        })
      }
    },
    // 处理更新kubeconfig角色
    async handleKubeConfigRoleUpdate() {
      this.kubeconfigForm.clusterId = this.kubeconfigSelectCluster
      this.kubeconfigForm.userId = this.currentUserId
      this.kubeconfigForm.expiredTs = Math.round(new Date(this.kubeconfigForm.expiredAt).getTime() / 1000)
      try {
        await ApiUpdateKubeConfigRole(toUnderLine(this.kubeconfigForm))
        // 刷新列表
        this.onSelectKubeConfigCluster(this.kubeconfigSelectCluster)
        this.kubeconfigEditModal = false
        noticeSucceed(this, '更新成功')
      } catch (error) {
        noticeError(this, `更新失败, ${errorMessage(error)}`)
        this.kubeconfigEditLoading = false
        this.$nextTick(() => {
          this.kubeconfigEditLoading = true
        })
      }
    },
    // 处理删除kubeconfig角色
    async handleKubeConfigRoleDelete(role_id) {
      const payload = {
        user_id: this.currentUserId,
        cluster_id: this.kubeconfigSelectCluster,
        role_id
      }
      try {
        await ApiDeleteKubeConfigRole(payload)
        // 刷新列表
        this.onSelectKubeConfigCluster(this.kubeconfigSelectCluster)
        noticeSucceed(this, '删除成功')
      } catch (error) {
        noticeError(this, `删除失败, ${errorMessage(error)}`)
      }
    },
    // 处理创建open按钮
    handleOpenCreateKubeConfigRole() {
      if (!this.kubeconfigSelectCluster) {
        noticeWarn(this, '需要先选择集群')
        return
      }
      this.kubeconfigEditModal = true
      this.kubeconfigCommitMode = 'create'
      this.$refs.kubeconfigRoleYaml.refresh()
      this.resetKubeConfigForm()
    },
    // 重置kubeconfigForm
    resetKubeConfigForm() {
      this.kubeconfigForm = {
        clusterId: null,
        roleId: null,
        roleName: null,
        namespace: null,
        userId: null,
        templateName: null,
        yaml: '',
        expiredAt: null
      }
    },
    // 获取kubeconfig的模板
    async getKubeConfigTemplateYaml(name) {
      this.kubeconfigForm.roleName = name
      await ApiKubectlRoleTemplateGet(name).then((res) => {
        this.kubeconfigForm.yaml = res.data.data.data
      })
    },
    // 重建kubeconfig权限
    async rebuildKubeConfigRole() {
      if (!this.kubeconfigSelectCluster) {
        noticeWarn(this, '需要先选择集群')
        return
      }

      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>确认重建 ${this.clusterList.find((item) => item.id === this.kubeconfigSelectCluster).name} ?</p>`,
        loading: true,
        onOk: async () => {
          try {
            const payload = {
              user_id: this.currentUserId,
              cluster_id: this.kubeconfigSelectCluster
            }
            await ApiRebuildKubeConfigRole(payload)
            // 刷新列表
            this.onSelectKubeConfigCluster(this.kubeconfigSelectCluster)
            noticeSucceed(this, '重建成功')
          } catch (error) {
            noticeError(this, `重建失败, ${errorMessage(error)}`)
          }
          this.$Modal.remove()
        }
      })
    },
    // 用户信息中kubeconfig选项卡下选中折叠项时触发
    handleSelectKubeConfigCluster(clusters) {
      if (!clusters.length) return
      this.kubeconfigGlobalTableData = []
      this.kubeconfigNsTableData = []
      this.originKubeconfigAuthData.cluster.forEach((item) => {
        if (item.clusterName === clusters[0]) {
          this.kubeconfigGlobalTableData.push({
            role_name: item.roleName,
            cluster_name: item.clusterName,
            namespace: item.namespace,
            yaml: item.yaml
          })
        }
      })
      this.originKubeconfigAuthData.namespace.forEach((item) => {
        if (item.clusterName === clusters[0]) {
          this.kubeconfigNsTableData.push({
            role_name: item.roleName,
            cluster_name: item.clusterName,
            namespace: item.namespace,
            yaml: item.yaml
          })
        }
      })
    },

    async getUserProfileInfo(uid) {
      this.profileModal = true
      this.profileLoading = true
      debugger
      await getUserInfoByUserId(uid)
        .then((res) => {
          let p = res.data.data.profile
          this.userInfo = {
            id: p.id,
            'uid (sso)': p.uid,
            username: p.username,
            nickName: p.nickName,
            isActive: p.isActive,
            isAdmin: p.isAdmin,
            isClusterAdmin: p.isClusterAdmin,
            importTime: p.createdAt
          }
          this.originK8sAuthData = res.data.data.k8s_auth
          this.originKubectlAuthData = res.data.data.kubectl_auth
          this.originKubeconfigAuthData = res.data.data.kubeconfig_auth
          // 合并后去重拿到不重复的clusterName
          this.originKubeconfigClusters = [
            ...this.originKubeconfigAuthData.cluster,
            ...this.originKubeconfigAuthData.namespace
          ]
            .filter((item, index, arr) => arr.findIndex((i) => i.clusterName === item.clusterName) === index)
            .map((item) => item.clusterName)

          this.originKubectlClusters = []
          this.originKubectlAuthData.cluster.forEach((item) => {
            if (this.originKubectlClusters.indexOf(item.clusterName) === -1) {
              this.originKubectlClusters.push(item.clusterName)
            }
          })
          this.originKubectlAuthData.namespace.forEach((item) => {
            if (this.originKubectlClusters.indexOf(item.clusterName) === -1) {
              this.originKubectlClusters.push(item.clusterName)
            }
          })

          let viewAuth = res.data.data.view_auth

          this.viewerTableData = viewAuth.map((item) => {
            return {
              auth: item
            }
          })
        })
        .catch((err) => {
          throw err
        })
        .finally(() => {
          this.profileLoading = false
        })
    },
    handleSelectK8sCluster(clusters) {
      if (clusters.length === 0) {
        return
      }
      this.k8sTableData = []
      for (let cluster in this.originK8sAuthData) {
        if (clusters[0] === cluster) {
          let auths = this.originK8sAuthData[cluster]
          for (let ns in auths) {
            let auth = auths[ns]
            for (let kind in auth) {
              this.k8sTableData.push({
                namespace: ns,
                kind: kind,
                authority: auth[kind]
              })
            }
          }
        }
      }
    },
    handleSelectKubectlCluster(clusters) {
      if (clusters.length === 0) {
        return
      }

      this.kubectlGlobalTableData = []
      this.kubectlNsTableData = []
      this.originKubectlAuthData.cluster.forEach((item) => {
        if (item.clusterName === clusters[0]) {
          this.kubectlGlobalTableData.push({
            role_name: item.roleName,
            cluster_name: item.clusterName,
            namespace: item.namespace,
            yaml: item.yaml
          })
        }
      })
      this.originKubectlAuthData.namespace.forEach((item) => {
        if (item.clusterName === clusters[0]) {
          this.kubectlNsTableData.push({
            role_name: item.roleName,
            cluster_name: item.clusterName,
            namespace: item.namespace,
            yaml: item.yaml
          })
        }
      })
    },
    handleOpenCreateKubectlRole() {
      if (this.kubectlSelectCluster === null || this.kubectlSelectCluster === undefined) {
        noticeWarn(this, '需要先选择集群')
        return
      }
      this.kubectlEditModal = true
      this.kubectlCommitMode = 'create'
      this.$refs.roleYaml.refresh()
      this.resetKubectlForm()
    },
    resetKubectlForm() {
      this.kubectlForm = {
        cluster_id: null,
        role_name: null,
        namespace: null,
        user_id: null,
        template_name: null,
        yaml: ''
      }
    },
    async getKubectlClusterWithNsList(cluster_id) {
      console.log('cluster-id:', cluster_id)
      if (cluster_id === undefined) {
        return
      }
      await ApiOpenNamespaceList(cluster_id).then((res) => {
        this.clusterNamespaceList = res.data.data.data
      })
    },
    async getKubectlTemplateNameList() {
      await ApiKubectlRoleTemplateNameList().then((res) => {
        this.kubectlTemplateList = res.data.data.data
        this.kubeconfigTemplateList = res.data.data.data
      })
    },
    async getKubectlTemplateYaml(name) {
      this.kubectlForm.role_name = name
      await ApiKubectlRoleTemplateGet(name).then((res) => {
        this.kubectlForm.yaml = res.data.data.data
      })
    },
    async onSelectKubectlCluster(cluster_id) {
      if (cluster_id === undefined) {
        return
      }
      // 获取namespace
      this.getKubectlClusterWithNsList(cluster_id)

      let params = {
        f_cluster_id: cluster_id,
        filterType: 'and',
        f_user_id: this.currentUserId
      }
      await ApiKubectlAuthRoleWithUserList(params).then((res) => {
        this.kubectlGlobalData = []
        this.kubectlNamespaceData = []
        const list = res.data.data.list
        if (list && list.length) {
          list.forEach((item) => {
            if (item.is_cluster_role === 1) {
              this.kubectlGlobalData.push(item)
            } else {
              this.kubectlNamespaceData.push(item)
            }
          })
        }
      })
    },
    handleKubectlRoleCommit() {
      if (this.kubectlCommitMode === 'create') {
        this.handleKubectlRoleCreate()
      }

      if (this.kubectlCommitMode === 'update') {
        this.handleKubectlRoleUpdate()
      }
    },
    async handleKubectlRoleDelete(role_id) {
      let cluster_id = this.kubectlSelectCluster
      if (this.kubectlEditMode === 'global') {
        await ApiKubectlGlobalRoleDelete(cluster_id, this.currentUserId, role_id)
          .then((res) => {
            this.onSelectKubectlCluster(cluster_id)
            noticeSucceed(this, '删除成功')
          })
          .catch((err) => {
            noticeError(this, '删除失败')
            noticeError(this, errorMessage(err))
          })
      } else {
        await ApiKubectlNsRoleDelete(cluster_id, this.currentUserId, role_id)
          .then((res) => {
            this.onSelectKubectlCluster(cluster_id)
            noticeSucceed(this, '删除成功')
          })
          .catch((err) => {
            noticeError(this, '删除失败')
            noticeError(this, errorMessage(err))
          })
      }
    },
    async handleKubectlRoleCreate() {
      this.kubectlForm.cluster_id = this.kubectlSelectCluster
      this.kubectlForm.user_id = this.currentUserId
      if (this.kubectlEditMode === 'global') {
        await ApiKubectlGlobalRoleCreate(this.kubectlForm)
          .then((res) => {
            this.onSelectKubectlCluster(this.kubectlSelectCluster)
            this.kubectlEditModal = false
            noticeSucceed(this, '创建成功')
          })
          .catch((err) => {
            noticeError(this, `创建失败, ${errorMessage(err)}`)
          })
          .finally(() => {
            this.kubectlEditLoading = false
          })
      } else {
        await ApiKubectlNsRoleCreate(this.kubectlForm)
          .then((res) => {
            this.onSelectKubectlCluster(this.kubectlSelectCluster)
            this.kubectlEditModal = false
            noticeSucceed(this, '创建成功')
          })
          .catch((err) => {
            noticeError(this, `创建失败, ${errorMessage(err)}`)
          })
          .finally(() => {
            this.kubectlEditLoading = false
          })
      }
    },
    async handleKubectlRoleUpdate() {
      this.kubectlForm.cluster_id = this.kubectlSelectCluster
      this.kubectlForm.user_id = this.currentUserId
      if (this.kubectlEditMode === 'global') {
        await ApiKubectlGlobalRoleUpdate(this.kubectlForm)
          .then((res) => {
            this.onSelectKubectlCluster(this.kubectlSelectCluster)
            this.kubectlEditModal = false
            noticeSucceed(this, '更新成功')
          })
          .catch((err) => {
            noticeError(this, `更新成功, ${errorMessage(err)}`)
          })
          .finally(() => {
            this.kubectlEditLoading = false
          })
      } else {
        await ApiKubectlNsRoleUpdate(this.kubectlForm)
          .then((res) => {
            this.onSelectKubectlCluster(this.kubectlSelectCluster)
            this.kubectlEditModal = false
            noticeSucceed(this, '更新成功')
          })
          .catch((err) => {
            noticeError(this, `更新失败, ${errorMessage(err)}`)
          })
          .finally(() => {
            this.kubectlEditLoading = false
          })
      }
    },
    async getNamespaceAuths() {
      this.currentNsExec = false
      this.currentNsRead = false
      this.currentNsWrite = false
      if (this.currentCluster === 0 || this.currentNamespace === '') {
        return
      }

      if (this.currentUserId === undefined || this.currentCluster === undefined || this.currentNamespace === '') {
        return
      }

      this.loading.ResourcePRSwitchNs = true
      await ApiPRAAuthK8sNamespaceGet(this.currentUserId, this.currentCluster, this.currentNamespace)
        .then((res) => {
          let data = res.data.data
          this.currentNsRead = data.r
          this.currentNsWrite = data.w
          this.currentNsExec = data.x
        })
        .finally(() => {
          this.loading.ResourcePRSwitchNs = false
        })
    },
    async createNamespaceAuth(auth) {
      if (this.currentCluster === undefined && this.currentNamespace) {
        noticeError(this, '无法提交，集群或命名空间选项不能为空.')
        return
      }
      await ApiPRAAuthK8sNamespaceCreate(this.currentUserId, this.currentCluster, this.currentNamespace, auth)
        .then((res) => {
          noticeSucceed(this, 'Update succeed.')
          this.getK8sAuthList(this.currentCluster, this.currentNamespace)
        })
        .catch((err) => {
          noticeError(this, `Update error. err: ${errorMessage(err)}`)
        })
    },
    async deleteNamespaceAuth(auth) {
      await ApiPRAAuthK8sNamespaceDelete(this.currentUserId, this.currentCluster, this.currentNamespace, auth)
        .then((res) => {
          noticeSucceed(this, 'Update succeed.')
          this.getK8sAuthList(this.currentCluster, this.currentNamespace)
        })
        .catch((err) => {
          noticeError(this, `Update error. err: ${errorMessage(err)}`)
        })
    },
    searchDeployment(deploySearch) {
      this.deployData = []
      this.originDeployData.forEach((item) => {
        if (item.auth_name.indexOf(deploySearch) !== -1) {
          this.deployData.push(item)
        }
      })
    },
    async getClusterList() {
      await ApiOpenClusterList(1).then((res) => {
        this.clusterList = res.data.data.data
      })
    },
    async getClusterWithNsList(cluster_id) {
      console.log('cluster:', cluster_id)
      if (cluster_id === undefined) {
        return
      }
      await ApiOpenNamespaceList(cluster_id).then((res) => {
        this.clusterNamespaceList = res.data.data.data
        this.clusterNamespaceList.unshift({
          metadata: { name: 'ALL-NAMESPACE' }
        })
      })
    },
    async getK8sAuthList(cluster_id, namespace) {
      let rsDataMap = {
        svc: 'svcData',
        pod: 'podData',
        vs: 'vsData',
        dr: 'drData',
        deploy: 'deployData',
        hpa: 'hpaData',
        configmap: 'configmapData',
        secret: 'secretData',
        gateway: 'gatewayData',
        se: 'seData',
        sidecar: 'sidecarData',
        endpoint: 'endpointData',
        job: 'jobData',
        cronJob: 'cronJobData',
        telemetry: 'telemetryData',
        crd: 'crdData',
        pvc: 'pvcData',
        statefulset: 'statefulsetData'
      }
      this.k8sResourceList.forEach((kind) => {
        this[rsDataMap[kind]] = []
        if (cluster_id === undefined || namespace === undefined) {
          return
        }
        ApiPRAuthK8sAuthList(cluster_id, namespace, kind).then((res) => {
          let authList = this.parseK8sAuthData(res.data.data.data)
          // console.log("svc auth list")
          // console.log(authList)

          ApiPRAuthK8sWithUserGet(this.currentUserId, cluster_id, namespace, kind).then((userRes) => {
            let userAuthList = userRes.data.data.data
            console.log(userAuthList)
            if (userAuthList !== null) {
              let userMap = {}
              userAuthList.forEach((item) => {
                if (userMap[item.desc] === undefined) {
                  userMap[item.desc] = {}
                }
                if (item.authority === 'r') {
                  userMap[item.desc]['read'] = true
                }

                if (item.authority === 'w') {
                  userMap[item.desc]['write'] = true
                }
                if (item.authority === 'x') {
                  userMap[item.desc]['exec'] = true
                }
              })

              this[rsDataMap[kind]] = authList.map((item) => {
                if (userMap[item.auth_name] !== undefined) {
                  if (userMap[item.auth_name].read !== undefined) {
                    item.read[1] = userMap[item.auth_name].read
                  }
                  if (userMap[item.auth_name].write !== undefined) {
                    item.write[1] = userMap[item.auth_name].write
                  }
                  if (userMap[item.auth_name].exec !== undefined) {
                    item.exec[1] = userMap[item.auth_name].exec
                  }
                }
                return item
              })
            } else {
              this[rsDataMap[kind]] = authList
            }

            if (kind === 'deploy') {
              this.originDeployData = authList
            }
            console.log(kind)
            console.log(this[rsDataMap[kind]])
          })
        })
      })
    },
    async createUserK8sAuth(authId) {
      ApiPRAuthK8sWithUserUpdate(this.currentUserId, authId)
        .then((res) => {
          noticeSucceed(this, 'Update succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async deleteUserK8sAuth(authId) {
      ApiPRAuthK8sWithUserDelete(this.currentUserId, authId)
        .then((res) => {
          noticeSucceed(this, 'Delete succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    parseK8sAuthData(data) {
      let tmpMap = {}
      let res = []
      data.forEach((item) => {
        if (tmpMap[item.desc] === undefined) {
          tmpMap[item.desc] = {}
        }
        if (item.authority === 'r') {
          tmpMap[item.desc]['read'] = [item.id, false]
        }
        if (item.authority === 'w') {
          tmpMap[item.desc]['write'] = [item.id, false]
        }
        if (item.authority === 'x') {
          tmpMap[item.desc]['exec'] = [item.id, false]
        }
      })
      for (let name in tmpMap) {
        let vM = tmpMap[name]
        res.push({
          auth_name: name,
          read: vM.read,
          write: vM.write,
          exec: vM.exec
        })
      }
      return res
    },

    // return:
    // [{
    //   "auth_name": "后台管理-用户管理",
    //   "read": [3, true],
    //   "write": [2, false],
    // }]
    parseViewerAuthData(data) {
      let tmpMap = {}
      let res = []
      console.log(data)
      data.forEach((item) => {
        if (tmpMap[item.desc] === undefined) {
          tmpMap[item.desc] = {}
        }
        if (item.authority === 'r') {
          tmpMap[item.desc]['read'] = [item.id, false]
        }
        if (item.authority === 'w') {
          tmpMap[item.desc]['write'] = [item.id, false]
        }
      })
      for (let name in tmpMap) {
        let vM = tmpMap[name]
        res.push({
          auth_name: name,
          read: vM.read,
          write: vM.write
        })
      }
      return res
    },
    async getViewerAuthList() {
      await ApiPRAuthViewerAuthList()
        .then((res) => {
          this.viewerAuthList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async getUserViewerAuthList(uid) {
      await ApiPRAuthViewerWithUserGet(uid).then((res) => {
        this.userViewerAuthList = res.data.data.data
        let tmpData = this.parseViewerAuthData(this.viewerAuthList)
        this.viewerData = []
        if (this.userViewerAuthList !== null) {
          let userMap = {}
          this.userViewerAuthList.forEach((item) => {
            if (userMap[item.desc] === undefined) {
              userMap[item.desc] = {}
            }
            if (item.authority === 'r') {
              userMap[item.desc]['read'] = true
            }

            if (item.authority === 'w') {
              userMap[item.desc]['write'] = true
            }
          })

          this.viewerData = tmpData.map((item) => {
            if (userMap[item.auth_name] !== undefined) {
              if (userMap[item.auth_name].read !== undefined) {
                item.read[1] = userMap[item.auth_name].read
              }
              if (userMap[item.auth_name].write !== undefined) {
                item.write[1] = userMap[item.auth_name].write
              }
            }
            return item
          })
        } else {
          this.viewerData = tmpData
        }
        console.log(this.viewerData)
      })
    },
    async createUserViewerAuth(uid, authId) {
      await ApiPRAuthViewerWithUserUpdate(uid, authId)
        .then((res) => {
          noticeSucceed(this, 'Update succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async deleteUserViewerAuth(uid, authId) {
      await ApiPRAuthViewerWithUserDelete(uid, authId)
        .then((res) => {
          noticeSucceed(this, 'Delete succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async handleNs() {
      let userId = this.nsForm.userId
      let namespaces = JSON.stringify(this.nsForm.select)
      await ApiSetUserNamespace({ userId, namespaces })
        .then((res) => {
          noticeSucceed(this, 'succeed')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
      this.reloadTable()
      this.loading.ns = false
      this.nsModal = false
    },
    async getNsList() {
      await ApiListAllClusterNs()
        .then((res) => {
          this.allNamespaceList = res.data.data.namespaces
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async reloadTable() {
      this.loading.table = true
      await ApiUserList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
      this.loading.table = false
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    rejectModifyAdmin(row) {
      if (row.username === 'admin') {
        noticeWarn(this, 'Action not permit.')
        return false
      }
      return true
    },
    setActive(row, isActive) {
      if (this.rejectModifyAdmin(row)) {
        ApiSetActive(row.id, isActive)
          .then((res) => {
            noticeSucceed(this, 'succeed')
          })
          .catch((err) => {
            noticeError(this, errorMessage(err))
            throw err
          })
      }
    },
    setClusterAdmin(row, isClusterAdmin) {
      if (this.rejectModifyAdmin(row)) {
        ApiSetClusterAdmin(row.id, isClusterAdmin)
          .then((res) => {
            noticeSucceed(this, 'succeed')
          })
          .catch((err) => {
            noticeError(this, errorMessage(err))
            throw err
          })
      }
    },
    setAdmin(row, isAdmin) {
      if (this.rejectModifyAdmin(row)) {
        ApiSetAdmin(row.id, isAdmin)
          .then((res) => {
            noticeSucceed(this, 'succeed')
          })
          .catch((err) => {
            noticeError(this, errorMessage(err))
            throw err
          })
      }
    }
  },
  mounted() {
    this.reloadTable()
    // this.getNsList()
    this.$refs.input.focus({
      cursor: 'start'
    })
  }
}
</script>

<style lang="less">
.info-modal .ivu-modal-body {
  height: 60vh;
  overflow: auto;
}
.ra-collapse-wrapper {
  height: 320px;
  overflow: auto;
}
</style>
