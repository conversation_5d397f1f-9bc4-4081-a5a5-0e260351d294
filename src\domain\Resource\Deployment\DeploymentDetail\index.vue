<template>
  <div class="wrapper">
    <Space class="header">
      <Button size="small" type="text" icon="ios-arrow-back" @click="gotoAppDeployment">
        <b>返回</b>
      </Button>
      <Divider type="vertical" />
      <b>Deployment</b>
      <b
        >[ {{ cluster }} / {{ namespace }} / <b style="color: #2a7cc3"> {{ deployment }} </b> ]</b
      >
      <Button size="small" type="primary" ghost icon="ios-paper-outline" @click="handleOpenYAML">YAML</Button>
      <Button
        @click="onSubscribeOrNot"
        size="small"
        type="primary"
        ghost
        :icon="isSubscribe ? 'ios-megaphone' : 'ios-megaphone-outline'"
        >{{ isSubscribe ? '退订' : '订阅' }}</Button
      >
      <Button @click="onOpenReplicaCountModal" size="small" ghost type="primary" icon="ios-settings"
        >调整副本（当前：{{ replicaCount }}）</Button
      >
      <Button @click="rolloutRestart" size="small" type="error" ghost icon="ios-play">滚动重启</Button>

      <Poptip popper-class="more-options-popper">
        <div slot="content" class="more-options">
          <div @click.stop="() => (logMergeModalVisible = true)">日志合并</div>
        </div>

        <Button size="small" type="primary" ghost>
          更多选项
          <Icon type="ios-arrow-down" />
        </Button>
      </Poptip>

      <Button
        @click="() => (this.loading = !this.loading)"
        type="warning"
        size="small"
        shape="circle"
        ghost
        icon="md-refresh"
        style="line-height: 24px"
      ></Button>

      <Button size="small" type="primary" ghost style="margin-left: auto">
        <common-icon type="_conversation1" />
        <a
          style="margin-left: 4px"
          target="_blank"
          href="https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=1eas73cf-b77c-43ec-95d1-926c1ed492c8"
        >
          联系我们
        </a>
      </Button>
    </Space>
    <div class="content">
      <ResourceSummary
        v-if="clusterID"
        type="deployment"
        :onResourceCardClick="(key) => (chosenTab = key)"
        :clusterId="clusterID"
        :namespace="namespace"
        :name="deployment"
      />
      <div>
        <Tabs type="card" :value="chosenTab" @on-click="handleRefresh" name="deployment" :animated="false">
          <TabPane label="元数据" name="meta">
            <WorkloadMeta v-if="chosenTab === 'meta'" :reloadFlag="loading"></WorkloadMeta>
          </TabPane>
          <TabPane label="运行时 (Pod)" name="runtime">
            <k8s-deployment-runtime
              v-if="chosenTab === 'runtime'"
              :clusterID="clusterID"
              :cluster="cluster"
              :namespace="namespace"
              :deployment="deployment"
              :loading="loading"
            ></k8s-deployment-runtime>
          </TabPane>
          <TabPane label="Service" name="service">
            <Card>
              <h4 style="margin-bottom: 16px">Service</h4>
              <Service type="AssociatedDeployment" v-if="chosenTab === 'service'" />
            </Card>
          </TabPane>
          <TabPane label="HPA" name="hpa">
            <k8s-deployment-hpa :loading="loading" v-if="chosenTab === 'hpa'"></k8s-deployment-hpa>
          </TabPane>

          <TabPane label="Configmap" name="configmap">
            <Card>
              <h4 style="margin-bottom: 16px">ConfigMap</h4>
              <Configmap type="AssociatedDeployment" v-if="chosenTab === 'configmap'" />
            </Card>
          </TabPane>
          <TabPane label="Secret" name="secret">
            <Card>
              <h4 style="margin-bottom: 16px">Secrets（秘文管理）</h4>
              <Secrets type="AssociatedDeployment" v-if="chosenTab === 'secret'" />
            </Card>
          </TabPane>
          <TabPane label="PVC" name="pvc">
            <Card>
              <h4 style="margin-bottom: 16px">PVC（持久卷声明）</h4>
              <PVC type="AssociatedDeployment" v-if="chosenTab === 'pvc'" />
            </Card>
          </TabPane>
          <TabPane label="服务网格" name="servicemesh">
            <k8s-deployment-service-mesh :key="loading" v-if="chosenTab === 'servicemesh'" />
          </TabPane>
          <TabPane label="监控图" name="monitor">
            <WorkloadPodContainerChart kind="Deployment" :reloadFlag="true" :entity="containerChartEntity" />
          </TabPane>
        </Tabs>
      </div>
      <Drawer title="查看 YAML" :closable="false" width="60" v-model="openYAML">
        <yaml v-model="currentDeployYAML" :forbiddenEdit="true"></yaml>
      </Drawer>
      <Modal title="调整副本数" width="15" v-model="replicaCountModal">
        从 <InputNumber v-model="replicaCount" readonly /> 调整至
        <InputNumber :max="1000" :min="0" v-model="tempReplicaCount" />
        <template #footer>
          <Button @click="() => (replicaCountModal = false)">取消</Button>
          <Button type="primary" @click="onSetReplicaCount">确认</Button>
        </template>
      </Modal>

      <LogMergeModal
        v-if="clusterID"
        v-model="logMergeModalVisible"
        :cluster="cluster"
        :clusterId="clusterID"
        :namespace="namespace"
        :workloadName="deployment"
        workloadKind="Deployment"
      />
    </div>
  </div>
</template>

<script>
import { nextTick } from 'vue'

import Config from '@/config'
import { Yaml, Space, CommonIcon } from '@/components'
import { useGet, usePost } from '@/libs/service.request'
import {
  WorkloadPodContainerChart,
  ResourceSummary,
  WorkloadMeta,
  Service,
  Secrets,
  PVC,
  Configmap
} from '@/domain/Resource'
import LogMergeModal from '@/domain/Resource/LogMergeModal/index.vue'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'

import { ApiResourceGetDeployment } from '@/api/k8s/authResource'
import { ApiV2DeploymentRestart } from '@/api/k8s/namespace/app/app'
import { ApiDeploymentBriefStatisticsList } from '@/api/k8s/namespace/worload/deployment'

import K8sDeploymentRuntime from './runtime'
import K8sDeploymentHpa from './hpa/index.vue'
import K8sDeploymentServiceMesh from './service-mesh.vue'

export default {
  name: 'k8s-deployment-index',
  components: {
    K8sDeploymentHpa,
    K8sDeploymentRuntime,
    WorkloadMeta,
    Yaml,
    K8sDeploymentServiceMesh,
    Space,
    CommonIcon,
    WorkloadPodContainerChart,
    ResourceSummary,
    Service,
    Secrets,
    PVC,
    Configmap,
    LogMergeModal
  },
  data() {
    return {
      openYAML: false,
      currentDeployYAML: '',
      cluster: null,
      namespace: null,
      deployment: null,
      clusterID: null,
      loading: false,
      chosenTab: 'runtime',
      briefCount: {
        service: 0,
        hpa: 0,
        pvc: 0,
        configmap: 0,
        secret: 0,
        dr: 0,
        vs: 0,
        pod: 0,
        gw: 0
      },
      isSubscribe: false,
      containerChartEntity: {},
      replicaCountModal: false,
      tempReplicaCount: 0,
      replicaCount: 0,
      intervalID: undefined,
      logMergeModalVisible: false
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  methods: {
    async rolloutRestart() {
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>确认重建 ${this.deployment} ?</p>`,
        loading: true,
        onOk: async () => {
          await ApiV2DeploymentRestart({
            clusterId: this.clusterID,
            namespace: this.namespace,
            name: this.deployment
          })
            .then((res) => {
              noticeSucceed(this, 'succeed.')
              this.loading = !this.loading
            })
            .catch((err) => {
              noticeError(this, `重启失败, ${errorMessage(err)}`)
            })
          this.$Modal.remove()
        }
      })
    },
    gotoAppDeployment() {
      // 返回时，填充C/N
      this.setClusterNamespace(this.cluster, this.clusterID, this.namespace)
      this.$router.push({ name: 'deployment-list' })
    },
    setHeader() {
      var title = document.getElementsByTagName('title')
      title[0].innerHTML = `Deployment ${this.deployment}`
    },
    setClusterNamespace(cluster, clusterId, namespace) {
      this.$store.commit('setCurrentCluster', { uid: this.$store.state.user.userId, cluster })
      this.$store.commit('setCurrentClusterId', { uid: this.$store.state.user.userId, clusterId })
      this.$store.commit('setCurrentNamespace', { uid: this.$store.state.user.userId, namespace })
    },
    handleRefresh(tabName) {
      if (tabName === undefined) {
        this.chosenTab = 'runtime'
        return
      }
      this.chosenTab = tabName
      if (tabName === 'monitor') {
        debugger
        this.containerChartEntity = {
          clusterId: this.clusterID,
          cluster: this.cluster,
          namespace: this.namespace,
          name: this.deployment
        }
      }
      // this.loading = !this.loading
    },
    handleOpenYAML() {
      let query = this.$route.query
      this.fetchDeploymentYAML(query.clusterId, query.namespace, query.deployment)
      this.openYAML = true
    },
    fetchDeploymentYAML(cluster_id, namespace, deployment) {
      ApiResourceGetDeployment(cluster_id, namespace, deployment)
        .then((res) => {
          this.currentDeployYAML = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    fetchBriefCount() {
      ApiDeploymentBriefStatisticsList({
        clusterId: this.clusterID,
        namespace: this.namespace,
        workloadName: this.deployment
      })
        .then((res) => {
          this.briefCount = res.data.data
        })
        .catch((err) => {
          throw err
        })
    },
    async getSubscribeStatus() {
      const res = await useGet(`${Config.Api.Base}${Config.Api.GetDeploymentSubscribeStatus}`, {
        params: {
          clusterId: this.clusterID,
          namespace: this.namespace,
          name: this.deployment
        }
      })
      if (res.success) {
        this.isSubscribe = !!res.data.data
      }
    },
    async onSubscribeOrNot() {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认${this.isSubscribe ? '退订' : '订阅'} ${this.deployment}？`,
        loading: true,
        onOk: async () => {
          const res = await usePost(
            `${Config.Api.Base}${this.isSubscribe ? Config.Api.UnsubscribeDeployment : Config.Api.SubscribeDeployment}`,
            {
              clusterId: this.clusterID,
              namespace: this.namespace,
              name: this.deployment
            }
          )
          if (res.success) {
            this.$Message.success(`${this.isSubscribe ? '退订' : '订阅'}成功！`)
            this.getSubscribeStatus()
            this.$Modal.remove()
          } else {
            this.$Modal.remove()
          }
        }
      })
    },
    async getReplicaCount() {
      const res = await useGet(`${Config.Api.Base}${Config.Api.GetDeploymentReplicasCount}`, {
        params: { clusterId: this.clusterID, namespace: this.namespace, name: this.deployment }
      })
      if (res.success) {
        this.replicaCount = res.data.data
      }
    },
    onSetReplicaCount() {
      if (this.tempReplicaCount === undefined) {
        this.$Message.warning('请先输入副本数')
        return
      }
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认调整副本数？`,
        loading: true,
        onOk: async () => {
          const res = await usePost(`${Config.Api.Base}${Config.Api.SetDeploymentReplicasCount}`, {
            clusterId: this.clusterID,
            namespace: this.namespace,
            name: this.deployment,
            count: this.tempReplicaCount
          })
          if (res.success) {
            setTimeout(async () => {
              this.getReplicaCount()
              this.$Message.success('修改副本数成功！')
              this.replicaCountModal = false
              this.tempReplicaCount = 0
              this.$Modal.remove()
              // 强行刷新runtime
              this.chosenTab = undefined
              await nextTick()
              this.chosenTab = 'runtime'
            }, 3000)
          }
        }
      })
    },
    onOpenReplicaCountModal() {
      this.replicaCountModal = true
      this.tempReplicaCount = this.replicaCount
    }
  },
  mounted() {
    let query = this.$route.query
    this.clusterID = query.clusterId
    this.namespace = query.namespace
    this.deployment = query.deployment
    this.cluster = query.cluster

    // CICD跳转后，判断C/N是否存在，若不存在则保存到store
    if (!(this.currentCluster && this.currentClusterId && this.currentNamespace)) {
      setClusterNamespace(query.cluster, query.clusterId, query.namespace)
    }

    this.setHeader()
    this.fetchBriefCount()
    this.getSubscribeStatus()
    this.getReplicaCount()
    // 定时获取副本数
    this.intervalID = setInterval(() => {
      this.getReplicaCount()
    }, 30000)
  },
  unmounted() {
    clearInterval(this.intervalID)
  }
}
</script>

<style lang="less" scope>
.wrapper {
  .header {
    padding: 16px;
    display: inline-flex;
    align-items: center;
    border-bottom: 1px solid #e9e9e9;
  }
  .content {
    height: ~'calc(100vh - 57px)';
    padding: 0 16px;
    overflow: auto;
  }

  .more-options-popper {
    min-width: auto;
    .ivu-poptip-body {
      padding: 4px 8px;
    }
  }

  .more-options {
    width: 120px;

    div {
      cursor: pointer;
      padding: 8px 16px;
      &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }
  }
}
.space {
  > :not(:last-child) {
    margin-bottom: 8px;
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
</style>
