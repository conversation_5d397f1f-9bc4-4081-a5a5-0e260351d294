import { ref } from 'vue'

import sha1 from './sha1'
import { useGet } from '@/libs/service.request'
import Config from '@/config'

interface Error {
  code: string
  msg: string
}
/** 飞书云文档组件 */
export const useFeishuDocsService = (appId, url, callback) => {
  const docsRef = ref()
  const jsApiTicket = ref()

  const getJSApiTicket = async () => {
    const res = await useGet<{ ticket: string }>(Config.Api.Base + '/api/v1/feishu/jsapi-ticket/get')

    if (res.success) {
      jsApiTicket.value = res.data.ticket
    }
  }

  const generateSignature = () => {
    const nonceStr = Math.random().toString(36).slice(2)
    const timestamp = new Date().valueOf()
    const signatureString = `jsapi_ticket=${
      jsApiTicket.value
    }&noncestr=${nonceStr}&timestamp=${new Date().valueOf()}&url=${url}`

    return {
      nonceStr,
      timestamp,
      signature: sha1(signatureString)
    }
  }

  const initWebComponent = async () => {
    await getJSApiTicket()
    const { nonceStr, signature, timestamp } = generateSignature()

    ;(window as any).webComponent
      .config({
        // openId, // 当前登录用户的open id，要确保与生成 signature 使用的 user_access_token 相对应，使用 app_access_token 时此项不填。
        signature, // 签名
        appId, // 应用 appId
        timestamp, // 时间戳（毫秒）
        nonceStr, // 随机字符串
        url, // 参与签名加密计算的url
        jsApiList: ['DocsComponent'], // 指定要使用的组件，请根据对应组件的开发文档填写。如云文档组件，填写['DocsComponent']
        lang: 'zh-CN' // 指定组件的国际化语言
      })
      .then((res) => {
        // 可以在这里进行组件动态渲染
        const myComponent = (window as any).webComponent.render(
          'DocsComponent',
          {
            //组件参数
            src: url,
            minHeight: '80vh',
            width: '100%'
          },
          document.querySelector('#version-doc') // 将组件挂在到哪个元素上
        )
        // 通过setFeatureConfig方法修改组件的配置属性
        myComponent.config.setFeatureConfig({
          // 详细参数请参考 FEATURE & Options
          HEADER: {
            enable: false // 隐藏头部
          },
          LIKE: { enable: false },
          SHARE: { enable: false },
          CONTENT: {
            readonly: true,
            unscrollable: true,
            padding: [0, 0, 0, 0],
            maxWidth: 650
          },
          MODAL: { outerMask: true },
          DIRECTORY: { enable: false },
          COMMENT: {
            global: { enable: false },
            partial: { enable: false }
          },
          SIDEBAR: { borderSide: [false, false, false, false] },
          MORE_MENU: { enable: false },
          COLLABORATOR: { enable: false },
          FULLSCREEN: { enable: false },
          FOOTER: { enable: false }
        })
        // 捕获 sdk 鉴权错误
        ;(window as any).webComponent.onAuthError(function (error: Error) {
          console.error('feishu docs auth error callback', error)
        })
        // 捕获组件内部错误
        myComponent.onError(function (error: Error) {
          console.error('feishu docs error:', error)
        })
        myComponent.event.onMountSuccess(function () {
          callback(myComponent)
        })
      })
  }

  return { initWebComponent, docsRef }
}
