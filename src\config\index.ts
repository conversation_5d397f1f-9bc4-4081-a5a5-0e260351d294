import Api from './api'
import { color } from '@/libs/consts'

export default {
  Api,
  Theme: { Color: color },
  /**
   * @description 配置显示在浏览器标签的title
   */
  title: '牵星',
  version: 'v1.22.0',
  tag: process.env.VUE_APP_VERSION_TAG,
  // tag: '内测版 (Alpha)',
  /**
   * @description token在Cookie中存储的天数，默认1天
   */
  cookieExpires: 30,
  /**
   * @description 是否使用国际化，默认为false
   *              如果不使用，则需要在路由中给需要在菜单中展示的路由设置meta: {title: 'xxx'}
   *              用来在菜单中显示文字
   */
  useI18n: false,
  // 废弃，走标准环境变量管理 https://cli.vuejs.org/zh/guide/mode-and-env.html#%E6%A8%A1%E5%BC%8F%E5%92%8C%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F
  //   /**
  //    * @description api请求基础路径
  //    */
  //   baseUrl: {
  //     // dev: 'http://*************:32234',
  //     // pro: 'https://cloud.ttyuyin.com'
  //     pro: 'https://alpha-cloud.ttyuyin.com',
  //     dev: 'http://*************:32234',
  //     // dev: 'http://localhost:8000',
  //   },
  /**
   * @description 默认打开的首页的路由name值，默认为home
   */
  homeName: 'workspace',
  /**
   * @description 需要加载的插件
   */
  plugin: {}
}
