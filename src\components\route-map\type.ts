import { TableColumn } from 'view-design/types/table'
import { EnumNodeType } from './config'
export type EnumToUnion<T extends string> = `${T}`

export interface RouterItem {
  nodeType: EnumToUnion<EnumNodeType>
  name: string
  clusterId: number
  clusterName: string
  namespace: string
  gvr: {
    group: string
    version: string
    resource: string
  }
  descriptions?: {
    l1?: Record<string, string>
    l2?: Record<string, string>
  }
  operations: {
    link?: boolean
    view?: string // 如果是"none"则不显示，如果是""则请求yaml, 否则直接展示view内容
    table?: {
      url: string // 请求地址
      args: Record<string, any> // 请求参数
    }
  }
  state?: {
    status: EnumToUnion<EnumStatus> // succeed, warning, error, none
    message: string
  }
  children?: RouterItem[]
}

export enum EnumStatus {
  succeed = 'succeed',
  warning = 'warning',
  error = 'error',
  none = 'none'
}

export interface TableData {
  gvr: {
    group: string
    version: string
    resource: string
  }
  name: string
  clusterId: number
  namespace: string
  [key: string]: any
}
export interface TableRequestResponseData {
  columns: TableColumn[]
  data: TableData[]
}
