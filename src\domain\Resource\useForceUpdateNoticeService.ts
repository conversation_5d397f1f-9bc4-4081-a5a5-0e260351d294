import { usePost } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, h } from 'vue'
import { useRequest } from 'vue-request'
import { Icon } from 'view-design'
import { LinkButton, Space } from '@/components'
import styled from 'vue-styled-components'

const ForceNoticeWrapper = styled('div')`
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    align-items: center;
    .ivu-icon {
      font-size: 24px;
      color: #ed4014;
      margin-right: 8px;
    }
  }
`

export default function useForceUpdateNoticeService() {
  const { proxy } = getCurrentInstance()

  const { run: forceUpdateNotice } = useRequest(
    (entity) => {
      return usePost(`${Config.Api.Base}${Config.Api.ForceUpdateNotice}`, {
        ...entity
      })
    },
    {
      manual: true
    }
  )

  const onForceUpdateNotice = (entity: { clusterId; namespace; objectName; kind }, editFun: () => void) => {
    proxy.$Modal.confirm({
      render: (h) => {
        return h(ForceNoticeWrapper, [
          h('div', { class: 'title' }, [h(Icon, { props: { type: 'md-warning' } }), h('div', '风险告知')]),
          h(
            'div',
            { class: 'content' },
            '该资源由为 “子环境路由” 的一部分, 由 CICD 平台 触发及 牵星-容器云平台 自动化构建; 若进行人工修改后导致路由不可用, 需自行承担该责任！'
          )
        ])
      },
      //   title: '风险告知',
      //   content: `该资源由为 “子环境路由” 的一部分, 由 CICD 平台 触发及 牵星-容器云平台 自动化构建; 若进行人工修改后导致路由不可用, 需自行承担该责任`,
      loading: true,
      okText: '我已知晓风险',
      onOk: async () => {
        await forceUpdateNotice(entity)
        editFun()
        proxy.$Modal.remove()
      }
    } as any)
  }

  return {
    onForceUpdateNotice
  }
}
