<template>
  <div class="side-menu-wrapper">
    <slot></slot>
    <div class="side-menu">
      <Menu
        ref="menu"
        v-show="!collapsed"
        :active-name="activeName"
        :open-names="openedNames"
        :accordion="accordion"
        :theme="theme"
        width="auto"
        @on-select="handleSelect"
      >
        <template v-for="item in menuList">
          <template v-if="item.children && item.children.length === 1">
            <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
            <menu-item v-else :name="getNameOrHref(item, true)" :key="`menu-${item.children[0].name}`">
              <common-icon :type="item.children[0].icon || ''" />
              <span>{{ showTitle(item.children[0], currentNamespace) }}</span></menu-item
            >
          </template>
          <template v-else-if="item.children.length > 1">
            <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
            <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.name}`">
              <common-icon :type="item?.icon || ''" />
              <span>{{ showTitle(item, currentNamespace) }}</span>
            </menu-item>
          </template>
        </template>
      </Menu>
      <div class="menu-collapsed" v-show="collapsed" :list="menuList">
        <template v-for="item in menuList">
          <collapsed-menu
            v-if="item.children && item.children.length > 1"
            @on-click="handleSelect"
            hide-title
            :root-icon-size="rootIconSize"
            :icon-size="iconSize"
            :theme="theme"
            :parent-item="item"
            :key="`drop-menu-${item.name}`"
          ></collapsed-menu>
          <Tooltip
            transfer
            v-else
            :content="showTitle(item.children && item.children[0] ? item.children[0] : item)"
            :key="`drop-menu-${item.name}`"
          >
            <a @click="handleSelect(getNameOrHref(item, true))" class="drop-menu-a" :style="{ textAlign: 'center' }"
              ><common-icon
                v-if="item?.icon || (item.children && item.children[0]?.icon)"
                :size="rootIconSize"
                :color="textColor"
                :type="item?.icon || (item.children && item.children[0]?.icon)"
            /></a>
          </Tooltip>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import SideMenuItem from './side-menu-item.vue'
import CollapsedMenu from './collapsed-menu.vue'
import { getUnion } from '@/libs/tools'
import mixin from './mixin'

export default {
  name: 'SideMenu',
  mixins: [mixin],
  components: {
    SideMenuItem,
    CollapsedMenu
  },
  props: {
    menuList: {
      type: Array,
      default() {
        return []
      }
    },
    collapsed: {
      type: Boolean
    },
    theme: {
      type: String,
      default: 'light'
    },
    rootIconSize: {
      type: Number,
      default: 20
    },
    iconSize: {
      type: Number,
      default: 16
    },
    accordion: Boolean,
    activeName: {
      type: String,
      default: ''
    },
    openNames: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      openedNames: []
    }
  },
  methods: {
    handleSelect(name) {
      this.$emit('on-select', name)
    },
    getOpenedNamesByActiveName(name) {
      return this.$route.matched.map((item) => item.name).filter((item) => item !== name)
    },
    updateOpenName(name) {
      if (name === this.$config.homeName) this.openedNames = []
      else this.openedNames = this.getOpenedNamesByActiveName(name)
    }
  },
  computed: {
    textColor() {
      return this.theme === 'dark' ? '#fff' : '#495060'
    },
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    }
  },
  watch: {
    activeName(name) {
      if (this.accordion) this.openedNames = this.getOpenedNamesByActiveName(name)
      else this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
    },
    openNames(newNames) {
      this.openedNames = newNames
    },
    openedNames() {
      this.$nextTick(() => {
        this.$refs.menu.updateOpened()
      })
    }
  },
  mounted() {
    this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
  }
}
</script>
<style lang="less">
.side-menu-wrapper {
  user-select: none;
  > .side-menu {
    height: ~'calc(100vh - 48px - 48px - 8px)';
    width: 13vw;
    overflow: hidden auto;
    &::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar {
      display: none;
    }
    .ivu-menu-vertical.ivu-menu-light:after {
      display: none;
    }
    .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
      color: #2a7cc3;
      background: #def4ff;
      z-index: 2;
      border-radius: 36px 0 0 36px;
    }
    .ivu-menu-item,
    .ivu-menu-submenu-title {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      .ivu-icon-ios-arrow-down {
        right: 0;
      }
    }
    .ivu-menu-submenu-title {
      z-index: 2;
      i {
        margin-right: 13px;
      }
    }
    .ivu-menu-child-item-active > .ivu-menu-submenu-title {
      color: #2a7cc3;
      &:after {
        content: '';
        display: block;
        width: 2px;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        background: #2a7cc3;
      }
    }
  }
  .menu-collapsed {
    padding-top: 16px;

    .ivu-dropdown {
      width: 100%;
      .ivu-dropdown-rel a {
        width: 100%;
      }
    }
    .ivu-tooltip {
      width: 100%;
      .ivu-tooltip-rel {
        width: 100%;
      }
      .ivu-tooltip-popper .ivu-tooltip-content {
        .ivu-tooltip-arrow {
          border-right-color: #fff;
        }
        .ivu-tooltip-inner {
          background: #fff;
          color: #495060;
        }
      }
    }
  }
  a.drop-menu-a {
    display: inline-block;
    padding: 6px 15px;
    width: 100%;
    text-align: center;
    color: #495060;
  }
}
.menu-title {
  padding-left: 6px;
}
</style>
