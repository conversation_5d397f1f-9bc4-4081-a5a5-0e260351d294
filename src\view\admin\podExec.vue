<template>
  <div>
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <div class="logs-header">
      <Space>
        <span style="font-size: 14px; font-weight: bold; color: #f9f9f9">
          容器 {{ this.pod }} / {{ this.container }}
        </span>

        <Dropdown @on-click="onBashClick">
          <Button type="error" size="small" style="font-weight: bold; width: 80px">
            {{ shell?.split('/')?.[2] }} &nbsp;&nbsp;
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="/bin/bash">/bin/bash</DropdownItem>
            <DropdownItem name="/bin/sh">/bin/sh</DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Dropdown @on-click="handleConnectContainer">
          <Button size="small" style="font-weight: bold; width: 80px">
            切换容器
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem v-for="item in containerList" :name="item.name" :key="item.name">{{
              item.name
            }}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Poptip placement="bottom" trigger="hover" content="60分钟内无操作自动断开连接。">
          <Icon type="md-help-circle" style="cursor: pointer; font-size: 18px; color: yellow" />
        </Poptip>
      </Space>

      <Button size="small" shape="circle" type="primary" @click="reloadShellWindow">Reload</Button>
    </div>
    <div style="height: 100%; width: 100%; padding: 16pxx; background-color: #0f0f0f">
      <iframe ref="shellWindow" :src="webShellUri" class="shell-windows"></iframe>
    </div>
  </div>
</template>

<script>
import { ApiResourceJson } from '@/api/k8s/namespace/workloads'
import { errorMessage, noticeError } from '@/libs/util'
import { useGet } from '@/libs/service.request'
import Config from '@/config'
import { Space } from '@/components'

export default {
  name: 'k8s-pod-exec',
  components: { Space },
  data() {
    return {
      showContainerDetail: false,
      spinShow: true,
      baseURI: '/webshell.html',
      baseHost: '',
      webShellUri: '',
      pod: null,
      container: null,
      clusterID: null,
      namespace: null,
      shell: null,
      title: null,
      priority: false,
      containerList: []
    }
  },
  methods: {
    onBashClick(shell) {
      this.$Modal.confirm({
        title: 'Tips',
        content: '<p>确认切换操作 ?</p>',
        loading: true,
        onOk: () => {
          // this.$refs.shellWindow.src = this.webShellUri
          this.mergeWebShellUri()
          console.log(this.webShellUri)
          this.$refs.shellWindow.src = this.webShellUri
          setTimeout(() => {
            this.shell = shell
            this.$Modal.remove()
          }, 1500)
        }
      })
    },
    handleConnectContainer(container) {
      if (this.container === container) {
        this.$Message.info('当前容器已连接')
        return
      }
      this.$Modal.confirm({
        title: 'Tips',
        content: '<p>确认切换操作 ?</p>',
        loading: true,
        onOk: () => {
          // this.$refs.shellWindow.src = this.webShellUri
          this.mergeWebShellUri()
          this.$refs.shellWindow.src = this.webShellUri
          setTimeout(() => {
            this.container = container
            this.$Modal.remove()
          }, 1500)
        }
      })
    },
    handleContainerSwitch() {
      this.fetchPodDetail()
      this.showContainerDetail = true
    },
    setHeader() {
      var title = document.getElementsByTagName('title')
      if (this.title === null) {
        title[0].innerHTML = `终端 ${this.pod}/${this.container}`
        return
      }
      title[0].innerHTML = this.title
    },
    reloadShellWindow() {
      this.$Modal.confirm({
        title: 'Tips',
        content: '<p>确认重载操作 ?</p>',
        loading: true,
        onOk: () => {
          this.$refs.shellWindow.src = this.webShellUri
          setTimeout(() => {
            this.$Modal.remove()
          }, 1500)
        }
      })
    },
    mergeWebShellUri() {
      this.webShellUri =
        this.baseURI +
        `?host=${this.baseHost}&cluster_id=${this.clusterID}&namespace=${this.namespace}&pod=${this.pod}&container=${this.container}&exec=${this.shell}` +
        `&token=${this.$store.state.user.token}&priority=${this.priority}`
    },
    async fetchPodDetail() {
      await ApiResourceJson({
        cluster_id: this.clusterID,
        namespace: this.namespace,
        resource_name: this.pod,
        kind: 'Pod'
      })
        .then((res) => {
          let data = res.data.data.data
          this.containerList = []
          if (data.status.initContainerStatuses !== undefined) {
            this.containerList = data.status.initContainerStatuses
          }

          data.status.containerStatuses.map((item) => {
            this.containerList.push(item)
          })
        })
        .catch((err) => {
          noticeError(this, `无可切换容器: ${errorMessage(err)}`)
        })
    },
    setBaseUrl() {
      this.baseHost = process.env.VUE_APP_GLOB_API_URL
    }
  },
  onSwitchRelativeContainer(container) {
    this.container = container
  },
  async created() {
    this.setBaseUrl()
    let query = this.$route.query
    this.clusterID = query.clusterId
    this.namespace = query.namespace
    this.deployment = query.deployment
    this.cluster = query.cluster
    this.pod = query.pod
    this.container = query.container
    this.shell = query.shell
    if (query.title !== undefined) {
      this.title = query.title
    }
    if (query.priority !== undefined) {
      this.priority = query.priority
    }

    this.setHeader()
    this.mergeWebShellUri()
    setTimeout(() => {
      this.spinShow = false
    }, 2000)

    const res = await useGet(`${Config.Api.Base}${Config.Api.GetPodRelativeContainerList}`, {
      params: {
        name: query.pod,
        clusterId: query.clusterId,
        namespace: query.namespace
      }
    })
    this.containerList = res.data.data ?? []
  }
}
</script>

<style scoped>
.logs-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #3a3333;
  color: #f9f9f9;
  font-size: 16px;
  font-weight: bold;
  height: 48px;
  padding: 8px 8px 8px 8px;
}
.shell-windows {
  height: calc(100vh - 66px);
  width: 100%;
  border: 0;
}
</style>
