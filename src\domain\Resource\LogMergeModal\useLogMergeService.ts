import { computed, ref, getCurrentInstance, watch } from 'vue'
import { useRouter } from '@/libs/useVueInstance'
import { useGet } from '@/libs/service.request'
import Config from '@/config'

export enum EnumWorkloadKind {
  Deployment = 'Deployment',
  StatefulSet = 'StatefulSet'
}

const columns = [
  {
    title: '容器名',
    key: 'containerName'
  },
  {
    title: '合并',
    key: 'isMerge',
    width: 80,
    align: 'center',
    slot: 'isMerge'
  }
]

const useLogMergeService = (props, emit) => {
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const visible = computed({
    get: () => props.value,
    set: (value) => emit('input', value)
  })
  const loading = ref(false)
  const tableData = ref<{ containerName: string; isMerge: boolean }[]>([])

  const handleMergeCheckChange = (index) => {
    tableData.value.forEach((i, idx) => {
      if (i.isMerge && index !== idx) {
        i.isMerge = false
      }
    })
  }

  const onLogMergeSubmit = () => {
    if (!tableData.value.some((i) => i.isMerge)) {
      proxy.$Message.warning('请先选择需要合并的容器')
      return
    }

    const container = tableData.value.find((i) => i.isMerge).containerName
    let r = router.resolve({
      path: '/kubernetes/logs',
      query: {
        clusterId: props.clusterId,
        namespace: props.namespace,
        cluster: props.cluster,
        workloadName: props.workloadName,
        workloadKind: props.workloadKind,
        container
      }
    })
    window.open(r.href, '_blank')
    visible.value = false
  }

  const getWorkloadContainerList = async () => {
    try {
      loading.value = true

      const reqApi =
        props.workloadKind === EnumWorkloadKind.Deployment
          ? Config.Api.GetDeploymentContainerRelate
          : Config.Api.GetStatefulsetContainerRelate

      const res = await useGet(`${Config.Api.Base}${reqApi}`, {
        params: {
          clusterId: props.clusterId,
          namespace: props.namespace,
          name: props.workloadName
        }
      })

      if (res.success) {
        tableData.value = res.data.data.map((i) => ({
          containerName: i,
          isMerge: false
        }))

        // 默认选中第一个
        tableData.value[0].isMerge = true
      }
    } catch (error) {
      console.warn(error)
    } finally {
      loading.value = false
    }
  }

  watch(visible, () => {
    visible.value && getWorkloadContainerList()
  })

  return {
    visible,
    loading,
    columns,
    tableData,
    handleMergeCheckChange,
    onLogMergeSubmit
  }
}

export default useLogMergeService
