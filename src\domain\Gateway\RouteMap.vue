<script lang="ts" setup>
import { RouteMap } from '@/components/route-map'
import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, nextTick, onMounted, ref } from 'vue'
import { useRequest } from 'vue-request'
import { RouterItem } from '@/components/route-map/type'
import Space from '@/components/space'
import { useSelectQueryClear } from '@/libs/useTools'

const { proxy } = getCurrentInstance()
const { deployment, deploymentNamespace, clusterId } = proxy.$route.query
const gatewayInfo = ref<string>()
const virtualServices = ref<string[]>()
const { selectRef, onOpenChangeToClearQuery } = useSelectQueryClear()

const {
  run: getRouteData,
  data: routeData,
  loading
} = useRequest(
  () => {
    const [namespace, gateway] = gatewayInfo.value.split('/')
    return usePost<{ data: RouterItem }>(`${Config.Api.Base}${Config.Api.GetGatewayRouterTree}`, {
      clusterId: clusterId,
      namespace: namespace,
      gateway: gateway,
      vsInfos: virtualServices.value?.map((i) => {
        const [namespace, name] = i.split('/')
        return { name, namespace }
      })
    })
  },
  {
    manual: true,
    formatResult: (res) => res.data.data
  }
)
const {
  run: getGatewayList,
  data: gatewayList,
  loading: gatewayListLoading
} = useRequest(
  () => {
    return useGet<{ list: { name: string; namespace: string }[] }>(
      `${Config.Api.Base}${Config.Api.GetGatewayTableData}`,
      {
        params: {
          namespace: deploymentNamespace,
          clusterId: clusterId,
          resourceName: deployment,
          page: 1,
          size: 999
        }
      }
    )
  },
  {
    manual: true,
    formatResult: (res) => {
      const set = new Set([])
      res.data.list?.forEach((i) => {
        set.add(`${i.namespace}/${i.name}`)
      })
      return Array.from(set).map((i) => ({ value: i, label: i }))
    },
    onSuccess: async (data) => {
      virtualServices.value = undefined
      virtualServiceList.value = undefined
      routeData.value = undefined
      if (data.length === 1) {
        gatewayInfo.value = data[0].value
        getVirtualServiceList()
        proxy.$Message.success('检测到只有一个gateway，已自动绑定')
      }
    }
  }
)

const {
  run: getVirtualServiceList,
  data: virtualServiceList,
  loading: virtualServiceListLoading
} = useRequest(
  () => {
    const [namespace, gateway] = gatewayInfo.value.split('/')
    return useGet<{ data: { name: string; namespace: string }[] }>(
      `${Config.Api.Base}${Config.Api.GetGatewayVirtualServiceList}`,
      { params: { namespace, clusterId: clusterId, gateway } }
    )
  },
  {
    manual: true,
    formatResult: (res) => {
      const set = new Set([])
      res.data.data?.forEach((i) => {
        set.add(`${i.namespace}/${i.name}`)
      })
      return Array.from(set).map((i) => ({ value: i, label: i }))
    },
    onSuccess: async (data) => {
      if (!data?.length || data?.length <= 10) {
        virtualServices.value = data?.map((i) => i.value)
        proxy.$Message.success('检测到vs节点少于10个，已自动请求全部数据')
        await nextTick()
        getRouteData()
      } else {
        proxy.$Message.info('检测到vs节点大于10个，请选择需要查看的vs节点再请求')
        routeData.value = undefined
      }
    }
  }
)
const onGatewayChange = (val) => {
  getVirtualServiceList()
  virtualServices.value = []
}

const onVirtualServicesChange = (val) => {
  if (virtualServices.value.length > 10) {
    proxy.$Message.warning('为了页面的最优性能，请不要选择展示超过10个VirtualService节点')
    virtualServices.value.splice(virtualServices.value.indexOf(val), 1)
  }
}

onMounted(() => {
  getGatewayList()
})
</script>

<template>
  <div>
    <Modal :value="true" fullscreen :title="`${deploymentNamespace} 路由图`" footer-hide :closable="false">
      <Alert show-icon
        >当前路由图根据您选择的 Gateway 配置进行域名的路由展示，让你快速了解 当前 Gateway 配置的所有路由架构。
        <div class="notice">注意：现阶段仅支持 FQDN 的域名进行查找</div>
      </Alert>
      <div class="operate">
        <Space>
          <Select
            v-model="gatewayInfo"
            placeholder="请选择Gateway"
            filterable
            :loading="gatewayListLoading"
            @on-change="onGatewayChange"
            style="width: 380px"
          >
            <Option v-for="item in gatewayList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>

          <Select
            ref="selectRef"
            v-model="virtualServices"
            :placeholder="gatewayInfo ? '请选择VirtualService' : '请先选择Gateway'"
            filterable
            multiple
            @on-change="onVirtualServicesChange"
            :loading="virtualServiceListLoading"
            :max-tag-count="5"
            @on-open-change="onOpenChangeToClearQuery"
          >
            <Option v-for="item in virtualServiceList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>

          <Button type="primary" icon="md-search" @click="getRouteData" :loading="loading">搜索</Button>
        </Space>
        <!-- <span class="notice">* 现阶段仅支持全域名domain的查找</span> -->
      </div>
      <RouteMap
        containerHeight="calc(100vh - 190px)"
        :routeData="routeData"
        :loading="virtualServiceListLoading || loading"
      >
      </RouteMap>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.operate {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}
.notice {
  color: #ed4014;
  margin-top: 8px;
}
</style>
