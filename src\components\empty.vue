<script lang="ts" setup>
const props = defineProps({
  description: { type: String, default: '' }
})
</script>

<template>
  <div class="empty">
    <img
      class="empty-image"
      alt="empty"
      src="https://gw.alipayobjects.com/mdn/miniapp_social/afts/img/A*pevERLJC9v0AAAAAAAAAAABjAQAAAQ/original"
    />

    <p class="empty-description">
      <span>{{ props.description ? props.description : '暂无数据' }}</span>
    </p>
  </div>
</template>

<style lang="less" scoped>
.empty {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .empty-image {
    height: 100px;
    margin-bottom: 8px;
    opacity: 1;
  }
  .empty-description {
    color: #999;
  }
}
</style>
