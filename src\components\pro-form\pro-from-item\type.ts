import { ExtractPropTypes, PropType } from 'vue'
export enum EnumFormItemControllerType {
  Switch = 'switch',
  Chosen = 'chosen'
}

export enum EnumContentStyleMode {
  Background = 'background',
  Border = 'border',
  DashBorder = 'dashBorder'
}

type EnumToUnion<T extends string> = `${T}`

export function formItemProps() {
  return {
    name: String, // 唯一标识（与 data表单数据对象 中的映射一致）
    label: String, // 标题
    desc: String, // 描述
    url: String, // 外链
    dataReloadFlag: [Boolean, String], // 表单字段实现隐藏控制（初始化触发点）
    contentStyleMode: String as PropType<EnumToUnion<EnumContentStyleMode>>, // 填充内容的样式，可选择色块包裹或边框包裹
    contentClassName: String, // 填充内容的类名
    mode: { type: String as PropType<EnumFormItemControllerType>, default: undefined }, // 模式：无、开关、单选
    chosenOptions: {
      type: Array as PropType<{ value: string; label: string }[]>,
      default: function () {
        return []
      }
    }, // 单选模式options
    data: {
      type: Object as PropType<Record<string, any>>,
      default: function () {
        return {}
      }
    }, // 表单数据对象
    initData: { type: [String, Number, Object, Array, Boolean], default: undefined },
    size: { type: String as PropType<'sm' | 'md' | 'lg' | 'xl'>, default: 'xl' },
    /** 只读-只适用select */
    readonly: Boolean
  }
}
export type FormItemProps = Partial<ExtractPropTypes<ReturnType<typeof formItemProps>>>
