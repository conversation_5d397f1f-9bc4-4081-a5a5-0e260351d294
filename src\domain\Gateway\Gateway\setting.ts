import { Ellipsis } from '@/components'
import { relativeTime } from '@/libs/tools'
import dayjs from 'dayjs'
import { Tooltip } from 'view-design'

export const TABLE_COLUMNS = [
  {
    title: 'UID',
    key: 'uid',
    width: 90,
    tooltip: true
  },
  {
    title: 'Name',
    key: 'name',
    slot: 'name',
    tooltip: true
  },
  {
    title: 'Namespace',
    key: 'namespace',
    tooltip: true
  },
  {
    title: 'Hosts',
    key: 'hosts',
    render: (h, params) => {
      return params.row.hosts
        ? h(Tooltip, {
            class: 'text-ellipsis',
            props: {
              transferClassName: 'text-ellipsis',
              transfer: true,
              placement: 'top-start',
              theme: 'light'
            },
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,

    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 220,
    align: 'center'
  }
]

export const SERVER_TABLE_COLUMNS = [
  {
    title: 'Host',
    key: 'hosts',

    render: (h, params) => {
      return params.row.hosts?.length
        ? h(Ellipsis, {
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Ports',
    key: 'port',
    render: (h, params) => {
      return h(
        Ellipsis,
        `${params.row.port?.data?.name ?? params.row.port?.name ?? '-'} | ${
          params.row.port?.data?.number ?? params.row.port?.number ?? '-'
        } | ${params.row.port?.data?.protocol ?? params.row.port?.protocol ?? '-'}`
      )
    }
  },
  {
    title: 'TLS / Mode',
    key: 'tls',
    render: (h, params) => {
      return params.row.tls?.data
        ? h(
            Ellipsis,
            `${params.row.tls?.data?.credentialName?.data ? params.row.tls?.data?.credentialName?.data : '-'} | ${
              params.row.tls?.data?.mode ? params.row.tls?.data?.mode : '-'
            }`
          )
        : h('div', '-')
    }
  }
]

export const HOSTS_TABLE_COLUMNS = [
  {
    title: 'Host',
    key: 'hosts',
    render: (h, params) => {
      return params.row.hosts
        ? h(Tooltip, {
            class: 'text-ellipsis',
            props: {
              transferClassName: 'text-ellipsis',
              transfer: true,
              placement: 'top-start',
              theme: 'light'
            },
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Ports',
    key: 'port',
    tooltip: true,
    render: (h, params) => {
      return h(
        'span',
        `${params.row.port?.data?.name ?? params.row.port?.name ?? '-'} | ${
          params.row.port?.data?.number ?? params.row.port?.number ?? '-'
        } | ${params.row.port?.data?.protocol ?? params.row.port?.protocol ?? '-'}`
      )
    }
  },
  {
    title: 'TLS / Mode',
    key: 'tls',
    tooltip: true,
    render: (h, params) => {
      return params.row.tls
        ? h(
            'span',
            `${params.row.tls?.credentialName ? params.row.tls?.credentialName : '-'} | ${
              params.row.tls?.mode ? params.row.tls?.mode : '-'
            }`
          )
        : h('div', '-')
    }
  }
]

export const Gateway_DETAIL_CONFIG = [
  {
    title: 'Name',
    key: 'name',
    render: (h, data) => {
      return h('b', data.name)
    }
  },
  {
    title: 'Namespace',
    key: 'namespace'
  },
  {
    title: 'CreationTime',
    key: 'creationTime',
    render: (h, data) => {
      return h('span', dayjs(data.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm'))
    }
  },
  {
    title: 'Labels',
    key: 'labels',
    slot: 'labels'
  }
]
