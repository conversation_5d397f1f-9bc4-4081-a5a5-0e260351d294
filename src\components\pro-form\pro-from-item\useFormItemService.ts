import { computed, onMounted, ref, set, watch } from 'vue'
import { FormItemProps, EnumFormItemControllerType, EnumContentStyleMode } from './type'

const SWITCH_CTL_KEY = 'switchCtl'
const CHOSEN_CTL_KEY = 'chosenCtl'

export default function useFormItemService(props: FormItemProps) {
  const isChosenCtl = computed(() => props.mode === EnumFormItemControllerType.Chosen && props.chosenOptions?.length)

  const chosenCtl = ref(props.data[props.name]?.[CHOSEN_CTL_KEY] || props.chosenOptions?.[0]?.value)

  const switchCtl = ref(props.data[props.name]?.[SWITCH_CTL_KEY] || true)

  const onSwitchChange = (value) => {
    switchCtl.value = value
    set(props.data, props.name, {
      [SWITCH_CTL_KEY]: value,
      data: props.data?.[props.name]?.data ?? props.initData ?? undefined
    })
  }

  const onChosenChange = (value) => {
    chosenCtl.value = value
    let initData = {}
    if (props.initData) {
      if (typeof props.initData === 'object') {
        initData = typeof props.initData === 'object' ? props.initData : {}
      } else {
        console.error(
          'ProFormItem with chosen type has error init data,data is not a object',
          props.name,
          props.initData
        )
      }
    }
    set(props.data, props.name, {
      ...initData,
      ...props.data[props.name],
      [CHOSEN_CTL_KEY]: value
    })
  }

  const onUpdateData = () => {
    if (props.mode === EnumFormItemControllerType.Chosen) {
      const value = props.data[props.name]?.[CHOSEN_CTL_KEY] || props.chosenOptions?.[0]?.value
      onChosenChange(value)
    } else {
      const value =
        props.data[props.name]?.[SWITCH_CTL_KEY] !== undefined ? props.data[props.name]?.[SWITCH_CTL_KEY] : false
      onSwitchChange(value)
    }
  }

  watch(
    () => props.dataReloadFlag,
    () => {
      if (props.dataReloadFlag && props.mode) {
        onUpdateData()
      }
    },
    {
      immediate: true
    }
  )

  const isDefaultShow = computed(() => {
    switch (props.mode) {
      case EnumFormItemControllerType.Switch:
        return switchCtl.value
      default:
        return true
    }
  })

  const contentClassName = computed(() => {
    switch (props.contentStyleMode) {
      case EnumContentStyleMode.Background:
        return `pro-form-item-background-content${props.contentClassName ? ' ' + props.contentClassName : ''}`

      case EnumContentStyleMode.Border:
        return `pro-form-item-border-content${props.contentClassName ? ' ' + props.contentClassName : ''}`

      case EnumContentStyleMode.DashBorder:
        return `pro-form-item-dash-border-content${props.contentClassName ? ' ' + props.contentClassName : ''}`

      default:
        return props.contentClassName ?? ''
    }
  })

  return {
    isChosenCtl,
    chosenCtl,
    switchCtl,
    isDefaultShow,
    onSwitchChange,
    onChosenChange,
    contentClassName
  }
}
