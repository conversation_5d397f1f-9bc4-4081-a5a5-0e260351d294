import { InjectionKey, computed, onMounted } from 'vue'

const CURRENT_VERSION = 'current-version'
const HAD_SHOW_VERSION = 'had-show-version'

export const VersionNoticeServiceProvide = Symbol(
  'VersionNoticeServiceProvide'
) as InjectionKey<VersionNoticeServiceReturn>

export type VersionNoticeServiceReturn = ReturnType<typeof useVersionNoticeService>

export default function useVersionNoticeService(
  /** 渲染系统刷新提示 */
  renderUpdateVersionNotice: (updateVersion: () => void) => void,
  /** 渲染系统版本更新提示 */
  renderVersionNotice: (isForceShow?: boolean) => void,
  /** 忽略更新提示的页面，如登录页面 内容为location.pathname */
  ignoreUpdateNoticeRoute?: string[]
) {
  /** 是否展示过版本更新信息 */
  const hadShowVersionNotice = computed<boolean>({
    get: () => {
      const data = localStorage.getItem(HAD_SHOW_VERSION)
      return data ? JSON.parse(data) : false
    },
    set: (val: boolean) => {
      localStorage.setItem(HAD_SHOW_VERSION, JSON.stringify(val))
    }
  })
  /** 本地版本号 */
  const currentVersion = computed<string>({
    get: () => {
      console.log('获取本地版本号')
      return localStorage.getItem(CURRENT_VERSION)
    },
    set: (val) => {
      console.log('设置本地版本号')
      localStorage.setItem(CURRENT_VERSION, val)
    }
  })

  /** 获取服务器版本号 */
  const getServerVersionInfo = async () => {
    const res = await fetch('/', {
      headers: {
        'Cache-Control': 'no-store'
      }
    })
    const html = await res.text()
    const reg = new RegExp(/<script(?:\s+[^>]*)?>(.*?)<\/script\s*>/gi) // script正则
    const scriptArr = html.match(reg) as string[] //匹配script标签
    return scriptArr.join(';')
  }

  /** 强制刷新 */
  const updateVersion = (serverVersion) => {
    hadShowVersionNotice.value = false
    currentVersion.value = serverVersion
    window.location.replace(window.location.href)
  }

  const onCompareVersion = async () => {
    /** 验证本地版本是否最新，弹出强制更新窗口 */
    const serverVersion = await getServerVersionInfo()

    const isLatest = serverVersion === currentVersion.value
    if (isLatest) {
      /** 是否已弹出过版本更新提示 */
      if (!hadShowVersionNotice.value) {
        hadShowVersionNotice.value = true
        renderVersionNotice && renderVersionNotice()
      }
      //   renderVersionNotice && renderVersionNotice(serverVersion)
    } else {
      const isIgnore = ignoreUpdateNoticeRoute?.length && ignoreUpdateNoticeRoute?.includes(window.location.pathname)
      if (isIgnore || !currentVersion.value) return
      renderUpdateVersionNotice && renderUpdateVersionNotice(() => updateVersion(serverVersion))
    }
  }
  onMounted(() => {
    onCompareVersion()
  })
  return { onCompareVersion, renderVersionNotice }
}
