import Config from '@/config'
import { useGet } from '@/libs/service.request'
import { computed, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import dayjs from 'dayjs'
import { formatMonitorDataToChartData } from './util'
import useChartService from './useChartService'

export type ChartType =
  | 'cpu usage'
  | 'cpu throttling'
  | 'mem usage'
  | 'cpu usage(workload stdvar)'
  | 'receive bandwidth'
  | 'transmit bandwidth'

export interface Entity {
  cluster: string
  namespace: string
  pod: string
  workload: string
}
enum EnumTimePickerType {
  last1Hour,
  last3Hour,
  last6Hour,
  last12Hour,
  last24Hour
}
type TimePickerItem = { value: EnumTimePickerType; label: string }
export const TIME_PICKER_LIST: TimePickerItem[] = [
  {
    value: EnumTimePickerType.last1Hour,
    label: '最近 1 小时'
  },
  {
    value: EnumTimePickerType.last3Hour,
    label: '最近 3 小时'
  },
  {
    value: EnumTimePickerType.last6Hour,
    label: '最近 6 小时'
  },
  {
    value: EnumTimePickerType.last12Hour,
    label: '最近 12 小时'
  },
  {
    value: EnumTimePickerType.last24Hour,
    label: '最近 24 小时'
  }
]

export default function useContainerChartService(props: { reloadFlag: boolean; entity: Entity; prefix: string }) {
  const timePicker = ref<EnumTimePickerType>(EnumTimePickerType.last1Hour)
  const containerIdMap = ref<Record<ChartType, string>>({} as any)

  const rateInterval = computed(() => {
    switch (timePicker.value) {
      case EnumTimePickerType.last1Hour:
      case EnumTimePickerType.last3Hour:
      case EnumTimePickerType.last6Hour:
        return '2m0s'
      case EnumTimePickerType.last12Hour:
        return '4m30s'
      case EnumTimePickerType.last24Hour:
      default:
        return '10m30s'
    }
  })

  const getParams = (type: ChartType) => {
    const { namespace, cluster, pod, workload } = props.entity
    switch (type) {
      case 'cpu usage':
        return `sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace="${namespace}", pod="${pod}", container!="POD",container!="",cluster="${cluster}"}) by (container)`
      case 'cpu throttling':
        return `sum(increase(container_cpu_cfs_throttled_periods_total{namespace="${namespace}", pod="${pod}", container!="POD", container!="", cluster="${cluster}"}[5m])) by (container) /
        sum(increase(container_cpu_cfs_periods_total{namespace="${namespace}", pod="${pod}", container!="POD", container!="", cluster="${cluster}"}[5m])) by (container)`
      case 'mem usage':
        return `sum(container_memory_working_set_bytes{cluster="${cluster}", namespace="${namespace}", pod="${pod}", container!="POD", container!="", image!=""}) by (container)`
      case 'cpu usage(workload stdvar)':
        return `stdvar(
            node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster="${cluster}", namespace="${namespace}"}
          * on(namespace,pod)
            group_left(workload, workload_type) namespace_workload_pod:kube_pod_owner:relabel{cluster="${cluster}", namespace="${namespace}", workload="${workload}"}
        )`
      case 'receive bandwidth':
        return `sum(irate(container_network_receive_bytes_total{cluster=~"${cluster}",namespace=~"${namespace}", pod=~"${pod}"}[${rateInterval.value}])) by (pod)`
      case 'transmit bandwidth':
        return `sum(irate(container_network_transmit_bytes_total{cluster=~"${cluster}",namespace=~"${namespace}", pod=~"${pod}"}[${rateInterval.value}])) by (pod)`
      default:
        break
    }
  }

  const toSecondsTimestamp = (millisecondsTimestamp) => Math.floor(millisecondsTimestamp / 1000)
  const getTimestampFromTimePicker = () => {
    switch (timePicker.value) {
      case EnumTimePickerType.last1Hour:
        return [toSecondsTimestamp(dayjs().subtract(1, 'hour').valueOf()), toSecondsTimestamp(dayjs().valueOf())]

      case EnumTimePickerType.last3Hour:
        return [toSecondsTimestamp(dayjs().subtract(3, 'hour').valueOf()), toSecondsTimestamp(dayjs().valueOf())]

      case EnumTimePickerType.last6Hour:
        return [toSecondsTimestamp(dayjs().subtract(6, 'hour').valueOf()), toSecondsTimestamp(dayjs().valueOf())]

      case EnumTimePickerType.last12Hour:
        return [toSecondsTimestamp(dayjs().subtract(12, 'hour').valueOf()), toSecondsTimestamp(dayjs().valueOf())]
      case EnumTimePickerType.last24Hour:
      default:
        return [toSecondsTimestamp(dayjs().subtract(24, 'hour').valueOf()), toSecondsTimestamp(dayjs().valueOf())]
    }
  }

  const getChartTypeDataByType = (type: ChartType) => {
    const { CONTAINER_ID, renderChart } = useChartService(type, props.prefix)
    containerIdMap.value[type] = CONTAINER_ID

    const { run, loading } = useRequest(
      () => {
        const [start, end] = getTimestampFromTimePicker()

        return {
          url: `${Config.Api.MonitorAPI}${Config.Api.GetMonitorData}?query=${getParams(
            type
          )}&dedup=true&partial_response=false&start=${start}&end=${end}&step=${
            timePicker.value === EnumTimePickerType.last24Hour ? '5m' : '1m'
          }&max_source_resolution=0s&engine=prometheus&explain=false`,
          headers: {
            'User-Agent': 'star-constack'
          }
        }
      },
      {
        manual: true,
        formatResult: (res: { data: { result: [] } }) => {
          const data = formatMonitorDataToChartData(
            res.data.result,
            type === 'cpu throttling' ? (value) => Number(Number(value).toFixed(4)) * 100 : undefined
          )
          renderChart(data)
        }
      }
    )
    return { run, loading }
  }

  const { run: getCpuUsage } = getChartTypeDataByType('cpu usage')
  const { run: getCpuThrottling } = getChartTypeDataByType('cpu throttling')
  const { run: getMemUsage } = getChartTypeDataByType('mem usage')
  const { run: getReceiveBandwidth } = getChartTypeDataByType('receive bandwidth')
  const { run: getTransmitBandwidth } = getChartTypeDataByType('transmit bandwidth')
  const { run: getCpuUsageWorkload, loading } = getChartTypeDataByType('cpu usage(workload stdvar)')

  const renderChart = () => {
    getCpuUsage()
    getCpuThrottling()
    getMemUsage()
    getReceiveBandwidth()
    getTransmitBandwidth()
    getCpuUsageWorkload()
  }

  const jumpToTelemetryDashboard = () => {
    const url = `https://tt-telemetry.ttyuyin.com/#/application-dashboard?name=${props.entity.workload}.${props.entity.namespace}&cluster=&lastXMin=15&isOversea=false`
    window.open(url, '_blank')
  }

  watch(
    () => props.reloadFlag,
    () => {
      if (props.reloadFlag) {
        renderChart()
      }
    },
    {
      deep: true
    }
  )

  watch(
    timePicker,
    () => {
      if (props.reloadFlag) {
        renderChart()
      }
    },
    {
      deep: true
    }
  )

  return { timePicker, containerIdMap, jumpToTelemetryDashboard, renderChart, loading }
}
