<script lang="ts" setup>
import { PropType } from 'vue'
import useResourceCardService from './useResourceCardService'
import { RelativeResourceModal } from '../'

import { Resource } from '../type'
import { ResourceName, ResourceActions, EnumAction, CONTAINER_COLUMNS } from '../config'
import { Space, LinkButton } from '@/components'
import { relativeTime } from '@/libs/tools'
import dayjs from 'dayjs'

const props = defineProps({
  data: { type: Object as PropType<Resource>, default: () => ({}) },
  isRelative: Boolean
})
const {
  jumpToResourceDetail,
  onActionBtnClick,
  relativeModalVisible,
  noticeUnAuthorized,
  containers,
  containersLoading,
  onContainersPopperShow
} = useResourceCardService(props)

const actionList = (ResourceActions[props.data.resource] ?? [EnumAction.Yaml])?.filter((i) =>
  props.isRelative ? i !== EnumAction.Relative : true
)

const title = ResourceName?.[props.data.resource] ?? props.data.resource
</script>

<template>
  <Poptip
    trigger="hover"
    :transfer-class-name="`${props.data.isAuthorized ? 'hidden-poptip' : ''}`"
    :class="`resource-item-wrapper ${props.data.isAuthorized ? '' : ' unauthorized-resource-item-wrapper'}`"
    :content="props.data.isAuthorized ? '' : '资源尚未授权'"
    transfer
  >
    <Space
      :size="8"
      v-bind="{
        ...(!props.data.isAuthorized
          ? {
              on: { click: noticeUnAuthorized }
            }
          : null)
      }"
    >
      <strong class="type" :title="title.slice(0, 1).toUpperCase() + title.slice(1)">{{
        title.slice(0, 1).toUpperCase() + title.slice(1)
      }}</strong>
      <Divider type="vertical" />
      <Space class="content" direction="vertical" :size="8">
        <div class="header">
          <Space :size="8">
            <LinkButton
              :text="props.data.name"
              @click="jumpToResourceDetail"
              class="resource-item-title"
              ellipsis
              tooltip
            />
            <Tag>{{ props.data.clusterName }}</Tag>
            <Tag v-if="props.data.namespace">{{ props.data.namespace }}</Tag>
            <span>{{ relativeTime(props.data.createdAt) }}</span>
          </Space>
          <Space class="operate">
            <template v-for="item in actionList">
              <Poptip
                v-if="item === EnumAction.Shell || item === EnumAction.Log"
                :key="item"
                :popper-class="`${!props.data.isAuthorized ? 'unauthorized-popper-wrapper' : ''}`"
                :class="`divider-item ${!props.data.isAuthorized ? ' unauthorized-btn' : ''}`"
                trigger="hover"
                transfer
                @on-popper-show="onContainersPopperShow"
                :width="542"
              >
                <LinkButton
                  :class="`divider-item ${!props.data.isAuthorized ? ' unauthorized-btn' : ''}`"
                  :text="item"
                />
                <template #content>
                  <Table
                    :loading="containersLoading"
                    :columns="CONTAINER_COLUMNS"
                    size="small"
                    :width="510"
                    :height="144"
                    :data="containers"
                  >
                    <template #state="{ row }">
                      <span :class="`container-state-${row.state.toLowerCase()}`">
                        {{ row.state }}
                      </span>
                    </template>
                    <template #startedAt="{ row }">{{ dayjs(row.startedAt).format('YYYY-MM-DD HH:mm:ss') }}</template>
                    <template #ops="{ row }">
                      <LinkButton
                        :class="`divider-item ${!props.data.isAuthorized ? ' unauthorized-btn' : ''}`"
                        :text="item"
                        @click="() => onActionBtnClick(item, row.name)"
                      />
                    </template>
                  </Table>
                </template>
              </Poptip>
              <LinkButton
                v-else
                :key="item"
                :class="`divider-item ${!props.data.isAuthorized ? ' unauthorized-btn' : ''}`"
                :text="item"
                @click="() => onActionBtnClick(item)"
              />
            </template>
          </Space>
        </div>

        <Tooltip
          trigger="hover"
          transferClassName="ellipsis-tooltip-wrapper"
          transfer
          placement="bottom-start"
          theme="light"
        >
          <Space class="desc">
            <template v-for="[key, value] in Object.entries(props.data.descriptions)">
              <span :key="key" class="divider-item">
                <span class="key">{{ key }}</span>
                <span class="value">{{ value }}</span>
              </span>
            </template>
          </Space>

          <template #content>
            <template v-for="[key, value] in Object.entries(props.data.descriptions)">
              <p :key="key" class="break-word">- {{ key }} : {{ value }}</p>
            </template>
          </template>
        </Tooltip>
      </Space>
      <RelativeResourceModal v-if="!props.isRelative" v-model="relativeModalVisible" :data="props.data" />
    </Space>
  </Poptip>
</template>

<style lang="less" scoped>
.unauthorized-resource-item-wrapper {
  cursor: not-allowed;
  position: relative;

  &,
  .value {
    color: #999;
  }
  &::after {
    content: '';
    width: 100%;
    position: absolute;
    background: #9d9d9d30;
    height: 100%;
    top: 0;
    left: 0;
    cursor: not-allowed;
  }
}
.resource-item-wrapper {
  border-radius: 4px;
  border: 1px solid #eee;
  padding: 8px;
  height: 66px;
  &,
  /deep/.ivu-poptip-rel {
    width: 100%;
  }
  &:hover {
    box-shadow: 0px 0px 4px 0 #dedede;
  }
  .type {
    width: 80px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
    line-height: 48px;
    text-align: center;
  }
  > .ivu-divider {
    height: 50px;
  }
  .content {
    width: ~'calc(100% - 80px - 16px)';
    .ivu-tooltip {
      width: fit-content;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .resource-item-title {
        font-weight: 600;
        max-width: 30vw;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .space-component {
        align-items: center;
      }
      .operate {
        width: auto;
      }
      .ivu-tag {
        cursor: auto;
      }
    }
  }
  .desc {
    color: #7f7f7f;
    white-space: nowrap;
    overflow: hidden;
    max-width: ~'calc(80vw - 528px)';
    > span {
      display: inline-flex;
    }
    .key {
      margin-right: 8px;
    }
    .value {
      display: inline-block;
      max-width: 254px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .divider-item {
    position: relative;
    &.unauthorized-btn {
      cursor: not-allowed;
    }
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: 16px;
      top: 0;
      right: -8px;
      position: absolute;
      background: #e8eaec;
    }
  }
}

.container-state-running {
  color: #1abe6b;
}
.container-state-terminated {
  color: #ed4041;
}
.container-state-waiting {
  color: #515a6e;
}
.break-word {
  white-space: pre-wrap;
  max-width: 500px;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
<style>
.hidden-poptip {
  display: none;
}
</style>
