<template>
  <div>
    <Card style="margin-bottom: 16px" :bordered="false">
      <Input
        ref="input"
        v-model="page.search"
        clearable
        placeholder="搜索 Name"
        search
        enter-button
        style="width: 100%; margin-bottom: 16px"
        @on-search="handleSearch"
        @on-clear="handleClear"
      />

      <Space class="operation">
        <Button size="small" type="primary" icon="ios-add" @click="onCreate">创建</Button>
        <Button size="small" type="primary" ghost icon="ios-copy" @click="() => batchCopyModalVisible = true">批量复制</Button>
        <Button size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable">刷新</Button>

        <Checkbox class="checkbox" v-model="labelSelectorCtrl"> 标签过滤 </Checkbox>

        <Space direction="vertical" v-if="labelSelectorCtrl">
          <div>
            <Input style="width: 200px" v-model="firstLabelKey"></Input>
            <span style="font-weight: bold; margin: 0 16px 0 16px"> = </span>
            <Input style="width: 200px" v-model="firstLabelValue"></Input>
          </div>

          <div v-for="(item, idx) in labelSelectorList" :key="idx">
            <Input style="width: 200px" v-model="item.key"></Input>
            <span style="font-weight: bold; margin: 0 16px 0 16px"> = </span>
            <Input style="width: 200px" v-model="item.value"></Input>
            <Button type="text" size="small" style="margin-left: 8px" @click="handleLabelSelectorListDelete(idx)">
              删除
            </Button>
          </div>

          <Button
            v-if="labelSelectorCtrl"
            class="label-selector-button"
            @click="handleLabelSelectorListAdd"
            type="text"
            size="small"
            icon="ios-add-circle"
          >
            添加
          </Button>
        </Space>
      </Space>
    </Card>
    <Card :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        placement="top"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <resource-form
      resourceType="deployment"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :onSubmitCallBack="this.reloadTable"
      forbiddenForm
      isSkipCheck
      notSynchronizeToUnifiedCluster
    />
    <view-yaml
      resourceType="deployment"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      notSynchronizeToUnifiedCluster
    />
    <Modal title="调整副本数" width="15" v-model="replicaCountModal">
      <InputNumber :max="1000" :min="0" v-model="replicaCountModalData.count" style="width: 100%" />
      <template #footer>
        <Button @click="() => (replicaCountModal = false)">取消</Button>
        <Button type="primary" @click="onSetReplicaCount">确认</Button>
      </template>
    </Modal>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="Deployment" />
  </div>
</template>

<script>
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { color } from '@/libs/consts'
import { relativeTime } from '@/libs/tools'
import { ApiV2DeploymentList, ApiV2DeploymentRestart } from '@/api/k8s/namespace/app/app'
import { ApiSecretDelete } from '@/api/k8s/namespace/secret'
import LinkButton from '@/components/link-button'
import { ViewYaml } from '@/components/yaml'
import { Space, Ellipsis } from '@/components'
import { EnumFormStatus, ResourceForm } from '@/components/resource-form'
import { usePost, useDelete, useGet } from '@/libs/service.request'
import useSingleK8SService from '@/libs/useSingleK8SService'
import Config from '@/config'
import { BatchCopy, EnumIstioResourceType } from '@/domain/Resource'

const SERVICE_ENTRY = { resource: 'deployments', group: 'apps', version: 'v1' }
export default {
  name: 'app-deployment',
  components: { ViewYaml, Space, ResourceForm, BatchCopy },
  setup() {
    const { K8SKey } = useSingleK8SService()
    return {
      K8SKey
    }
  },
  data() {
    return {
      styles: {
        overflow: 'unset',
        position: 'static'
      },
      selectedVersionId: null,
      historyVersionList: [],
      historyVersionSelected: '',

      loading: {
        table: false
      },
      columns: [
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          tooltipTheme: 'light',
          minWidth: 300,
          render: (h, params) => {
            let renderList = [
              h(
                'span',
                {
                  style: {
                    color: color.primary,
                    fontWeight: 'bold',
                    cursor: 'pointer'
                  },
                  on: {
                    click: async () => {
                      let r = this.$router.resolve({
                        path: '/kubernetes/namespace/deployment-detail',
                        query: {
                          clusterId: this.currentClusterId,
                          namespace: this.currentNamespace,
                          deployment: params.row.name,
                          cluster: this.currentCluster,
                          uuid: params.row.uuid
                        }
                      })
                      window.open(r.href, '_blank')
                    }
                  }
                },
                params.row.name
              )
            ]

            if (params.row.restartStatus) {
              renderList.push(
                h(
                  'span',
                  {
                    style: {
                      marginLeft: '4px',
                      color: '#ed4014'
                    }
                  },
                  '[重启或扩缩容中...]'
                )
              )
            }
            return h('div', {}, renderList)
          }
        },
        {
          title: 'Ready',
          key: 'ready',
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center',
          width: 100
        },
        {
          title: 'Available',
          key: 'available',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 100
        },
        {
          title: 'Label',
          key: 'label',
          tooltip: true,
          tooltipTheme: 'light',
          minWidth: 100,
          render: (h, params) => {
            // const label = params.row.label?.split(',') ?? []
            return params.row.label
              ? h(Ellipsis, {
                  scopedSlots: {
                    default: () => params.row.label,
                    //   label?.length > 8
                    //     ? h('div', [
                    //         label.slice(0, 8)?.map((i) => h(Ellipsis, { props: { type: 'text' } }, i)),
                    //         h('div', '...')
                    //       ])
                    //     : label?.map((i) => h('div', i)),
                    content: () => params.row.label?.split(',')?.map((i) => h('p', `- ${i}`))
                  }
                })
              : h('div', '-')
          }
        },
        {
          title: 'Age',
          key: 'age',
          tooltip: true,
          tooltipTheme: 'light',
          width: 150,
          render: (h, params) => {
            return h('div', {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 300,
          align: 'center',
          render: (h, params) => {
            console.log(params.row.isSubscribe)
            return h(Space, { props: { justify: true } }, [
              h(LinkButton, {
                props: { text: 'YAML' },
                on: {
                  click: () => this.onOpenYaml(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: '编辑' },
                on: {
                  click: () => this.onEdit(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: '调整副本' },
                on: {
                  click: () => this.onOpenReplicaCountModal(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: params.row.isSubscribe ? '退订' : '订阅' },
                on: {
                  click: () => this.onSubscribeOrNot(params.row)
                }
              }),

              h(LinkButton, {
                props: { text: '滚动重启', type: 'danger' },
                on: {
                  click: () => this.onRolloutRestart(params.row.name)
                }
              }),
              h(LinkButton, {
                props: { text: '删除', type: 'danger' },
                on: {
                  click: () => this.onDelete(params.row)
                }
              })
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        search: '',
        total: 0,
        clusterId: '',
        namespace: '',
        labelSelector: []
      },
      labelSelectorCtrl: false,
      labelSelectorList: [],
      firstLabelKey: '',
      firstLabelValue: '',
      viewYamlVisible: false,
      formEntity: '',
      yamlHistoryParams: '',
      formVisible: false,
      formStatus: '',
      yamlInitData: '',
      replicaCountModal: false,
      replicaCountModalData: {},
      batchCopyModalVisible: false
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  watch: {
    K8SKey() {
      this.reloadTable()
    }
  },
  methods: {
    onOpenYaml(record) {
      this.formEntity = {
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        resourceName: record.name,
        ...SERVICE_ENTRY
      }
      this.yamlHistoryParams = {
        kind: 'Deployment',
        uuid: record.uuid
      }
      this.viewYamlVisible = true
    },
    makeLabelSelectorList() {
      let tmpSelectorList = []
      if (this.labelSelectorCtrl) {
        tmpSelectorList.push({
          key: this.firstLabelKey,
          op: '=',
          value: this.firstLabelValue
        })
        this.labelSelectorList.forEach((item) => {
          item.op = '='
          tmpSelectorList.push(item)
        })
      }
      this.page.labelSelector = tmpSelectorList
    },
    handleLabelSelectorListAdd() {
      this.labelSelectorList.push({
        key: '',
        op: '',
        value: ''
      })
    },
    handleLabelSelectorListDelete(idx) {
      delete this.labelSelectorList.splice(idx, 1)
    },
    onCreate() {
      console.log('onCreate')
      this.formVisible = true
      this.formStatus = EnumFormStatus.Blank
      this.formEntity = {
        ...SERVICE_ENTRY
      }
      this.yamlInitData = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: 必须修改
  namespace: ${this.currentNamespace}
spec:`
    },
    onEdit(record) {
      console.log('onEdit')
      this.formVisible = true
      this.formStatus = EnumFormStatus.Edit
      this.formEntity = {
        ...SERVICE_ENTRY,
        resourceName: record.name
      }
    },
    onDelete(record) {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认删除 ${record.name}？`,
        loading: true,
        onOk: async () => {
          const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteDeployment}`, {
            params: {
              namespace: this.currentNamespace,
              clusterId: this.currentClusterId,
              resourceName: record.name
            }
          })
          if (res.success) {
            this.reloadTable()
            this.$Modal.remove()
            this.$Message.success('删除成功！')
          } else {
            this.$Modal.remove()
          }
        }
      })
    },
    onRolloutRestart(name) {
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>对 <b>${name}</b> 发起滚动重启, 可刷新界面查看重启状态；</p>`,
        loading: true,
        onOk: async () => {
          await this.rolloutRestart(name)
          this.reloadTable()
          this.$Modal.remove()
        }
      })
    },
    async onSubscribeOrNot(record) {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认${record.isSubscribe ? '退订' : '订阅'} ${record.name}？`,
        loading: true,
        onOk: async () => {
          const res = await usePost(
            `${Config.Api.Base}${
              record.isSubscribe ? Config.Api.UnsubscribeDeployment : Config.Api.SubscribeDeployment
            }`,
            {
              clusterId: this.currentClusterId,
              namespace: this.currentNamespace,
              name: record.name
            }
          )
          if (res.success) {
            this.$Message.success(`${record.isSubscribe ? '退订' : '订阅'}成功！`)
            this.reloadTable()
            this.$Modal.remove()
          } else {
            this.$Modal.remove()
          }
        }
      })
    },
    async onOpenReplicaCountModal(record) {
      const count = await this.getReplicaCount(record.name)
      this.replicaCountModal = true
      this.replicaCountModalData = {
        count,
        name: record.name
      }
    },
    async getReplicaCount(deployment) {
      const res = await useGet(`${Config.Api.Base}${Config.Api.GetDeploymentReplicasCount}`, {
        params: { clusterId: this.currentClusterId, namespace: this.currentNamespace, name: deployment }
      })

      return res.data?.data ?? 0
    },
    onSetReplicaCount() {
      if (this.replicaCountModalData.count === undefined) {
        this.$Message.warning('请先输入副本数')
        return
      }
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认调整副本数？`,
        loading: true,
        onOk: async () => {
          const res = await usePost(`${Config.Api.Base}${Config.Api.SetDeploymentReplicasCount}`, {
            clusterId: this.currentClusterId,
            namespace: this.currentNamespace,
            ...this.replicaCountModalData
          })
          if (res.success) {
            setTimeout(() => {
              this.$Message.success('修改副本数成功！')
              this.replicaCountModal = false
              this.reloadTable()
              this.$Modal.remove()
            }, 3000)
          }
        }
      })
    },
    async rolloutRestart(name) {
      await ApiV2DeploymentRestart({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: name
      })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, `重启失败, ${errorMessage(err)}`)
        })
    },
    async deleteRecord(name) {
      await ApiSecretDelete({
        clusterId: parseInt(this.currentClusterId),
        namespace: this.currentNamespace,
        name: name
      })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async reloadTable() {
      this.loading.table = true
      this.makeLabelSelectorList()
      await ApiV2DeploymentList({
        ...this.page,
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace
      })
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.loading.table = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    }
  },
  mounted() {
    this.reloadTable()
    this.$refs.input.focus({
      cursor: 'start'
    })
  }
}
</script>

<style scoped lang="less">
.operation {
  display: flex;
  align-items: baseline;
  .checkbox {
    width: 100px;
  }
  .label-selector-button {
    width: 60px;
  }
}
</style>
