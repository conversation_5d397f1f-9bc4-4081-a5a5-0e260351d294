import G6, { Item } from '@antv/g6'
import { ShapeAttrs } from '@antv/g-base'
import { ModelConfig } from '@antv/g6-core'
import { EnumNodeTypeLabel, StatusColors } from '../config'
import { EnumStatus, RouterItem } from '../type'
import EyeSvg from '../assets/eye.svg'
import LinkSvg from '../assets/link.svg'
import TableSvg from '../assets/table.svg'
import TriangleSvg from '../assets/triangle.svg'
import { getEllipsisText, getStringArrayByWidth, getStringWidth } from '../util'

export const NodeName = 'flow-rect'

const NODE_WIDTH = 240
const SPACE = 8
const ICON_SIZE = 16
const DESC_ITEM_HEIGHT = 24
const FONT_SIZE = 12
const STATE_HEIGHT = 6
const STROKE = '#CED4D9'
const TITLE_HEIGHT = FONT_SIZE + SPACE * 2
const SEARCH_COLOR = '#ff0000'

const ABBREVIATION_NODE_HEIGHT = 60
const ABBREVIATION_NODE_WIDTH = NODE_WIDTH
const ABBREVIATION_FONT_SIZE = 16

const MESSAGE_WRAPPER_ARROW_HEIGHT = 20
export const MESSAGE_GROUP_KEY = 'message-shape'
export const ABBREVIATION_GROUP_KEY = 'abbreviation-node-shape'
const SEARCH_NODE_KEY = 'search-shape'

export const getEllipsisTextNoticeKey = (key: string) => ({
  textKey: `ellipsis-text-notice-${key}`,
  backgroundKey: `ellipsis-text-notice-background-${key}`
})

export const calcNodeHeight = (item: RouterItem) => {
  const descItems = Object.keys({ ...(item.descriptions?.l1 ?? {}), ...(item.descriptions?.l2 ?? {}) }).length

  return TITLE_HEIGHT + DESC_ITEM_HEIGHT * descItems + (item.state?.status !== 'none' ? STATE_HEIGHT : 0)
}

// 渲染文本裁切后的悬浮提示
const renderEllipsisTextNotice = (group, setting: ShapeAttrs, text, key) => {
  const { textKey, backgroundKey } = getEllipsisTextNoticeKey(key)
  // 提示
  const textShape = group.addShape('text', {
    attrs: {
      ...setting,
      textBaseline: 'bottom',
      x: setting.x + SPACE,
      y: setting.y + setting.fontSize + SPACE / 2,
      text: text,
      fill: '#333'
    },
    name: textKey,
    visible: false
  })

  const keyShapeBBox = textShape.getBBox()

  group.addShape('rect', {
    attrs: {
      ...setting,
      width: keyShapeBBox.width + SPACE * 2,
      height: setting.fontSize + SPACE,
      radius: [2],
      fill: '#fff',
      shadowColor: '#cdcdcd',
      shadowBlur: 6,
      shadowOffsetX: 1,
      shadowOffsetY: 1
    },
    name: backgroundKey,
    visible: false
  })
  textShape.toFront()
}
const getKeyShapeOriginConfig = (cfg: ModelConfig & RouterItem) => {
  const height = calcNodeHeight(cfg)

  return {
    width: NODE_WIDTH,
    height: height,
    x: -NODE_WIDTH / 2,
    y: -height / 2,
    lineWidth: 1,
    fontSize: 12,
    fill: '#fff',
    radius: 4,
    stroke: STROKE
  }
}
export const registerNode = () => {
  G6.registerNode(
    NodeName,
    {
      shapeType: 'flow-rect',
      draw(cfg: ModelConfig & RouterItem, group) {
        const rectConfig = getKeyShapeOriginConfig(cfg)

        const rect = group.addShape('rect', {
          attrs: rectConfig,
          name: 'key-shape'
        })

        const rectBBox = rect.getBBox()
        const textConfig: ShapeAttrs = {
          textAlign: 'left',
          textBaseline: 'middle',
          fontSize: FONT_SIZE
        }

        // 渲染描述
        const renderDescription = (key, value, type: 'L1' | 'L2', currentY) => {
          const keyShape = group.addShape('text', {
            attrs: {
              ...textConfig,
              x: SPACE + rectConfig.x + SPACE / 2,
              y: currentY + FONT_SIZE,
              text: key,
              fontWeight: 600,
              fill: '#fff'
            },
            name: `desc-key-shape-${key}`
          })

          const keyShapeBBox = keyShape.getBBox()

          const keyBackgroundShape = group.addShape('rect', {
            attrs: {
              x: SPACE + rectConfig.x,
              y: currentY + SPACE / 2,
              width: keyShapeBBox.width + SPACE,
              height: ICON_SIZE,
              radius: [2],
              fill: type === 'L1' ? '#7198e8' : '#999999'
            },
            name: `desc-key-background-shape-${key}`
          })
          keyShape.toFront()

          const labelWidth = NODE_WIDTH - keyBackgroundShape.getBBox().width - SPACE * 3 - ICON_SIZE / 2

          const textKey = `desc-value-shape-${key}`
          group.addShape('text', {
            attrs: {
              ...textConfig,
              x: keyShapeBBox.maxX + SPACE,
              y: currentY + FONT_SIZE,
              text: getEllipsisText(value, labelWidth, FONT_SIZE),
              fill: '#7f7f7f',
              cursor: 'pointer'
            },
            name: textKey
          })

          renderEllipsisTextNotice(
            group,
            {
              x: keyShapeBBox.maxX + SPACE * 2,
              y: currentY - FONT_SIZE - SPACE,
              ...textConfig
            },
            value,
            textKey
          )

          return currentY + DESC_ITEM_HEIGHT
        }
        // ---Title
        // 类型
        const NodeType = group.addShape('text', {
          attrs: {
            ...textConfig,
            x: SPACE + rectConfig.x,
            y: -rectBBox.height / 2 + TITLE_HEIGHT / 2 + 1,
            text: EnumNodeTypeLabel[cfg.nodeType] ?? cfg.nodeType,

            fontWeight: 600,
            fill: '#2a7cc3'
          },
          name: 'node-type-shape'
        })
        const Table =
          cfg.operations?.table &&
          group.addShape('image', {
            attrs: {
              x: rectBBox.maxX - SPACE - ICON_SIZE,
              y: -(rectBBox.height / 2 - TITLE_HEIGHT / 2 + ICON_SIZE / 2),
              width: ICON_SIZE,
              height: ICON_SIZE,
              cursor: 'pointer',
              img: TableSvg
            },
            name: 'table-btn-shape'
          })

        const tableBBox = cfg.operations?.table
          ? Table?.getBBox()
          : {
              width: 0,
              minX: rectBBox.maxX - SPACE
            }

        const Link =
          cfg.operations?.link &&
          group.addShape('image', {
            attrs: {
              x: tableBBox.minX - ICON_SIZE,
              y: -(rectBBox.height / 2 - TITLE_HEIGHT / 2 + ICON_SIZE / 2),
              width: ICON_SIZE,
              height: ICON_SIZE,
              cursor: 'pointer',
              img: LinkSvg
            },
            name: 'link-btn-shape'
          })
        const linkBBox = cfg.operations?.link
          ? Link.getBBox()
          : {
              width: 0,
              minX: tableBBox.minX
            }
        // yaml
        const Yaml =
          cfg.operations?.view !== 'none' &&
          group.addShape('image', {
            attrs: {
              x: linkBBox.minX - ICON_SIZE,
              y: -(rectBBox.height / 2 - TITLE_HEIGHT / 2 + ICON_SIZE / 2),
              width: ICON_SIZE,
              height: ICON_SIZE,
              cursor: 'pointer',
              img: EyeSvg
            },
            name: 'yaml-btn-shape'
          })
        const yamlTypeBBox =
          cfg.operations?.view !== 'none'
            ? Yaml.getBBox?.()
            : {
                minX: linkBBox.minX
              }

        // 标题
        const LABEL_SHAPE_KEY = 'name-shape'
        const nodeTypeBBox = NodeType.getBBox()
        const labelWidth = Math.min(linkBBox.minX, yamlTypeBBox.minX, tableBBox.minX) - SPACE * 3 - nodeTypeBBox.maxX

        const notShowNamespaceNodeTypeArr = [
          EnumNodeTypeLabel.Domain,
          EnumNodeTypeLabel['Http.Route'],
          EnumNodeTypeLabel.Pod
        ]
        // const nodeName = notShowNamespaceNodeTypeArr.includes(cfg.nodeType) ? cfg.name : `${cfg.namespace}/${cfg.name}`
        const Label = group.addShape('text', {
          attrs: {
            ...textConfig,
            x: SPACE * 2 + rectConfig.x + nodeTypeBBox.width,
            y: -rectBBox.height / 2 + TITLE_HEIGHT / 2 + 1,
            text: getEllipsisText(cfg.name, labelWidth, textConfig.fontSize),
            fill: '#000',
            cursor: 'pointer'
          },
          name: LABEL_SHAPE_KEY
        })

        renderEllipsisTextNotice(
          group,
          {
            x: SPACE * 3 + rectConfig.x + nodeTypeBBox.width,
            y: rectConfig.y - textConfig.fontSize,
            ...textConfig
          },
          cfg.name,
          LABEL_SHAPE_KEY
        )

        // ---Title

        if (cfg.descriptions?.l1 && JSON.stringify(cfg.descriptions?.l1) !== '{}') {
          const L1Split = group.addShape('rect', {
            attrs: {
              x: -rectConfig.width / 2,
              y: -(rectConfig.height / 2) + TITLE_HEIGHT,
              width: rectConfig.width,
              height: 1,
              fill: '#d7d7d7'
            },
            name: 'description-L1-split-shape'
          })
          let currentY = L1Split.getBBox().maxY

          for (const [key, value] of Object.entries(cfg.descriptions.l1)) {
            currentY = renderDescription(key, value, 'L1', currentY)
          }
        }
        if (cfg.descriptions?.l2 && JSON.stringify(cfg.descriptions?.l2) !== '{}') {
          const L2Split = group.addShape('rect', {
            attrs: {
              x: -rectConfig.width / 2,
              y:
                -(rectConfig.height / 2) +
                TITLE_HEIGHT +
                (cfg.descriptions?.l1 ? DESC_ITEM_HEIGHT * Object.keys(cfg.descriptions.l1).length : 0),
              width: rectConfig.width,
              height: 1,
              fill: '#d7d7d7'
            },
            name: 'description-L2-split-shape'
          })
          let currentY = L2Split.getBBox().maxY

          for (const [key, value] of Object.entries(cfg.descriptions.l2)) {
            currentY = renderDescription(key, value, 'L2', currentY)
          }
        }

        if (cfg.state?.status && cfg.state?.status !== EnumStatus.none) {
          const State = group.addShape('rect', {
            attrs: {
              x: -rectConfig.width / 2,
              y: rectConfig.height / 2 - STATE_HEIGHT,
              width: rectConfig.width,
              height: STATE_HEIGHT,
              radius: [0, 0, 4, 4],

              fill: StatusColors[cfg.state.status]
            },
            name: 'state-shape'
          })
        }
        if (cfg.state?.status !== EnumStatus.none && cfg.state?.message?.length > 0) {
          const messageWidth = NODE_WIDTH - SPACE * 4

          const messageArr = getStringArrayByWidth(cfg.state.message, messageWidth, textConfig.fontSize)
          const messageWrapperHeight = textConfig.fontSize * (messageArr.length + 1) + SPACE * (messageArr.length + 2)

          const messageGroup = group.addGroup({ name: MESSAGE_GROUP_KEY, zIndex: 100 })
          const MessageWrapper = messageGroup.addShape('rect', {
            attrs: {
              x: -rectConfig.width / 2,
              y: -rectConfig.height / 2 - MESSAGE_WRAPPER_ARROW_HEIGHT - messageWrapperHeight,
              width: NODE_WIDTH,
              height: messageWrapperHeight,
              radius: 4,
              stroke: STROKE,
              fill: '#fff'
            },
            name: 'message-wrapper-shape'
          })

          const messageWrapperBBox = MessageWrapper.getBBox()
          const triangleShape = messageGroup.addShape('image', {
            attrs: {
              x: -MESSAGE_WRAPPER_ARROW_HEIGHT / 2,
              y: messageWrapperBBox.maxY - 1.1,
              width: MESSAGE_WRAPPER_ARROW_HEIGHT,
              height: MESSAGE_WRAPPER_ARROW_HEIGHT,
              cursor: 'pointer',
              img: TriangleSvg
            },
            name: 'triangle-shape'
          })

          // 类型
          const MessageType = messageGroup.addShape('text', {
            attrs: {
              ...textConfig,
              textBaseline: 'bottom',
              x: SPACE + rectConfig.x,
              y: messageWrapperBBox.minY + SPACE + textConfig.fontSize,
              text: cfg.state.status.slice(0, 1).toUpperCase() + cfg.state.status.slice(1) + ':',
              fill: StatusColors[cfg.state.status]
            },
            name: 'message-type-shape'
          })
          messageArr.map((i, index) => {
            messageGroup.addShape('text', {
              attrs: {
                ...textConfig,
                textBaseline: 'bottom',
                x: SPACE + rectConfig.x,
                y: messageWrapperBBox.minY + SPACE * (2 + index) + textConfig.fontSize * (2 + index),
                text: i,
                fill: '#7f7f7f'
              },
              name: `message-type-shape-${index}`
            })
          })

          messageGroup.hide()
        }
        // collapse rect
        if (cfg.children && cfg.children.length) {
          group.addShape('rect', {
            attrs: {
              x: rectConfig.width / 2 - ICON_SIZE / 2,
              y: -(ICON_SIZE / 2),
              width: ICON_SIZE,
              height: ICON_SIZE,
              stroke: STROKE,
              cursor: 'pointer',
              fill: '#fff',
              radius: 4
            },
            name: 'collapse-back',
            modelId: cfg.name
          })

          // collpase text
          group.addShape('text', {
            attrs: {
              x: rectConfig.width / 2,
              y: cfg.collapsed ? 1 : 0,
              textAlign: 'center',
              textBaseline: 'middle',
              text: cfg.collapsed ? '+' : '-',
              fontSize: ICON_SIZE,
              cursor: 'pointer',
              fill: '#7f7f7f'
            },
            name: 'collapse-text',
            modelId: cfg.name
          })
        }
        group.addShape('rect', {
          attrs: {
            width: rectConfig.width + 10,
            height: rectConfig.height + 10,
            x: -(rectConfig.width + 10) / 2,
            y: -(rectConfig.height + 10) / 2,
            stroke: SEARCH_COLOR,
            lineWidth: 2,
            shadowColor: SEARCH_COLOR,
            shadowBlur: 8,
            radius: 4
          },
          name: SEARCH_NODE_KEY,
          visible: false
        })

        this.drawLinkPoints(cfg, group)
        return rect
      },
      update(cfg: ModelConfig & RouterItem & { level?: 1 | 0 }, item: Item) {
        const group = item.getContainer()
        let abbreviationGroup = group?.cfg?.children?.filter((i) => i.cfg.name === ABBREVIATION_GROUP_KEY)?.[0]
        const keyShape = group.find((ele) => ele.get('name') === 'key-shape')
        const searchNode = group.find((ele) => ele.get('name') === SEARCH_NODE_KEY)

        if (cfg.level === 0) {
          group.get('children').forEach((child) => {
            if (child.get('name')?.includes('collapse')) return
            if (child.get('name') === 'key-shape') return
            child.hide()
          })
          keyShape.animate(
            {
              x: -ABBREVIATION_NODE_WIDTH / 2,
              y: -ABBREVIATION_NODE_HEIGHT / 2,
              width: ABBREVIATION_NODE_WIDTH,
              height: ABBREVIATION_NODE_HEIGHT,
              radius: 4,
              ...(cfg.state?.status && cfg.state?.status !== EnumStatus.none
                ? {
                    fill: StatusColors[cfg.state.status]
                  }
                : {
                    fill: '#fff',
                    shadowColor: '#f3f3f3',
                    shadowBlur: 2
                  }),
              opacity: 1
            },
            100
          )
          searchNode.animate(
            {
              width: ABBREVIATION_NODE_WIDTH + 10,
              height: ABBREVIATION_NODE_HEIGHT + 10,
              x: -(ABBREVIATION_NODE_WIDTH + 10) / 2,
              y: -(ABBREVIATION_NODE_HEIGHT + 10) / 2
            },
            100
          )

          if (!abbreviationGroup) {
            abbreviationGroup = group.addGroup({ name: ABBREVIATION_GROUP_KEY })

            const abbreviationNodeType = abbreviationGroup.addShape('text', {
              attrs: {
                fill: cfg.state?.status && cfg.state?.status !== EnumStatus.none ? '#fff' : '#2a7cc3',
                fontSize: ABBREVIATION_FONT_SIZE,
                x: -ABBREVIATION_NODE_WIDTH / 2 + SPACE,
                y: 10,
                text: EnumNodeTypeLabel[cfg.nodeType] ?? cfg.nodeType,
                fontWeight: 600
              },
              name: 'abbreviation-node-type'
            })

            const abbreviationNodeTypeBBox = abbreviationNodeType.getBBox()
            const abbreviationNodeNameKey = 'abbreviation-node-name'
            abbreviationGroup.addShape('text', {
              attrs: {
                fill: cfg.state?.status && cfg.state?.status !== EnumStatus.none ? '#fff' : '#333',
                fontStyle: 'normal',
                fontSize: ABBREVIATION_FONT_SIZE,
                x: abbreviationNodeTypeBBox.maxX + SPACE,
                y: 10,
                text: getEllipsisText(
                  cfg.name,
                  ABBREVIATION_NODE_WIDTH - SPACE * 4 - ICON_SIZE / 2 - abbreviationNodeTypeBBox.width,
                  ABBREVIATION_FONT_SIZE
                )
              },
              name: abbreviationNodeNameKey
            })

            renderEllipsisTextNotice(
              group,
              {
                x: abbreviationNodeTypeBBox.maxX + SPACE * 2,
                y: -ABBREVIATION_FONT_SIZE - SPACE * 2,
                fontSize: ABBREVIATION_FONT_SIZE
              },
              cfg.name,
              abbreviationNodeNameKey
            )

            const collapseRect = group.find((ele) => ele.get('name') === 'collapse-back')
            const collapseText = group.find((ele) => ele.get('name') === 'collapse-text')
            collapseRect?.toFront()
            collapseText?.toFront()
          } else {
            abbreviationGroup.show()
          }
          abbreviationGroup.animate({ opacity: 1 }, 100)
        } else {
          group.get('children').forEach((child) => {
            if (child.get('name')?.includes('collapse')) return
            if (child.get('name')?.includes('ellipsis-text-notice')) return
            if (child.get('name')?.includes('message')) return
            child.show()
          })
          const originKeyShapeAttr = { ...getKeyShapeOriginConfig(cfg) }
          keyShape?.animate(originKeyShapeAttr, {
            duration: 100
          })
          searchNode?.animate(
            {
              width: originKeyShapeAttr.width + 10,
              height: originKeyShapeAttr.height + 10,
              x: -(originKeyShapeAttr.width + 10) / 2,
              y: -(originKeyShapeAttr.height + 10) / 2
            },
            {
              duration: 100
            }
          )
          abbreviationGroup?.animate(
            { opacity: 0 },
            {
              duration: 100,
              callback: () => {
                abbreviationGroup.hide()
              }
            }
          )
        }

        const searchNodes = group.findAll((e) => e.get('name') === SEARCH_NODE_KEY)
        searchNodes?.map((search) => (item._cfg.states.includes('search') ? search.show() : search.hide()))
      },
      setState(name, value, item) {
        if (name === 'search') {
          const group = item.getContainer()
          const searchNodes = group.findAll((e) => e.get('name') === SEARCH_NODE_KEY)
          searchNodes?.map((search) => (value ? search.show() : search.hide()))
        } else if (name === 'collapse') {
          const group = item.getContainer()
          const collapseText = group.find((e) => e.get('name') === 'collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
      },
      getAnchorPoints() {
        return [
          [0, 0.5],
          [1, 0.5]
        ]
      }
    },
    'rect'
  )
}
