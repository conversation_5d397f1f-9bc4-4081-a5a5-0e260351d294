<template>
  <div>
    <Card>
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <div style="display: inline-flex; margin-bottom: 16px">
        <h4>水平自动伸缩</h4>
        <a
          style="font-size: 12px; color: #808695; margin-left: 20px"
          target="_blank"
          href="https://kubernetes.io/zh-cn/docs/tasks/run-application/horizontal-pod-autoscale/"
        >
          什么是 HorizontalPodAutoscaler
          <span style="font-size: 14px"> <Icon type="md-open" /><Icon type="md-help-circle" /> </span>
        </a>
      </div>
      <Row type="flex" style="margin-bottom: 16px">
        <Col span="24">
          <Tooltip placement="right" v-if="disableCreated" content="已经存在 HPA, 不允许再创建" transfer>
            <Button :disabled="disableCreated" icon="ios-add" type="primary" size="small">创建</Button>
          </Tooltip>
          <Button
            v-else
            type="primary"
            size="small"
            icon="ios-add"
            @click="
              () => {
                this.openEdit = true
                this.editTitle = '创建'
                this.editMode = 'create'
                this.editHpaName = this.sysName
              }
            "
            >创建</Button
          >
        </Col>
      </Row>
      <Table size="small" :loading="loadingTable" :columns="columns" :data="data"></Table>
    </Card>
    <div>
      <Drawer :title="yamlTitle" width="60" v-model="openYaml">
        <Alert show-icon>没有修改版本原因: 未从平台做过配置修改。</Alert>
        <div>
          <Select
            @on-change="setCurrentYaml"
            @on-clear="
              () => {
                this.handleYamlGet(this.currentResourceName)
              }
            "
            clearable
            v-model="currentYaml"
            placeholder="历史版本"
            style="width: 250px; margin-bottom: 16px"
          >
            <Option v-for="item in historyVersionList" :value="item.yaml" :key="item.id">{{ item.createdAt }}</Option>
          </Select>
        </div>
        <div style="max-height: 90%; overflow: auto">
          <yaml v-model="currentYaml" ref="refYaml" :forbiddenEdit="true" />
        </div>
      </Drawer>
    </div>
    <div>
      <Drawer :title="detailTitle" :closable="false" width="60" v-model="openDetail">
        <div v-if="openDetail">
          <hpa-detail
            v-if="detailObject !== undefined"
            :detail="detailObject"
            :cluster-id="clusterId"
            :namespace="namespace"
            :cluster-name="clusterName"
          ></hpa-detail>
        </div>
      </Drawer>
    </div>
    <div>
      <Drawer :title="editTitle" :mask-closable="false" width="50" v-model="openEdit" :styles="styles">
        <div>
          <Spin fix v-if="commitBtnLoading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <hpa-form
            ref="hpaFrom"
            :cluster-id="clusterId"
            :namespace="namespace"
            :create-or-update="editMode"
            :name="editHpaName"
            :yamlName="yamlEditName"
            :kind="editHpaKind"
            :is-workload-mode="true"
            :workload-name="workloadName"
            @createWithForm="createWithForm"
            @createWithYaml="createWithYaml"
            @updateWithForm="updateWithForm"
            @updateWithYaml="updateWithYaml"
            v-if="openEdit"
          ></hpa-form>
        </div>
        <div class="drawer-footer">
          <Button :loading="commitBtnLoading" style="margin-right: 16px" type="primary" @click="handleHpaFromCommit"
            >提交</Button
          >
          <Button @click="openEdit = false">取消</Button>
        </div>
      </Drawer>
    </div>
  </div>
</template>

<script>
import HpaForm from './hpa-form'
import HpaDetail from './hpa-detail'
import { Yaml } from '@/components'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { color } from '@/libs/consts'
import { relativeTime } from '@/libs/tools'
import { ApiResourceYamlGet, ApiResourceOpRecordList } from '@/api/k8s/resource'
import { ApiDeployHpaList } from '@/api/k8s/namespace/worload/statefulsetHpa'

import {
  ApiHpaDelete,
  ApiHpaFormCreate,
  ApiHpaFormUpdate,
  ApiHpaGet,
  ApiHpaYamlCreate,
  ApiHpaYamlUpdate
} from '@/api/k8s/namespace/hpa'
export default {
  name: 'hpa',
  components: { Yaml, HpaDetail, HpaForm },
  props: {
    loading: Boolean,
    sysName: String
  },
  watch: {
    loading() {
      console.log('触发更新 - hpa')
      this.spinShow = true
      this.reloadTable()
      this.spinShow = false
    }
  },
  data() {
    return {
      spinShow: false,
      mTitle: 'HPA (水平自动伸缩)',
      historyVersionList: [],
      openYaml: false,
      yamlTitle: '详情',
      currentYaml: 'apiVersion:\nkind:\nmetadata:\nspec:',
      openEdit: false,
      editTitle: '编辑',
      editMode: 'create',
      editHpaName: '',
      editHpaKind: '',
      yamlEditName: '',
      currentResourceName: '',
      detailTitle: '详情',
      detailObject: undefined,
      openDetail: false,
      loadingTable: false,
      columns: [
        {
          title: 'Name',
          key: 'name',
          fixed: 'left',
          tooltip: true,
          minWidth: 170,
          render: (h, params) => {
            return h(
              'div',
              {
                style: {
                  color: color.primary,
                  fontWeight: 'bold',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.openDetail = true
                    this.detailTitle = `详情 ${params.row.name}`
                    ApiHpaGet({
                      clusterId: this.clusterId,
                      namespace: this.namespace,
                      name: params.row.name
                    })
                      .then((res) => {
                        this.detailObject = res.data.data.data
                      })
                      .catch((err) => {
                        this.$Message.error(errorMessage(err))
                        throw err
                      })
                  }
                }
              },
              params.row.name
            )
          }
        },
        {
          title: 'Kind',
          key: 'kind',
          tooltip: true,
          align: 'center',
          width: 120
        },
        {
          title: 'Reference',
          key: 'scaleTargetRef',
          tooltip: true,
          minWidth: 200,
          render: (h, params) => {
            return h('div', {}, `${params.row.scaleTargetRef.kind}/${params.row.scaleTargetRef.name}`)
          }
        },
        {
          title: 'Targets',
          key: 'targets',
          tooltip: true,

          width: 160
        },
        {
          title: 'Min',
          key: 'minPods',
          tooltip: true,
          align: 'center',
          width: 100
        },
        {
          title: 'Max',
          key: 'maxPods',
          align: 'center',
          tooltip: true,
          width: 100
        },
        {
          title: 'Replicas',
          key: 'replicas',
          tooltip: true,
          width: 100,
          align: 'center'
        },
        {
          title: 'Age',
          key: 'age',
          tooltip: true,
          width: 150,
          render: (h, params) => {
            return h('div', {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 160,
          fixed: 'right',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    marginRight: '16px'
                  },
                  on: {
                    click: async () => {
                      this.openYaml = true
                      this.yamlTitle = `${params.row.name}`
                      this.fetchHistoryVersionList(params.row.uuid)
                      await this.handleYamlGet(params.row.name, params.row.kind)
                      this.currentResourceName = params.row.name
                      this.$nextTick(() => {
                        this.$refs.refYaml.refresh()
                      })
                    }
                  }
                },
                'YAML'
              ),
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    marginRight: '16px'
                  },
                  on: {
                    click: () => {
                      this.openEdit = true
                      this.editTitle = '编辑 ' + params.row.name
                      this.editMode = 'update'
                      this.editHpaName = params.row.name
                      this.editHpaKind = params.row.kind
                      this.yamlEditName = params.row.yamlEditName
                    }
                  }
                },
                '编辑'
              ),
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.error
                  },
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: `<p>确认删除: <b>${params.row.name}</b></p>`,
                        loading: true,
                        onOk: async () => {
                          await this.deleteRecord(params.row.name, params.row.kind)
                          setTimeout(() => {
                            this.reloadTable()
                          }, 500)
                          this.$Modal.remove()
                        }
                      })
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 100,
        total: 0,
        clusterId: '',
        namespace: '',
        workloadName: ''
      },
      commitBtnLoading: false,
      styles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      },
      clusterId: null,
      namespace: null,
      workloadName: null,
      clusterName: null,
      disableCreated: false
    }
  },
  methods: {
    setCurrentYaml(yaml) {
      console.log(yaml)
      this.$nextTick(() => {
        this.currentYaml = yaml
        this.$refs.refYaml.refresh()
      })
    },
    async deleteRecord(name, kind) {
      await ApiHpaDelete({
        clusterId: this.clusterId,
        namespace: this.namespace,
        name: name,
        kind: kind
      })
        .then((res) => {
          noticeSucceed(this, 'delete succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async fetchHistoryVersionList(uuid) {
      await ApiResourceOpRecordList({
        uuid: uuid,
        clusterId: this.clusterId,
        namespace: this.namespace,
        kind: 'HorizontalPodAutoscaler'
      })
        .then((res) => {
          this.historyVersionList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, `获取历史版本失败, ${errorMessage(err)}`)
          throw err
        })
    },
    async handleYamlGet(resourceName, kind) {
      let resource
      let group
      let version
      // if (kind === "多维度HPA") {
      //   resource = "scaledobjects"
      //   group = "keda.sh"
      //   version = "v1alpha1"
      // } else {
      resource = 'horizontalpodautoscalers'
      group = 'autoscaling'
      // version = 'v2beta2'
      version = 'v2'
      // }
      await ApiResourceYamlGet({
        resource: resource,
        group: group,
        version: version,
        cluster_id: this.clusterId,
        namespace: this.namespace,
        is_edit: false,
        resource_name: resourceName
      })
        .then((res) => {
          console.log(res.data.data)
          this.currentYaml = res.data.data.data
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
    },
    async reloadTable() {
      this.loadingTable = true
      this.page.clusterId = this.clusterId
      this.page.namespace = this.namespace
      this.page.name = this.workloadName
      await ApiDeployHpaList(this.page)
        .then((res) => {
          this.disableCreated = false
          this.data = res.data.data.data
          if (this.data.length !== 0) {
            this.disableCreated = true
          }
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.loadingTable = false
        })
    },
    handleHpaFromCommit() {
      this.$refs.hpaFrom.commitYamlOrForm()
    },
    async createWithForm(data) {
      this.commitBtnLoading = true
      ApiHpaFormCreate(data)
        .then((res) => {
          noticeSucceed(this, '创建成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `创建失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async updateWithForm(data) {
      console.log(JSON.stringify(data))
      this.commitBtnLoading = true
      ApiHpaFormUpdate(data)
        .then((res) => {
          noticeSucceed(this, '更新成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `更新失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async createWithYaml(data) {
      this.commitBtnLoading = true
      ApiHpaYamlCreate(data)
        .then((res) => {
          noticeSucceed(this, '创建成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `创建失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    async updateWithYaml(data) {
      this.commitBtnLoading = true
      ApiHpaYamlUpdate(data)
        .then((res) => {
          noticeSucceed(this, '更新成功')
          this.reloadTable()
          this.openEdit = false
        })
        .catch((err) => {
          noticeError(this, `更新失败. ${errorMessage(err)}`)
        })
        .finally(() => {
          this.commitBtnLoading = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    }
  },
  mounted() {
    let query = this.$route.query
    this.clusterName = query.cluster
    this.clusterId = query.clusterId
    this.namespace = query.namespace
    this.workloadName = query.name
    this.reloadTable()
  }
}
</script>

<style scoped></style>
