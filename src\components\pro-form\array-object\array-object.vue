<script lang="ts" setup>
import { arrayObjectProps, EnumArrayObjectModalStatus } from './type'

import LinkButton from '@/components/link-button'
import Space from '@/components/space'
import { computed, getCurrentInstance, nextTick, ref, set, useSlots, watch } from 'vue'
import { cloneDeep } from 'lodash-es'
// import { genNonDuplicateID } from '@/libs/tools'

interface Emits {
  (event: 'on-ok', status: string, data: any, index: number, onVisible: (visible) => void): void | boolean
  (event: 'on-delete', index: string): void | boolean
  (event: 'get-add-data'): object
  (event: 'on-add', data: any): void
  (event: 'on-edit', data: any): void
  (event: 'on-drag-drop', index1: any, index2: any): void
}

const props = defineProps(arrayObjectProps())
const emits = defineEmits<Emits>()
const slots = useSlots()

const MAX_WIDTH = 400
const MIN_WIDTH = 80

const { proxy } = getCurrentInstance()
const data = computed(() => {
  if (!props.data?.[props.name]) {
    // 解决当数据初始化为undefined时，数据更新不触发响应式的问题
    if (props.data) {
      set(props.data, props.name, props.initData ?? [])
    } else {
      console.warn(`ArrayObject初始化${props.name}时，发现注入的data异常，data为${props.data}`)
    }
  }
  return props.data?.[props.name] ?? []
})
const modal = ref({
  visible: false,
  status: EnumArrayObjectModalStatus.Edit,
  data: {} as any,
  index: 0
})

const columns = ref([])

watch(
  () => props.columns,
  async () => {
    // const valueKey = props.columns?.map((i) => i.key)?.join(';')
    // const currentColumnsKey = columns.value
    //   ?.filter((i) => i.key !== 'ops')
    //   ?.map((i) => i.key)
    //   ?.join(';')
    // if (valueKey !== currentColumnsKey) {
    // console.log(1111111111, props.columns, columns.value)
    const tempColumns = props.columns ? cloneDeep(props.columns) : []

    if (props.resizable) {
      tempColumns?.forEach((i, index, arr) => (arr[index] = { width: 180, ...i, resizable: true }))
    }
    tempColumns.push({
      title: 'Ops',
      key: 'ops',
      slot: 'ops',
      align: 'center',
      ...(props.opsWidth !== undefined ? { width: props.opsWidth } : { width: 140 }),
      ...(props.resizable ? { minWidth: props.opsWidth ?? 140, fixed: 'right' } : null)
    })
    columns.value = tempColumns
    // }
  },
  {
    immediate: true,
    deep: true
  }
)

const onAdd = () => {
  const value = { ...(props.getAddData?.() ?? {}) }
  emits('on-add', value)
  modal.value = {
    visible: true,
    status: EnumArrayObjectModalStatus.Create,
    data: value,
    index: data?.value ? (data.value instanceof Array ? data.value?.length : Object.keys(data.value ?? {}).length) : 0
  }
}
const onEdit = (record, index) => {
  const value = cloneDeep(record)
  emits('on-edit', value)
  modal.value = { visible: true, status: EnumArrayObjectModalStatus.Edit, data: value, index }
}
const onDelete = (index) => {
  proxy.$Modal.confirm({
    title: '提示',
    content: `是否确认删除数据？`,
    loading: true,
    onOk: async () => {
      console.log('删除')
      emits('on-delete', index)
      proxy.$Modal.remove()
    }
  })
}

const isAllowAdd = computed(() => {
  const length = data.value && data.value instanceof Object ? Object.keys(data.value).length : data.value.length
  return !props.max || length < props.max
})

const onOk = () => {
  let modalVisible = false
  emits('on-ok', modal.value.status, modal.value.data, modal.value.index, (visible) => {
    modalVisible = visible
  })
  modal.value.visible = modalVisible
}
</script>

<template>
  <div>
    <LinkButton
      :disabled="!isAllowAdd"
      @click="onAdd"
      :text="`添加${props.label}`"
      v-if="props.addButtonPosition === 'top'"
      style="margin-bottom: 8px"
    />

    <Alert v-if="slots.alter" show-icon>
      <slot name="alter" />
    </Alert>
    <Table
      :columns="columns"
      :data="data"
      v-if="!slots['custom-data']"
      :draggable="props.draggable"
      @on-drag-drop="
        (index1, index2) => {
          emits('on-drag-drop', index1, index2)
        }
      "
      @on-column-width-resize="
        (newWidth, oldWidth, column, event) => {
          if (newWidth > MAX_WIDTH) {
            set(columns[column._index], 'width', MAX_WIDTH)
          } else if (newWidth < MIN_WIDTH) {
            set(columns[column._index], 'width', MIN_WIDTH)
          }
        }
      "
      v-bind="{
        ...(props.resizable
          ? {
              border: true
            }
          : null)
      }"
    >
      <template #ops="{ row, index }">
        <Space justify>
          <LinkButton @click="() => onEdit(row, data.length)" text="复制" v-if="props.canCopy" />
          <LinkButton @click="() => onEdit(row, index)" text="编辑" v-if="props.canEdit" />
          <LinkButton @click="() => onDelete(index)" type="danger" text="删除" />
        </Space>
      </template>
      <template v-for="itemName in Object.keys($scopedSlots)" #[itemName]="{ row }">
        <slot :name="itemName" :row="row" />
      </template>
    </Table>
    <slot name="custom-data" :data="data" :onDelete="onDelete" :onEdit="onEdit"></slot>
    <LinkButton
      :disabled="!isAllowAdd"
      @click="onAdd"
      :text="`添加${props.label}`"
      v-if="props.addButtonPosition === 'bottom'"
    />
    <Modal
      class-name="vertical-center-modal"
      v-model="modal.visible"
      :mask-closable="false"
      :width="props.modalWidth"
      :title="`${modal.status === EnumArrayObjectModalStatus.Edit ? '编辑' : '添加'}${props.label}`"
    >
      <slot
        name="default"
        :record="modal.data"
        :index="modal.index"
        :status="modal.status"
        :visible="modal.visible"
      ></slot>
      <template #footer>
        <Button type="text" @click="() => (modal.visible = false)">取消</Button>
        <Button type="primary" @click="onOk">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
/deep/.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
    .ivu-modal-body {
      max-height: 70vh;
      overflow: auto;
    }
  }
}
</style>
