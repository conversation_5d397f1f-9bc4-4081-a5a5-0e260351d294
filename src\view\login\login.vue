<style lang="less">
@import './login.less';
</style>

<template>
  <div class="login">
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <Row type="flex" justify="center">
      <Col>
        <div class="login-con">
          <Row type="flex" align="middle" :gutter="5">
            <Col>
              <img
                src="/logo-login.png"
                style="margin-left: 20px; margin-right: 16px; width: 45px; height: 45px; margin-top: 7px"
              />
            </Col>
            <Col>
              <b style="font-size: 30px; color: #808695">{{ title }}</b>
            </Col>
          </Row>
          <Card icon="log-in" title="Sign-In">
            <div class="form-con">
              <login-form :btn-loading="loginBtnLoading" @on-success-valid="handleSubmit"></login-form>
            </div>
          </Card>
          <br />
          <Card>
            <div>
              请通过
              <!--              <a style="color: #2a7cc3; cursor: pointer" @click="openModal = true">Create an account.</a>-->
              <a style="color: #2a7cc3; cursor: pointer" :href="ywPlatform">运维平台</a>
              点击 "容器云平台" 登录
            </div>
          </Card>
        </div>
      </Col>
    </Row>
    <div>
      <Modal
        width="400"
        v-model="openModal"
        title="Sign-Up"
        ok-text="Commit"
        cancel-text="Cancel"
        @on-ok="handleSignUp"
      >
        <div>
          <Form ref="signUpForm" :model="signUpForm" :rules="signUpRules" @keydown.enter.native="handleOk">
            <FormItem prop="username" label="Username">
              <Input v-model="signUpForm.username"> </Input>
            </FormItem>
            <FormItem prop="nickName" label="Nick name">
              <Input v-model="signUpForm.nickName"> </Input>
            </FormItem>
            <FormItem prop="password" label="Password">
              <Input type="password" v-model="signUpForm.password"> </Input>
            </FormItem>
            <FormItem prop="passwordConfirm" label="Confirm password">
              <Input type="password" v-model="signUpForm.passwordConfirm"> </Input>
            </FormItem>
          </Form>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
import config from '@/config'
import LoginForm from '_c/login-form'
import { mapActions } from 'vuex'
import { errorMessage, getParams } from '@/libs/util'
import { ApiSignUp, ssoLogin } from '@/api/user'
export default {
  components: {
    LoginForm
  },
  data() {
    return {
      ywPlatform: process.env.VUE_APP_OPERATION_PLATFORM,
      title: 'Star-ConStack',
      loginBtnLoading: false,
      openModal: false,
      loading: false,
      signUpSubmit: false,
      signUpForm: {
        username: '',
        nickName: '',
        password: '',
        passwordConfirm: ''
      },
      signUpRules: {
        username: [{ required: true, message: 'Cannot be empty', trigger: 'blur' }],
        nickName: [{ required: true, message: 'Cannot be empty', trigger: 'blur' }],
        password: [{ required: true, message: 'Cannot be empty', trigger: 'blur' }],
        passwordConfirm: [{ required: true, message: 'Cannot be empty', trigger: 'blur' }]
      },
      spinShow: false
    }
  },
  methods: {
    ...mapActions(['handleLogin', 'getUserInfo']),
    async handleSubmit({ userName, password }) {
      this.loginBtnLoading = true
      await this.handleLogin({ userName, password })
        .then((res) => {
          this.getUserInfo().then((res) => {
            this.$router.push({
              name: this.$config.homeName
            })
          })
        })
        .catch((err) => {
          this.$Message.error('Login error. err:' + errorMessage(err))
          throw err
        })
        .finally(() => {
          setTimeout(() => {
            this.loginBtnLoading = false
          }, 2000)
        })
    },
    handleSignUp() {
      this.$refs.signUpForm.validate((valid) => {
        if (valid) {
          ApiSignUp(
            this.signUpForm.username,
            this.signUpForm.nickName,
            this.signUpForm.password,
            this.signUpForm.passwordConfirm
          )
            .then((res) => {
              this.$Message.success('Sign up succeed.')
            })
            .catch((err) => {
              this.$Message.error('Error: ' + errorMessage(err))
              throw err
            })
        } else {
          this.$Message.warning('需正确填写表单。')
        }
      })
    },
    handleSSO() {
      let paramsObj = getParams(window.location.href)
      let ticket = paramsObj['ticket']
      console.log(ticket)
      ssoLogin(decodeURIComponent(ticket))
        .then((res) => {
          const data = res.data.data
          console.log(data)
          this.$store.commit('setToken', data.token)
          this.$router.push({
            name: this.$config.homeName
          })
        })
        .catch((err) => {
          this.$Message.error('SSO login error. err:' + errorMessage(err))
        })
    }
  },
  mounted() {
    this.spinShow = true
    setTimeout(() => {
      this.spinShow = false
    }, 2500)
    this.handleSSO()
  }
}
</script>

<style></style>
