<script lang="ts" setup>
import { multiInputProps } from './type'

import LinkButton from '@/components/link-button'
import { computed, ref, watch } from 'vue'
import { genNonDuplicateArr, genNonDuplicateID } from '@/libs/tools'

const props = defineProps(multiInputProps())
const multiArr = ref<string[]>([])

const onAdd = () => {
  multiArr.value.splice(multiArr.value.length, 0, genNonDuplicateID())
  props.onAdd?.(multiArr.value.length)
}
const onDelete = (key: string, index: number) => {
  multiArr.value.splice(multiArr.value.indexOf(key), 1)
  props.onDelete?.(index)
}

watch(
  () => props.data,
  function () {
    const initArr = props.onInit?.()
    multiArr.value = initArr?.length ? initArr : genNonDuplicateArr(1)
  },
  {
    immediate: true
  }
)
const isAllowAdd = computed(() => !props.max || multiArr.value.length < props.max)
</script>

<template>
  <div>
    <LinkButton
      v-if="props.addButtonPosition === 'top'"
      :disabled="!isAllowAdd"
      @click="onAdd"
      :text="props.addLabel ?? '添加'"
      style="line-height: 10px"
    />
    <div v-for="(item, index) in multiArr" :key="item" class="multi-input-item">
      <slot name="default" :data="props.data" :index="index" :onDelete="() => onDelete(item, index)"></slot>
      <LinkButton v-if="!props.hiddenDefaultDeleteBtn" @click="() => onDelete(item, index)" type="danger" text="删除" />
    </div>
    <LinkButton
      v-if="props.addButtonPosition === 'bottom'"
      :disabled="!isAllowAdd"
      @click="onAdd"
      :text="props.addLabel ?? '添加'"
    />
  </div>
</template>

<style lang="less" scoped>
.multi-input-item:not(:last-child) {
  flex-wrap: nowrap;
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin-bottom: 16px;
  > a {
    margin: 0 16px;
  }
}
</style>
