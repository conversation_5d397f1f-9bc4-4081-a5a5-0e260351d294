import { ref, onMounted, getCurrentInstance, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import { EnumComponentType, YamlGVR } from '@/domain/Resource'
import useSingleK8SService from '@/libs/useSingleK8SService'

export interface PVC {
  namespace: string
  creationTimestamp: string
  hosts: string[]
  name: string
  uuid: string
  exportTo: string[]
  gateways: string[]
  isAllowEdit: boolean
}

export default function usePVCService(props: { type: EnumComponentType }) {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()
  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string; relativeName?: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const yamlInitData = ref<string>()

  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>({ ...YamlGVR['PVC'] })

  const onDelete = (record: PVC) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeletePVC}`, {
          params: {
            ...K8SInstance.value,
            name: record.name,
            namespace: record.namespace
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...YamlGVR['PVC'],
      ...K8SInstance.value
    }
    yamlInitData.value = `apiVersion:  
kind: PersistentVolumeClaim
metadata:
  name: 必须修改
  namespace: ${K8SInstance.value.namespace}
spec:`
  }
  const onEdit = (record: PVC) => {
    console.log('onEdit')

    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...K8SInstance.value,
      ...YamlGVR['PVC'],
      resourceName: record.name,
      namespace: record.namespace
    }
  }

  const onViewYaml = (record: PVC) => {
    formEntity.value = {
      ...K8SInstance.value,
      ...YamlGVR['PVC'],
      resourceName: record.name,
      namespace: record.namespace
    }
    yamlHistoryParams.value = {
      kind: 'PVC',
      uuid: record.uuid
    }
    viewYamlVisible.value = true
  }

  const getTableData: TableRequest = async (params) => {
    let path = ''
    let query = {}
    switch (props.type) {
      case EnumComponentType.Independent:
        path = Config.Api.GetPVCList
        query = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          page: params.pageIndex,
          size: params.pageSize,
          search: params.searchValue ?? ''
        }
        break

      case EnumComponentType.AssociatedDeployment:
        path = Config.Api.GetPVCListWidthDeployment
        query = {
          cluster_id: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          name: K8SInstance.value.relativeName
        }
        break
      default:
        path = Config.Api.GetPVCListWidthStatefulset
        query = {
          clusterId: K8SInstance.value.clusterId,
          namespace: K8SInstance.value.namespace,
          name: K8SInstance.value.relativeName
        }
        break
    }

    const res = await useGet<PageList<PVC[]>>(`${Config.Api.Base}${path}`, {
      params: query
    })

    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data:
        props.type === EnumComponentType.Independent
          ? res.data?.list ?? []
          : (res.data as unknown as { data: PVC[] }).data ?? []
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const initK8SInstance = () => {
    if (props.type === EnumComponentType.Independent) {
      K8SInstance.value = {
        namespace: store.state.k8s.currentNamespace,
        clusterId: store.state.k8s.currentClusterId
      }
    } else {
      K8SInstance.value = {
        namespace: proxy.$route.query.namespace as string,
        clusterId: proxy.$route.query.clusterId as string,
        relativeName:
          props.type === EnumComponentType.AssociatedDeployment
            ? (proxy.$route.query.deployment as string)
            : (proxy.$route.query.name as string)
      }
    }
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  return {
    getTableData,
    refObject,

    onEdit,
    onDelete,

    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,

    onSubmitSuccess,
    onCreate,
    yamlInitData
  }
}
