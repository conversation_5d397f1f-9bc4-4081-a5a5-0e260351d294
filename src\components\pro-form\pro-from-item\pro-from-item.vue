<script lang="ts" setup>
import { formItemProps, EnumFormItemControllerType } from './type'
import useFormItemService from './useFormItemService'
import { FormItem } from 'view-design'

const props = defineProps(formItemProps())
defineEmits(['input'])

const { isChosenCtl, chosenCtl, switchCtl, isDefaultShow, onSwitchChange, onChosenChange, contentClassName } =
  useFormItemService(props)
</script>

<template>
  <FormItem :prop="props.name" :class="`pro-form-item-${props.size}`" :style="{ width: 300 }">
    <template #label v-if="props.label">
      <div>
        <span>{{ props.label }}：</span>

        <Checkbox
          v-if="!props.readonly && props.mode === EnumFormItemControllerType.Switch"
          :value="switchCtl"
          @on-change="onSwitchChange"
        >
          开启
          <!-- {{ switchCtl ? '开启' : '关闭' }} -->
        </Checkbox>
      </div>
      <div class="inline-flex-wrapper">
        <Poptip
          always
          v-if="props.desc"
          trigger="hover"
          :content="props.desc"
          placement="left"
          transfer
          transfer-class-name="form-item-poptip"
        >
          <Icon type="ios-alert-outline" style="color: #2a7cc3; font-size: 14px; cursor: pointer; margin-right: 16px" />
        </Poptip>
        <Tooltip v-if="props.url" content="官方文档" placement="top-end">
          <a target="_blank" :href="props.url">
            <Icon type="ios-open-outline" style="cursor: pointer; font-size: 14px; color: #2a7cc3; margin-right: 16px">
            </Icon>
          </a>
        </Tooltip>
      </div>
    </template>
    <div :class="contentClassName" v-show="isDefaultShow">
      <RadioGroup
        v-if="!props.readonly && isChosenCtl"
        :value="chosenCtl"
        @on-change="onChosenChange"
        class="pro-form-item-radio"
      >
        <Radio v-for="item in props.chosenOptions" :key="item.value" :label="item.value" :disabled="item.disabled">{{
          item.label
        }}</Radio>
      </RadioGroup>

      <slot name="default" :data="props.data"></slot>
      <template v-for="slotName in Object.keys($slots)">
        <slot :name="slotName" :data="props.data" v-if="chosenCtl === slotName"></slot>
      </template>
    </div>
  </FormItem>
</template>

<style lang="less" scoped>
.pro-form-item-sm {
  width: 300px;
}
.pro-form-item-md {
  width: 450px;
}
.pro-form-item-lg {
  width: 600px;
}

.ivu-form-item {
  margin-bottom: 16px;
  &.ivu-form-item-error {
    margin-bottom: 24px;
  }
}
/deep/.ivu-form-item-label {
  width: 100%;
  display: flex;
  //   justify-content: space-between;
  align-items: center;
  padding: 0 0 8px 0;
  float: unset;
  > div {
    display: flex;
    align-items: center;
    > .ivu-checkbox-wrapper {
      height: 14px;
    }
  }
}
.pro-form-item-background-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #f3f4f8;
  border-radius: 4px;
}
.pro-form-item-border-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
}
.pro-form-item-dash-border-content {
  .ivu-form-item {
    padding: 16px;
    border: 1px dashed #dcdee2;
    background: #fff;
  }
}

/deep/.form-item-poptip {
  color: red;
  /deep/.ivu-poptip-inner {
    max-width: 350px;
    white-space: pre-line;
  }
}

.pro-form-item-radio {
  margin-bottom: 8px;
}
</style>

<style lang="less">
.form-item-poptip {
  .ivu-poptip-inner {
    max-width: 350px;
    white-space: pre-line;
  }
}
.inline-flex-wrapper {
  margin-left: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  .ivu-poptip {
    width: 32px;
    height: 14px;
  }
}
</style>
