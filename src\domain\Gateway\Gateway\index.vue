<script lang="ts" setup>
import { set } from 'vue'
import Space from '@/components/space'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import { DetailDrawer, DetailCard } from '@/components/detail-drawer'
import { ResourceForm, EnumFormStatus } from '@/components/resource-form'
import {
  ProFormSelect,
  ProFormText,
  ProFormItem,
  ArrayObject,
  EnumFormItemControllerType,
  ProFormRadio
} from '@/components/pro-form'
import { formatEnumToLabelValue, EnumProtocol, EnumMode } from './enum'

import { TABLE_COLUMNS, SERVER_TABLE_COLUMNS, Gateway_DETAIL_CONFIG, HOSTS_TABLE_COLUMNS } from './setting'
import useGatewayService from './useGatewayService'
import { RelativeVirtualService } from '../index'

export type Props = {
  initFlag: boolean
  resourceName: string
  namespace: string
  clusterId: string
  clusterName: string
}

const props = defineProps<Props>()

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  formInitData,
  onSubmitServerForm,
  onSubmitSuccess,
  onViewDetail,
  viewDetailVisible,
  onInitDetailFormat,
  tlsCredentialNameList,
  credentialNameModal,
  virtualServiceModal,
  onOpenCreateCredentialNameModal,
  onCreateCredentialName,
  onEditVS,
  hostList
} = useGatewayService(props)
</script>

<template>
  <div>
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      :action-ref="refObject"
      border
      row-key="uid"
      :on-create="onCreate"
      alert="如果当前网关Namespace不是istio-ingress，则并非为统一网关，平台不支持变更，请联系管理员迁移！"
      :search="[
        { value: 'DNS', label: '域名' },
        { value: 'Gateway', label: '资源名' }
      ]"
    >
      <template #name="{ row }">
        <link-button
          @click="() => onViewDetail(row)"
          :text="row.name"
          style="font-weight: 600"
          ellipsis
          :disabled="row.namespace !== props.namespace"
        />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button @click="() => onEdit(row)" text="编辑" :disabled="row.namespace !== props.namespace" />
          <link-button @click="() => onEditVS(row)" text="编辑路由" :disabled="row.namespace !== props.namespace" />
          <link-button
            @click="() => onDelete(row)"
            text="删除"
            type="danger"
            :disabled="row.namespace !== props.namespace"
          />
        </space>
      </template>
    </pro-table>

    <view-yaml
      resourceType="unified-gateway/gateway"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />

    <resource-form
      resourceType="unified-gateway/gateway"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :formInitData="formInitData"
      :onSubmitCallBack="onSubmitSuccess"
      notSynchronizeToUnifiedCluster
      :get-preview-params="
        () => ({
          deployName: props.resourceName,
          deployNamespace: props.namespace
        })
      "
    >
      <template #form="{ data, dataReloadFlag }">
        <ProFormText name="namespace" label="Namespace（命名空间）" :data="data" readonly />
        <ProFormText name="name" label="Name（名称）" :data="data" :readonly="formStatus === EnumFormStatus.Edit" />

        <ProFormItem
          name="servers"
          label="Server（主机列表）"
          :data="data"
          contentStyleMode="background"
          :dataReloadFlag="dataReloadFlag"
          desc="server列表。"
          url="https://istio.io/latest/docs/reference/config/networking/gateway/#:~:text=Required-,servers,-Server%5B%5D"
        >
          <ArrayObject
            label="服务"
            :data="data"
            name="servers"
            canEdit
            addButtonPosition="top"
            modalWidth="60vw"
            :columns="SERVER_TABLE_COLUMNS"
            @on-ok="(type, record, index) => onSubmitServerForm(data, type, record, index)"
            @on-delete="(index) => data.servers?.splice(index, 1)"
            @on-edit="formatTempHostsList"
          >
            <template #default="{ record, visible }">
              <Form>
                <ProFormSelect
                  name="hosts"
                  label="Hosts（主机）"
                  :data="record"
                  :options="hostList"
                  allowCreate
                  multiple
                  contentStyleMode="background"
                  desc="此网关公开的一个或多个主机。
                  虽然通常适用于HTTP服务，但它也可以用于使用TLS和SNI的TCP服务。
                  主机被指定为具有可选命名空间/前缀的 dnsName。dnsName 应使用 FQDN 格式指定，可以选择在最左侧的组件中包含通配符（例如，prod/*.example.com）。
                  将 dnsName 设置为 * 以从指定的命名空间（例如 prod/*）中选择所有虚拟服务主机。"
                  url="https://istio.io/latest/docs/reference/config/networking/gateway/#Server:~:text=No-,hosts,-string%5B%5D"
                />
                <ProFormItem
                  name="port"
                  label="Port（端口）"
                  :data="record"
                  contentStyleMode="background"
                  :dataReloadFlag="visible"
                  :mode="EnumFormItemControllerType.Switch"
                  desc="服务监听的端口。"
                  url="https://istio.io/latest/docs/reference/config/networking/gateway/#Server:~:text=Required-,port,-Port"
                  :init-data="{}"
                >
                  <template #default>
                    <div class="multi-item-wrapper">
                      <Input
                        placeholder="端口名称"
                        :value="record.port?.data?.name"
                        @on-change="(e) => set(record.port?.data, 'name', e.target.value)"
                      />
                      <Input
                        placeholder="端口号"
                        :value="record.port?.data?.number"
                        @on-change="(e) => set(record.port?.data, 'number', e.target.value)"
                      />
                      <Select
                        placeholder="端口协议"
                        filterable
                        transfer
                        :value="record.port?.data?.protocol"
                        @on-change="(val) => set(record.port?.data, 'protocol', val)"
                      >
                        <Option
                          v-for="item in formatEnumToLabelValue(EnumProtocol)"
                          :key="item.value"
                          :value="item.value"
                          >{{ item.label }}</Option
                        >
                      </Select>
                    </div>
                  </template>
                </ProFormItem>
                <ProFormItem
                  name="tls"
                  label="TLS （传输安全）"
                  :data="record"
                  contentStyleMode="background"
                  :dataReloadFlag="visible"
                  :mode="EnumFormItemControllerType.Switch"
                  desc="一组与 TLS 相关的选项，用于控制server的行为。
                  使用这些选项可控制是否应将所有 http 请求重定向到 https，以及要使用的 TLS 模式。"
                  url="https://istio.io/latest/docs/reference/config/networking/gateway/#ServerTLSSettings"
                  :init-data="{}"
                >
                  <ProFormItem
                    name="credentialName"
                    label="CredentialName （证书名）"
                    :data="record.tls?.data"
                    contentClassName="credential-name-wrapper"
                    :mode="EnumFormItemControllerType.Switch"
                    :dataReloadFlag="visible"
                    desc="保存 TLS 证书（包括 CA 证书）的密钥的名称。仅适用于 Kubernetes。"
                    url="https://istio.io/latest/docs/reference/config/networking/gateway/#ServerTLSSettings-TLSmode:~:text=No-,credentialName,-string"
                  >
                    <Select
                      placeholder="请选择或自动创建证书名"
                      transfer
                      filterable
                      :value="record.tls?.data?.credentialName?.data"
                      @on-change="
                        (val) => {
                          set(record.tls?.data?.credentialName, 'data', val)
                        }
                      "
                    >
                      <Option v-for="item in tlsCredentialNameList" :key="item.value" :value="item.value">{{
                        item.label
                      }}</Option>
                    </Select>
                    <link-button text="自动创建" @click="() => onOpenCreateCredentialNameModal(record)" />
                  </ProFormItem>
                  <ProFormSelect
                    name="mode"
                    label="Mode（模式）"
                    :data="record.tls?.data"
                    :dataReloadFlag="visible"
                    :options="formatEnumToLabelValue(EnumMode)"
                    desc="指示是否应使用 TLS 保护与此端口的连接。此字段的值确定如何强制实施 TLS。目前提供了常用的PASSTHROUGH，SIMPLE模式。如果需要支持其他的模式，请联系我们。"
                    url="https://istio.io/latest/docs/reference/config/networking/gateway/#ServerTLSSettings:~:text=No-,mode,-TLSmode"
                  />
                  <ProFormRadio
                    name="httpsRedirect"
                    label="HttpsRedirect（HTTP转发）"
                    :data="record.tls?.data"
                    isBoolean
                    :dataReloadFlag="visible"
                    desc="如果设置为 是,负载均衡器将为所有http连接发送301重定向,要求客户端使用 https."
                    url="https://istio.io/latest/docs/reference/config/networking/virtual-service/#Destination:~:text=No-,mirrorPercentage,-Percent"
                  />
                </ProFormItem>
              </Form>
            </template>
          </ArrayObject>
        </ProFormItem>
      </template>
    </resource-form>

    <detail-drawer
      resourceType="unified-gateway/gateway"
      :title="'Gateway 详情'"
      v-model="viewDetailVisible"
      :resource-entity="formEntity"
      :onInitFormat="onInitDetailFormat"
      :z-index="1100"
    >
      <template #default="{ data }">
        <detail-card :title="'基本信息'" :data="data" :config="Gateway_DETAIL_CONFIG">
          <template #labels="{ data }">
            <template v-for="(label, index) in data.labels">
              <Tag :key="index" color="#2a7cc3"> {{ label.key }}:{{ label.value }} </Tag>
            </template>
          </template>
        </detail-card>
        <detail-card :title="'关联Hosts'" :data="data">
          <template #default="{ data }">
            <Table style="margin-top: 16px" :columns="HOSTS_TABLE_COLUMNS" :data="data.hosts" />
          </template>
        </detail-card>
      </template>
    </detail-drawer>

    <Modal v-model="credentialNameModal.visible" title="创建证书">
      <Alert type="info" show-icon> 自动生成证书名字, 可按需再次修改 </Alert>
      <div>证书名：</div>
      <Input v-model="credentialNameModal.name" />

      <template #footer>
        <Button @click="credentialNameModal.visible = false">取消</Button>
        <Button type="primary" @click="onCreateCredentialName">提交</Button>
      </template>
    </Modal>

    <RelativeVirtualService
      v-model="virtualServiceModal.visible"
      :clusterId="props.clusterId"
      :clusterName="props.clusterName"
      :namespace="props.namespace"
      :gatewayName="virtualServiceModal.gatewayName"
    />
  </div>
</template>

<style lang="less" scoped>
.multi-item-wrapper {
  display: flex;
  flex: 1 0 0%;
  > div {
    flex: 1 0 0%;
    &:not(:last-child) {
      margin-right: 16px;
    }
  }
}
/deep/.credential-name-wrapper {
  display: flex;
  flex-direction: row;
  > a {
    margin-left: 16px;
    flex-shrink: 0;
  }
}
</style>
