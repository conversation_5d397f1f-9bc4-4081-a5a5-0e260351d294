import { Ref, getCurrentInstance, ref } from 'vue'

// import { UserInfoServiceProvide, UserInfoServiceReturn } from '../useUserInfoService'
import { downloadFile, json2yaml } from '@/libs/util'
import { useRequest } from 'vue-request'
import Config from '@/config'
import { useGet, usePost } from '@/libs/service.request'
import { List } from './type'

export default function useViewAuthService() {
  const authMode = ref('kube-config-proxy')

  const yamlVisible = ref(false)
  const yamlData = ref()
  const { proxy } = getCurrentInstance()
  const originKubectlProxyList = ref()
  const originKubectlWebShellList = ref()
  //   const originK8SList = ref()

  //   const { userInfo, loading } = inject(UserInfoServiceProvide) as UserInfoServiceReturn

  //   const viewerTableData = computed(() => userInfo.value?.view_auth?.map((i) => ({ auth: i })) ?? [])

  const openYaml = (record) => {
    yamlVisible.value = true
    yamlData.value = json2yaml(record.yaml)
  }

  //   const { loading: K8SListLoading, data: K8SList } = useRequest(
  //     () => {
  //       return useGet<{ data: { k8sAuth: Record<string, Record<string, Record<string, string[]>>> } }>(
  //         `${Config.Api.Base}${Config.Api.GetUserK8SList}`
  //       )
  //     },
  //     {
  //       formatResult: (res) => {
  //         const K8SList: { cluster: string; children: Record<string, string>[] }[] = []
  //         for (const [cluster, namespaceObj] of Object.entries(res.data.data.k8sAuth)) {
  //           const children: Record<string, string>[] = []
  //           for (const [namespace, namespaceChildren] of Object.entries(namespaceObj)) {
  //             for (const [kind, authorityList] of Object.entries(namespaceChildren)) {
  //               children.push({
  //                 namespace,
  //                 kind,
  //                 authority: authorityList.join(',')
  //               })
  //             }
  //           }
  //           K8SList.push({ cluster, children })
  //         }
  //         originK8SList.value = K8SList
  //         return K8SList
  //       }
  //     }
  //   )

  const { loading: kubectlProxyListLoading, data: kubectlProxyList } = useRequest(
    () => {
      return useGet<{ data: { cluster: Record<string, List[]>; namespace: Record<string, List[]> } }>(
        `${Config.Api.Base}${Config.Api.GetUserKubectlProxyList}`
      )
    },
    {
      formatResult: (res) => formatKubectlList(res.data.data, originKubectlProxyList)
    }
  )
  const { loading: kubectlWebShellListLoading, data: kubectlWebShellList } = useRequest(
    () => {
      return useGet<{ data: { cluster: Record<string, List[]>; namespace: Record<string, List[]> } }>(
        `${Config.Api.Base}${Config.Api.GetUserKubectlWebShellList}`
      )
    },
    {
      formatResult: (res) => formatKubectlList(res.data.data, originKubectlWebShellList)
    }
  )

  const formatKubectlList = (
    data: { cluster: Record<string, List[]>; namespace: Record<string, List[]> },
    ref: Ref
  ) => {
    const kubectlList: Record<string, List[]> = {}

    for (const [cluster, globalList] of Object.entries(data.cluster)) {
      kubectlList[cluster] = [...globalList]
    }
    for (const [cluster, namespaceList] of Object.entries(data.namespace)) {
      kubectlList[cluster] = [...namespaceList]
    }
    const res: { cluster: string; list: List[] }[] = []
    for (const [cluster, list] of Object.entries(kubectlList)) {
      res.push({ cluster, list })
    }
    ref.value = res
    return res
  }

  const onDownloadMergeConfig = () => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认下载配置合并数据？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${Config.Api.DownloadKubectlProxyMergeConfig}`,
          {},
          {
            skipErrorHandler: true,
            isReturnNativeResponse: true
          }
        )

        let fileName = ''
        if (res.response.headers['content-disposition']) {
          fileName = res.response.headers['content-disposition'].split(';')[1].split('=')[1]
        }
        downloadFile(fileName, res.response.data)
        proxy.$Modal.remove()
      }
    })
  }
  const onKubectlRebuild = (clusterId) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认重建权限？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${Config.Api.RebuildKubectlProxyConfigAuthRole}`,
          {
            userId: 0,
            clusterId
          },
          {
            skipErrorHandler: true,
            isReturnNativeResponse: true
          }
        )
        if (res.success) {
          proxy.$Message.success('重建成功')
        }
        proxy.$Modal.remove()
      }
    })
  }
  const onDownloadConfig = (clusterId) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认下载配置数据？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${Config.Api.DownloadKubectlProxyConfig}`,
          {
            clusterId
          },
          {
            skipErrorHandler: true,
            isReturnNativeResponse: true
          }
        )
        let fileName = ''
        if (res.response.headers['content-disposition']) {
          fileName = res.response.headers['content-disposition'].split(';')[1].split('=')[1]
        }
        downloadFile(fileName, res.response.data)
        proxy.$Modal.remove()
      }
    })
  }
  const onKubectlConsole = async (cluster_id, cluster_name) => {
    console.log(cluster_id)
    proxy.$Modal.confirm({
      title: '提示',
      content: '<p>确认连接 ? 若确认后请耐心等待环境准备。</p>',
      loading: true,
      onOk: async () => {
        const res = await useGet(`${Config.Api.Base}${Config.Api.GetExecPod}`, {
          params: { cluster_id }
        })
        if (res.success) {
          const r = proxy.$router.resolve({
            path: '/pod-console',
            query: {
              clusterId: cluster_id,
              cluster: cluster_name,
              namespace: res.data.namespace,
              pod: res.data.pod,
              container: res.data.container,
              priority: 'true',
              type: 'kubectl'
            }
          })
          window.open(r.href, '_blank')
        }
        proxy.$Modal.remove()
      }
    })
  }
  const onSearchKubectlProxyList = (val) => {
    if (val) {
      kubectlProxyList.value = originKubectlProxyList.value?.filter((i) => i.cluster.includes(val))
    } else {
      kubectlProxyList.value = [...originKubectlProxyList.value]
    }
  }
  const onSearchKubectlWebShellList = (val) => {
    if (val) {
      kubectlWebShellList.value = originKubectlWebShellList.value?.filter((i) => i.cluster.includes(val))
    } else {
      kubectlWebShellList.value = [...originKubectlWebShellList.value]
    }
  }
  //   const onSearchK8SList = (val) => {
  //     if (val) {
  //       K8SList.value = originK8SList.value?.filter((i) => i.cluster.includes(val))
  //     } else {
  //       K8SList.value = [...originK8SList.value]
  //     }
  //   }

  return {
    authMode,
    // viewerTableData,

    // K8SList,
    // K8SListLoading,
    kubectlProxyListLoading,
    kubectlProxyList,
    kubectlWebShellListLoading,
    kubectlWebShellList,

    openYaml,
    yamlVisible,
    yamlData,

    onKubectlRebuild,
    onDownloadConfig,
    onDownloadMergeConfig,
    onKubectlConsole,

    onSearchKubectlProxyList,
    onSearchKubectlWebShellList
    // onSearchK8SList
  }
}
