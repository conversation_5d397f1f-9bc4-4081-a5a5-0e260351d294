<script setup lang="ts">
import Draggable from 'vuedraggable'
import Space from '@/components/space'
import usePodLogService from './usePodLogService'
const {
  loading,
  namespace,
  pod,
  workloadName,
  container,
  onOpenSearchModal,
  searchObj,
  isNormalConnected,
  setWSTimeout,
  handleLineChange,
  initLine,
  isConnected,
  autoFollow,
  handleAutoFollow,
  autoWrap,
  handleStop,
  searchModalVisible,
  searchFormRef,
  tempSearchObj,
  searchFilterObj,
  historyParamsList,
  searchViewFormRef,
  searchViewModel,
  showErrorMsg,
  searchFilterErrorMsg,
  handleSearchFilterChange,
  onMatchModeChange,
  onOpenSearchViewModal,
  onSearchViewSave,
  onSearchViewClick,
  onSearchViewDelete,
  keywordList,
  onSearch,
  onKeywordCreate,
  handleToggleConnected,
  handleCreateCondition,
  onOpenSearchFilterModal,
  onRemoveCondition,
  logDomRef,
  searchFilterModal,
  isSearchConnected,
  onResetToNormalStatus,
  downloadModal,
  downloadLine,
  onDownloadLog,
  onClearLog,
  onHistoryParamsListVisible
} = usePodLogService()
</script>

<template>
  <div class="log-wrapper">
    <Spin fix v-if="loading">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <div class="logs-header">
      <Space :size="8">
        <span class="header-title" :title="`日志 ${namespace} / ${pod || workloadName} / ${container}`"
          >日志 {{ namespace }} / {{ pod || workloadName }} / {{ container }}</span
        >

        <div @click="onOpenSearchModal" class="log-search-btn">
          <Poptip trigger="hover" :disabled="!searchObj.conditions.length">
            <div slot="content" class="search-filter-condition">
              <div v-for="(item, index) in searchObj.conditions" :key="index" class="search-filter-condition-item">
                <span :style="{ color: item.type === 'Inclusion' ? '#1abe6b' : '#ed4014' }">
                  {{ item.type === 'Inclusion' ? '包含' : '排除' }}
                </span>
                <Divider type="vertical" />
                <span>{{ !item.regex ? `通配 ${item.keywords.join(',')}` : `正则 /${item.regex}/` }}</span>
              </div>
            </div>
            <div class="log-search-btn-wrapper">
              <div class="log-search-btn-content">
                <template v-if="searchObj.conditions.length">
                  <span
                    v-for="(item, index) in searchObj.conditions"
                    :key="index"
                    :style="{ color: item.type === 'Inclusion' ? '#1abe6b' : '#ed4014' }"
                  >
                    [{{ (item.regex && `/${item.regex}/`) || item.keywords.join(',') }}]
                  </span>
                </template>
                <template v-else>
                  <span style="color: gray">请搜索日志</span>
                </template>
              </div>
            </div>
          </Poptip>

          <Space :size="8">
            <Button :disabled="!searchObj.conditions.length" size="small" type="primary" @click.stop="onSearch">
              <b>搜索</b>
            </Button>

            <Button size="small" @click.stop="onResetToNormalStatus">
              <b>重置</b>
            </Button>
          </Space>
        </div>
        <div>
          <span>初始化行数：</span>
          <Select @on-change="handleLineChange" v-model="initLine" style="width: 100px" size="small">
            <Option :value="100">100</Option>
            <Option :value="500">500</Option>
            <Option :value="1000">1000</Option>
            <Option :value="5000">5000</Option>
            <Option :value="10000">10000</Option>
            <Option :value="50000">50000</Option>
          </Select>
        </div>
        <Button type="primary" size="small" @click="() => (downloadModal = true)">
          <b>下载日志</b>
        </Button>
        <Button type="error" size="small" @click="onClearLog">
          <b>清空屏幕</b>
        </Button>
        <Checkbox v-model="autoWrap">自动换行</Checkbox>
        <Checkbox v-model="autoFollow" @on-change="handleAutoFollow">日志滚动</Checkbox>
        <Poptip placement="bottom" trigger="hover" transfer>
          <Icon type="md-help-circle" style="cursor: pointer; font-size: 18px; color: yellow" />
          <template #content>
            <!-- <p>10 分钟内不移动鼠标则自动关闭连接;</p> -->
            <p>输入Enter直接换行。</p>
          </template>
        </Poptip>
      </Space>
      <Space :size="8">
        <i-switch v-model="isConnected" size="small" @on-change="handleToggleConnected"></i-switch>
        <div v-if="isNormalConnected || isSearchConnected" class="connect connected">已连接</div>
        <div v-else class="connect not-connected">已断开</div>
      </Space>
    </div>
    <div :class="['logs-card', !autoWrap && 'logs-nowrap']" ref="logDomRef">
      <!-- <div class="logs-card" ref="logDomRef" @mousemove="setWSTimeout"> -->
      <p ref="line">xxxxxxx</p>
    </div>

    <Modal v-model="searchModalVisible" title="高级搜索" width="50vw" class-name="search-log-modal">
      <Space class="action-area">
        <Button icon="md-add" size="small" type="primary" @click="onOpenSearchFilterModal"> 过滤条件 </Button>
        <Button type="primary" size="small" ghost @click="onOpenSearchViewModal">保存搜索视图</Button>
        <Dropdown
          transfer
          transfer-class-name="log-search-history-list"
          trigger="click"
          @on-visible-change="onHistoryParamsListVisible"
          @on-click="onSearchViewClick(JSON.parse($event))"
        >
          <div class="history-search-btn">
            <img src="@/assets/collection-list.svg" />
            视图列表
          </div>

          <DropdownMenu slot="list">
            <DropdownItem v-for="(item, index) in historyParamsList" :key="index" :name="JSON.stringify(item)">
              <div class="keyword">{{ item?.name }}</div>
              <Icon type="md-trash" @click.stop="() => onSearchViewDelete(item)" />
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </Space>

      <div class="mode-title">过滤条件（可拖拽排序）</div>
      <Draggable v-model="tempSearchObj.conditions" :animation="200">
        <transition-group class="search-filter-condition mode-content">
          <div
            v-for="(item, index) in tempSearchObj.conditions"
            :key="`condition_${index}`"
            class="search-filter-condition-item"
          >
            <span :style="{ color: item.type === 'Inclusion' ? '#1abe6b' : '#ed4014' }">
              {{ item.type === 'Inclusion' ? '包含' : '排除' }}
            </span>
            <Divider type="vertical" />
            <span>{{ !item.regex ? `通配 ${item.keywords.join(',')}` : `正则 /${item.regex}/` }}</span>
            <Icon type="ios-close" :size="24" @click="onRemoveCondition(index)" />
          </div>
        </transition-group>
      </Draggable>

      <div class="mode-title">输出模式</div>
      <div class="mode-content">
        <Alert>
          <p><strong>滚动模式: </strong>根据条件高亮输出匹配的日志内容, 可选仅输出匹配行;</p>
          <p><strong>静态模式: </strong>根据条件高亮输出匹配的日志内容,仅输出匹配行及指定上下文行数;</p>
        </Alert>

        <Form
          ref="searchFormRef"
          :label-width="90"
          label-position="left"
          label-colon
          :model="tempSearchObj"
          @submit.native.prevent
        >
          <FormItem prop="mode" :label-width="0">
            <RadioGroup v-model="tempSearchObj.mode">
              <Radio label="scroll">滚动输出</Radio>
              <Radio label="static">静态输出</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="仅输出匹配行" prop="isMatch" v-if="tempSearchObj.mode === 'scroll'">
            <Checkbox v-model="tempSearchObj.isMatch">开启</Checkbox>
          </FormItem>
          <FormItem label="上下文本行数" prop="contextLine" v-else>
            <InputNumber
              v-model="tempSearchObj.contextLine"
              :disabled="!tempSearchObj.conditions.some((i) => i.type === 'Inclusion')"
              placeholder="请输入上下文本行数"
              style="width: 180px"
              :min="0"
            />
          </FormItem>
        </Form>
      </div>

      <template #footer>
        <Button type="text" @click="() => (searchModalVisible = false)">取消</Button>
        <Button type="primary" @click="onSearch">确定</Button>
      </template>
    </Modal>

    <Modal v-model="downloadModal" title="日志下载" width="15">
      <div class="flex-wrapper">
        <span>下载行数：</span>
        <Select v-model="downloadLine">
          <Option :value="100">100</Option>
          <Option :value="500">500</Option>
          <Option :value="1000">1000</Option>
          <Option :value="5000">5000</Option>
          <Option :value="10000">10000</Option>
          <Option :value="50000">50000</Option>
        </Select>
      </div>
      <template #footer>
        <Button type="text" @click="() => (downloadModal = false)">取消</Button>
        <Button type="primary" @click="onDownloadLog">确定</Button>
      </template>
    </Modal>

    <Modal v-model="searchFilterModal" title="过滤条件" width="45vw" class-name="search-filter-modal">
      <div class="mode-title">过滤方式</div>
      <RadioGroup v-model="searchFilterObj.type" class="mb16">
        <Radio label="Inclusion">包含（Inclusion）</Radio>
        <Radio label="Exclusion">排除（Exclusion）</Radio>
      </RadioGroup>

      <div class="mode-title">匹配模式</div>
      <div class="mode-content">
        <RadioGroup v-model="searchFilterObj.matchMode" @on-change="onMatchModeChange" class="mb16">
          <Radio label="keywords">通配</Radio>
          <Radio label="regex">正则表达式 RE2</Radio>
        </RadioGroup>

        <div :class="['ivu-form', 'ivu-form-item', 'ivu-form-item-required', showErrorMsg && 'ivu-form-item-error']">
          <label class="ivu-form-item-label" />
          <div class="ivu-form-item-content">
            <div v-if="searchFilterObj.matchMode === 'keywords'" class="ivu-input-group">
              <div class="ivu-input-group-prepend">OR</div>
              <Select
                v-model="searchFilterObj.keywords"
                filterable
                multiple
                allow-create
                @on-create="onKeywordCreate"
                @on-change="handleSearchFilterChange"
                placeholder="请输入关键字并回车确认"
              >
                <Option v-for="item in keywordList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>

            <div v-else class="ivu-input-group is-regex">
              <div class="ivu-input-group-prepend">RE2</div>
              <Input
                placeholder="请输入正则表达式"
                v-model.trim="searchFilterObj.regex"
                @on-change="handleSearchFilterChange"
              >
                <span slot="prepend">/</span>
                <span slot="append">/</span>
              </Input>
            </div>
            <div v-show="showErrorMsg" class="ivu-form-item-error-tip">
              {{ searchFilterErrorMsg }}
            </div>
          </div>
        </div>

        <a
          v-if="searchFilterObj.matchMode === 'regex'"
          href="https://www.jyshare.com/front-end/854/"
          target="_blank"
          class="regex-test-link"
        >
          RE2 测试地址
        </a>
      </div>

      <template #footer>
        <Button type="text" @click="() => (searchFilterModal = false)">取消</Button>
        <Button type="primary" @click="handleCreateCondition">确定</Button>
      </template>
    </Modal>

    <Modal v-model="searchViewModel.visible" title="保存搜索视图" width="30vw">
      <Form ref="searchViewFormRef" label-position="left" label-colon :model="searchViewModel" @submit.native.prevent>
        <FormItem
          prop="name"
          :rules="{
            required: true,
            message: '视图名称不能为空，请先输入信息！',
            trigger: 'blur'
          }"
        >
          <template #label>
            <span>视图名称</span>
            <span style="color: #999">（为当前的搜索视图创建自定义名称，方便再次搜索）</span>
          </template>
          <Input v-model="searchViewModel.name" placeholder="请输入视图名称" />
        </FormItem>
      </Form>

      <template #footer>
        <Button type="text" @click="() => (searchViewModel.visible = false)">取消</Button>
        <Button type="primary" @click="onSearchViewSave">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<style scoped lang="less">
.log-wrapper {
  height: 100vh;
  .logs-header {
    background-color: #333333;
    color: #f9f9f9;
    font-size: 14px;
    font-weight: bold;
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-title {
      max-width: 320px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .space-component {
      width: auto;
      align-items: center;
    }
    .connect {
      font-size: 12px;
      font-weight: 600;
      white-space: nowrap;
      &.connected {
        color: #33ff33;
      }
      &.not-connected {
        color: #fa3806;
      }
    }
    .log-search-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      .log-search-input {
        width: 200px;
        top: 0;
        pointer-events: none;
        margin-right: 8px;
        /deep/.ivu-input-group-prepend {
          background: transparent;
          color: #fff;
        }
        /deep/.ivu-input {
          border-radius: 0;
        }
      }

      &-wrapper {
        display: flex;
        align-items: center;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #dcdee2;
        color: #515a6e;
        background-color: #fff;
        padding: 1px 7px;
        font-size: 12px;
        margin-right: 8px;
      }

      &-content {
        display: flex;
        width: 200px;
        height: 100%;
        overflow: hidden;
        align-items: center;
        font-weight: 400;
      }

      .search-filter-condition {
        max-width: 600px;
        min-height: auto;
      }

      .search-filter-condition-item {
        color: #515a6e;
        font-weight: 400;
        padding: 4px 8px;
      }
    }
    /deep/.ivu-poptip-rel {
      height: 24px;
    }
  }
  .logs-card {
    height: ~'calc(100vh - 40px)';
    color: #c7c7c7;
    background-color: #000000;
    font-size: 14px;
    font-weight: 400;
    padding: 16px;
    overflow: hidden auto;
    white-space: pre-wrap;
    &::-webkit-scrollbar-track-piece {
      background-color: #f1f1f1;
      -webkit-border-radius: 0;
      -moz-border-radius: 0;
      border-radius: 0;
    }
    &::-webkit-scrollbar {
      width: 16px;
      height: 16px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      -webkit-border-radius: 0;
      -moz-border-radius: 0;
      border-radius: 0;
    }
    &::-webkit-scrollbar-corner {
      background-color: #f1f1f1;
    }
  }
  .logs-nowrap {
    white-space: pre;
    overflow: auto;
  }
  .logs-tail {
    padding: 6px;
    background-color: #e9edf3;
    font-size: 13px;
    height: 35px;
  }
}
.mb16 {
  margin-bottom: 16px;
}
.mode-title {
  line-height: 32px;
}
.mode-content {
  padding: 16px;
  border: 1px solid #dddddd;
  border-radius: 4px;
}
.search-filter-condition {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  min-height: 74px;

  &-item {
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #dddddd;
    padding: 4px 4px 4px 8px;
    margin: 4px 8px 4px 0;
  }

  /deep/.ivu-icon {
    cursor: pointer;
    margin-left: 8px;
  }
}

.ivu-form-item {
  margin-bottom: 16px;
}
/deep/.search-log-modal {
  .ivu-modal {
    top: 72px;
  }

  .action-area {
    align-items: center;
  }

  .history-search-btn {
    display: flex;
    height: 32px;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #2a7cc3;
    cursor: pointer;
    > img {
      margin-right: 8px;
      height: 18px;
      font-size: 18px;
      cursor: pointer;
      color: #2a7cc3;
    }
  }
}

/deep/.search-filter-modal {
  .ivu-select-selection {
    border-radius: 0 4px 4px 0;

    .ivu-select-input {
      height: 26px;
    }
  }

  .ivu-form-item {
    display: flex;
    .ivu-form-item-label {
      padding-right: 0;
    }
    .ivu-form-item-content {
      flex: 1;
      line-height: unset;
    }
  }

  .ivu-input-group.is-regex {
    .ivu-input-group {
      top: 0;

      .ivu-input-group-prepend {
        border-radius: 0;
        background-color: #f8f8f9;
        color: #515a6e;
      }
    }
  }

  .ivu-input-group-prepend {
    background-color: #fff;
    color: #2a7cc3;
    font-weight: bold;
  }

  .regex-test-link {
    display: inline-block;
    margin-top: 4px;
  }
}

/deep/span.high-light {
  background: yellow;
  color: black;
}

.flex-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
</style>

<style lang="less">
.log-search-history-list {
  width: 300px;
  padding: 0;
  .ivu-dropdown-menu {
    padding: 2px 4px;
    .ivu-dropdown-item {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:not(:last-child) {
        border-bottom: 1px solid #dedede;
      }
    }
    .keyword {
      font-size: 14px;
    }
    .ivu-icon {
      font-size: 16px;
      color: #ed4041;
    }
  }
  .split-space {
    > span:not(:last-child) {
      position: relative;
      margin-right: 8px;
      &::after {
        position: absolute;
        content: '/';
        top: 0;
        right: -6px;
      }
    }
  }
}
</style>
