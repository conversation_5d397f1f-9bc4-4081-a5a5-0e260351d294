export interface PodInApi {
  conditions?: Condition[]
  containers?: Container[]
  initContainers?: Container[]
  metrics?: Metrics
  name?: string
  namespace?: string
  nodeIp?: string
  phase?: string
  podIp?: string
  reason?: string
  uuid: string
  creationTimestamp: string
}

export interface Metrics {
  cpuLimit?: number
  cpuRequest?: number
  cpuUsage?: number
  memLimit?: number
  memRequest?: number
  memUsage?: number
}

export interface Condition {
  lastProbeTime: string
  lastTransitionTime: string
  status: string
  type: EnumConditionType
}

export interface Container {
  exitCode?: string
  finishedAt?: string
  image?: string
  name?: string
  reason?: string
  restartCount?: number
  startedAt?: string
  state?: string
}

export enum EnumConditionType {
  Initialized = 'Initialized',
  Ready = 'Ready',
  ContainersReady = 'ContainersReady',
  PodScheduled = 'PodScheduled'
}

export type ConditionMap = Record<EnumConditionType, Condition>

export type Pod = Omit<PodInApi, 'conditions'> & {
  conditions?: ConditionMap
}
