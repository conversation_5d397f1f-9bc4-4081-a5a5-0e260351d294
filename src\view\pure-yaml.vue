<script lang="ts" setup>
import { yaml2json } from '@/libs/util'
import { Yaml } from '@/components'
import { onMounted, ref } from 'vue'

const yamlData = ref()

onMounted(() => {
  yamlData.value = sessionStorage.getItem('yaml')
  sessionStorage.removeItem('yaml')
})
</script>

<template>
  <yaml v-if="yamlData" style="height: 100vh; width: 100vw; overflow-y: auto" :value="yamlData" :forbiddenEdit="true" />
</template>

<style lang="less" scoped></style>
