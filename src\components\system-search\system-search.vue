<script lang="ts" setup>
import { Space, Empty } from '@/components'
import useSystemSearchService from './useSystemSearchService'
import { ResourceCard } from './'

const {
  onSearch,
  visible,
  keyword,
  clusters,
  clusterList,
  licenseInfoLoading,
  namespaceList,
  namespaces,

  resources,
  resourceList,
  ip,
  onHistoryParamsListVisible,

  historyParamsList,
  data,
  loading,
  loadingMore,
  noMore,
  refresh,
  onContentScroll,
  saveViewModal,
  onOpenSaveViewModal,
  onSaveSearchView,
  onDeleteSearchView,
  onSearchViewClick,
  onClearFilter
} = useSystemSearchService()
</script>

<template>
  <div>
    <Space class="search-wrapper">
      <div @click="onSearch">
        <Input placeholder="多维度资源搜索" class="search-input" icon="ios-search" readonly />
      </div>
    </Space>
    <Modal v-model="visible" :title="null" footer-hide :closable="false" :width="80" class-name="search-modal">
      <Space class="search-modal-header">
        <Input
          v-model="keyword"
          placeholder="请输入需要搜索的资源名称"
          class="search-input search-modal-input"
          @on-enter="refresh"
        />
        <Tooltip content="搜索" theme="light">
          <Icon class="header-icon" type="ios-search" @click="refresh" />
        </Tooltip>
        <Divider type="vertical" />

        <Dropdown
          transfer
          transfer-class-name="history-dropdown-list"
          trigger="click"
          @on-visible-change="onHistoryParamsListVisible"
          @on-click="(item) => onSearchViewClick(JSON.parse(item))"
        >
          <div class="header-btn">
            <img class="header-icon" src="@/assets/collection-list.svg" />
            视图列表
          </div>

          <DropdownMenu slot="list">
            <DropdownItem v-for="(item, index) in historyParamsList" :key="index" :name="JSON.stringify(item)">
              <div class="keyword">{{ item?.name }}</div>

              <Icon type="md-trash" @click.stop="() => onDeleteSearchView(item)" />
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>

        <Divider type="vertical" />

        <div class="header-btn" @click="onOpenSaveViewModal">
          <img class="header-icon" src="@/assets/save.svg" />
          保存视图
        </div>
      </Space>
      <div class="search-modal-content">
        <Space class="search-modal-content-left" direction="vertical">
          <Space class="search-modal-content-left-header" :size="8">
            <strong>过滤选项</strong>

            <div class="search-modal-content-left-header-ops">
              <div class="header-btn" @click="onClearFilter">
                <Icon class="header-icon" type="ios-refresh" />
                一键清空
              </div>

              <Divider type="vertical" />

              <div class="header-btn" @click="refresh">
                <Icon class="header-icon" type="ios-search" />
                搜索
              </div>
            </div>
          </Space>
          <Space class="search-modal-content-left-filter" direction="vertical">
            <Spin fix v-if="licenseInfoLoading" />

            <Select v-model="clusters" filterable multiple placeholder="请选择集群" :max-tag-count="5">
              <Option v-for="item in clusterList" :value="item.clusterId" :key="item.clusterId">{{
                item.clusterName
              }}</Option>
            </Select>
            <Select v-model="namespaces" filterable multiple placeholder="请选择命名空间" :max-tag-count="5">
              <Option v-for="item in namespaceList" :value="item" :key="item">{{ item }}</Option>
            </Select>
            <Select v-model="resources" filterable multiple placeholder="请选择资源类型" :max-tag-count="5">
              <Option
                v-for="item in resourceList"
                :value="`${item.resource}/${item.group}`"
                :label="`${item.resource}${item.group ? ` / ${item.group}` : ''}`"
                :key="`${item.resource}/${item.group}`"
              >
                <Space class="filter-resource-item-wrapper" direction="vertical" :size="4">
                  <div class="resource">{{ item.resource }}</div>
                  <div class="group" v-if="item.group">{{ item.group }}</div>
                </Space>
              </Option>
            </Select>

            <Input v-model="ip" closed placeholder="请输入ip" />
          </Space>
        </Space>
        <Space
          v-if="!!data?.list?.length || loading"
          class="search-modal-content-right"
          direction="vertical"
          @scroll="onContentScroll"
        >
          <Spin fix v-if="licenseInfoLoading || loading" />
          <template v-for="(item, index) in data?.list">
            <ResourceCard :data="item" :key="item.name + item.createdAt + index" />
          </template>
          <div class="search-modal-content-right-footer" v-if="noMore">全部数据加载完毕 ~</div>
          <div class="search-modal-content-right-footer" v-else-if="loadingMore || loading">
            <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>加载中...
          </div>
          <div class="search-modal-content-right-footer" v-else>滚动到底，加载更多数据 ~</div>
        </Space>
        <Empty v-else />
      </div>
    </Modal>
    <Modal v-model="saveViewModal.visible" title="保存搜索视图" @on-ok="onSaveSearchView" width="380">
      <Space class="save-view-modal" direction="vertical" :size="8">
        <div class="title">
          视图名称
          <span class="required">*</span>
          <span class="desc">（为当前的搜索视图创建自定义名称，方便再次搜索）</span>
        </div>
        <div>
          <Input v-model="saveViewModal.name" placeholder="请输入视图名称" />
          <div class="error" v-if="!saveViewModal.name">视图名称不能为空，请先输入信息！</div>
        </div>
      </Space>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.search-wrapper {
  align-items: center;
  .search-input {
    width: 230px;
  }
}
.search-input {
  /deep/.ivu-input-icon {
    color: #2a7cc3;
  }
  /deep/input {
    border-radius: 32px;
    border-color: #2a7cc3;
    transition-property: all;
    transition-duration: 0.3s;
    box-shadow: unset;
    padding: 7px 8px;
    &:hover,
    &.focus {
      border-radius: 4px;
    }
  }
}
.search-modal-header {
  align-items: center;
  width: ~'calc(100% - 32px)';
  margin: 16px;
  .ivu-divider {
    height: 24px;
  }
}
.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  color: #2a7cc3;
  cursor: pointer;
  > .header-icon {
    margin-right: 8px;
  }
}
.header-icon {
  height: 18px;
  font-size: 18px;
  cursor: pointer;
  color: #2a7cc3;
}
.search-modal-content {
  display: flex;
  height: 75vh;
  border-top: 1px solid #eee;
  flex-direction: row-reverse;
  .search-modal-content-left {
    width: 320px;
    flex-shrink: 0;
    padding: 16px;
    border-left: 1px solid #eee;
    .search-modal-content-left-header {
      display: flex;
      align-items: center;
      white-space: nowrap;
      justify-content: space-between;
      .search-modal-content-left-header-ops {
        display: flex;
        align-items: center;
        white-space: nowrap;
      }
    }
    .search-modal-content-left-filter {
      position: relative;
      overflow: hidden auto;
      height: ~'calc(100% - 32px)';
    }
    .filter-resource-item-wrapper {
      .resource {
      }
      .group {
        color: #a7a7a7;
      }
    }
  }
  .search-modal-content-right {
    flex: 1 0 0%;
    padding: 16px;
    overflow-y: auto;
    position: relative;

    .search-modal-content-right-footer {
      height: 48px;
      color: #999;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;

      .spin-icon-load {
        margin-right: 8px;
        color: #2a7cc3;
        animation: ani-demo-spin 1s linear infinite;
      }
    }
  }
}
/deep/.search-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    padding: 0;
  }
}
.save-view-modal {
  .title {
  }
  .required,
  .error {
    color: #ed4014;
  }
  .desc {
    color: #999;
  }
}
</style>
<style lang="less">
.history-dropdown-list {
  width: 300px;
  padding: 0;
  .ivu-dropdown-menu {
    padding: 2px 4px;
    .ivu-dropdown-item {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:not(:last-child) {
        border-bottom: 1px solid #dedede;
      }
    }
    .keyword {
      font-size: 14px;
    }
    .ivu-icon {
      font-size: 16px;
      color: #ed4041;
    }
  }
  .split-space {
    > span:not(:last-child) {
      position: relative;
      margin-right: 8px;
      &::after {
        position: absolute;
        content: '/';
        top: 0;
        right: -6px;
      }
    }
  }
}
</style>
