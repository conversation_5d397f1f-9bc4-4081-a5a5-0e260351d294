import axios from '@/libs/api.request'


export const ApiDeployConfigmapList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/configmap/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiDeployConfigmapGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/configmap/get`,
    method: 'get',
    data: {},
    params: params
  })
}

