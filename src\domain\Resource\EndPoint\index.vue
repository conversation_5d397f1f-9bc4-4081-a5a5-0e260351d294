<script lang="ts" setup>
import { Space, ViewYaml, ProTable, LinkButton, ResourceForm } from '@/components'
import { EventTable, BatchCopy } from '@/domain/Resource'
import { EnumResourceType } from '@/components/system-search/config'
import { TABLE_COLUMNS } from './setting'
import useEndPointService from './useEndPointService'
import { getCurrentInstance, ref } from 'vue'


const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  onSubmitSuccess,
  onViewDetail,
  detail,
  K8SInstance
} = useEndPointService()

const { proxy } = getCurrentInstance()
const batchCopyModalVisible = ref(false)
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>当前页面中您可以管理 EndPoint 资源, 主要用于限制当前空间出口流量允许到达的空间。</p>
      </Space>
    </Alert>
    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
      :on-create="onCreate"
    >
      <template #operate-buttons>
        <Button size="small" type="primary" ghost icon="md-copy" @click="() => (batchCopyModalVisible = true)">批量复制</Button>
      </template>
      <template #name="{ row }">
        <link-button @click="() => onViewDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #endpoints="{ row }">
        <template v-if="row.endpoints">
          <Poptip trigger="hover" transfer>
            <template #content>
              <div class="endpoints-poptip-wrapper">
                <Tag v-for="ep in row.endpoints.split(',')" :key="ep" color="blue">{{ ep }}</Tag>
              </div></template
            >
            <div class="endpoints-wrapper">
              <span v-for="(ep, index) in row.endpoints.split(',')" :key="ep">
                <Tag color="blue" v-if="index < 50">{{ ep }}</Tag>
              </span>
            </div>
          </Poptip>
        </template>
        <span v-else>-</span>
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button @click="() => onEdit(row)" text="编辑" />
          <link-button @click="() => onDelete(row)" text="删除" type="danger" />
        </space>
      </template>
    </pro-table>

    <view-yaml
      resourceType="endpoint"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V1"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
    <resource-form
      resourceType="endpoint"
      v-model="formVisible"
      resource-version="V1"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :onSubmitCallBack="onSubmitSuccess"
      notSynchronizeToUnifiedCluster
      forbiddenForm
      isSkipCheck
    />

    <Drawer title="查看详情" v-model="detail.visible" :closable="true" width="50">
      <Card>
        <h4>基本信息</h4>
        <Row type="flex" :gutter="20" style="margin-top: 20px">
          <Col span="10" class="info-key">Name</Col>
          <Col span="14" class="info-value">{{ detail.data.name }}</Col>
        </Row>
        <Row type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Namespace</Col>
          <Col span="14" class="info-value">{{ K8SInstance?.namespace }}</Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <EventTable :uuid="detail.data.uuid" :clusterId="K8SInstance?.clusterId" />
      </Card>
    </Drawer>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="Endpoint" />
  </div>
</template>

<style lang="less" scoped>
.endpoints-wrapper {
  overflow: hidden;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.ivu-poptip {
  &,
  /deep/.ivu-poptip-rel {
    height: 100%;
    display: block;
  }
}
</style>
<style lang="less">
.endpoints-poptip-wrapper {
  max-width: 548px;
  max-height: 240px;
  white-space: pre-wrap;
}
</style>
