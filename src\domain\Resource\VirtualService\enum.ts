export function formatEnumToLabelValue(enumObj) {
  const arr: { value: string; label: string }[] = []
  for (const [value, label] of Object.entries(enumObj)) {
    arr.push({ value, label: label.toString() })
  }
  return arr
}

export enum EnumPorts {
  HTTP = 'HTTP',
  HTTPS = 'HTTPS',
  GRPC = 'GRPC',
  HTTP2 = 'HTTP2',
  MONGO = 'MONGO',
  TCP = 'TCP',
  TLS = 'TLS'
}

export enum EnumComponentType {
  AssociatedDeployment = 'AssociatedDeployment', // 关联deployment模式
  AssociatedGateway = 'AssociatedGateway', // 关联gateway模式
  Independent = 'Independent' // 独立模式
}

export enum EnumMatchCategory {
  uri = 'URL匹配',
  headers = 'Headers匹配',
  queryParams = 'QueryParams匹配'
}
export enum EnumMatchType {
  exact = '精准匹配（exact）',
  prefix = '前缀匹配（prefix）',
  regex = '正则匹配（regex）'
}

export enum EnumRoute {
  route = 'Route（目标主机）',
  delegate = 'Delegate（路由委派）',
  redirect = 'Redirect（重定向）'
}

export enum EnumRetryOn {
  '5xx' = '5xx',
  'gateway-error' = 'gateway-error',
  'reset' = 'reset',
  'connect-failure' = 'connect-failure',
  'refused-stream' = 'refused-stream',
  'envoy-ratelimited' = 'envoy-ratelimited',
  'retriable-4xx' = 'retriable-4xx'
}

export enum EnumAllowMethods {
  GET = 'GET',
  POST = 'POST',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
  OPTIONS = 'OPTIONS',
  PUT = 'PUT'
}

export enum EnumHeadersType {
  request = 'request（请求）',
  response = 'response（响应）'
}
export enum EnumHeadersMethod {
  set = 'set',
  add = 'add',
  remove = 'remove'
}
