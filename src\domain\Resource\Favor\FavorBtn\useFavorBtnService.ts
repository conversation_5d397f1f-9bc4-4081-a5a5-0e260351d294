import Config from '@/config'
import { useDelete, useGet, usePost } from '@/libs/service.request'
import { useStore } from '@/libs/useVueInstance'
import { computed, getCurrentInstance } from 'vue'
import { useRequest } from 'vue-request'
import { EnumFavorResourceName, EnumFavorResourceTitle } from '../enum'

export default function useFavorBtnService(props: { resourceKey: keyof typeof EnumFavorResourceName }) {
  const store = useStore()
  const { proxy } = getCurrentInstance()

  const currentCluster = computed(() => {
    store.commit('getCurrentCluster', store.state.user.userId)
    return store.state.k8s.currentCluster
  })
  const currentNamespace = computed(() => {
    store.commit('getCurrentNamespace', store.state.user.userId)
    return store.state.k8s.currentNamespace
  })
  const currentClusterID = computed(() => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    return store.state.k8s.currentClusterId
  })

  const getFavorReady = computed(() => currentCluster.value && currentNamespace.value && currentClusterID.value)

  const {
    data: favorId,
    // loading: favorIdLoading,
    run: getFavorId
  } = useRequest(
    () => {
      return useGet<{ data: { collectionId: string } }>(`${Config.Api.Base}${Config.Api.GetResourceFavorId}`, {
        params: {
          clusterId: currentClusterID.value,
          namespace: currentNamespace.value,
          resourceName: EnumFavorResourceName[props.resourceKey]
        }
      })
    },
    {
      //   manual: true,
      formatResult: (res) => res.data.data?.collectionId,
      ready: getFavorReady,
      initialData: undefined,
      refreshDeps: [currentCluster, currentNamespace, currentClusterID]
    }
  )

  const onFavor = async () => {
    const res = await usePost(`${Config.Api.Base}${Config.Api.AddResourceFavor}`, {
      clusterId: currentClusterID.value,
      namespace: currentNamespace.value,
      resourceName: EnumFavorResourceName[props.resourceKey]
    })
    if (res.success) {
      getFavorId()
      proxy.$Message.success(
        `已收藏 ${currentCluster.value} / ${currentNamespace.value} 下的 ${
          EnumFavorResourceTitle[props.resourceKey]
        } 资源`
      )
    }
  }
  const onDisfavor = async () => {
    const res = await useDelete(`${Config.Api.Base}${Config.Api.RemoveResourceFavor}/`, {
      params: {
        collectionId: favorId.value
      }
    })
    if (res.success) {
      getFavorId()
      proxy.$Message.success(
        `已取消收藏${currentCluster.value} / ${currentNamespace.value}下的${
          EnumFavorResourceTitle[props.resourceKey]
        }资源`
      )
    }
  }

  return {
    onFavor,
    onDisfavor,
    currentCluster,
    currentNamespace,
    currentClusterID,
    favorId,
    // favorIdLoading,
    getFavorId
  }
}
