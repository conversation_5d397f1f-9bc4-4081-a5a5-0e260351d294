<script lang="ts" setup>
import { EnumComponentType } from './enum'
import { PropType, ref } from 'vue'
import LinkButton from '@/components/link-button'
import Space from '@/components/space'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  }
})
const showMoreNotice = ref(false)
</script>

<template>
  <Alert show-icon>
    <div v-if="props.type === EnumComponentType.AssociatedGateway">当前数据表是网关关联的 VirtualService ；</div>
    <Space direction="vertical" :size="4" v-else>
      <p :style="`margin-bottom: ${props.type === EnumComponentType.Independent ? '0px' : '16px'}`">
        当前页面中您可以管理 VirtualService 资源, 主要用于主机的路由走向, 负载均衡, 超时等配置, 默认情况对所有空间生效；
      </p>
      <div v-show="showMoreNotice">
        <p style="font-weight: 600">&nbsp;&nbsp;&nbsp;&nbsp;配置技巧：</p>
        <p>
          &nbsp;&nbsp;&nbsp;&nbsp;<Badge status="default" />同一个主机应在同一个 VirtualService 中进行配置, 避免多个
          VirtualService 合并时匹配顺序有误；
          <span style="color: #2a7cc3">推荐命名方式: {三级域}-vs</span>
        </p>
        <p>
          &nbsp;&nbsp;&nbsp;&nbsp;<Badge status="default" />当一个 VirtualService 配置过于膨胀时, 我们可以通过 Delegate
          (路由委托)的方式对不同的服务功能进行拆分；
          <span style="color: #2a7cc3">推荐命名方式: {三级域}-{服务名/功能名}-delegator</span>
        </p>
        <p v-if="props.type === EnumComponentType.AssociatedDeployment" style="font-weight: 600">
          &nbsp;&nbsp;&nbsp;&nbsp;配置限制：
        </p>
        <p v-if="props.type === EnumComponentType.AssociatedDeployment">
          &nbsp;&nbsp;&nbsp;&nbsp;<Badge status="default" />
          <span style="color: #2a7cc3">http.destination.host: </span>
          只允许选择当前应用的 Service;
        </p>
        <p v-if="props.type === EnumComponentType.AssociatedDeployment">
          &nbsp;&nbsp;&nbsp;&nbsp;<Badge status="default" />
          <span style="color: #2a7cc3">http.delegate: </span>
          只允许选择当前应用的 Delegate
        </p>
      </div>
      <LinkButton
        v-if="props.type === EnumComponentType.AssociatedDeployment && !showMoreNotice"
        text="查看更多"
        @click="showMoreNotice = true"
      />
      <LinkButton
        v-if="props.type === EnumComponentType.AssociatedDeployment && showMoreNotice"
        text="收起信息"
        @click="showMoreNotice = false"
      />
    </Space>
  </Alert>
</template>

<style lang="less" scoped></style>
