<script lang="ts" setup>
import { ResourceContainer } from '@/domain/Resource'
import AppStatefulSet from '@/domain/Resource/StatefulSet/StatefulSetList/index.vue'
</script>

<template>
  <ResourceContainer resourceKey="StatefulSet">
    <Alert show-icon>
      页面展示了当前空间中已部署的<span style="color: #2a7cc3">StatefulSet(有状态服务)</span
      >，您可以从应用详情页面中管理与应用相关的资源。
      <p style="margin-top: 16px; margin-left: 16px">
        <b>标签过滤: </b>
        可以启用标签, 通过标签进行进一步筛选资源, 暂时只支持 "=" 操作符, 并且多个条件之间是 AND 关系;
      </p>
      <p style="margin-top: 16px; margin-left: 16px">
        <b>滚动重启: </b>
        列表中的副本会被一个接一个进行重启, 重启过程中表格中会存在
        <span style="color: #ed4014">[重启或扩缩容中...]</span> 标识;
      </p>
    </Alert>
    <AppStatefulSet />
  </ResourceContainer>
</template>
