import { computed, getCurrentInstance, onBeforeMount, ref, watch, reactive, onMounted } from 'vue'
import { useStore } from '@/libs/useVueInstance'
import { useRequest } from 'vue-request'
import { useGet } from '@/libs/service.request'
import Config from '@/config'

const CLUSTER_ENV_STORAGE_KEY = 'cluster_env'
const NAMESPACE_BUSINESS_STORAGE_KEY = 'namespace_business'

export default function useK8SSwitchService(props: {
  onClusterChange: (clusterId, cluster) => void
  setClusterOnly: boolean
  filterNamespace: (namespace) => boolean
  setFirstNamespaceWhileIllegal: boolean
}) {
  const store = useStore()
  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const tempCluster = ref()
  const tempNamespace = ref()
  const tempClusterID = ref()
  const originClusterList = ref(null)
  const originNamespaceList = ref(null)
  const searchKeyword = reactive({
    cluster: '',
    namespace: ''
  })
  const clusterEnv = ref(localStorage.getItem(CLUSTER_ENV_STORAGE_KEY) ?? '')
  const clusterEnvList = ref([])
  const namespaceBusiness = ref(localStorage.getItem(NAMESPACE_BUSINESS_STORAGE_KEY) ?? '')
  const namespaceBusinessList = ref([])

  const currentCluster = computed(() => {
    store.commit('getCurrentCluster', store.state.user.userId)
    const data = store.state.k8s.currentCluster
    tempCluster.value = data

    if (!data) {
      visible.value = true
    }
    return data
  })
  const currentNamespace = computed(() => {
    store.commit('getCurrentNamespace', store.state.user.userId)
    const data = store.state.k8s.currentNamespace
    tempNamespace.value = data
    if (!data) {
      visible.value = true
    }
    return data
  })
  const currentClusterID = computed(() => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    const data = store.state.k8s.currentClusterId
    tempClusterID.value = data
    if (!data) {
      visible.value = true
    }
    return data
  })

  const onClusterEnvChange = (val) => {
    clusterEnv.value = val
    onSearchCluster(val)
    localStorage.setItem(CLUSTER_ENV_STORAGE_KEY, val ? val : '')
  }

  const onNamespaceBusinessChange = (val) => {
    namespaceBusiness.value = val
    onSearchNamespace(val)
    localStorage.setItem(NAMESPACE_BUSINESS_STORAGE_KEY, val ? val : '')
  }

  const { data: clusterList, run: getClusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetClusterList}`
      )
    },
    {
      manual: true,
      formatResult(res) {
        const list = res.data.data?.map((i) => ({ ...i, id: i.id.toString() }))
        originClusterList.value = list
        clusterEnvList.value = list?.map((i) => i.env)?.filter((i, index, arr) => !!i && arr.indexOf(i) === index)

        if (clusterEnv.value) {
          return list?.filter((i) => i.env === clusterEnv.value) ?? []
        } else {
          return list
        }
      }
    }
  )

  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet<{ data: { name: string; business: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetNamespaceList}`,
        {
          params: {
            clusterName: tempCluster.value
          }
        }
      )
    },
    {
      manual: true,
      formatResult(res) {
        const list = props.filterNamespace ? res.data.data?.filter(props.filterNamespace) : res.data.data
        originNamespaceList.value = list
        namespaceBusinessList.value = list
          ?.map((i) => i.business)
          ?.filter((i, index, arr) => !!i && arr.indexOf(i) === index)

        if (props.setClusterOnly) {
          tempNamespace.value = list?.[0].name
          setClusterNamespace()
        }

        if (namespaceBusiness.value) {
          return list?.filter((i) => i.business === namespaceBusiness.value) ?? []
        } else {
          return list
        }
      }
    }
  )

  const onCancel = () => {
    if (!currentCluster.value || !currentNamespace.value) {
      proxy.$Message.error('进入系统前，请先选择集群或命名空间！')
      visible.value = true
    } else {
      visible.value = false
    }
  }

  const setClusterNamespace = () => {
    if (!tempCluster.value || !tempClusterID.value) {
      visible.value = true
      proxy.$Message.warning('集群为空，请选择目标集群数据！')
    } else if (!tempNamespace.value) {
      visible.value = true
      proxy.$Message.warning('命名空间为空，请选择目标命名空间！')
    } else {
      visible.value = false
      store.commit('setCurrentCluster', { uid: store.state.user.userId, cluster: tempCluster.value })
      store.commit('setCurrentNamespace', { uid: store.state.user.userId, namespace: tempNamespace.value })
      store.commit('setCurrentClusterId', { uid: store.state.user.userId, clusterId: tempClusterID.value })
    }
  }

  const onSearchCluster = (env) => {
    if (env && env !== '') {
      clusterList.value =
        originClusterList.value?.filter((i) => i.env === env && i.name.includes(searchKeyword.cluster ?? '')) ?? []
    } else {
      clusterList.value = originClusterList.value?.filter((i) => i.name.includes(searchKeyword.cluster ?? '')) ?? []
    }
  }

  const onSearchNamespace = (business) => {
    if (business) {
      namespaceList.value =
        originNamespaceList.value?.filter(
          (i) => i.business === business && i.name.includes(searchKeyword.namespace ?? '')
        ) ?? []
    } else {
      namespaceList.value =
        originNamespaceList.value?.filter((i) => i.name.includes(searchKeyword.namespace ?? '')) ?? []
    }
  }

  const onSelectCluster = (id) => {
    tempCluster.value = clusterList.value?.filter((i) => i.id === id)[0].name
    tempNamespace.value = undefined
    namespaceList.value = []
    getNamespaceList()
    props.onClusterChange(tempClusterID.value, tempCluster.value)
  }

  const onInit = async () => {
    await getClusterList()
    tempCluster.value && (await getNamespaceList())
  }

  onBeforeMount(() => {
    if (proxy.$route.query.clusterId) {
      store.commit('setCurrentClusterId', {
        uid: store.state.user.userId,
        clusterId: proxy.$route.query.clusterId
      })
    }
    if (proxy.$route.query.cluster) {
      store.commit('setCurrentCluster', {
        uid: store.state.user.userId,
        cluster: proxy.$route.query.cluster
      })
    }
    if (proxy.$route.query.namespace) {
      store.commit('setCurrentNamespace', {
        uid: store.state.user.userId,
        namespace: proxy.$route.query.namespace
      })
    }
  })

  const onNamespaceChange = (val) => {
    if (val) {
      setClusterNamespace()
    }
  }

  const onOpenPopperShow = async () => {
    await onInit()
    onScrollToRadio()
  }

  const onScrollToRadio = () => {
    const clusterRadio = document.getElementById(tempClusterID.value)
    const namespaceRadio = document.getElementById(`${tempClusterID.value}${tempNamespace.value}`)

    clusterRadio?.scrollIntoView({
      behavior: 'smooth'
    })
    namespaceRadio?.scrollIntoView({
      behavior: 'smooth'
    })
  }

  onMounted(() => {
    // 初始化时，发现需要做初始化判断，则需要调用onInit，否则在切换器打开才调用
    props.setFirstNamespaceWhileIllegal && onInit()
  })

  watch(visible, () => {
    visible.value && onInit()
  })

  watch(
    () => [props.setFirstNamespaceWhileIllegal, namespaceList.value],
    () => {
      if (
        props.setFirstNamespaceWhileIllegal &&
        namespaceList.value?.length &&
        !namespaceList.value?.filter((i) => i.name === currentNamespace.value).length
      ) {
        // 发现当前命名空间不在列表中，则默认选择第一个并初始化

        tempNamespace.value = namespaceList.value?.[0].name
        setClusterNamespace()
      }
    }
  )

  return {
    visible,
    currentCluster,
    currentNamespace,
    currentClusterID,
    clusterList,
    namespaceList,
    tempCluster,
    tempClusterID,
    tempNamespace,
    onSearchCluster,
    onSearchNamespace,
    onSelectCluster,
    onCancel,
    onNamespaceChange,
    onOpenPopperShow,
    searchKeyword,
    clusterEnv,
    onClusterEnvChange,
    clusterEnvList,
    namespaceBusiness,
    namespaceBusinessList,
    onNamespaceBusinessChange
  }
}
