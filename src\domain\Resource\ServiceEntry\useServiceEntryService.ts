import { ref, onMounted, getCurrentInstance, computed, nextTick, watch } from 'vue'

import Config from '@/config'
import { useStore } from '@/libs/useVueInstance'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { ServiceEntry } from './type'
import { EnumFormStatus, FormSubmitParams, ResourceEntity } from '@/components/resource-form'
import { EnumArrayObjectModalStatus } from '@/components/pro-form'
import { EnumResolution } from './enum'
import { useRequest } from 'vue-request'
import useSingleK8SService from '@/libs/useSingleK8SService'

export const SERVICE_ENTRY = { resource: 'serviceentries', group: 'networking.istio.io', version: 'v1beta1' }

export default function useServiceEntryService() {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const viewDetailVisible = ref(false)
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const formInitData = computed(() => ({
    name: 'default',
    namespace: K8SInstance.value?.namespace,
    location: 'MESH_EXTERNAL',
    hosts: [],
    ports: [{}],
    workload: {
      endpoints: []
    },
    resolution: EnumResolution.DNS
  }))

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...SERVICE_ENTRY
    }
    yamlInitData.value = `apiVersion: networking.istio.io/v1beta1 
kind: ServiceEntry
metadata:
  name: 必须修改
  namespace: ${K8SInstance.value.namespace}
spec:`
  }
  const onDelete = (record: ServiceEntry) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteServiceEntry}`, {
          params: {
            ...K8SInstance.value,
            resourceName: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onEdit = (record: ServiceEntry) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
  }
  const onViewYaml = (record: ServiceEntry) => {
    yamlHistoryParams.value = {
      kind: 'ServiceEntry',
      uuid: record.uid
    }
    formEntity.value = {
      ...K8SInstance.value,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const transformKeyValueToArray = (obj, separator = ':') => {
    const arr = []
    for (const key in obj) {
      arr.push(`${key}${separator}${obj[key]}`)
    }
    return arr
  }

  const onViewDetail = async (record: ServiceEntry) => {
    console.log('onViewDetail')
    viewDetailVisible.value = true
    formEntity.value = {
      ...K8SInstance.value,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
  }

  const onInitDetailFormat = (data) => {
    return {
      ...data,
      labels: transformKeyValueToArray(data.labels),
      workloadSelector: transformKeyValueToArray(data.workload?.workloadSelector),
      endPoints: data.workload?.endpoints?.map((endPoint) => ({
        address: endPoint.address,
        ports: transformKeyValueToArray(endPoint.ports, '/')
      }))
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<ServiceEntry[]>>(
      `${Config.Api.Base}${Config.Api.GetServiceEntryTableData}?search=${params.searchValue ?? ''}&clusterId=${
        K8SInstance.value.clusterId
      }&namespace=${K8SInstance.value.namespace}&page=${params.pageIndex}&size=${params.pageSize}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onPortKeyChange = (data, key, index, val) => {
    if (!data.ports?.length) {
      data.ports = []
    }
    if (!data.ports?.[index]) {
      data.ports[index] = {}
    }
    data.ports[index][key] = val
  }

  const onWorkloadSelectorKeyChange = (data, key, index, val) => {
    if (!data.workload.workloadSelector?.length) {
      data.workload.workloadSelector = []
    }
    if (!data.workload.workloadSelector?.[index]) {
      data.workload.workloadSelector[index] = {}
    }
    data.workload.workloadSelector[index][key] = val
  }

  const onEndpointsKeyChange = (record, key, index, val) => {
    if (!record.ports?.data?.length) {
      record.ports = Object.assign({}, record.ports, { data: [] })
    }
    if (!record.ports.data[index]) {
      record.ports.data[index] = {}
    }

    record.ports.data[index][key] = val
  }
  const onSubmitWorkloadEndpoints = (data, type, record, index) => {
    if (data.workload?.endpoints?.length) {
      data.workload?.endpoints.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.workload.endpoints = [record]
    }
  }

  const onInitFormat = (data) => {
    const workloadSelector = []
    if (data.workload.workloadSelector) {
      for (const [key, value] of Object.entries(data.workload.workloadSelector)) {
        workloadSelector.push({ key, value })
      }
    }
    data.workload.workloadSelector = workloadSelector
    data.workload.endpoints?.forEach((i, index, arr) => {
      const data = []

      if (i.ports.data) {
        for (const [key, value] of Object.entries(i.ports.data)) {
          data.push({ key, value })
        }
      }
      i.ports.data = data
    })
    console.log('onInitFormat', data)
    return data
  }
  const onSubmitFormat = (params: FormSubmitParams) => {
    const workloadSelector = {}
    params.data.workload.workloadSelector?.forEach((i) => {
      workloadSelector[i.key] = i.value
    })

    params.data.workload.workloadSelector = workloadSelector
    params.data.workload.endpoints?.forEach((i) => {
      const data = {}
      i.ports.data?.forEach((i) => {
        data[i.key] = i.value
      })
      i.ports.data = data
    })

    return params
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const { data: exportToList, run: getExportToList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetExportToList}?ClusterId=${K8SInstance.value.clusterId}`)
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const initK8SInstance = () => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    store.commit('getCurrentNamespace', store.state.user.userId)
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId
    }

    nextTick(getExportToList)
  }

  onMounted(() => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  watch(K8SKey, () => {
    initK8SInstance()
    refObject.tableRef.value.reload()
  })

  return {
    getTableData,
    refObject,
    onCreate,
    onDelete,
    onEdit,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    formInitData,
    onPortKeyChange,
    onInitFormat,
    onWorkloadSelectorKeyChange,
    onSubmitFormat,
    onEndpointsKeyChange,
    onSubmitWorkloadEndpoints,
    onSubmitSuccess,
    onViewDetail,
    viewDetailVisible,
    onInitDetailFormat,
    exportToList
  }
}
