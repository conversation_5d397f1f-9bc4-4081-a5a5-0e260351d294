<template>
  <div id="app">
    <div class="div">
      <router-view />
    </div>
    <!-- <Spin fix v-if="versionModalLoading"></Spin>
    <Modal v-model="versionModal" :value="true" class="version-notice" :closable="!isFirstNoticeLoading" footer-hide>
      <template #header>
        <div class="version-modal-title">
          <span>版本更新</span>
          <Divider type="vertical" />
          <Tooltip content="在浏览器中打开" theme="light">
            <Icon type="md-compass" class="header-icon" @click="onJumpToDos" />
          </Tooltip>
        </div>
      </template>
      <div class="doc-wrapper">
        <div id="version-doc"></div>
        <Spin fix v-if="versionModalLoading"></Spin>
      </div>
    </Modal> -->
  </div>
</template>

<script name="App" setup lang="ts">
// import { useVersionNoticeService, useFeishuDocsService } from '@/libs/service.version'
import { onBeforeMount, getCurrentInstance } from 'vue'

// const VERSION_DOC_TITLE = 'feishu_version_title'

const { proxy } = getCurrentInstance()

// const versionModal = ref(false)
// const versionModalInit = ref(false)
// const isForceShowVersionModal = ref(false)
// const versionModalLoading = ref(false)

// const isFirstNoticeLoading = computed(() => !isForceShowVersionModal.value && versionModalLoading.value)

// const versionDocTitle = computed({
//   get() {
//     return window.localStorage.getItem(VERSION_DOC_TITLE) ?? undefined
//   },
//   set(value: string) {
//     window.localStorage.setItem(VERSION_DOC_TITLE, value)
//   }
// })
// const docUrl = 'https://q9jvw0u5f5.feishu.cn/docx/SY1gdzSvpoH4DdxlhqQcKJ8xnyd'
// const { initWebComponent } = useFeishuDocsService('cli_a3abf73e30b91013', docUrl, (docs) => {
//   versionModalLoading.value = false
//   versionModalInit.value = true

//   docs.invoke.getTitle().then((res) => {
//     const newTitle = res.data
//     if (isForceShowVersionModal.value || versionDocTitle.value !== newTitle) {
//       versionDocTitle.value = newTitle
//       versionModal.value = true
//     }
//   })
// })

// const onJumpToDos = () => {
//   window.open(docUrl, '_blank')
// }

// const renderUpdateVersionNotice = (updateVersion) => {
//   proxy.$Modal.info({
//     title: '更新提醒',
//     content: '系统有新版本，请刷新系统',
//     okText: '刷新',
//     onOk: updateVersion,
//     closable: false
//   })
// }
// const renderVersionNotice = async (isForceShow) => {
//   isForceShowVersionModal.value = !!isForceShow
//   versionModal.value = isForceShowVersionModal.value
//   versionModalLoading.value = isForceShowVersionModal.value
//   if (!versionModalInit.value) {
//     await initWebComponent()
//   } else {
//     versionModalLoading.value = false
//   }
//   //   isForceShowVersionModal.value = !!isForceShow
//   //   versionModal.value = true
//   //   versionModalLoading.value = true
//   //   if (!versionModalInit.value) {
//   //     await initWebComponent()
//   //   } else {
//   //     versionModalLoading.value = false
//   //   }
// }
// const VersionNoticeService = useVersionNoticeService(renderUpdateVersionNotice, renderVersionNotice, ['/login'])

// provide('VersionNoticeServiceProvide', VersionNoticeService)

onBeforeMount(() => {
  proxy.$Message.config({
    duration: 5,
    closable: true
  })
})
</script>

<style lang="less">
.size {
  width: 100%;
  height: 100%;
}
html,
body {
  .size;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
#app {
  .size;
  position: relative;
}

.ivu-table-wrapper {
  position: relative;
  border: 1px solid #dcdee2;
  border-bottom: 0;
  border-right: 0;
  overflow: hidden;
  border-radius: 8px;
}

.custom-align-right {
  display: flex !important;
  justify-content: right !important;
}

.custom-align-left {
  display: flex !important;
  justify-content: left !important;
}

.custom-align-center {
  display: flex !important;
  justify-content: center !important;
}

.custom-justify-center {
  display: flex !important;
  align-content: center !important;
}

.info-key {
  font-size: 13px;
  font-weight: bold;
  color: #555555;
}

.info-value {
  font-size: 13px;
  color: #111e3c;
  //font-family: Consolas,Menlo,Bitstream Vera Sans Mono,Monaco,微软雅黑,monospace!important;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}

.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  font-weight: bold;
  margin: 0;
  margin-right: 4px;
  height: 31px;
  padding: 8px 16px 4px;
  border: 1px solid #dcdee2;
  border-bottom: 0;
  border-radius: 4px 4px 0 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  background: #f8f8f9;
}

.ivu-table .demo-table-info-row td {
  //color: #F9F9F9 !important;
  background-color: #bee5ff !important;
}

.ivu-table-row-hover td {
  //color: #F9F9F9 !important;
  background-color: #bee5ff !important;
}
.ivu-table-row-highlight td {
  //color: #F9F9F9 !important;
  background-color: #bee5ff !important;
}

.line-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 16px 16px;
  //text-align: right;
  background: #fff;
}
</style>

<style lang="less" scoped>
.version-notice {
  &,
  /deep/.ivu-modal-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /deep/.ivu-modal {
    width: 720px !important;
    top: 0;
  }
  /deep/.ivu-modal-body {
    padding: 0;
  }
  .doc-wrapper {
    position: relative;
    height: 80vh;
    overflow: hidden auto;
    #version-doc {
      height: 100%;
      margin: -25px;
    }
  }
}
.version-modal-title {
  display: flex;
  align-items: center;
  > span {
    font-weight: 600;
  }
  .ivu-divider {
    height: 18px;
  }
  .header-icon {
    cursor: pointer;
    font-size: 18px;
    color: #2a7cc3;
  }
}
</style>
