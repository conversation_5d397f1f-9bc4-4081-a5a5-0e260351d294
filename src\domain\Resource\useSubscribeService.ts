import { useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, ref } from 'vue'

export default function useSubscribeService(type: 'deployment' | 'statefulset') {
  const { proxy } = getCurrentInstance()

  const isSubscribe = ref(false)

  const onSubscribeOrNot = async (clusterId, namespace, name) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认${isSubscribe.value ? '退订' : '订阅'} ${name}？`,
      loading: true,
      onOk: async () => {
        let path = ''
        let params = {}
        switch (type) {
          case 'deployment':
            path = isSubscribe.value ? Config.Api.UnsubscribeDeployment : Config.Api.SubscribeDeployment
            params = {
              clusterId: clusterId,
              namespace: namespace,
              name: name
            }
            break

          default:
            path = Config.Api.onSubscribeStatefulsetOrNot
            params = {
              clusterId: clusterId,
              namespace: namespace,
              name: name,
              isSubscribe: !isSubscribe.value
            }
            break
        }
        const res = await usePost(`${Config.Api.Base}${path}`, params)
        if (res.success) {
          proxy.$Message.success(`${isSubscribe.value ? '退订' : '订阅'}成功！`)
          getSubscribeStatus(clusterId, namespace, name)
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const getSubscribeStatus = async (clusterId, namespace, name) => {
    const res = await useGet(
      `${Config.Api.Base}${
        type === 'deployment' ? Config.Api.GetDeploymentSubscribeStatus : Config.Api.GetStatefulsetSubscribeStatus
      }`,
      {
        params: {
          clusterId: clusterId,
          namespace: namespace,
          name: name
        }
      }
    )
    if (res.success) {
      isSubscribe.value = !!res.data.data
    }
  }

  return { isSubscribe, onSubscribeOrNot, getSubscribeStatus }
}
