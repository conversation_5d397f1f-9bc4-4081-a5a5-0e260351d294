import { defineComponent, h } from 'vue'

import TableProps, { TablePaginationConfig } from './tableProps'
import useProTableService from './useProTableService'
import { Page, Operation, OperateBtn, OperateSearch, Table, BlockStyleOperation, BlockStyleTable } from './style'
import { Table as IViewTable, Input, Button, Select, Option, Alert } from 'view-design'
export * from './tableProps'
import SearchComponent from './SearchComponent.vue'

const ProTable = defineComponent({
  name: 'ProTable',
  props: TableProps(),
  components: {
    Select,
    Option,
    SearchComponent
  },
  setup(props, { slots, attrs, listeners }) {
    const {
      pagination,
      dataSource,
      loading,
      tableId,
      onCurrentChange,
      onPageSizeChange,
      searchKey,
      searchValue,
      searchObj,
      actionType,
      inputRef
    } = useProTableService(props)
    const OperationComponent = props.search ? BlockStyleOperation : Operation
    const TableWrapper = props.search ? BlockStyleTable : Table
    return () => (
      <div>
        {props.columns?.length ? (
          <div>
            {!props.hideSearchOperation && (
              <OperationComponent border={props.border}>
                {props.search && (
                  <OperateSearch>
                    {
                      <Input
                        ref={inputRef}
                        value={searchValue.value}
                        clearable
                        placeholder={`搜索${searchObj.value?.label}`}
                        search
                        enter-button
                        {...{
                          on: {
                            ['on-change']: (e) => {
                              searchValue.value = e.target.value
                            },
                            ['on-search']: () => actionType.value.reload(),
                            ['on-clear']: () => (searchValue.value = undefined)
                          }
                        }}
                      >
                        {props.search.length > 1 && (
                          <SearchComponent
                            slot="prepend"
                            options={props.search}
                            init-value={props.search.filter((i) => i.initData)?.[0]?.value ?? props.search?.[0].value}
                            isFillValue
                            on-change={(val) => {
                              searchKey.value = val
                            }}
                            style={{ width: '120px' }}
                          />
                        )}
                      </Input>
                    }
                  </OperateSearch>
                )}
                <OperateBtn>
                  {props.onCreate && (
                    <Button
                      type={'primary'}
                      size={'small'}
                      icon={'md-add'}
                      {...{
                        on: {
                          click: props.onCreate
                        }
                      }}
                    >
                      创建
                    </Button>
                  )}
                  {slots?.['operate-buttons'] ? slots['operate-buttons']() : null}

                  <Button
                    size={'small'}
                    type={'primary'}
                    ghost
                    icon={'md-refresh'}
                    {...{
                      on: {
                        click: actionType.value?.reload
                      }
                    }}
                  >
                    刷新
                  </Button>
                </OperateBtn>
              </OperationComponent>
            )}

            <TableWrapper border={props.border}>
              {props.alert && <Alert show-icon>{props.alert}</Alert>}
              <IViewTable
                ref={props.viewDesignTableRef?.tableRef}
                key={tableId}
                {...props}
                {...attrs}
                size="small"
                rowKey={props.rowKey ?? 'id'}
                loading={loading.value || props.loading}
                data={dataSource.value || props.data}
                columns={props.columns}
                height={props.height}
                scopedSlots={{
                  ...slots
                }}
                {...{
                  on: {
                    ...listeners
                  }
                }}
              />
              {!!props.pagination && (
                <Page
                  show-total
                  show-sizer
                  //   {...pagination.value} TBD vue2不支持这种写法
                  total={pagination.value.total}
                  current={pagination.value.current}
                  pageSize={pagination.value.pageSize}
                  placement={'top'}
                  simple={props.pagination && !!(props.pagination as TablePaginationConfig)?.simple}
                  //   onChange={onChange} vue2不支持这种写法
                  //   onOnPageSizeChange={onPageSizeChange} vue2不支持这种写法
                  {...{
                    on: {
                      ['on-change']: onCurrentChange,
                      ['on-page-size-change']: onPageSizeChange
                    }
                  }}
                />
              )}
            </TableWrapper>
          </div>
        ) : null}
      </div>
    )
  }
})

export default ProTable
