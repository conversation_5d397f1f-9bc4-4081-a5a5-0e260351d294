<script lang="ts" setup>
import ProTable from '@/components/pro-table'
import LinkButton from '@/components/link-button'
import useSubscribeConfigService from './useSubscribeConfigService'
import { Container } from '@/components'
import Space from '@/components/space'

const { columns, getTableData, refObject, onCreate, onEdit, onDelete, modal, onSubmit, formRef } =
  useSubscribeConfigService()
</script>

<template>
  <Container>
    <Space class="table-wrapper" direction="vertical">
      <Button type="primary" size="small" icon="md-add" @click="() => onCreate()"> 创建 </Button>
      <pro-table
        :columns="columns"
        :request="getTableData"
        hideSearchOperation
        :action-ref="refObject"
        row-key="uid"
        :pagination="false"
      >
        <template #ops="{ row }">
          <space justify>
            <link-button @click="() => onEdit(row)" text="编辑" />
            <link-button @click="() => onDelete(row)" text="删除" type="danger" />
          </space>
        </template>
      </pro-table>
    </Space>
    <Modal :title="modal.status === 'blank' ? '创建订阅配置' : '编辑订阅配置'" v-model="modal.visible">
      <Form ref="formRef" :label-width="160" :model="modal.data" @submit.native.prevent>
        <FormItem label="Kind" prop="kind" :rules="{ required: true, message: '请选择 Kind', trigger: 'blur' }">
          <Select v-if="modal.status === 'blank'" v-model="modal.data.kind" placeholder="请选择Kind">
            <Option value="Deployment">Deployment</Option>
          </Select>
          <Tag v-else color="primary">{{ modal.data.kind }}</Tag>
        </FormItem>

        <FormItem
          label="SameEventInterval (s)"
          prop="same_event_interval"
          :rules="{ required: true, type: 'number', message: '请输入 SameEventInterval', trigger: 'blur' }"
        >
          <InputNumber
            v-model="modal.data.same_event_interval"
            placeholder="请输入 SameEventInterval"
            :min="1"
            style="width: 100%"
          />
        </FormItem>

        <FormItem
          label="SendInterval  (s)"
          prop="send_interval"
          :min="1"
          :rules="{ required: true, type: 'number', message: '请输入 SendInterval', trigger: 'blur' }"
        >
          <InputNumber v-model="modal.data.send_interval" placeholder="请输入 SendInterval" style="width: 100%" />
        </FormItem>
      </Form>

      <template #footer>
        <Button type="text" @click="() => (modal.visible = false)">取消</Button>
        <Button type="primary" @click="onSubmit">确定</Button>
      </template>
    </Modal>
  </Container>
</template>

<style lang="less" scoped>
.table-wrapper {
  background: #fff;
  border-radius: 4px;
  display: block;
  padding: 16px;
}
</style>
