<template>
  <Container>
    <Alert show-icon>UserName为<b>none</b>，表明接口不需要记录用户信息或用户信息已过期失效。</Alert>
    <Card style="margin-bottom: 16px" :bordered="false">
      <Row :gutter="8">
        <Col span="3">
          <Select
            @on-change="handleUserChange"
            allow-create
            @on-create="handleUserCreate"
            v-model="username"
            clearable
            filterable
            style="width: 100%"
            placeholder="用户名(英文)"
          >
            <Option v-for="item in userList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Col>
        <Col span="2">
          <Select
            @on-change="handleMethodChange"
            v-model="searchForm.method"
            filterable
            clearable
            style="width: 100%"
            placeholder="Http method"
          >
            <Option v-for="item in methodList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Col>
        <Col span="2">
          <Select
            @on-change="handleCodeChange"
            v-model="searchForm.code"
            clearable
            style="width: 100%"
            placeholder="Http code"
          >
            <Option v-for="item in codeList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Col>
        <Col span="3">
          <Input v-model="query_params" clearable style="width: 100%" placeholder="Query params" />
        </Col>
        <Col span="3">
          <Input v-model="query_body" clearable style="width: 100%" placeholder="Query body" />
        </Col>
        <Col span="6">
          <Input v-model="uri" clearable placeholder="Http uri" style="width: 100%" />
        </Col>
        <Col span="5">
          <DatePicker
            :value="dateRange"
            format="yyyy/MM/dd HH:mm"
            type="datetimerange"
            placement="bottom-end"
            @on-change="handleDateRangeChange"
            placeholder="Select date"
            style="width: 100%"
          ></DatePicker>
        </Col>
      </Row>
      <Row style="margin-top: 16px">
        <Space :size="8">
          <Button type="primary" icon="ios-search" @click="handleSearch">查询</Button>
          <Button type="primary" ghost icon="ios-refresh" @click="reloadTable">刷新</Button>
        </Space>
      </Row>
    </Card>
    <Card :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
  </Container>
</template>

<script>
import { Container, Space } from '@/components'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { apiConfigCreate, apiConfigDelete, apiConfigUpdate } from '@/api/setting/config'
import { apiOpRecordList } from '@/api/setting/oplog'
import { apiLogicFilterList } from '@/api/simplelogic'
import { useGet } from '@/libs/service.request'
import Config from '@/config'
export default {
  name: 'accessOperationLog',
  components: { Container, Space },
  data() {
    return {
      userList: [],
      codeList: [],
      query_params: '',
      query_body: '',
      uri: '',
      dateRange: '',
      username: '',
      searchForm: {
        username: '',
        code: '',
        method: ''
      },
      methodList: [],
      loading: {
        table: false,
        edit: true
      },
      columns: [
        {
          title: 'UserName',
          key: 'username',
          tooltip: true,
          tooltipTheme: 'light',
          width: 120,
          fixed: 'left'
        },
        {
          title: 'Uri',
          key: 'uri',
          minWidth: 250,
          tooltipTheme: 'light',

          tooltip: true
        },
        {
          title: 'Method',
          key: 'method',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 80
        },
        {
          title: 'Code',
          key: 'code',
          width: 80,
          render: (h, params) => {
            let c = params.row.code
            let color = 'success'
            if (c >= 300 && c < 400) {
              color = 'warning'
            }
            if (c >= 400) {
              color = 'error'
            }
            return h(
              'Tag',
              {
                props: {
                  color: color
                }
              },
              c
            )
          }
        },
        {
          title: 'QParams',
          key: 'queryParams',
          minWidth: 250,
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'Resp',
          key: 'respBody',
          minWidth: 200,
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'CreatedAt',
          key: 'createdAt',
          width: 160,
          align: 'center'
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        total: 0
      }
    }
  },
  methods: {
    handleUserCreate(val) {
      console.log('create: ', val)
      this.userList.push({
        username: val
      })
      if (val === undefined) {
        delete this.page.f_username
        return
      }
      this.page.userName = val
      this.username = val
    },
    handleDateRangeChange(date) {
      console.log(date)
      if (date[0].length === 0 || date[1].length === 0) {
        delete this.page.startTimeStamp
        delete this.page.endTimeStamp
        return
      }
      let start = new Date(date[0]).getTime() / 1000
      let end = new Date(date[1]).getTime() / 1000
      this.dateRange = date
      console.log(this.dateRange)
      this.page.startTimeStamp = start
      this.page.endTimeStamp = end
    },
    handleUserChange(v) {
      console.log('user-change: ', v)
      if (v === undefined) {
        delete this.page.f_username
        return
      }
      this.page.userName = v
    },
    handleMethodChange(v) {
      if (v === undefined) {
        delete this.page.f_method
        return
      }
      this.page.method = v
    },
    handleCodeChange(v) {
      if (v === undefined) {
        delete this.page.code
        return
      }
      this.page.code = v
    },
    async createKV() {
      await apiConfigCreate({
        key: this.editModalForm.key,
        value: this.editModalForm.value,
        category: this.editModalForm.category,
        desc: this.editModalForm.desc
      })
        .then((res) => {
          noticeSucceed(this, 'succeed')
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async editKV() {
      await apiConfigUpdate({
        pid: this.editModalForm.id,
        value: this.editModalForm.value,
        category: this.editModalForm.category,
        desc: this.editModalForm.desc
      })
        .then((res) => {
          noticeSucceed(this, 'succeed')
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async editTable() {
      if (this.editModalState === 'create') {
        await this.createKV()
        return
      }
      await this.editKV()
      setTimeout(() => {
        this.editModal = false
      }, 1000)
    },
    async deleteKV(pid) {
      await apiConfigDelete({ pid: pid })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    handleCreate() {
      console.log(this.$refs)
      this.$refs['editModalForm'].resetFields()
      this.editModal = true
      this.editModalState = 'create'
    },
    async reloadTable() {
      this.loading.table = true
      await apiOpRecordList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total

          if (data.list === null) {
            this.data = []
          } else {
            this.data = data.list
          }
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.loading.table = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.url = this.uri === '' ? undefined : this.uri
      this.page.queryParams = this.query_params === '' ? undefined : this.query_params
      this.page.queryBody = this.query_body === '' ? undefined : this.query_body
      this.page.page = 1
      this.reloadTable()
    },
    async fetchFilterList() {
      const res = await useGet(`${Config.Api.Base}${Config.Api.GetAccessOperateLogFilterList}`)
      if (res.success) {
        this.userList = res.data.data.userNameList
        this.codeList = res.data.data.codeList
        this.methodList = res.data.data.methodList
      }
    },
    setDatetimeRange() {
      let now = new Date()
      this.page.endTimeStamp = parseInt(now.getTime() / 1000)
      this.page.startTimeStamp = parseInt(now.setHours(now.getHours() - 1) / 1000)
      this.dateRange = [this.page.startTimeStamp * 1000, this.page.endTimeStamp * 1000]
    }
  },
  mounted() {
    this.fetchFilterList()
    this.setDatetimeRange()
    this.reloadTable()
  }
}
</script>

<style scoped></style>
