import { Ref, computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue'
import { EnumComponentType, EnumFormStatus, EnumResourceFormModel, FormSubmitParams, ResourceFormProps } from './type'
import { useRequest } from 'vue-request'
import { useGet, usePost } from '@/libs/service.request'
import { yaml2json } from '@/libs/util'

import Config from '@/config'
import { cloneDeep } from 'lodash-es'
import store from '@/store'
import useSingleK8SService from '@/libs/useSingleK8SService'

const INIT_YAML_DATA = `please enter……`

export default function useResourceFormService(props: ResourceFormProps, visible: Ref<Boolean>) {
  const isConvertible = ref(false)
  const yamlRef = ref()
  const previewYamlRef = ref()
  const dataReloadFlag = ref(false)
  const syncUnifiedClusterModal = ref({ visible: false, data: [], params: {} })
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService({ skipNotice: true })

  const K8SInstance = ref<{ namespace: string; clusterId: string; clusterName: string }>()
  const isApplyToUnifiedCluster = ref(false)
  const modal = ref({
    visible: false,
    data: '',
    lastYaml: '',
    // 是否来自提交按钮触发
    isClickBySubmit: false
  })
  const RESOURCE_URL =
    props.type === EnumComponentType.Associated
      ? Config.Api.RelatedResource
      : props.resourceVersion === 'V1'
      ? Config.Api.ResourceV1
      : Config.Api.Resource

  const synchronizeToUnifiedCluster = computed(() => !props.notSynchronizeToUnifiedCluster)

  onMounted(() => {
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId,
      clusterName: store.state.k8s.currentCluster
    }
  })
  watch(
    () => props.value,
    () => {
      props.value && getIsUnifiedCluster()
    }
  )
  watch(K8SKey, () => {
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId,
      clusterName: store.state.k8s.currentCluster
    }
  })

  const { data: isUnifiedCluster, run: getIsUnifiedCluster } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetIsUnifiedCluster}?ClusterName=${
          props.resourceEntity?.clusterName ?? K8SInstance.value.clusterName
        }`
      )
    },
    {
      manual: true,
      ready: synchronizeToUnifiedCluster,
      formatResult: (res) => {
        const isUnifiedCluster = res.data.isUnifiedCluster
        isApplyToUnifiedCluster.value = false
        return isUnifiedCluster
      },
      onSuccess: () => {
        getUnifiedClusterList()
      }
    }
  )

  const { data: unifiedClusterList, run: getUnifiedClusterList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetUnifiedClusterList}`, {
        params: {
          clusterId: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => res.data.data ?? []
    }
  )

  const {
    run: getFormModel,
    data: formModel,
    loading: formModelLoading
  } = useRequest(
    () => {
      return useGet<{ isConvertible: boolean }>(
        `${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${Config.Api.IsFormConvertible}`,
        {
          params: {
            clusterId: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
            ...(props.isSkipNamespaceInParams
              ? {}
              : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
            resourceName: props.resourceEntity.resourceName
          }
        }
      )
    },
    {
      manual: true,
      formatResult(res) {
        isConvertible.value = res.data.isConvertible
        !res.data.isConvertible &&
          proxy.$Message.warning('如果配置中存在不支持的字段，表单模式将被禁用，您只能使用YAML模式进行编辑。')
        return res.data.isConvertible ? EnumResourceFormModel.Form : EnumResourceFormModel.Yaml
      }
    }
  )

  const {
    data: yamlData,
    loading: yamlLoading,
    run: getYaml
  } = useRequest(
    () => {
      const { resourceName, ...rest } = props.resourceEntity

      const params = {
        ...rest,
        cluster_id: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
        ...(props.isSkipNamespaceInParams
          ? {}
          : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
        resource_name: resourceName,
        is_edit: props.status === EnumFormStatus.Edit
      }
      return useGet(`${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`, {
        params
      })
    },
    {
      manual: true,
      formatResult: (res) => res.data.data,
      onSuccess() {
        nextTick(yamlRef.value.refresh())
      },
      initialData: INIT_YAML_DATA
    }
  )

  const {
    data: formData,
    loading: formDataLoading,
    run: getFormData
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${Config.Api.GetFormData}`, {
        params: {
          clusterId: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
          ...(props.isSkipNamespaceInParams
            ? {}
            : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
          resourceName: props.resourceEntity.resourceName
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => (props.onInitFormat ? props.onInitFormat(res.data.data) : res.data.data),
      initialData: props.formInitData ? cloneDeep(props.formInitData) : {}
    }
  )

  const onCancel = () => {
    visible.value = false
    formData.value = props.formInitData ? cloneDeep(props.formInitData) : {}
    yamlData.value = INIT_YAML_DATA
  }

  const onSubmit = async (params) => {
    if (formModel.value === EnumResourceFormModel.Form) {
      onFormSubmit(params)
    } else {
      onYamlSubmit(params)
    }
    isApplyToUnifiedCluster.value &&
      onSyncUnifiedCluster({
        name: formModel.value === EnumResourceFormModel.Form ? params?.data?.name : params?.data?.metadata?.name
      })
  }

  const onSyncUnifiedCluster = async (extraParams) => {
    const params = {
      clusterId: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
      ...(props.isSkipNamespaceInParams
        ? {}
        : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
      ...extraParams
    }
    const res = await usePost(
      `${Config.Api.Base}${Config.Api.Resource}${props.resourceType}${Config.Api.SyncUnifiedCluster}`,
      params,
      { skipLoading: false }
    )
    if (res.success) {
      syncUnifiedClusterModal.value = { visible: true, data: res.data.data, params }
    }
  }

  const onFormSubmit = async (params) => {
    const res = await usePost(
      `${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${
        props.status === EnumFormStatus.Blank ? Config.Api.CreateFormData : Config.Api.EditFormData
      }`,
      params,
      { skipLoading: false }
    )
    if (res.success) {
      visible.value = false
      proxy.$Message.success(props.status === EnumFormStatus.Blank ? '创建成功' : '编辑成功')
      proxy.$nextTick().then(() => {
        props.onSubmitCallBack?.()
      })
    }
  }
  const onYamlSubmit = async (params) => {
    const res = await usePost(
      `${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${
        props.status === EnumFormStatus.Blank ? Config.Api.CreateYamlData : Config.Api.EditYamlData
      }`,
      params,
      { skipLoading: false }
    )
    if (res.success) {
      visible.value = false
      proxy.$Message.success(props.status === EnumFormStatus.Blank ? '创建成功' : '编辑成功')
      props.onSubmitCallBack?.()
    }
  }
  const formatParams = () => {
    let params: FormSubmitParams = {
      clusterId: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
      ...(props.isSkipNamespaceInParams
        ? {}
        : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
      isApplyToUnifiedCluster: isApplyToUnifiedCluster.value,
      resourceName: props.resourceEntity.resourceName,
      data: undefined,
      ...(props.getPreviewParams ? props.getPreviewParams() : {})
    }
    if (formModel.value === EnumResourceFormModel.Form) {
      params.data = cloneDeep(formData.value)
      if (props.onSubmitFormat) {
        params = props.onSubmitFormat(params)
      }
    } else {
      params.data = yaml2json(yamlData.value)
    }
    console.log(params)
    return params
  }

  const onCheckBeforeSubmit = async () => {
    const params = formatParams()
    if (props.isSkipCheck) {
      proxy.$Modal.confirm({
        title: '提示',
        content: '是否确认提交？',
        loading: true,
        onOk: () => {
          onSubmit(params).then(() => {
            proxy.$Modal.remove()
          })
        }
      })
    } else {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { resourceName, ...checkParams } = params
      const res = await usePost(
        `${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${
          formModel.value === EnumResourceFormModel.Form ? Config.Api.CheckFormData : Config.Api.CheckYamlData
        }`,
        { ...checkParams, action: props.status === EnumFormStatus.Blank ? 'create' : 'update' },
        {
          skipLoading: false
        }
      )
      if (res.success) {
        if (res.data) {
          proxy.$Modal.confirm({
            title: '提示',
            content: res.data?.data,
            loading: true,
            onOk: () => {
              onSubmit(params).then(() => {
                proxy.$Modal.remove()
              })
            }
          })
        } else {
          proxy.$Modal.confirm({
            title: '提示',
            content: '是否确认提交？',
            loading: true,
            onOk: () => {
              onSubmit(params).then(() => {
                proxy.$Modal.remove()
              })
            }
          })
        }
      }
    }
  }

  const onReviewFormModel = async (isClickBySubmit = false) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { resourceName, ...checkParams } = formatParams()
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { resourceName: propsResourceName, ...rest } = props.resourceEntity

    const [currentYaml, lastYaml] = await Promise.all([
      formModel.value === EnumResourceFormModel.Form
        ? usePost(
            `${Config.Api.Base}${RESOURCE_URL}${props.resourceType}${Config.Api.ReviewFormData}`,
            { ...checkParams, action: props.status === EnumFormStatus.Blank ? 'create' : 'update' },
            {
              skipLoading: false
            }
          )
        : Promise.resolve({ data: { data: yamlData.value } }),
      props.status === EnumFormStatus.Edit
        ? useGet(`${Config.Api.Base}${Config.Api.Resource}${Config.Api.GetLatestYaml}`, {
            params: {
              ...rest,
              cluster_id: props.resourceEntity?.clusterId ?? K8SInstance.value.clusterId,
              ...(props.isSkipNamespaceInParams
                ? {}
                : { namespace: props.resourceEntity?.namespace ?? K8SInstance.value.namespace }),
              resource_name: resourceName,
              is_edit: props.status === EnumFormStatus.Edit
            }
          })
        : Promise.resolve({ data: { data: '' } })
    ])
    modal.value.data = currentYaml.data.data
    modal.value.lastYaml = lastYaml.data.data
    modal.value.isClickBySubmit = isClickBySubmit
    modal.value.visible = true
    nextTick(previewYamlRef.value.refresh())
  }

  watch(
    () => props.value,
    async () => {
      if (props.value) {
        isApplyToUnifiedCluster.value = false
        if (props.status === EnumFormStatus.Edit) {
          getYaml()
          if (props.forbiddenForm) {
            formModel.value = EnumResourceFormModel.Yaml
          } else {
            getFormModel().then(() => {
              isConvertible.value &&
                getFormData().then(() => {
                  dataReloadFlag.value = true
                })
            })
          }
        } else {
          // 创建不需要进行表单模式校验
          isConvertible.value = !props.forbiddenForm
          formModel.value = props.forbiddenForm ? EnumResourceFormModel.Yaml : EnumResourceFormModel.Form
          yamlData.value = props.yamlInitData || INIT_YAML_DATA
          nextTick(yamlRef.value.refresh())
          const formInitData = props.formInitData ? cloneDeep(props.formInitData) : {}
          formData.value = props.onInitFormat ? props.onInitFormat(formInitData) : formInitData
          dataReloadFlag.value = true
        }
      } else {
        dataReloadFlag.value = false
      }
    }
  )

  return {
    formModel,
    isConvertible,
    yamlData,
    yamlLoading,
    formData,
    formDataLoading,
    yamlRef,
    onCheckBeforeSubmit,
    dataReloadFlag,
    isUnifiedCluster,
    isApplyToUnifiedCluster,
    onReviewFormModel,
    modal,
    onCancel,
    formModelLoading,
    unifiedClusterList,
    syncUnifiedClusterModal,
    onSyncUnifiedCluster,
    previewYamlRef
  }
}
