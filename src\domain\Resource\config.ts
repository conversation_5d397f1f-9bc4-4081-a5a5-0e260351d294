export enum EnumIstioResourceType {
  VirtualService = 'VirtualService',
  DestinationRule = 'DestinationRule',
  PVC = 'PVC',
  Gateway = 'Gateway',
  Pod = 'Pod',
  ServiceEntry = 'ServiceEntry',
  SideCar = 'SideCar',
  StatefulSet = 'StatefulSet',
  Service = 'Service',
  EndPoint = 'EndPoint',
  CronJob = 'CronJob',
  Job = 'Job',
  Telemetry = 'Telemetry',
  RayJob = 'RayJob'
}

type EnumToUnion<T extends string> = `${T}`

export const YamlGVR: Record<
  EnumToUnion<EnumIstioResourceType>,
  {
    group: string
    version: string
    resource: string
  }
> = {
  VirtualService: {
    group: 'networking.istio.io',
    version: 'v1beta1',
    resource: 'virtualservices'
  },
  DestinationRule: {
    group: 'networking.istio.io',
    version: 'v1beta1',
    resource: 'destinationrules'
  },
  PVC: {
    group: '',
    version: 'v1',
    resource: 'persistentvolumeclaims'
  },
  Gateway: { resource: 'gateways', group: 'networking.istio.io', version: 'v1beta1' },
  Pod: { resource: 'pods', group: '', version: 'v1' },
  ServiceEntry: { resource: 'serviceentries', group: 'networking.istio.io', version: 'v1beta1' },
  SideCar: { resource: 'sidecars', group: 'networking.istio.io', version: 'v1beta1' },
  StatefulSet: { resource: 'statefulsets', group: 'apps', version: 'v1' },
  Service: {
    group: '',
    version: 'v1',
    resource: 'services'
  },
  EndPoint: {
    group: '',
    version: 'v1',
    resource: 'endpoints'
  },
  CronJob: {
    group: 'batch',
    version: 'v1',
    resource: 'cronjobs'
  },
  Job: {
    group: 'batch',
    version: 'v1',
    resource: 'jobs'
  },
  Telemetry: {
    group: 'telemetry.istio.io',
    version: 'v1alpha1',
    resource: 'telemetries'
  },
  RayJob: {
    group: 'ray.io',
    version: 'v1',
    resource: 'rayjobs'
  },
}

export enum EnumComponentType {
  AssociatedDeployment = 'AssociatedDeployment', // 关联deployment模式
  AssociatedStatefulset = 'AssociatedStatefulset', // 关联Statefulset模式
  Independent = 'Independent', // 独立模式
  Rayjob = 'Rayjob', // rayjob模式
}
