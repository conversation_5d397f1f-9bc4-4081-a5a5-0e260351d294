<template>
  <div>
    <Row :gutter="10">
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <Col :span="6">
        <Table
          style="cursor: pointer"
          :columns="rsColumns"
          :data="rsData"
          :row-class-name="rowReplicaSetClass"
          :loading="rsLoading"
          @on-row-click="setReplicasetRow"
          @on-current-change="setReplicaset"
          highlight-row
        >
          <template slot="rs" slot-scope="{ row, index }">
            <div style="padding: 16px 0 16px 0">
              <Row type="flex" :wrap="false" style="align-items: center">
                <Col :span="16">
                  <Tooltip :content="row.images" placement="bottom-start" :transfer="true" max-width="500">
                    <b>{{ row.name }}</b>
                  </Tooltip>
                </Col>
                <Col :span="8" style="display: flex">
                  <div style="margin-left: auto; display: flex; align-items: center">
                    <Button size="small" ghost type="primary" @click.stop="() => handleRsYaml(row.name)">YAML</Button>
                    <Button
                      @click.stop="handleViewRsEvents(row, index)"
                      size="small"
                      ghost
                      type="primary"
                      style="margin-left: 16px"
                      >事件</Button
                    >
                  </div>
                </Col>
              </Row>

              <Row style="margin-top: 16px">
                <Col :span="18">
                  <span>期望: {{ row.desired }}</span>
                  <span style="margin-left: 16px">当前: {{ row.current }}</span>
                  <span style="margin-left: 16px">就绪: {{ row.ready }}</span>
                </Col>
                <Col :span="6"> </Col>
              </Row>
            </div>
          </template>
        </Table>
      </Col>
      <Col :span="6">
        <Table
          style="cursor: pointer"
          class="pod-list-wrapper"
          :columns="podColumns"
          :data="podData"
          :loading="podLoading"
          highlight-row
          :row-class-name="rowPodClass"
          @on-row-click="setPodRow"
        >
          <template slot="pod" slot-scope="{ row }">
            <div style="padding: 16px 0 16px 0">
              <Row type="flex" align="middle">
                <Col :span="16">
                  <div class="line-ellipsis" :title="row.metadata.name">
                    <b>{{ row.metadata.name }}</b>
                  </div>
                </Col>
                <Col :span="8">
                  <Button
                    @click="handleDeletePod(row.metadata.name)"
                    size="small"
                    ghost
                    type="error"
                    style="float: right; margin-left: 16px"
                    >重建</Button
                  >
                  <Button
                    @click="handleOpenAnalyzer(row.metadata.name)"
                    size="small"
                    ghost
                    type="primary"
                    style="float: right"
                    >诊断</Button
                  >
                </Col>
              </Row>
              <Row style="margin-top: 16px" type="flex" :wrap="false">
                <Col :span="16">
                  <Space>
                    <span> {{ row.metadata.creationTimestamp | relativeTime }}</span>
                    <span>
                      <b style="background-color: #2a7cc3; color: #f9f9f9">P</b>
                      <span> {{ row.status.podIP }}</span>
                    </span>
                    <span>
                      <b style="background-color: #808695; color: #f9f9f9">H</b>
                      <span> {{ row.status.hostIP }}</span>
                    </span>
                  </Space>
                </Col>
                <Col :span="8">
                  <Button
                    @click="onOpenPortForwardModal(row.metadata.name)"
                    size="small"
                    ghost
                    type="primary"
                    style="float: right"
                    >端口转发</Button
                  >
                </Col>
              </Row>
              <Row style="margin-top: 16px" type="flex" :wrap="false">
                <Space :size="4">
                  <b class="line-ellipsis" :style="`color: ${getColor(row.statusData)};`" :title="row.statusData">{{
                    row.statusData
                  }}</b>
                  <span>|</span>
                  <span style="color: '#a3a3a3'">{{ row.reason ? row.reason : '正常运转' }}</span>
                </Space>
              </Row>
              <Row style="margin-top: 16px" type="flex" :wrap="false">
                <Col flex="auto">
                  <span>CPU / Mem（Usage）：</span>
                  <span>
                    {{ row.metrics.cpuUsage | parseMetricCPU }}
                    /
                    {{ row.metrics.memUsage | parseMetricMemory }}
                  </span>
                </Col>
              </Row>
            </div>
          </template>
        </Table>
      </Col>
      <Col :span="12">
        <Card>
          <EventTable :key="eventTableKey" :uuid="currentPod.metadata?.uid" :clusterId="clusterId" hide-refresh />
        </Card>
        <Card class="container">
          <Spin fix v-if="containerLoading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <div slot="title" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap">
            <span style="font-weight: bold" :title="currentPod.metadata?.name">
              [{{ containerList.length + initContainerList.length }}] 容器组 {{ currentPod.metadata?.name }}
            </span>
          </div>
          <div slot="extra">
            <a @click="() => handleRefreshContainer(this.currentReplicaset?.metadata.name)">刷新</a>
            <a href="#" @click="handlePodYaml">YAML</a>
          </div>
          <div v-if="showPodInfo">
            <div>
              <Row :gutter="20">
                <Col :span="8">
                  <div class="info-key">所在节点</div>
                  <div>{{ currentPod.status?.hostIP }}</div>
                </Col>
                <Col :span="8">
                  <div class="info-key">容器IP</div>
                  <div>{{ currentPod.status?.podIP }}</div>
                </Col>
                <Col :span="8">
                  <div class="info-key">状态</div>
                  <div>{{ currentPod.status?.phase }}</div>
                </Col>
              </Row>
              <template v-if="currentPod.status?.conditions !== undefined">
                <template v-if="currentPod.status?.conditions.length === 4">
                  <Row :gutter="20" style="margin-top: 16px">
                    <Col :span="12">
                      <Card :padding="5">
                        <Icon
                          v-if="currentPod.status?.conditions[3].status === 'True'"
                          type="md-checkmark-circle"
                          style="color: #19be6b"
                        />
                        <Icon v-else type="md-close-circle" style="color: #ed4014" />
                        <span style="margin-left: 8px; font-size: 12px">已调度</span>
                        <span style="float: right">
                          {{ currentPod.status?.conditions[3].lastTransitionTime | dateFormat }}</span
                        >
                      </Card>
                    </Col>
                    <Col :span="12">
                      <Card :padding="5">
                        <Icon
                          v-if="currentPod.status?.conditions[0].status === 'True'"
                          type="md-checkmark-circle"
                          style="color: #19be6b"
                        />
                        <Icon v-else type="md-close-circle" style="color: #ed4014" />
                        <span style="margin-left: 8px; font-size: 12px">已初始化</span>
                        <span style="float: right">
                          {{ currentPod.status?.conditions[0].lastTransitionTime | dateFormat }}</span
                        >
                      </Card>
                    </Col>
                  </Row>
                  <Row :gutter="20" style="margin-top: 16px">
                    <Col :span="12">
                      <Card :padding="5">
                        <Icon
                          v-if="currentPod.status?.conditions[1].status === 'True'"
                          type="md-checkmark-circle"
                          style="color: #19be6b"
                        />
                        <Icon v-else type="md-close-circle" style="color: #ed4014" />
                        <span style="margin-left: 8px; font-size: 12px">容器已就绪</span>
                        <span style="float: right">
                          {{ currentPod.status?.conditions[1].lastTransitionTime | dateFormat }}</span
                        >
                      </Card>
                    </Col>
                    <Col :span="12">
                      <Card :padding="5">
                        <Icon
                          v-if="currentPod.status?.conditions[2].status === 'True'"
                          type="md-checkmark-circle"
                          style="color: #19be6b"
                        />
                        <Icon v-else type="md-close-circle" style="color: #ed4014" />
                        <span style="margin-left: 8px; font-size: 12px">容器组已就绪</span>
                        <span style="float: right">
                          {{ currentPod.status?.conditions[2].lastTransitionTime | dateFormat }}</span
                        >
                      </Card>
                    </Col>
                  </Row>
                </template>
              </template>
            </div>
            <template v-if="initContainerList.length !== 0">
              <!--              Todo: 删除了 :key -->
              <Card style="margin-top: 16px" :padding="10" v-for="(c, idx) in initContainerList" :key="c.name">
                <Row>
                  <Col :span="2">
                    <img src="@/assets/docker.svg" style="height: 22px" />
                  </Col>
                  <Col :span="22">
                    <Row type="flex">
                      <Col :span="24">
                        <Row type="flex">
                          <Col :span="18">
                            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                              <Tag color="blue" style="margin-right: 16px">初始化容器</Tag>
                              <b :title="c.name">{{ c.name }}</b>
                            </div>
                          </Col>
                          <Col :span="6" class="custom-align-right">
                            <template v-for="(stateMap, state) in c.state">
                              <Tag v-if="state === 'running'" :key="'c' + state" color="success">{{ state }}</Tag>
                              <Tag v-else :key="'c' + state">{{ state }}</Tag>
                            </template>
                          </Col>
                        </Row>
                      </Col>
                    </Row>
                    <Row style="margin-top: 8px">
                      <Col :span="24">
                        <span style="font-size: 12px; color: #808695">镜像: {{ c.image }}</span>
                      </Col>
                    </Row>
                    <Row style="margin-top: 8px">
                      <Col :span="24">
                        <span style="font-size: 12px; color: #808695; margin-right: 8px"
                          >ExitCode:
                          {{
                            c?.lastState?.terminated?.exitCode !== undefined ? c?.lastState?.terminated?.exitCode : '-'
                          }}</span
                        >
                        <span style="font-size: 12px; color: #808695"
                          >Reason: {{ c?.lastState?.terminated?.reason ? c?.lastState?.terminated?.reason : '-' }}</span
                        >
                      </Col>
                    </Row>
                    <Row v-for="(stateMap, state) in c.state" style="margin-top: 8px" :key="'d' + state">
                      <Col :span="24">
                        <span v-for="(value, key) in stateMap" :key="'e' + key">
                          <span style="background-color: #f8f8f9; margin-left: 16px" v-if="key === 'startedAt'"
                            >{{ key }}: {{ value | dateFormat }}</span
                          >
                          <span style="background-color: #f8f8f9; margin-left: 16px" v-if="key === 'finishedAt'"
                            >{{ key }}: {{ value | dateFormat }}</span
                          >
                        </span>
                      </Col>
                    </Row>
                    <Row style="margin-top: 16px">
                      <Button
                        type="primary"
                        @click="
                          () => {
                            handleOpenLog(c.name)
                          }
                        "
                        size="small"
                        style="width: 70px; font-weight: bold"
                        >日志
                      </Button>
                    </Row>
                  </Col>
                </Row>
              </Card>
            </template>
            <template v-if="containerList.length !== 0">
              <Card style="margin-top: 16px" :padding="10" v-for="(c, idx) in containerList" :key="idx">
                <Row>
                  <Col :span="2">
                    <img src="@/assets/docker.svg" style="height: 22px" />
                  </Col>
                  <Col :span="22">
                    <Row type="flex">
                      <Col :span="24">
                        <Row type="flex">
                          <Col :span="18">
                            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                              <Tag color="primary" style="margin-right: 16px">容器</Tag>
                              <b :title="c.name">{{ c.name }}</b>
                            </div>
                          </Col>
                          <Col :span="6" class="custom-align-right">
                            <Poptip
                              trigger="hover"
                              title="资源 需求/限制"
                              style="margin-right: 16px"
                              width="300"
                              placement="left"
                            >
                              <Button ghost size="small" type="warning" style="padding: 0" icon="md-alert"></Button>
                              <template v-if="containerOrgList?.[idx]?.resources">
                                <div slot="content">
                                  <Row>
                                    <Col :span="6">
                                      <b>CPU</b>
                                    </Col>
                                    <Col :span="7">
                                      <b v-if="containerOrgList?.[idx]?.resources?.requests">{{
                                        containerOrgList?.[idx]?.resources?.requests?.cpu??'-'
                                      }}</b>
                                    </Col>
                                    <Col :span="4">
                                      <b v-if="containerOrgList?.[idx]?.resources?.limits">--></b>
                                    </Col>
                                    <Col :span="7">
                                      <b v-if="containerOrgList?.[idx]?.resources?.limits">{{
                                        containerOrgList?.[idx]?.resources?.limits?.cpu??'-'
                                      }}</b>
                                    </Col>
                                  </Row>
                                  <Row>
                                    <Col :span="6">
                                      <b>内存</b>
                                    </Col>
                                    <Col :span="7">
                                      <b v-if="containerOrgList?.[idx]?.resources.requests">{{
                                        containerOrgList?.[idx]?.resources.requests.memory
                                      }}</b>
                                    </Col>
                                    <Col :span="4">
                                      <b v-if="containerOrgList?.[idx]?.resources.limits">--></b>
                                    </Col>
                                    <Col :span="7">
                                      <b v-if="containerOrgList?.[idx]?.resources.limits">{{
                                        containerOrgList?.[idx]?.resources.limits.memory
                                      }}</b>
                                    </Col>
                                  </Row>
                                </div>
                              </template>
                            </Poptip>
                            <template v-for="(stateMap, state) in c.state">
                              <Tag v-if="state === 'running'" color="success" :key="'b' + state">{{ state }}</Tag>
                              <Tag v-else :key="'b' + state">{{ state }}</Tag>
                            </template>
                          </Col>
                        </Row>
                      </Col>
                    </Row>
                    <Row style="margin-top: 16px" :key="c.name">
                      <Col :span="24">
                        <Space>
                          <Space
                            v-if="containerMetrics !== null && containerMetrics[c.name] !== undefined"
                            style="width: auto"
                          >
                            <span style="color: #808695"
                              >CPU
                              <b style="color: #515a6e; font-size: 14px">{{
                                containerMetrics[c.name].cpuUsage | parseMetricCPU
                              }}</b>
                            </span>
                            <span style="color: #808695"
                              >内存
                              <b style="color: #515a6e; font-size: 14px">{{
                                containerMetrics[c.name].memUsage | parseMetricMemory
                              }}</b>
                            </span>
                            <span style="color: #515a6e">{{
                              (containerMetrics[c.name].timestamp * 1000) | dateFormat
                            }}</span>
                          </Space>

                          <div v-else style="display: inline-flex; color: #ed4014; font-size: 12px">
                            Not found metrics-server
                          </div>

                          <Button
                            :key="c.name"
                            :loading="metricsLoading"
                            shape="circle"
                            size="small"
                            icon="ios-refresh"
                            @click="fetchContainerMetrics"
                          >
                            刷新
                          </Button>
                        </Space>
                      </Col>
                    </Row>
                    <Row style="margin-top: 8px">
                      <Col :span="24">
                        <span style="font-size: 12px; color: #808695">镜像: {{ c.image }}</span>
                      </Col>
                    </Row>
                    <Row style="margin-top: 8px">
                      <Col :span="24">
                        <span style="font-size: 12px; color: #808695; margin-right: 8px"
                          >ExitCode:
                          {{
                            c?.lastState?.terminated?.exitCode !== undefined ? c?.lastState?.terminated?.exitCode : '-'
                          }}</span
                        >
                        <span style="font-size: 12px; color: #808695"
                          >Reason: {{ c?.lastState?.terminated?.reason ? c?.lastState?.terminated?.reason : '-' }}</span
                        >
                      </Col>
                    </Row>
                    <Row style="margin-top: 8px">
                      <Col :span="24">
                        <div style="display: flex">
                          <Tag v-if="c.started" color="green">
                            <Icon type="md-checkmark-circle" style="color: #19be6b; font-size: 12px" />
                            Started
                          </Tag>
                          <Tag v-else color="red">
                            <Icon type="md-close-circle" style="color: #ed4014; font-size: 12px"> </Icon>
                            Started
                          </Tag>
                          <Tag v-if="c.ready" color="green">
                            <Icon type="md-checkmark-circle" style="color: #19be6b; font-size: 12px" />
                            Ready
                          </Tag>
                          <Tag v-else color="red">
                            <Icon type="md-close-circle" style="color: #ed4014; font-size: 12px"> </Icon>
                            Ready
                          </Tag>
                          <Tag>
                            <Icon type="md-refresh" style="font-size: 12px" />
                            RestartCount: {{ c.restartCount }}
                          </Tag>
                        </div>
                      </Col>
                    </Row>
                    <Row v-for="(stateMap, state) in c.state" style="margin-top: 8px" :key="'c' + state">
                      <Col :span="24">
                        <span v-for="(value, key) in stateMap" :key="'d' + key">
                          <span style="background-color: #f8f8f9" v-if="key === 'startedAt'"
                            >{{ key }}: {{ value | dateFormat }}</span
                          >
                          <span style="background-color: #f8f8f9" v-if="key === 'finishedAt'"
                            >{{ key }}: {{ value | dateFormat }}</span
                          >
                        </span>
                      </Col>
                    </Row>

                    <Row style="margin-top: 8px">
                      <Poptip placement="top-start" width="450">
                        <Button
                          @click="
                            () => {
                              setContainerPort(idx)
                            }
                          "
                          size="small"
                          type="warning"
                          style="width: 70px; font-weight: bold"
                          >端口
                        </Button>
                        <div slot="content">
                          <Table size="small" :columns="portCols" :data="portData"></Table>
                        </div>
                      </Poptip>

                      <Button
                        type="error"
                        size="small"
                        style="margin-left: 16px; font-weight: bold; width: 80px"
                        @click="() => onBashClick(c.name)"
                      >
                        shell
                      </Button>

                      <Button
                        type="primary"
                        size="small"
                        @click="
                          () => {
                            handleOpenLog(c.name)
                          }
                        "
                        style="margin-left: 16px; width: 70px; font-weight: bold"
                        >日志
                      </Button>
                      <Dropdown
                        v-if="c.name === 'istio-proxy'"
                        trigger="click"
                        style="margin-left: 16px"
                        @on-click="
                          (loglevel) => {
                            handleProxyOpenLog(c.name, loglevel)
                          }
                        "
                        placement="top"
                      >
                        <Button type="primary" size="small" style="width: 80px; font-weight: bold"
                          >日志级别
                          <Icon type="ios-arrow-down"></Icon>
                        </Button>
                        <DropdownMenu slot="list">
                          <DropdownItem name="info" style="font-weight: bold">level info</DropdownItem>
                          <DropdownItem name="debug" style="font-weight: bold">level debug</DropdownItem>
                          <DropdownItem name="warning" style="font-weight: bold">level warning</DropdownItem>
                        </DropdownMenu>
                      </Dropdown>

                      <Button
                        v-if="c.restartCount !== 0"
                        type="primary"
                        size="small"
                        @click="() => openCrashLog(c.name)"
                        style="margin-left: 10px; width: 90px; font-weight: bold"
                        >崩溃日志
                      </Button>
                      <Button
                        v-if="c.name !== 'istio-proxy'"
                        type="primary"
                        size="small"
                        @click="() => onFileModalOpen(c)"
                        style="margin-left: 10px; width: 70px; font-weight: bold"
                        >上传下载
                      </Button>
                      <Button
                        type="primary"
                        size="small"
                        @click="() => handleOpenEphemeralContainerModal(c.name)"
                        style="margin-left: 10px; width: 80px; font-weight: bold"
                        >调试模式
                      </Button>
                    </Row>
                  </Col>
                </Row>
              </Card>
            </template>
          </div>
        </Card>
      </Col>
    </Row>
    <Drawer title="查看 YAML" width="60" v-model="openYAML" :mask-closable="false">
      <yaml v-model="currentYAML"></yaml>
    </Drawer>

    <pod-analyzer v-model="openAnalyzer" :analyze-data="analyzeData" :loading="analyzeLoading" />

    <CrashLogModal v-model="crashLogModalVisible" :entity="crashLogModalEntity" />

    <Modal v-model="rsEventModalVisible" footer-hide width="50" title="ReplicaSet 事件">
      <EventTable :uuid="rsUid2Event" :clusterId="clusterId" hide-refresh hideTitle />
    </Modal>

    <ContainerFileDownload :entity="fileEntity" v-model="fileModal" />

    <EphemeralContainerModal v-model="ephemeralContainerModal" :entity="ephemeralContainerEntity" />
    <PortForwardModal v-model="portForwardModal" :entity="portForwardEntity" type="AssociatedDeployment" />
  </div>
</template>

<script>
import Config from '@/config'
import { color } from '@/libs/consts'
import { usePost, useGet } from '@/libs/service.request'
import { Yaml, Space, LinkButton } from '@/components'
import { TimeTrans, getRelativeTime, relativeTime, formatMetricCPU, formatMetricMemory } from '@/libs/tools'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'

import PodAnalyzer from '@/domain/Resource/Pod/PodAnalyzer.vue'
import { EventTable, ContainerFileDownload, EphemeralContainerModal, CrashLogModal } from '@/domain/Resource'
import { PortForwardModal } from '@/domain/PortForward'

import { ApiResourceYaml } from '@/api/k8s/namespace/service'
import { ApiDeployPodDelete, ApiPodAnalyze } from '@/api/k8s/namespace/worload/deployment/pod'
import { ApiNsDeployRsList, ApiNsDeployRsPodList } from '@/api/k8s/namespace/workloads'
import { getPodStatusColor } from '@/domain/Resource/Pod/setting'
import { Input } from 'view-design'

export default {
  name: 'k8s-deployment-runtime',
  components: {
    EventTable,
    Yaml,
    PodAnalyzer,
    CrashLogModal,
    Space,
    ContainerFileDownload,
    EphemeralContainerModal,
    PortForwardModal
  },
  props: {
    loading: Boolean
  },
  computed: {
    color() {
      return color
    }
  },
  watch: {
    loading() {
      console.log('触发 Deployment 更新 - runtime')
      this.spinShow = true
      this.init()
      setTimeout(() => {
        this.spinShow = false
      }, 1000)
    }
  },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    },
    relativeTime: (msg) => {
      var t = new Date(msg)
      return getRelativeTime(t.getTime())
    },
    parseMetricCPU: formatMetricCPU,
    parseMetricMemory: formatMetricMemory
  },

  data() {
    return {
      rsUid2Event: '',
      openAnalyzer: false,
      analyzeData: {},
      analyzeLoading: false,
      metricsLoading: false,
      spinShow: false,
      openPortModal: false,
      openYAML: false,
      currentYAML: '',
      initContainerList: [],
      containerList: [],
      containerOrgList: [],
      containerMetrics: {},
      currentPod: {
        metadata: {
          name: ''
        }
      },
      currentReplicaset: {
        uid: ''
      },
      rsColumns: [
        {
          title: 'ReplicaSet (副本集)',
          slot: 'rs'
        }
      ],
      rsData: [],
      podColumns: [
        {
          title: 'Pod (容器组)',
          slot: 'pod',
          renderHeader: (h) =>
            h('div', { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' } }, [
              h('span', 'Pod (容器组)'),
              h('span', { style: { display: 'flex', justifyContent: 'flex-end', alignItems: 'center' } }, [
                h(Input, {
                  props: { placeholder: '请搜索pod 名称', search: true, clearable: true, value: this.filterPodName },
                  on: {
                    'on-search': () => this.onFilterPodName(),
                    'on-clear': () => this.onFilterPodName(),
                    input: this.setFilterPodName
                  },
                  style: { width: '80%', marginRight: '8px' }
                }),
                h(LinkButton, {
                  props: { text: '刷新' },
                  style: { fontWeight: '400' },
                  on: {
                    click: () => this.reloadPodTable(this.currentReplicaset.metadata.name, true)
                  }
                })
              ])
            ])
        }
      ],
      podData: [],
      originPodData: [],
      rsList: [],
      portData: [],
      portCols: [
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          align: 'center',
          width: 100
        },
        {
          title: 'ContainerPort',
          key: 'containerPort',
          width: 120,
          align: 'center',
          tooltip: true
        },
        {
          title: 'HostPort',
          key: 'hostPort',
          width: 100,
          align: 'center',
          tooltip: true
        },
        {
          title: 'Protocol',
          key: 'protocol',
          tooltip: true,
          align: 'center',
          width: 100
        }
      ],
      cluster: null,
      deployment: null,
      namespace: null,
      clusterId: null,
      uuid: null,

      rsLoading: false,
      podLoading: false,
      eventLoading: false,
      showPodInfo: false,
      containerLoading: false,

      currentRsRow: 0,
      currentPodRow: 0,

      rsEventModalVisible: false,

      eventTableKey: 0,
      fileEntity: {},
      fileModal: false,
      ephemeralContainerModal: false,
      ephemeralContainerEntity: {},
      crashLogModalVisible: false,
      crashLogModalEntity: {},
      portForwardEntity: {},
      portForwardModal: false,
      filterPodName: ''
    }
  },
  methods: {
    setFilterPodName(val) {
      this.filterPodName = val
    },
    async onFilterPodName() {
      if (this.filterPodName) {
        this.podData = this.originPodData?.filter((i) => i.metadata.name.includes(this.filterPodName))
      } else {
        this.podData = this.originPodData
      }
    },
    getColor(status) {
      return getPodStatusColor(status)
    },
    openCrashLog(container) {
      this.crashLogModalEntity = {
        clusterId: this.clusterId,
        cluster: this.cluster,
        namespace: this.namespace,
        podName: this.currentPod.metadata.name,
        container
      }

      this.crashLogModalVisible = true
    },
    handleOpenEphemeralContainerModal(container) {
      this.ephemeralContainerEntity = {
        clusterId: this.clusterId,
        cluster: this.cluster,
        namespace: this.namespace,
        pod: this.currentPod.metadata.name,
        container
      }

      this.ephemeralContainerModal = true
    },

    async handleOpenAnalyzer(podName) {
      this.openAnalyzer = true
      this.analyzeLoading = true
      await ApiPodAnalyze({
        clusterId: this.clusterId,
        namespace: this.namespace,
        name: podName
      })
        .then((res) => {
          this.analyzeData = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          setTimeout(() => {
            this.analyzeLoading = false
          }, 1000)
        })
      // clusterId, namespace
    },
    async handleDeletePod(name) {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认滚动重启: ${name}`,
        loading: true,
        onOk: async () => {
          await ApiDeployPodDelete({
            clusterId: this.clusterId,
            namespace: this.namespace,
            name: name
          })
            .then((res) => {
              noticeSucceed(this, 'succeed')
            })
            .catch((err) => {
              noticeError(this, errorMessage(err))
            })
          this.handleRefreshContainer(this.currentReplicaset?.metadata.name)
          this.$Modal.remove()
        }
      })
    },
    onBashClick(container) {
      let r = this.$router.resolve({
        path: '/pod-console',
        query: {
          clusterId: this.clusterId,
          namespace: this.namespace,
          cluster: this.cluster,
          pod: this.currentPod.metadata.name,
          container: container,
          priority: true
        }
      })
      window.open(r.href, '_blank')
      //   window.open(r.href, windowName, 'height=600, width=800, top=0, left=0, scrollbars=no')
    },
    handleViewRsEvents(row, index) {
      this.rsUid2Event = this.rsList[index]?.metadata.uid
      this.rsEventModalVisible = true
    },
    async fetchContainerMetrics() {
      this.metricsLoading = true
      await useGet(`${Config.Api.Base}${Config.Api.GetContainerMetrics}`, {
        params: {
          clusterId: this.clusterId,
          namespace: this.namespace,
          podName: this.currentPod.metadata?.name
        },
        // pod 重建时，会出现一分钟内的 404，所以需要跳过错误处理
        skipErrorHandler: true
      })
        .then((res) => {
          if (res.success && res.data.data?.length) {
            console.log(res.data.data)
            res.data.data.forEach((item) => {
              this.containerMetrics[item.containerName] = item
            })
          } else {
            this.containerMetrics = {}
          }
        })
        .catch((error) => {
          this.containerMetrics = {}
        })
        .finally(() => {
          this.metricsLoading = false
        })
    },
    async handleProxyOpenLog(container, loglevel) {
      await usePost(`${Config.Api.Base}${Config.Api.SetDeployPodSidecarLogLevel}`, {
        clusterId: this.clusterId,
        namespace: this.namespace,
        pod: this.currentPod.metadata.name,
        level: loglevel
      })
        .then((res) => {
          noticeSucceed(this, `设置日志级别成功`)
        })
        .catch((err) => {
          noticeError(this, `设置日志级别失败. ${errorMessage(err)}`)
        })
    },
    handleOpenLog(container) {
      let r = this.$router.resolve({
        path: '/kubernetes/logs',
        query: {
          clusterId: this.clusterId,
          namespace: this.namespace,
          deployment: this.deployment,
          cluster: this.cluster,
          pod: this.currentPod.metadata.name,
          container: container
        }
      })
      window.open(r.href, '_blank')
    },
    handleRsYaml(name) {
      this.currentYAML = ''
      ApiResourceYaml({
        cluster_id: this.clusterId,
        namespace: this.namespace,
        resource_name: name,
        kind: 'ReplicaSet'
      })
        .then((res) => {
          this.currentYAML = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      this.openYAML = true
    },
    handlePodYaml() {
      this.currentYAML = ''
      ApiResourceYaml({
        cluster_id: this.clusterId,
        namespace: this.namespace,
        resource_name: this.currentPod.metadata.name,
        kind: 'Pod'
      })
        .then((res) => {
          this.currentYAML = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      this.openYAML = true
    },
    async handleRefreshContainer(rsName) {
      this.containerLoading = true
      await this.reloadPodTable(rsName)
      this.setPodRow(null, this.currentPodRow)
      this.fetchContainerMetrics()
    },
    async reloadReplicasetTable() {
      this.rsLoading = true
      await ApiNsDeployRsList({
        clusterId: this.clusterId,
        namespace: this.namespace,
        name: this.deployment
      })
        .then((res) => {
          let data = res.data.data.data
          if (data !== null) {
            this.rsList = data
            this.rsData = data.map((item) => {
              let imageList = item.spec.template.spec.containers.map((item) => {
                return item.image
              })

              return {
                name: item.metadata.name,
                desired: item.metadata.annotations['deployment.kubernetes.io/desired-replicas'],
                current: item.status.replicas,
                ready: item.status.readyReplicas === undefined ? 0 : item.status.readyReplicas,
                age: relativeTime(item.metadata.creationTimestamp),
                events: [],
                images: `Images: ${imageList.join(',')}`
              }
            })
          }
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          setTimeout(() => {
            this.rsLoading = false
          }, 700)
        })
    },
    async reloadPodTable(rsName, isForceReloadContainer) {
      this.podLoading = true
      await ApiNsDeployRsPodList({
        clusterId: this.clusterId,
        namespace: this.namespace,
        rsName
      })
        .then((res) => {
          let data =
            res.data.data.data?.map((i) => ({
              ...i?.pod,
              metrics: i.metrics,
              // i?.pod.status是一个复杂对象，不能覆盖重写status覆盖...i?.pod，后续优化掉
              //   status: i.status,
              statusData: i.status,
              reason: i.reason
            })) ?? []

          this.originPodData = data
          this.onFilterPodName()
          isForceReloadContainer && this.handleRefreshContainer(rsName)
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      setTimeout(() => {
        this.podLoading = false
      }, 700)
    },

    setContainerPort(idx) {
      this.portData = this.containerOrgList?.[idx]?.ports
    },
    async setPodRow(row, index) {
      this.containerLoading = true
      this.currentPodRow = index
      this.currentPod = this.podData[index] ?? {}
      this.fetchContainerMetrics()
      this.setInitContainerList()
      this.setContainerList()
      this.showPodInfo = true
      this.eventTableKey = Math.random()

      setTimeout(() => {
        this.containerLoading = false
      }, 500)
    },
    rowPodClass(row) {
      if (row.metadata.name === this.currentPod.metadata.name) {
        return 'demo-table-info-row'
      }
      return ''
    },
    clearCurrentPod() {
      this.currentPod = {
        metadata: {
          name: ''
        }
      }
      this.initContainerList = []
      this.containerList = []
    },
    setInitContainerList() {
      if (this.currentPod.status?.initContainerStatuses !== undefined) {
        this.initContainerList = this.currentPod.status.initContainerStatuses
      }
    },
    setContainerList() {
      if (this.currentPod.status?.containerStatuses !== undefined) {
        this.containerList = this.currentPod.status.containerStatuses
        this.containerOrgList = []
        this.containerList.forEach((cs) => {
          this.currentPod.spec.containers.forEach((c) => {
            if (c.name === cs.name) {
              this.containerOrgList.push(c)
            }
          })
        })
      }
    },
    setReplicaset(cur, old) {},
    async setReplicasetRow(row, index) {
      console.log('setReplicasetRow', index)
      this.currentRsRow = index
      this.currentReplicaset = this.rsList[index]
      await this.reloadPodTable(this.rsData[index].name)
      // 设置 pod table, 初始化为第0行
      if (this.podData.length !== 0) {
        this.setPodRow(null, 0)
      } else {
        this.showPodInfo = false
        this.clearCurrentPod()
        this.eventData = []
      }
    },
    rowReplicaSetClass(row, index) {
      if (index === this.currentRsRow) {
        return 'demo-table-info-row'
      }
      return ''
    },

    async init() {
      await this.reloadReplicasetTable()
      if (this.rsData.length !== 0) {
        // 取第一个存在pod的
        const index = this.rsData.findIndex((i) => i.replicas !== 0 && i.current !== 0)
        this.setReplicasetRow(null, index === -1 ? 0 : index)
      }
    },

    onFileModalOpen(record) {
      this.fileEntity = {
        container: record.name,
        pod: this.currentPod.metadata.name,
        clusterId: this.clusterId,
        namespace: this.namespace
      }
      this.fileModal = true
    },
    onOpenPortForwardModal(relativePodName) {
      this.portForwardEntity = {
        clusterId: this.clusterId,
        cluster: this.cluster,
        namespace: this.namespace,
        relativePodName,
        relativeName: this.deployment
      }
      this.portForwardModal = true
    }
  },
  mounted() {
    let query = this.$route.query
    this.cluster = query.cluster
    this.clusterId = query.clusterId
    this.namespace = query.namespace
    this.deployment = query.deployment
    this.uuid = query.uuid
    this.init()
  }
}
</script>

<style scoped lang="less">
.pod-list-wrapper {
  /deep/.ivu-table-cell {
    width: 100%;
  }
}

/deep/.ivu-card-head {
  background-color: antiquewhite;
}

.custom-card-pointer:hover {
  cursor: pointer;
}

.custom-card {
  color: #f9f9f9;
  background-color: #348eed;
}
.container {
  margin-top: 16px;
}
.container :deep(.ivu-card-extra) {
  top: 10px;
}
.container :deep(.ivu-card-extra) a {
  font-size: 12px;
}
.container :deep(.ivu-card-extra) a:first-child {
  margin-right: 16px;
}
/*:deep( .ivu-table) .demo-table-info-row td {*/
/*  color: #F9F9F9 !important;*/
/*  background-color: #29be8f !important;;*/
/*}*/

/*:deep( .ivu-table-row-hover) td{*/
/*  color: #F9F9F9 !important;*/
/*  background-color: #29be8f !important;;*/
/*}*/

/*:deep(.ivu-table-row-highlight) td{*/
/* color: #F9F9F9 !important;*/
/*  background-color: #29be8f !important;;*/
/*}*/

/deep/.modal-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    position: relative;
    height: 84vh;
    overflow: auto;
  }
}

.crash-log-modal-title,
.crash-log-modal-title span {
  display: flex;
  align-items: center;
  .title {
    font-weight: 600;
  }
  .ivu-input-number {
    width: 120px;
  }
  .header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #2a7cc3;
    cursor: pointer;
    > .header-icon {
      margin-right: 8px;
    }
  }
  .space-component {
    display: inline-flex;
    width: auto;
    align-items: center;
  }
}
</style>
