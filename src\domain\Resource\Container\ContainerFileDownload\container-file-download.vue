<script lang="ts" setup>
import { PropType } from 'vue'

import dayjs from 'dayjs'

import { Space, Empty } from '@/components'
import { formatFileSize } from '@/libs/tools'
import Config from '@/config'
import useContainerFileDownloadService from './useContainerFileDownloadService'

interface Entity {
  container: string
  clusterId: number
  namespace: string
  pod: string
}

const props = defineProps({
  value: Boolean,
  entity: { type: Object as PropType<Entity>, default: () => ({}) }
})

defineEmits(['input'])

const {
  split,
  fileTreeData,
  loadTreeChildren,
  selectDirPath,
  tempFileList,
  fileListLoading,

  onDownloadFile,
  onSearchFileName,
  onUpload
} = useContainerFileDownloadService(props)
</script>

<template>
  <Modal
    :title="`${props.entity.container} 文件管理`"
    width="65"
    footer-hide
    :mask-closable="false"
    :value="props.value"
    @on-visible-change="
      (val) => {
        $emit('input', val)
      }
    "
    @on-cancel="
      () => {
        $emit('input', false)
      }
    "
  >
    <Alert show-icon>文件名不允许带空格，否则会影响文件的展示。</Alert>
    <div class="file-wrapper">
      <Split v-model="split" min="300px" max="450px">
        <div slot="left" class="split-pane">
          <div class="box-title">目录列表</div>
          <Tree :data="fileTreeData" :load-data="loadTreeChildren" expand-node></Tree>
        </div>
        <div slot="right" class="split-pane">
          <div class="box-title">文件列表</div>
          <div class="dir">
            <span>当前目录：{{ selectDirPath ? selectDirPath : '/' }}</span>
            <Upload action="123" :before-upload="onUpload" name="uploadFile">
              <div class="header-btn">
                <Icon class="header-icon" type="md-cloud-upload" />
                上传
              </div>
            </Upload>
          </div>

          <div class="search">
            <Input placeholder="搜索文件名" search @on-search="onSearchFileName" />
          </div>
          <div class="file-list-wrapper">
            <Spin fix v-if="fileListLoading" />
            <template v-if="tempFileList?.length">
              <div v-for="item in tempFileList" :key="item.name" class="file">
                <Space class="basic-info" :size="8">
                  <Icon type="md-document" />
                  <div class="basic-info-name" :title="item.name">{{ item.name }}</div>
                  <div class="basic-info-size">{{ formatFileSize(item.size) }}</div>
                  <div class="basic-info-time">
                    {{ dayjs(item.lastModifyTime * 1000).format('YYYY-MM-DD HH:mm:ss') }}
                  </div>
                </Space>
                <div class="header-btn" @click="() => onDownloadFile(item)">
                  <Icon class="header-icon" type="md-download" />
                  下载
                </div>
              </div>
            </template>
            <Empty v-else />
          </div>
        </div>
      </Split>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.file-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  height: 60vh;
  width: 100%;

  .header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #2a7cc3;
    cursor: pointer;
    > .header-icon {
      font-size: 16px;
      margin-right: 8px;
    }
  }

  .split-pane {
    position: relative;
    padding: 16px;
    height: 100%;
    .box-title {
      font-weight: 600;
      font-size: 14px;
    }
    .ivu-tree {
      height: ~'calc(100% - 16px)';
      overflow: auto;
      /deep/.ivu-tree-title {
        width: ~'calc(100% - 24px)';
      }
      /deep/.dir-title {
        align-items: center;
        font-size: 14px;
        > span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 90%;
        }
      }
      /deep/.ivu-tree-title .selected {
        & {
          background: #d3eafd;
          border-radius: 2px;
          margin-left: -4px;
          padding-left: 4px;
          width: ~'calc(100% + 8px)';
        }
        .ivu-icon {
          color: #2a7cc3;
        }
      }
    }
    .dir {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px 0 0;
      margin-top: 8px;
      font-weight: 600;
    }
    .search {
      margin-top: 8px;
    }
    .file-list-wrapper {
      height: ~'calc(100% - 96px)';
      overflow: auto;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
      padding: 8px;
      margin-top: 8px;
      position: relative;
      background: #f9f9f9;
      .file {
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 4px;
        border: 1px dashed #e8e8e8;
        padding: 8px;
        background: #fff;
        &:not(:last-child) {
          margin-bottom: 8px;
        }
        .basic-info {
          display: flex;
          align-items: center;
          flex: 1;
          width: ~'calc(100% - 64px)';
          margin-right: 16px;
          > .ivu-icon {
            font-size: 16px;
          }
          &-name {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          &-size {
          }
        }
      }
    }
  }
}
</style>
