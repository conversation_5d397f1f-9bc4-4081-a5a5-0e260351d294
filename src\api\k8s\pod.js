import axios from '@/libs/api.request'

// 获取pod list
export const ApiGetPodList = (params) => {
  return axios.request({
    url: '/api/v1/resource/pod/list',
    method: 'get',
    params
  })
}

// 获取pod detail
export const ApiGetPodDetail = (params) => {
  return axios.request({
    url: '/api/v1/resource/pod',
    method: 'get',
    params
  })
}

// 新建 pod
export const ApiCreatePod = (data) => {
  return axios.request({
    url: '/api/v1/resource/pod/create',
    method: 'post',
    data
  })
}

// 删除 pod
export const ApiDeletePod = (data) => {
  return axios.request({
    url: '/api/v1/resource/pod/delete',
    method: 'delete',
    data
  })
}

// 编辑 pod
export const ApiUpdatePod = (data) => {
  return axios.request({
    url: '/api/v1/resource/pod/update',
    method: 'post',
    data
  })
}


// metrics
export const ApiPodMetrics = (clusterId, namespace, pods) => {
  return axios.request({
    url: '/api/v1/resource/pod-metrics/list',
    method: 'post',
    data: {
      clusterId: clusterId,
      namespace: namespace,
      pods: pods
    }
  })
}
