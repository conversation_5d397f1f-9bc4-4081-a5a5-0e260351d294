<template>
  <Submenu :name="`${parentName}`">
    <template slot="title">
      <common-icon v-if="parentItem.icon" :type="parentItem.icon || ''" />
      <span v-else class="blank-block" />
      <span>{{ showTitle(parentItem) }}</span>
    </template>
    <template v-for="item in children">
      <template v-if="item.children && item.children.length === 1">
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item
          v-else
          :name="getNameOrHref(item, true)"
          style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
          :key="`menu-${item.children[0].name}`"
        >
          <common-icon v-if="item.children[0]?.icon" :type="item.children[0].icon || ''" />
          <span v-else class="blank-block" />
          <span :title="showTitle(item, currentNamespace)">{{ showTitle(item, currentNamespace) }}</span>
        </menu-item>
      </template>
      <template v-else>
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item
          v-else
          style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
          :name="getNameOrHref(item)"
          :key="`menu-${item.name}`"
        >
          <common-icon v-if="item?.icon" :type="item?.icon || ''" />
          <span v-else class="blank-block" />
          <span :title="showTitle(item, currentNamespace)">{{ showTitle(item, currentNamespace) }}</span>
        </menu-item>
      </template>
    </template>
  </Submenu>
</template>
<script>
import mixin from './mixin'
import itemMixin from './item-mixin'
export default {
  name: 'SideMenuItem',
  mixins: [mixin, itemMixin],
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    }
  }
}
</script>
<style scoped>
.blank-block {
  width: 16px;
  height: 16px;
  display: inline-block;
}
</style>
