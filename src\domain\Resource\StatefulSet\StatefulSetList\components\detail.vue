<template>
  <div>
    <Card>
      <h4>基本信息</h4>
      <Row type="flex" :gutter="20" style="margin-top: 20px">
        <Col span="10" class="info-key">名称</Col>
        <Col span="14" class="info-value">{{ innerDetail.name }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">命名空间</Col>
        <Col span="14" class="info-value">{{ innerDetail.namespace }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">创建时间</Col>
        <Col span="14" class="info-value">{{ (innerDetail.creationTimestamp * 1000) | dateFormat }}</Col>
      </Row>
      <Row type="flex" :gutter="20" style="margin-top: 16px">
        <Col span="10" class="info-key">标签</Col>
        <Col span="14" class="info-value">
          <template v-for="(value, key) in innerDetail.label">
            <Tag color="#2a7cc3" :key="key"
              ><b>{{ key }}: {{ value }}</b></Tag
            >
          </template>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px">
      <div style="margin: 8px 0px; display: flex; align-item: center; justify-content: space-between">
        <h4>关联 Pod</h4>
        <Button @click="fetchPods" size="small" icon="ios-refresh" type="primary" ghost>刷新</Button>
      </div>
      <Table size="small" :columns="podCols" :data="podData">
        <template slot-scope="{ row, index }" slot="btns">
          <a @click="handleYamlGet(row.name)" style="margin-right: 16px; cursor: pointer; color: #2a7cc3">YAML</a>
          <template v-for="(action, idx) in actionList">
            <ContainerPoptip
              v-if="row.containerList.length !== 0"
              :key="row.name + action + idx"
              :clusterId="currentClusterId"
              :namespace="currentNamespace"
              :pod="row.name"
              :text="action"
              style="margin-right: 16px"
            >
              <template #action="{ record }">
                <LinkButton
                  :disabled="action === '上传下载' && row.name === 'istio-proxy'"
                  :text="action"
                  @click="() => onActionBtnClick(action, row.name, record.name)"
                />
              </template>
            </ContainerPoptip>
          </template>
          <a @click="deletePod(row.name)" style="cursor: pointer; color: #ed4014">删除</a>
        </template>
      </Table>
    </Card>
    <Card style="margin-top: 16px">
      <EventTable :key="eventListKey" :uuid="detail.uuid" :clusterId="currentClusterId" />
    </Card>
    <Drawer :title="yamlTitle" width="45" v-model="openYaml" :mask-closable="false">
      <div style="max-height: 90%; overflow: auto">
        <yaml v-model="currentYaml" ref="refYaml" />
      </div>
    </Drawer>
    <ContainerFileDownload :entity="fileEntity" v-model="fileModal" />
    <EphemeralContainerModal v-model="ephemeralContainerModal" :entity="ephemeralContainerEntity" />
  </div>
</template>

<script>
import { TimeTrans, relativeTime } from '@/libs/tools'
import { color } from '@/libs/consts'
import { ApiResourceYamlGet } from '@/api/k8s/resource'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { Yaml, LinkButton } from '@/components'
import { ApiStatefulSetPodDelete, ApiStatefulSetPodList } from '@/api/k8s/namespace/app/app'
import { EventTable, ContainerFileDownload, EphemeralContainerModal } from '@/domain/Resource'
import ContainerPoptip from './ContainerPoptip.vue'

export default {
  name: 'detail',
  components: { Yaml, EventTable, ContainerFileDownload, LinkButton, EphemeralContainerModal, ContainerPoptip },
  props: {
    detail: Object
  },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  data() {
    return {
      eventListKey: 0,
      yamlTitle: '',
      currentYaml: '',
      openYaml: false,
      innerDetail: {},
      podCols: [
        {
          title: 'Name',
          key: 'name',
          tooltip: true,
          minWidth: 200,
          fixed: 'left'
        },
        {
          title: 'Ready',
          key: 'ready',
          tooltip: true,
          width: 100,
          align: 'center'
        },
        {
          title: 'Status',
          key: 'status',
          width: 120,
          align: 'center',
          tooltip: true,

          render: (h, params) => {
            return h(
              'span',
              {
                style: {
                  color: this.getColor(params.row.status),
                  fontWeight: 700
                }
              },
              params.row.status
            )
          }
        },
        {
          title: 'CPU / Mem (Usage)',
          key: 'metrics',
          minWidth: 170
        },
        {
          title: 'Restarts',
          key: 'restart',
          align: 'center',
          width: 100,
          tooltip: true
        },
        {
          title: 'Age',
          key: 'age',
          align: 'center',
          width: 150,
          tooltip: true,
          render: (h, params) => {
            return h('span', {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'IP',
          key: 'ip',
          width: 150,
          tooltip: true,
          align: 'center'
        },
        {
          title: 'Node',
          key: 'nodeIp',
          width: 150,
          tooltip: true,
          align: 'center'
        },
        {
          title: '操作',
          key: 'ops',
          width: 340,
          align: 'center',
          fixed: 'right',
          slot: 'btns'
        }
      ],
      podData: [],
      intervalObj: {
        pods: null
      },
      fileModal: false,
      fileEntity: {},
      ephemeralContainerModal: false,
      ephemeralContainerEntity: {},
      actionList: ['SHELL', '日志', '上传下载', '调试模式']
    }
  },
  methods: {
    onActionBtnClick(action, ...args) {
      switch (action) {
        case 'SHELL':
          this.handleOpenShell(...args)
          break
        case '日志':
          this.handleOpenLog(...args)
          break
        case '上传下载':
          this.handleOpenFileDownloadModal(...args)
          break
        case '调试模式':
          this.handleOpenEphemeralContainerModal(...args)
          break
        default:
          break
      }
    },
    handleOpenEphemeralContainerModal(podName, containerName) {
      this.ephemeralContainerEntity = {
        container: containerName,
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        pod: podName,
        cluster: this.currentCluster
      }
      this.ephemeralContainerModal = true
    },

    handleOpenLog(podName, containerName) {
      let r = this.$router.resolve({
        path: '/kubernetes/logs',
        query: {
          clusterId: this.currentClusterId,
          namespace: this.currentNamespace,
          cluster: this.currentCluster,
          pod: podName,
          container: containerName
        }
      })
      window.open(r.href, '_blank')
    },
    async handleOpenShell(podName, containerName) {
      try {
        const r = this.$router.resolve({
          path: '/pod-console',
          query: {
            clusterId: this.currentClusterId,
            cluster: this.currentCluster,
            namespace: this.currentNamespace,
            pod: podName,
            container: containerName,
            priority: true
          }
        })
        window.open(r.href, '_blank')
      } catch (error) {
        noticeError(this, `获取Terminal失败 ${errorMessage(error)}`)
      }
    },
    async handleOpenFileDownloadModal(podName, containerName) {
      this.fileEntity = {
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        pod: podName,
        container: containerName
      }
      this.fileModal = true
    },

    async handleYamlGet(resourceName) {
      let resource
      let group
      let version
      resource = 'pods'
      group = ''
      version = 'v1'
      // }
      this.yamlTitle = resourceName
      await ApiResourceYamlGet({
        resource: resource,
        group: group,
        version: version,
        cluster_id: this.currentClusterId,
        namespace: this.currentNamespace,
        is_edit: false,
        resource_name: resourceName
      })
        .then((res) => {
          this.openYaml = true
          this.currentYaml = res.data.data.data
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
        })
      this.$nextTick(() => {
        this.$refs.refYaml.refresh()
      })
    },
    getColor(status) {
      switch (status && status.toLowerCase()) {
        case 'running':
          return color['success']
        case 'pending':
          return color['info']
        default:
          return color['error']
      }
    },
    async fetchPods() {
      await ApiStatefulSetPodList({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: this.innerDetail.name
      })
        .then((res) => {
          this.podData = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async deletePod(name) {
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>确认重建: ${name}</p>`,
        loading: true,
        onOk: async () => {
          await ApiStatefulSetPodDelete({
            clusterId: this.currentClusterId,
            namespace: this.currentNamespace,
            name: name
          })
            .then((res) => {
              noticeSucceed(this, 'succeed')
              this.fetchPods()
            })
            .catch((err) => {
              noticeError(this, errorMessage(err))
            })
          this.fetchPods()
          this.eventListKey = Math.random()
          this.$Modal.remove()
        }
      })
    }
  },
  mounted() {
    this.innerDetail = this.detail
    this.fetchPods()
  }
}
</script>

<style scoped></style>
