import Vue from 'vue'
import Router from 'vue-router'

import { LoadingBar } from 'view-design'
import store from '@/store'
import config from '@/config'
import routes from './routers'
import { axiosInterceptorRequest } from '@/libs/service.request'

const { setToken, getToken, canTurnTo, setTitle } = require('@/libs/util')

const { homeName } = config

let lastRoute = null
export function getLastRoute() {
  return lastRoute
}
Vue.use(Router)
const router = new Router({
  routes,
  mode: 'history'
})
const LOGIN_PAGE_NAME = 'login'
const NAMESPACE_SWITCH = 'namespace-switch'

const turnTo = (to, access, next) => {
  if (canTurnTo(to.name, access, routes)) next() // 有权限，可访问
  else next({ replace: true, name: 'error_401' }) // 无权限，重定向到401页面
}

router.beforeEach((to, from, next) => {
  LoadingBar.start()
  // 保存url中携带的token
  if (to.query.token) {
    setToken(to.query.token)
    axiosInterceptorRequest({
      headers: {
        Authorization: 'Bearer ' + to.query.token
      }
    })

    next({ path: to.fullPath.replace(/&token=[^&]*/, '') })
  }
  const token = getToken()
  if (!token && to.name !== LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    next({
      name: LOGIN_PAGE_NAME // 跳转到登录页
    })
  } else if (!token && to.name === LOGIN_PAGE_NAME) {
    // 未登陆且要跳转的页面是登录页
    next() // 跳转
  } else if (token && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    next({
      name: homeName // 跳转到homeName页
    })
  } else {
    if (store.state.user.hasGetInfo) {
      turnTo(to, store.state.user.access, next)
    } else {
      store
        .dispatch('getUserInfo')
        .then((user) => {
          // 拉取用户信息，通过用户权限和跳转的页面的name来判断是否有权限访问;access必须是一个数组，如：['super_admin'] ['super_admin', 'admin']
          turnTo(to, user.access, next)
        })
        .catch(() => {
          setToken('')
          next({
            name: 'login'
          })
        })
    }
  }
})

router.afterEach((to, from) => {
  if (process.env.VUE_APP_ENV === 'production' || process.env.VUE_APP_ENV === 'alpha') {
    const matomo = (window as any).Piwik?.getTracker()

    if (!matomo?.getUserId()) {
      matomo?.setUserId(store.state.user.userName)
    }
    matomo?.setCustomUrl(window.location.origin + to.fullPath) // 如果使用哈希模式，需要把#移除
    const title = (to?.meta?.title as string) || document.title
    matomo?.trackPageView(title)
  }
  setTitle(to, router.app)
  lastRoute = from
  LoadingBar.finish()
  window.scrollTo(0, 0)
})

export default router
