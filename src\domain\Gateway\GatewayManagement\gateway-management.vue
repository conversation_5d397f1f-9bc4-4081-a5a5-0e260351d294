<script lang="ts" setup>
import { getCurrentInstance } from 'vue'

import { relativeTime } from '@/libs/tools'
import { ViewYaml } from '@/components/yaml'
import ProTable from '@/components/pro-table'
import { Space, Container, LinkButton, K8SSwitch } from '@/components'
import LogMergeModal from '@/domain/Resource/LogMergeModal/index.vue'
import { GatewayInfo } from '@/domain/Gateway'

import { TABLE_COLUMNS, LOG_COLUMNS } from './setting'
import useGatewayManagementService, { GatewayNamespaceList } from './useGatewayManagementService'

const { proxy } = getCurrentInstance()
const {
  clusterId,
  cluster,
  namespace,

  getTableData,
  refObject,
  onViewDetail,
  onViewYaml,
  viewYamlVisible,
  formEntity,
  yamlHistoryParams,

  gatewayInfo,
  gatewayInfoModal,
  onViewRoute,

  openLogModal,
  onOpenYamlFromTable,
  jumpToPodLog,
  getLogModalTableData,
  logModal,
  refPodObject,
  viewPodYamlVisible,
  podYamlEntity,

  logMergeModal,
  openLogMergeModal
} = useGatewayManagementService()
</script>

<template>
  <Container>
    <template #header-right>
      <K8SSwitch
        :filterNamespace="(namespace) => GatewayNamespaceList.includes(namespace.name)"
        setFirstNamespaceWhileIllegal
      />
    </template>
    <Alert show-icon>
      <p style="margin-bottom: 8px">
        <strong>网关管理</strong>仅限于各集群中 istio-ingress 和 istio-system 命名空间下的网关实例和网关资源。
      </p>
      <p>当前数据表是网关实例（Deployment资源）。</p>
    </Alert>

    <pro-table
      :columns="TABLE_COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uid"
      :search="[
        { value: 'DNS', label: '域名' },
        { value: 'Deployment', label: '实例', initData: proxy.$route.query.deploymentName },
        { value: 'Gateway', label: '资源名', initData: proxy.$route.query.name }
      ]"
    >
      <template #name="{ row }">
        <link-button @click="() => onViewYaml(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => openLogMergeModal(row)" text="网关日志" />
          <link-button @click="() => openLogModal(row)" text="关联Pod" />
          <link-button @click="() => onViewRoute(row)" text="路由图" />
          <link-button @click="() => onViewDetail(row)" text="网关配置" />
        </space>
      </template>
    </pro-table>
    <Modal ref="gatewayInfoModal" v-model="gatewayInfo.visible" footer-hide width="80" :title="gatewayInfo.data?.name">
      <GatewayInfo
        :init-flag="gatewayInfo.visible"
        :resourceName="gatewayInfo.data ? gatewayInfo.data.name : ''"
        :namespace="namespace"
        :clusterId="clusterId"
        :clusterName="cluster"
        v-if="gatewayInfo.visible"
      />
    </Modal>
    <view-yaml
      resourceType="deployment"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      :isCheckYaml="false"
      notSynchronizeToUnifiedCluster
    />

    <Modal
      v-model="logModal.visible"
      title="Pod列表"
      footer-hide
      width="50"
      class-name="vertical-center-modal pod-modal"
    >
      <pro-table
        :columns="LOG_COLUMNS"
        :request="getLogModalTableData"
        manual-request
        :action-ref="refPodObject"
        row-key="uid"
        :search="false"
        :height="300"
        size="small"
      >
        <template #name="{ row }">
          <LinkButton @click="() => onOpenYamlFromTable(row)" :text="row.name" class="ellipsis-text" ellipsis />
        </template>
        <template #age="{ row }">
          <div>{{ relativeTime(row.age) }}</div>
        </template>
        <template #ops="{ row }">
          <LinkButton @click="() => jumpToPodLog(row)" text="日志" class="ellipsis-text" ellipsis />
        </template>
      </pro-table>
      <view-yaml
        class-name="pod-yaml-drawer"
        resourceType="pods"
        v-model="viewPodYamlVisible"
        :resource-entity="podYamlEntity"
        resource-version="V2"
        :isCheckYaml="false"
        notSynchronizeToUnifiedCluster
      />
    </Modal>

    <LogMergeModal
      v-model="logMergeModal.visible"
      title="选择容器"
      :clusterId="clusterId"
      :cluster="cluster"
      :namespace="namespace"
      :workloadName="logMergeModal.data.name"
      workloadKind="Deployment"
    />
  </Container>
</template>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  font-weight: 600;
  /deep/.menu-title {
    margin-bottom: 0;
    margin-right: 32px;
  }

  > span {
    font-size: 14px;
  }
}
.enter-btn {
  cursor: pointer;
  color: #2a7cc3;
  white-space: nowrap;
  align-items: center;
}
/deep/.cluster-switch-poptip {
  .ivu-poptip-title {
    display: none;
  }
  .ivu-poptip-body-content {
    .header-content-wrapper {
      width: 480px;
      display: flex;
      justify-content: space-between;

      .box {
        flex: 1 0 0%;
        // padding: 10px;
        // border: 1px solid #e8e8e8;
        // border-radius: 8px;

        .ivu-radio-group {
          flex-direction: column;
          align-items: flex-start;
          height: 240px;
          overflow-y: auto;
        }
      }
    }
  }
}
</style>
