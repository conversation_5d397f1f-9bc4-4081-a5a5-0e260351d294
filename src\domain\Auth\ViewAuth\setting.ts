import { Ellipsis } from '@/components'

export const viewerTableCols = [
  {
    title: '权限名称',
    key: 'auth'
  }
]

export const k8sTableCols = [
  {
    title: '命名空间',
    key: 'namespace',
    tooltip: true
  },
  {
    title: '资源种类',
    key: 'kind',
    tooltip: true
  },
  {
    title: '授权',
    key: 'authority'
  }
]

export const k8sTableData = [
  {
    kind: 'Pod',
    namespace: '',
    authority: 'rw'
  }
]

export const kubeconfigTableCols = [
  {
    title: '角色名称',
    key: 'roleName',
    tooltip: true
  },
  {
    title: '集群',
    key: 'clusterName',
    tooltip: true
  },
  {
    title: '命名空间',
    key: 'namespace',
    render: (h, params) => {
      return h(Ellipsis, params.row.namespace ? params.row.namespace : '所有空间')
    }
  },
  {
    title: 'Ops',
    key: 'yaml',
    width: 100,
    align: 'center',
    slot: 'yaml'
  }
]
