import axios from '@/libs/api.request'


export const ApiNsServiceList = (page) => {
  return axios.request({
    url: `/api/v1/resource/service/list`,
    method: 'get',
    data: {},
    params: page
  })
}

export const ApiGetNsService = (svc_uid, cluster_id, namespace) => {
  return axios.request({
    url: `/api/v1/resource/service/${svc_uid}`,
    method: 'get',
    data: {},
    params: {
      cluster_id: cluster_id,
      namespace: namespace
    }
  })
}

export const ApiGetNsServicePod = ({uuid, cluster_id, namespace}) => {
  return axios.request({
    url: `/api/v1/resource/related/service/pod`,
    method: 'get',
    data: {},
    params: {
      uuid,
      cluster_id,
      namespace,
    }
  })
}

export const ApiNsServicePodOwnerDeployment = (cluster_id, namespace, pod_name) => {
  return axios.request({
    url: `/api/v1/resource/related/service/goto-deployment/`,
    method: 'post',
    data: {
      cluster_id,
      namespace,
      pod_name
    },
    params: {}
  })
}
export const ApiResourceYaml = ({cluster_id, namespace, resource_name, kind}) => {
  return axios.request({
    url: `/api/v1/resource/yaml/get`,
    method: 'get',
    data: {},
    params: {
      cluster_id, namespace, resource_name, kind
    }
  })
}



export const ApiSvcAuthCheck = ({cluster_id, namespace, auth}) => {
  return axios.request({
    url: `/api/v1/resource/service/auth/check`,
    method: 'get',
    data: {},
    params: {
      cluster_id, namespace, auth
    }
  })
}

