import { ref, onMounted, getCurrentInstance, watch } from 'vue'

import Config from '@/config'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList, usePost } from '@/libs/service.request'

import { Entity, EnumComponentType, PortForward } from '../type'
import { useRequest } from 'vue-request'

export default function usePortForwardService(props: { type: EnumComponentType; entity: Entity }) {
  const { proxy } = getCurrentInstance()

  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const cluster = ref()
  const namespace = ref()
  const pod = ref()
  const createFormRef = ref()

  const createModal = ref<{
    visible: boolean
    data?: {
      name?: string
      pod?: string
      targetPort?: string
    }
  }>({
    visible: false,
    data: {}
  })

  const { data: clusterList, run: getClusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetClusterList}`
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet<{ data: { name: string; business: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetNamespaceList}`,
        {
          params: {
            clusterName: cluster.value?.label
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const { data: portList, run: getPortList } = useRequest(
    () => {
      let path = ''
      let params = {}

      switch (props.type) {
        case EnumComponentType.AssociatedDeployment:
          path = Config.Api.GetPortListWithDeployment
          params = {
            name: props.entity.relativeName,
            clusterId: props.entity.clusterId,
            namespace: props.entity.namespace
          }
          break
        case EnumComponentType.AssociatedStatefulset:
          path = Config.Api.GetPortListWithStatefulset
          params = {
            name: props.entity.relativePodName,
            clusterId: props.entity.clusterId,
            namespace: props.entity.namespace
          }
        case EnumComponentType.AssociatedPod:
          path = Config.Api.GetPortListWithPod
          params = {
            name: props.entity.relativePodName,
            clusterId: props.entity.clusterId,
            namespace: props.entity.namespace
          }
          break
        default:
          break
      }
      return useGet<{ data: any[] }>(`${Config.Api.Base}${path}`, {
        params
      })
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) => res.data.data?.map((i) => ({ value: String(i), label: String(i) }))
    }
  )

  const { data: podList, run: getPodList } = useRequest(
    () => {
      let path = ''

      switch (props.type) {
        case EnumComponentType.AssociatedDeployment:
          path = Config.Api.GetPodListWithDeployment
          break
        case EnumComponentType.AssociatedStatefulset:
        default:
          path = Config.Api.GetPodListWithStatefulset
          break
      }
      return useGet<{ data: any[] }>(`${Config.Api.Base}${path}`, {
        params: {
          name: props.entity.relativeName,
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace
        }
      })
    },
    {
      manual: true,
      initialData: [],
      formatResult: (res) =>
        res.data.data?.map((i) => {
          switch (props.type) {
            case EnumComponentType.AssociatedDeployment:
              return { value: i.metadata.name, label: i.metadata.name }
            case EnumComponentType.AssociatedStatefulset:
              return { value: i.name, label: i.name }
            default:
              return { value: i, label: i }
          }
        })
    }
  )

  const onCreatePodList = (val) => {
    if (!podList.value) {
      podList.value = []
    }
    podList.value.push({
      value: String(val),
      label: String(val)
    })
  }

  const onCreatePortList = (val) => {
    if (!portList.value) {
      portList.value = []
    }
    portList.value.push({
      value: val,
      label: val
    })
  }

  const onDelete = (record: PortForward) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        let path = ''

        switch (props.type) {
          case EnumComponentType.Independent:
            path = Config.Api.DeletePortForward

            break

          case EnumComponentType.AssociatedDeployment:
            path = Config.Api.DeletePortForwardWithDeployment

            break
          case EnumComponentType.AssociatedStatefulset:
            path = Config.Api.DeletePortForwardWithStatefulset

            break
          default:
            path = Config.Api.DeletePortForwardWithPod

            break
        }
        const res = await useDelete(`${Config.Api.Base}${path}`, {
          params: {
            clusterId: record.clusterId,
            namespace: record.namespace,
            pod: record.pod,
            targetPort: record.targetPort,
            proxyPort: record.proxyPort
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onOpenCreateModal = () => {
    console.log('onCreate')
    createModal.value = {
      visible: true,
      data: {}
    }

    getPortList()
    if (props.type === EnumComponentType.AssociatedPod) {
      podList.value = [{ value: props.entity.relativePodName, label: props.entity.relativePodName }]
      createModal.value = {
        visible: true,
        data: {
          pod: props.entity.relativePodName
        }
      }
    } else {
      getPodList()
    }
  }

  const onCreate = async () => {
    const valid = await createFormRef.value?.validate()

    if (valid) {
      const targetPort = Number(createModal.value.data.targetPort)
      if (isNaN(targetPort)) {
        proxy.$Message.error('检测到目标端口不是数字，请输入合法的目标端口')
        return
      } else if (1 > targetPort || targetPort > 65535) {
        proxy.$Message.error('检测到目标端口范围不在1-65535中，请输入合法的目标端口')
        return
      }
      proxy.$Modal.confirm({
        title: '提示',
        content: `是否确认创建端口转发？`,
        loading: true,
        onOk: async () => {
          let path = ''
          switch (props.type) {
            case EnumComponentType.AssociatedDeployment:
              path = Config.Api.CreatePortForwardWithDeployment
              break
            case EnumComponentType.AssociatedStatefulset:
              path = Config.Api.CreatePortForwardWithStatefulset
              break
            default:
              path = Config.Api.CreatePortForwardWithPod
              break
          }
          const res = await usePost(`${Config.Api.Base}${path}`, {
            clusterId: props.entity.clusterId,
            namespace: props.entity.namespace,
            pod: createModal.value.data.pod,
            targetPort,
            name: createModal.value.data.name
          })
          if (res.success) {
            createModal.value.visible = false
            refObject.tableRef.value.reload()
            proxy.$Modal.remove()
          }
        }
      })
    }
  }
  const onRebuild = (record: PortForward) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认重建 ${record.name}？`,
      loading: true,
      onOk: async () => {
        let path = ''

        switch (props.type) {
          case EnumComponentType.Independent:
            path = Config.Api.RebuildPortForward
            break

          case EnumComponentType.AssociatedDeployment:
            path = Config.Api.RebuildPortForwardWithDeployment
            break
          case EnumComponentType.AssociatedStatefulset:
            path = Config.Api.RebuildPortForwardWithStatefulset
            break
          default:
            path = Config.Api.RebuildPortForwardWithPod
            break
        }
        const res = await usePost(`${Config.Api.Base}${path}`, {
          clusterId: record.clusterId,
          namespace: record.namespace,
          pod: record.pod,
          targetPort: record.targetPort,
          proxyPort: record.proxyPort
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onRefresh = () => {
    refObject.tableRef.value?.reload()
  }

  const getTableData: TableRequest = async (params) => {
    let path = ''
    let query = {}
    switch (props.type) {
      case EnumComponentType.Independent:
        path = Config.Api.GetPortForwardList
        query = {
          page: params.pageIndex,
          size: params.pageSize,
          clusterId: cluster.value?.value,
          namespace: namespace.value,
          search: pod.value
        }
        break

      case EnumComponentType.AssociatedDeployment:
        path = Config.Api.GetPortForwardListWithDeployment
        query = {
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          pod: props.entity.relativePodName
        }
        break
      case EnumComponentType.AssociatedStatefulset:
        path = Config.Api.GetPortForwardListWithStatefulset
        query = {
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          pod: props.entity.relativePodName
        }
        break
      default:
        path = Config.Api.GetPortForwardListWithPod
        query = {
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          pod: props.entity.relativePodName
        }
        break
    }

    const res = await useGet<PageList<PortForward[]>>(`${Config.Api.Base}${path}`, {
      params: query
    })

    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data:
        props.type === EnumComponentType.Independent
          ? res.data?.list ?? []
          : (res.data as unknown as { data: PortForward[] }).data ?? []
    }
  }

  watch(cluster, () => {
    cluster.value && JSON.stringify(cluster.value) !== '{}' && getNamespaceList()
  })

  watch(
    () => props.entity,
    () => {
      if (props.entity) {
        refObject.tableRef.value.reload()
      }
    }
  )

  onMounted(() => {
    if (props.type === EnumComponentType.Independent) {
      refObject.tableRef.value.reload()
    }
    if (props.type === EnumComponentType.Independent) {
      getClusterList()
    }
  })

  return {
    getTableData,
    refObject,
    onOpenCreateModal,
    onCreate,
    onRebuild,
    onDelete,
    onRefresh,

    cluster,
    clusterList,
    namespace,
    namespaceList,
    pod,
    createModal,
    podList,
    portList,
    createFormRef,
    onCreatePodList,
    onCreatePortList
  }
}
