<script lang="ts" setup>
import { PropType } from 'vue/types/v3-component-props'
import { EnumFavorResourceName } from '../enum'
import useFavorService from './useFavorBtnService'

const props = defineProps({
  resourceKey: { type: String as PropType<keyof typeof EnumFavorResourceName>, default: '' }
})

const { onFavor, onDisfavor, favorId } = useFavorService(props)
</script>

<template>
  <!-- <Icon class="favor-icon" type="ios-loading" v-if="favorIdLoading" /> -->
  <Tooltip :content="favorId ? '点击取消收藏资源' : '点击收藏资源'" theme="light" class="favor-btn">
    <div class="favor-icon">
      <svg v-if="favorId" width="1em" height="1em" viewBox="0 0 24 24" @click="onDisfavor">
        <path
          d="M10.74 1.951a1.5 1.5 0 0 1 2.52 0l2.863 4.434a1 1 0 0 0 .578.422l5.088 1.379a1.5 1.5 0 0 1 .777 2.386l-3.32 4.14a1 1 0 0 0-.219.677l.271 5.303a1.5 1.5 0 0 1-2.037 1.476l-4.901-1.89a1 1 0 0 0-.72 0l-4.9 1.89a1.5 1.5 0 0 1-2.04-1.476l.271-5.303a1 1 0 0 0-.218-.677l-3.32-4.14a1.5 1.5 0 0 1 .777-2.386l5.087-1.379a1 1 0 0 0 .579-.422L10.74 1.95Z"
          fill="#ffc60a"
        />
      </svg>
      <svg v-else width="1em" height="1em" viewBox="0 0 24 24" @click="onFavor">
        <path
          d="m12 3.687 2.443 3.783a3 3 0 0 0 1.735 1.268l4.35 1.179-2.842 3.543a3 3 0 0 0-.656 2.03l.232 4.535-4.182-1.613a3 3 0 0 0-2.16 0l-4.182 1.613.232-4.534a3 3 0 0 0-.656-2.03L3.47 9.916l4.35-1.18A3 3 0 0 0 9.557 7.47L12 3.687Zm1.26-1.736a1.5 1.5 0 0 0-2.52 0L7.877 6.385a1 1 0 0 1-.579.422L2.211 8.186a1.5 1.5 0 0 0-.778 2.386l3.32 4.14a1 1 0 0 1 .22.677L4.7 20.692a1.5 1.5 0 0 0 2.038 1.476l4.901-1.89a1 1 0 0 1 .72 0l4.9 1.89a1.5 1.5 0 0 0 2.038-1.476l-.27-5.303a1 1 0 0 1 .218-.677l3.32-4.14a1.5 1.5 0 0 0-.777-2.386L16.7 6.807a1 1 0 0 1-.578-.422L13.26 1.95Z"
          fill="currentColor"
        />
      </svg>
    </div>
  </Tooltip>
</template>

<style lang="less" scoped>
.favor-btn {
  display: flex;
}
.favor-icon {
  cursor: pointer;
  font-size: 16px;
  &,
  & > svg {
    height: 21px;
  }
}
</style>
