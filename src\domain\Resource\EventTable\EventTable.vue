<script lang="ts" setup>
import { ActionType, TableRequest } from '@/components/pro-table'
import { ProTable } from '@/components'
import { PageList, useGet } from '@/libs/service.request'
import Config from '@/config'
import { EVENT_TABLE_COLUMNS } from './setting'
import { ref, watch } from 'vue'

const props = defineProps({
  uuid: String,
  clusterId: String,
  // 是否显示小标题
  miniTitle: Boolean,
  // 是否隐藏标题
  hideTitle: Boolean,
  // 是否隐藏刷新按钮
  hideRefresh: Boolean
})

const refObject = {
  tableRef: ref<ActionType | null>(null)
}

const getTableData: TableRequest = async (params) => {
  const res = await useGet<PageList<[]>>(`${Config.Api.Base}${Config.Api.GetRelativeEvent}`, {
    params: {
      page: params.pageIndex,
      size: params.pageSize,
      uuid: props.uuid,
      clusterId: props.clusterId
    }
  })
  return {
    success: res.success,
    total: res.data?.total ?? 0,
    data: res.data?.list ?? []
  }
}

const onReload = () => refObject?.tableRef.value?.reload()
watch(
  [() => props.uuid, () => props.clusterId, () => !!refObject?.tableRef.value],
  () => {
    if (props.uuid && props.clusterId && refObject?.tableRef.value) {
      onReload()
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div>
    <div class="event-table-operation" v-if="!props.hideTitle">
      <strong :class="props.miniTitle ? 'mini-title' : ''">关联事件</strong>
      <Button v-if="!props.hideRefresh" size="small" icon="ios-refresh" type="primary" ghost @click="onReload"
        >刷新</Button
      >
    </div>
    <pro-table
      manualRequest
      :columns="EVENT_TABLE_COLUMNS"
      :action-ref="refObject"
      :request="getTableData"
      hideSearchOperation
      :pagination="{ defaultPageSize: 10, simple: true }"
      :height="280"
    />
  </div>
</template>

<style lang="less" scoped>
.event-table-operation {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  justify-content: space-between;
  .mini-title {
    font-size: 12px;
  }
  .ivu-btn {
    margin-left: 8px;
  }
}
</style>
