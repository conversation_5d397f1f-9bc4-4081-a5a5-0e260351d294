import axios from '@/libs/api.request'

export const ssoLogin = (ticket) => {
  return axios.request({
    url: '/api/v1/auth/sso/login',
    params: {
      ticket: ticket
    },
    method: 'post'
  })
}

export const login = ({ userName, password }) => {
  const data = {
    userName,
    password
  }
  return axios.request({
    url: '/api/v1/auth/login',
    data,
    method: 'post'
  })
}

export const getUserInfo = () => {
  return axios.request({
    url: '/api/v1/user/profile',
    method: 'get'
  })
}
export const getUserInfoByUserId = (uid) => {
  return axios.request({
    url: '/api/v1/user/get',
    method: 'get',
    params: {
      uid: uid
    }
  })
}

export const logout = () => {
  return axios.request({
    url: '/api/v1/auth/logout',
    method: 'post'
  })
}

export const ApiSignUp = (username, nickName, password, passwordConfirm) => {
  var data = {
    username,
    nickName,
    password,
    passwordConfirm
  }
  return axios.request({
    url: '/api/v1/auth/sign-up',
    method: 'post',
    data: data
  })
}

export const ApiUserList = (params) => {
  return axios.request({
    url: '/api/v1/user/list',
    method: 'get',
    params: params
  })
}

export const ApiSetAdmin = (userId, isAdmin) => {
  return axios.request({
    url: '/api/v1/user/set-admin',
    method: 'post',
    data: {
      userId,
      isAdmin
    }
  })
}

export const ApiSetClusterAdmin = (userId, isClusterAdmin) => {
  return axios.request({
    url: '/api/v1/user/set-cluster-admin',
    method: 'post',
    data: {
      userId,
      isClusterAdmin
    }
  })
}

export const ApiSetUserNamespace = ({ userId, namespaces }) => {
  return axios.request({
    url: `/api/v1/user/set-namespace-scope`,
    method: 'post',
    data: {
      userId,
      namespaces
    },
    params: {}
  })
}

export const ApiSetActive = (userId, isActive) => {
  return axios.request({
    url: '/api/v1/user/set-active',
    method: 'post',
    data: {
      userId,
      isActive
    }
  })
}

export const ApiUserProfile = () => {
  return axios.request({
    url: '/api/v1/user/profile/',
    method: 'get'
  })
}

// 下面的 api 是为以后有肯能出现的需求留的。

export const getUnreadCount = () => {
  return axios.request({
    url: 'message/count',
    method: 'get'
  })
}

export const getMessage = () => {
  return axios.request({
    url: 'message/init',
    method: 'get'
  })
}

export const getContentByMsgId = (msg_id) => {
  return axios.request({
    url: 'message/content',
    method: 'get',
    params: {
      msg_id
    }
  })
}

export const hasRead = (msg_id) => {
  return axios.request({
    url: 'message/has_read',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const removeReaded = (msg_id) => {
  return axios.request({
    url: 'message/remove_readed',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const restoreTrash = (msg_id) => {
  return axios.request({
    url: 'message/restore',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const ApiUserCount = () => {
  return axios.request({
    url: '/api/v1/user/count',
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiUserOpCount = () => {
  return axios.request({
    url: '/api/v1/user/operation-count',
    method: 'get',
    data: {},
    params: {}
  })
}
