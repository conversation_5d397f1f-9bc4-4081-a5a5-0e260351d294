<script lang="ts" setup>
import { Space, ViewYaml, ProTable, LinkButton, ResourceForm, Empty } from '@/components'

import { EventTable } from '@/domain/Resource'
import useCRDService from './useCRDService'
import { getCurrentInstance } from 'vue'
import { relativeTime } from '@/libs/tools'
import { color } from '@/libs/consts'

const {
  getTableData,
  refObject,
  onCreate,
  onEdit,
  onDelete,
  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,
  yamlInitData,
  onSubmitSuccess,
  onViewDetail,
  originCategoryDataLoading,
  categoryData,
  onSearchCategoryData,
  currentCategory,
  onSelectCategory,
  tableColumns,
  tableColumnsLoading,
  detail,
  K8SInstance
} = useCRDService()

const { proxy } = getCurrentInstance()
</script>

<template>
  <div>
    <Alert show-icon>
      <Space direction="vertical" :size="4">
        <p>当前页面中您可以管理所有自定义资源。</p>
      </Space>
    </Alert>
    <div class="crd-wrapper">
      <div class="left-wrapper">
        <Input search placeholder="请输入类型搜索" @on-search="onSearchCategoryData" />
        <div class="category-wrapper">
          <div class="category-header">Resource / Group Version</div>
          <div class="category-content">
            <Spin v-if="originCategoryDataLoading" />
            <Empty v-if="!categoryData?.length" />
            <template v-else>
              <div class="menu-wrapper">
                <div
                  :class="`menu-item${currentCategory.name === item.name ? ' active' : ''}`"
                  v-for="item in categoryData"
                  :key="item.name"
                  @click="() => onSelectCategory(item)"
                >
                  <Tooltip
                    :content="`当前crd资源的scope为${item.namespaced ? 'namespace' : 'global'}`"
                    placement="top"
                    transfer
                  >
                    <div :class="`icon ${item.namespaced ? 'ns' : 'cl'}`">{{ item.namespaced ? 'ns' : 'gl' }}</div>
                  </Tooltip>
                  <div class="title">
                    {{ item.resource }}
                  </div>

                  <div class="desc">{{ item.group }} / {{ item.version }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="right-wrapper">
        <Empty v-if="!tableColumns?.length" style="background: #fff" description="该类型暂未配置，请联系相关人员处理" />
        <pro-table
          :columns="tableColumns"
          :loading="tableColumnsLoading"
          :request="getTableData"
          manual-request
          :action-ref="refObject"
          row-key="uuid"
          :search="[{ value: 'keyword', label: '名称', initData: proxy.$route.query?.name ?? '' }]"
          :on-create="onCreate"
        >
          <template #name="{ row }">
            <link-button style="fontweight: 600" @click="() => onViewDetail(row)" :text="row.name" ellipsis tooltip />
          </template>
          <template #ready="{ row }">
            <Icon
              v-if="typeof row.ready === 'boolean'"
              :type="row.ready ? 'ios-checkmark-circle' : 'ios-close-circle'"
              :color="row.ready ? color.success : color.error"
              size="16"
            />
            <span v-else>{{ row.ready }}</span>
          </template>
          <template #creationTimestamp="{ row }">
            {{ relativeTime(row.creationTimestamp) }}
          </template>
          <template #ops="{ row }">
            <space justify>
              <link-button @click="() => onViewYaml(row)" text="YAML" />
              <link-button @click="() => onEdit(row)" text="编辑" />
              <link-button @click="() => onDelete(row)" text="删除" type="danger" />
            </space>
          </template>
        </pro-table>
      </div>
    </div>

    <view-yaml
      resourceType="crd/object"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V1"
      isCheckYaml
      notSynchronizeToUnifiedCluster
      :isSkipNamespaceInParams="!currentCategory.namespaced"
    />
    <resource-form
      resourceType="crd/object"
      v-model="formVisible"
      :status="formStatus"
      resource-version="V1"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :onSubmitCallBack="onSubmitSuccess"
      notSynchronizeToUnifiedCluster
      forbiddenForm
      isSkipCheck
      :isSkipNamespaceInParams="!currentCategory.namespaced"
    />

    <Drawer title="查看详情" v-model="detail.visible" :closable="true" width="50">
      <Card>
        <h4>基本信息</h4>
        <Row type="flex" :gutter="20" style="margin-top: 20px">
          <Col span="10" class="info-key">Name</Col>
          <Col span="14" class="info-value">{{ detail.data.name }}</Col>
        </Row>
        <Row v-if="currentCategory.namespaced" type="flex" :gutter="20" style="margin-top: 16px">
          <Col span="10" class="info-key">Namespace</Col>
          <Col span="14" class="info-value">{{ K8SInstance.namespace }}</Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <EventTable :uuid="detail.data.uuid" :clusterId="K8SInstance.clusterId" />
      </Card>
    </Drawer>
  </div>
</template>

<style lang="less" scoped>
.crd-wrapper {
  display: flex;
  height: ~'calc(100vh - 48px - 48px - 16px * 2 - 32px - 16px)';
  .left-wrapper {
    width: 282px;
    padding: 16px;
    background: #fff;
    margin-right: 16px;
    .category-wrapper {
      margin-top: 16px;
      height: ~'calc(100% - 32px - 16px)';
      .category-header {
        font-weight: 600;
        border: 1px solid #dcdee2;
        background: #f8f8f9;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
      .category-content {
        height: ~'calc(100% - 38px)';
        overflow: hidden auto;
        .menu-wrapper {
          width: 250px;

          .menu-item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            border-bottom: 1px solid #dcdee2;
            cursor: pointer;
            position: relative;
            &.active {
              background: #def4ff;
            }
            .title {
              font-weight: 600;
              font-size: 12px;
              color: #2a7cc3;
              width: 100%;
              text-align: center;
            }
            .ivu-tooltip {
              height: 100%;
              position: absolute;
              top: -2px;
              left: -8px;
            }
            .icon {
              font-size: 12px;
              color: #fff;
              border-radius: 0 0 24px 0;
              width: 24px;
              height: 24px;
              margin-left: 8px;
              line-height: 20px;
              padding: 0px 0 0 4px;
              &.ns {
                background: #2b7dc3;
              }
              &.cl {
                background: #34a9b5;
              }
            }
            .desc {
              font-size: 12px;
              color: #7f7f7f;
              width: 100%;
              text-align: center;
            }
          }
        }
      }
    }
  }
  .right-wrapper {
    width: ~'calc(100% - 282px - 16px)';
  }
}
</style>
