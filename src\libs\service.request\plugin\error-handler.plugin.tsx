/** 标准的错误处理功能 */

import type { AxiosResponse } from 'axios'
import type { RequestConfig, RequestError, Response } from '../type'
import { get } from 'lodash-es'
import { getCurrentInstance } from 'vue'

const HttpStatusCodeToErrorMsg = {
  400: '请求错误',
  401: '登录过期，请重新登录',
  403: '拒绝访问',
  404: '请求地址出错',
  408: '请求超时',
  409: '服务器处理请求时遇到冲突',
  410: '请求的资源不见了',
  500: '服务器内部错误',
  501: '服务未实现',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  505: 'HTTP版本不受支持'
}

/** 判断本次请求的错误信息 */
export function getErrorMsg<T>(error: RequestError<T>, errorMsgPath?: string): [errorMsg: string] {
  let msg = ''

  if (error.message === 'Network Error') {
    msg = '网络错误'
  } else if (error.message?.startsWith('timeout of')) {
    msg = '请求超时'
  } else if (error.response) {
    const errorMsg = get(error.response?.data, errorMsgPath ?? 'msg')
    if (typeof errorMsg === 'string' && errorMsg.length > 0) {
      msg = errorMsg
    } else if (error.response?.status) {
      const statusCodeMsg = HttpStatusCodeToErrorMsg[error.response?.status]
      if (statusCodeMsg) {
        msg = statusCodeMsg
      } else {
        msg = '其他错误'
      }
    }
  } else if (error.message) {
    msg = error.message
  } else {
    msg = '未知错误'
  }

  return [msg]
}

export function getErrorObj<T>(e: Error, response: AxiosResponse<Response<T>>, config: RequestConfig): RequestError<T> {
  return {
    requestConfig: config,
    response,
    ...e,
    errorMsg: '尚未定义的错误'
  }
}

/** 标准错误处理 */
export function handleError<T>(error: RequestError<T>): Promise<never> {
  console.log('标准错误处理', error)
  const { requestConfig } = error

  if (requestConfig?.skipErrorHandler) return Promise.reject(error)

  requestConfig.vue.$Message.error(`${error.errorMsg}`)

  return Promise.reject(error)
}
