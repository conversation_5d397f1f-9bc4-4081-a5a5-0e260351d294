import { PageList, useDelete, useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { getCurrentInstance, nextTick, ref } from 'vue'
import { useRequest, useLoadMore } from 'vue-request'
import { HistoryParam, LicenseInfo, Resource } from './type'
import { debounce } from 'lodash-es'

export default function useSystemSearchService() {
  const { proxy } = getCurrentInstance()
  const visible = ref()
  const keyword = ref()

  const clusterList = ref([])
  const namespaceList = ref()
  const resourceList = ref([])

  const clusters = ref([])
  const namespaces = ref([])
  const resources = ref(['deployments/apps'])
  const ip = ref()
  const mode = ref<'loadMore' | 'refresh'>()

  const saveViewModal = ref({ visible: false, name: '' })

  const onSearch = () => {
    visible.value = true
    getLicenseInfo()
  }

  const { loading: licenseInfoLoading, run: getLicenseInfo } = useRequest(
    () => {
      return useGet<LicenseInfo>(`${Config.Api.Base}${Config.Api.GetMultiClusterSearchLicenseInfo}`)
    },
    {
      manual: true,
      formatResult(res) {
        const { clusters: clusterApiList, namespaces: namespaceApiList, resources: resourcesApiList } = res.data

        clusterList.value = clusterApiList
        clusters.value = clusterApiList.length <= 5 ? clusterApiList.slice(0, 5)?.map((i) => i.clusterId) : []
        namespaces.value = namespaceApiList.length <= 5 ? namespaceApiList.slice(0, 5) : []

        namespaceList.value = namespaceApiList

        resourceList.value = resourcesApiList
        refresh()

        return res.data
      }
    }
  )
  const { data, loading, run, loadingMore, noMore } = useLoadMore(
    (param?: { data }) => {
      return usePost<PageList<Resource[]>>(`${Config.Api.Base}${Config.Api.SearchMultiClusterList}`, {
        clusters: clusters.value?.map((i) => ({ clusterId: i })),
        namespaces: namespaces.value,
        resources: resources.value?.map((i) => {
          const [resource, group] = i.split('/')
          return { resource, group }
        }),
        search: keyword.value,
        extra: ip.value,
        page: mode.value === 'loadMore' ? (data.value?.page ?? 1) + 1 : 1,
        size: 10
      })
    },
    {
      manual: true,
      isNoMore: () => {
        const { page, size, total } = data?.value ?? {}

        return page * size >= total
      },
      pagination: {
        currentKey: 'page'
      },
      formatResult: (res: { data: PageList<Resource[]> }) => {
        if (mode.value === 'refresh') return res.data
        return { ...res.data, list: [...(data.value?.list ?? []), ...res.data.list] }
      },
      onError: (err) => {
        console.log(55, err)
      }
    }
  )
  const refresh = async () => {
    mode.value = 'refresh'
    if (ip.value && !resources.value.includes('pods/')) {
      resources.value = [...resources.value, 'pods/']
    }
    run()
    const container = document.getElementsByClassName('search-modal-content-right')?.[0]
    container?.scrollTo({
      top: 0
    })
  }
  const loadMore = async (scrollTop) => {
    mode.value = 'loadMore'
    await run()
    await nextTick()
    const container = document.getElementsByClassName('search-modal-content-right')?.[0]
    container?.scrollTo({
      top: scrollTop
    })
  }

  const { data: historyParamsList, run: getHistoryParamsList } = useRequest(
    () => {
      return useGet<{ data: HistoryParam[] }>(`${Config.Api.Base}${Config.Api.GetMultiClusterSearchHistoryParams}`)
    },
    {
      manual: true,
      formatResult: (res) => {
        return res.data.data
      }
    }
  )

  const onHistoryParamsListVisible = (visible) => {
    visible && getHistoryParamsList()
  }

  const onContentScroll = debounce((e) => {
    const { scrollTop, clientHeight, scrollHeight } = e.target
    if (scrollHeight - (scrollTop + clientHeight) <= 1) {
      loadMore(scrollTop)
    }
  }, 150)

  const onOpenSaveViewModal = () => {
    if (!keyword.value && !clusters.value?.length && !namespaces.value?.length && !resources.value?.length) {
      return proxy.$Message.warning('搜索视图为空，请先进行选择')
    }
    saveViewModal.value = {
      visible: true,
      name: keyword.value ?? ''
    }
  }

  const onSaveSearchView = () => {
    if (!saveViewModal.value.name) {
      return
    }
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认保存视图 - ${saveViewModal.value.name} ？`,
      loading: true,
      onOk: async () => {
        const params = {
          clusters: clusters.value?.map((i) => ({ clusterId: i })),
          namespaces: namespaces.value,
          resources: resources.value?.map((i) => {
            const [resource, group] = i.split('/')
            return { resource, group }
          }),
          search: keyword.value,
          name: saveViewModal.value.name
        }
        console.log(111, params)
        const res = await usePost(`${Config.Api.Base}${Config.Api.SaveMultiClusterSearchHistoryParams}`, params)
        if (res.success) {
          proxy.$Message.success(`保存视图 - ${saveViewModal.value.name} 成功`)
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onDeleteSearchView = (record) => {
    /** 强行修改dropdown层级，修复iview modal和dropdown 每次点击都叠加z-index，但叠加的数量不一致导致的覆盖问题 */
    const modal = document.getElementsByClassName('search-modal')?.[0] as HTMLElement
    const dropdown = document.getElementsByClassName('history-dropdown-list')?.[0] as HTMLElement
    if (modal && dropdown) {
      dropdown.style.zIndex = (Number(modal.style.zIndex) + 1).toString()
    }
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除视图 - ${record.name}？`,
      loading: true,
      onOk: async () => {
        console.log(111, record)
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteMultiClusterSearchHistoryParams}`, {
          data: record
        })
        if (res.success) {
          proxy.$Message.success(`删除视图 - ${record.name} 成功`)
          getHistoryParamsList()
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onSearchViewClick = (record: HistoryParam) => {
    keyword.value = record?.search
    clusters.value = record?.clusters?.map((i) => i.clusterId) ?? null
    namespaces.value = record?.namespaces ?? null
    resources.value = record?.resources?.map((i) => `${i.resource}/${i.group}`) ?? null
    refresh()
  }

  const onClearFilter = () => {
    clusters.value = []
    namespaces.value = []
    resources.value = []
    ip.value = ''
    refresh()
  }

  return {
    onSearch,
    visible,
    keyword,
    clusters,
    clusterList,
    licenseInfoLoading,
    namespaceList,
    namespaces,

    resources,
    resourceList,
    ip,
    onHistoryParamsListVisible,

    historyParamsList,
    data,
    loading,
    loadingMore,
    noMore,
    refresh,
    onContentScroll,
    saveViewModal,
    onOpenSaveViewModal,
    onSaveSearchView,
    onDeleteSearchView,
    onSearchViewClick,
    onClearFilter
  }
}
