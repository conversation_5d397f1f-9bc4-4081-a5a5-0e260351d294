<script lang="ts" setup>
import { Container, K8SSwitch } from '@/components'
import { FavorBtn, EnumFavorResourceName } from '@/domain/Resource'
import { useStore } from '@/libs/useVueInstance'
import { computed, PropType } from 'vue'
const store = useStore()
const props = defineProps({
  resourceKey: { type: String as PropType<keyof typeof EnumFavorResourceName>, default: '' },
  setClusterOnly: { type: Boolean, default: false }
})

const resourceKey = computed(() => {
  return `${store.state.k8s.currentCluster ?? 'none'}-${store.state.k8s.currentClusterId ?? 'none'}-${
    store.state.k8s.currentNamespace ?? 'none'
  }`
})
</script>

<template>
  <Container>
    <template #header-left>
      <FavorBtn v-if="props.resourceKey" :resourceKey="props.resourceKey" />
    </template>
    <template #header-right>
      <K8SSwitch :setClusterOnly="!!props.setClusterOnly" />
    </template>
    <div v-if="!resourceKey.includes('none')">
      <slot name="default" />
    </div>
  </Container>
</template>

<style lang="less" scoped></style>
