import { ExtractPropTypes, PropType, Ref } from 'vue'
import { TableColumn as iTableColumn } from 'view-design/types/table'
import { VNodeRef } from 'vue/types/vnode'

export type RequestData<T> = {
  data: T[] | undefined
  success?: boolean
  total?: number
} & Record<string, any>

export type TableRequest<T = {}, U = {}> = (
  params: U & {
    searchKey?: string
    searchValue?: string
    pageSize?: number
    pageIndex?: number
  }
) => Promise<Partial<RequestData<T>>>

export type TableParams = Parameters<TableRequest>

interface TableExtra {
  copyable?: boolean
}
export type TableColumn = iTableColumn & TableExtra

export type TableColumns = TableColumn[]

export function tableProps<T, U>() {
  return {
    loading: { type: Boolean, default: false },
    rowKey: {
      type: String,
      default: 'id'
    },
    /** 表格列的配置描述 */
    data: {
      type: Array
    },
    /** 表格列的配置描述 */
    columns: {
      type: Array as PropType<TableColumns>
    },
    /** 获取 dataSource 的方法 */
    request: {
      type: Function as PropType<TableRequest<T, U>>
    },
    /** 是否需要手动触发首次请求  */
    manualRequest: { type: Boolean, default: false },
    /**
     * @name 初始化的参数，可以操作 table
     *
     * @example 重新刷新表格
     * actionRef.value?.reload();
     *
     * @example 重置表格
     * actionRef.value?.reset();
     */
    actionRef: { type: Object as PropType<{ tableRef: Ref<ActionType | undefined> }> },
    // view-design 系统组件没有暴露底层状态，需要绑定table的vue实例产生的需求
    viewDesignTableRef: { type: Object as PropType<{ tableRef: VNodeRef }> },
    /** 分页器控制 */
    pagination: {
      type: [Boolean, Object] as PropType<false | TablePaginationConfig>,

      default: function () {
        return {
          defaultPageSize: 10
        }
      }
    },
    /** 隐藏顶部整个搜索 */
    hideSearchOperation: Boolean,
    /** 搜索内容配置 */
    search: {
      type: [Boolean, Array] as PropType<false | TableSearchConfigItem[]>,
      default: undefined
    },
    /** 创建 */
    onCreate: {
      type: Function,
      default: undefined
    },
    /** 提示内容 */
    alert: {
      type: String,
      default: undefined
    },
    height: Number,
    /** 是否显示边框 */
    border: {
      type: Boolean,
      default: false
    }
    // /** 一屏展示配置 */
    // fullScreen: {
    //   type: [Boolean, Object] as PropType<fullScreenSetting>
    // },
    // /** 表格容器样式，若已在 fullScreen containerHeight定义高度，此处不要重写height */
    // containerStyle: {
    //   type: Object as PropType<CSSProperties>
    // }
  }
}

export default tableProps

export type TableProps = Partial<ExtractPropTypes<ReturnType<typeof tableProps>>>

export interface ActionType {
  reload: () => void
  reset: () => void
}

export interface TablePaginationConfig {
  /** 当前页 */
  current?: number
  /** 每页数据量 */
  pageSize?: number
  /** 总数 */
  total?: number
  /** 默认每页数量 */
  defaultPageSize?: number
  /** 是否简洁版 */
  simple?: boolean
}
export interface TableSearchConfigItem {
  value: string
  label: string
  // 初始化请求的参数
  initData?: string
}

// export interface fullScreenSetting {
//   /** 表格容器高度，定义复杂场景的一屏展示宽高，不传默认100% */
//   containerHeight?: string
//   /** 表格分页器高度，定义复杂场景的一屏展示宽高，不传默认有分页器且高度为40px */
//   paginationHeight?: number
// }
