<template>
  <div>
    <Card style="margin-bottom: 16px" :bordered="false">
      <Input
        ref="input"
        v-model="page.search"
        clearable
        placeholder="搜索 Name"
        search
        enter-button
        style="width: 100%; margin-bottom: 16px"
        @on-search="handleSearch"
        @on-clear="handleClear"
      />

      <Space class="operation">
        <Button size="small" type="primary" icon="ios-add" @click="onCreate">创建</Button>
        <Button size="small" type="primary" ghost icon="ios-copy" @click="() => batchCopyModalVisible = true">批量复制</Button>
        <Button size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable">刷新</Button>

        <Checkbox class="checkbox" v-model="labelSelectorCtrl"> 标签过滤 </Checkbox>

        <Space direction="vertical" v-if="labelSelectorCtrl">
          <div>
            <Input style="width: 200px" v-model="firstLabelKey"></Input>
            <span style="font-weight: bold; margin: 0 16px 0 16px"> = </span>
            <Input style="width: 200px" v-model="firstLabelValue"></Input>
          </div>

          <div v-for="(item, idx) in labelSelectorList" :key="idx">
            <Input style="width: 200px" v-model="item.key"></Input>
            <span style="font-weight: bold; margin: 0 16px 0 16px"> = </span>
            <Input style="width: 200px" v-model="item.value"></Input>
            <Button type="text" size="small" style="margin-left: 8px" @click="handleLabelSelectorListDelete(idx)">
              删除
            </Button>
          </div>

          <Button
            v-if="labelSelectorCtrl"
            class="label-selector-button"
            @click="handleLabelSelectorListAdd"
            type="text"
            size="small"
            icon="ios-add-circle"
          >
            添加
          </Button>
        </Space>
      </Space>
    </Card>
    <Card :bordered="false">
      <Table size="small" :loading="tableLoading" :columns="columns" :data="data">
        <template #name="{ row }">
          <div class="name-wrapper">
            <div class="name">
              <LinkButton :text="row.name" ellipsis tooltip @click="onOpenDetail(row)" />
              <span v-if="row.restartStatus" class="notice">[重启或扩缩容中...]</span>
            </div>
            <Tooltip content="查看监控图" transfer>
              <Icon type="md-stats" @click="onOpenContainerChart(row)"
            /></Tooltip>
          </div>
        </template>
      </Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
        placement="top"
      >
      </Page>
    </Card>
    <resource-form
      resourceType="statefulset"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :yamlInitData="yamlInitData"
      :onSubmitCallBack="this.reloadTable"
      forbiddenForm
      isSkipCheck
      notSynchronizeToUnifiedCluster
    />
    <view-yaml
      resourceType="statefulset"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      resource-version="V2"
      notSynchronizeToUnifiedCluster
    />
    <div>
      <Drawer :title="detailTitle" :closable="false" width="60" v-model="openDetail">
        <detail v-if="openDetail" :detail="detailObject"></detail>
      </Drawer>
    </div>

    <Modal
      :title="`监控图 - ${containerChartEntity.name}`"
      width="85"
      footer-hide
      :mask-closable="false"
      v-model="containerChartVisible"
    >
      <WorkloadPodContainerChart
        kind="StatefulSet"
        :reloadFlag="containerChartVisible"
        :entity="containerChartEntity"
      />
    </Modal>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="StatefulSet" />
  </div>
</template>

<script>
import Config from '@/config'
import { relativeTime } from '@/libs/tools'
import { useDelete } from '@/libs/service.request'
import useSingleK8SService from '@/libs/useSingleK8SService'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { Space, Ellipsis, LinkButton, ResourceForm, EnumFormStatus, ViewYaml } from '@/components'

import { WorkloadPodContainerChart, BatchCopy } from '@/domain/Resource'
import { openStatefulSetDetail } from '@/domain/Resource/StatefulSet'
import { ApiStatefulSetList, ApiStatefulSetRestart } from '@/api/k8s/namespace/app/app'

import Detail from './components/detail'

const SERVICE_ENTRY = { resource: 'statefulsets', group: 'apps', version: 'v1' }

export default {
  name: 'app-stateful-set',
  components: { ViewYaml, Detail, Space, ResourceForm, WorkloadPodContainerChart, LinkButton, BatchCopy },
  setup() {
    const { K8SKey } = useSingleK8SService()
    return { K8SKey }
  },
  data() {
    return {
      styles: {
        overflow: 'unset',
        position: 'static'
      },
      selectedVersionId: null,
      historyVersionList: [],
      historyVersionSelected: '',
      yamlTitle: '详情',
      detailTitle: '详情',
      detailObject: undefined,
      openDetail: false,
      tableLoading: false,

      columns: [
        {
          title: 'Name',
          key: 'name',
          slot: 'name',
          minWidth: 300
        },
        {
          title: 'Ready',
          key: 'ready',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 100
        },
        {
          title: 'Available',
          key: 'available',
          tooltip: true,
          tooltipTheme: 'light',
          align: 'center',
          width: 100
        },
        {
          title: 'Label',
          key: 'label',
          tooltip: true,
          tooltipTheme: 'light',
          minWidth: 100,
          render: (h, params) => {
            // const label = params.row.label?.split(',') ?? []
            return params.row.label
              ? h(Ellipsis, {
                  scopedSlots: {
                    default: () => params.row.label,
                    //   label?.length > 8
                    //     ? h('div', [
                    //         label.slice(0, 8)?.map((i) => h(Ellipsis, { props: { type: 'text' } }, i)),
                    //         h('div', '...')
                    //       ])
                    //     : label?.map((i) => h('div', i)),
                    content: () => params.row.label?.split(',')?.map((i) => h('p', `- ${i}`))
                  }
                })
              : h('div', '-')
          }
        },
        {
          title: 'Age',
          key: 'age',
          tooltip: true,
          width: 180,
          render: (h, params) => {
            return h('div', {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 240,
          align: 'center',
          render: (h, params) => {
            return h(Space, { props: { justify: true } }, [
              h(LinkButton, {
                props: { text: 'YAML' },
                on: {
                  click: () => this.onOpenYaml(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: '编辑' },
                on: {
                  click: () => this.onEdit(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: '删除', type: 'danger' },
                on: {
                  click: () => this.onDelete(params.row)
                }
              }),
              h(LinkButton, {
                props: { text: '滚动重启', type: 'danger' },
                on: {
                  click: () => this.onRolloutRestart(params.row.name)
                }
              })
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        search: this.$route.query.name ?? '',
        total: 0,
        clusterId: '',
        namespace: ''
      },
      labelSelectorCtrl: false,
      labelSelectorList: [],
      firstLabelKey: '',
      firstLabelValue: '',
      viewYamlVisible: false,
      formEntity: '',
      yamlHistoryParams: '',
      formVisible: false,
      formStatus: '',
      yamlInitData: '',
      containerChartVisible: false,
      containerChartEntity: {},
      batchCopyModalVisible: false
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    },
    currentCluster() {
      this.$store.commit('getCurrentCluster', this.$store.state.user.userId)
      return this.$store.state.k8s.currentCluster
    }
  },
  methods: {
    async onOpenDetail(row) {
      openStatefulSetDetail(this.currentClusterId, this.currentCluster, this.currentNamespace, row.name, row.uuid)
      //   this.detailTitle = `详情 ${row.name}`
      //   await ApiStatefulSetGet({
      //     clusterId: this.currentClusterId,
      //     namespace: this.currentNamespace,
      //     name: row.name
      //   })
      //     .then((res) => {
      //       this.detailObject = res.data.data.data
      //       this.openDetail = true
      //     })
      //     .catch((err) => {
      //       noticeError(this, '获取详情失败')
      //       noticeError(this, errorMessage(err))
      //     })
    },
    onOpenYaml(record) {
      this.formEntity = {
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        resourceName: record.name,
        ...SERVICE_ENTRY
      }
      this.yamlHistoryParams = {
        kind: 'StatefulSet',
        uuid: record.uuid
      }
      this.viewYamlVisible = true
    },
    onCreate() {
      console.log('onCreate')
      this.formVisible = true
      this.formStatus = EnumFormStatus.Blank
      this.formEntity = {
        ...SERVICE_ENTRY
      }
      this.yamlInitData = `apiVersion: apps/v1 
kind: StatefulSet
metadata:
  name: 必须修改
  namespace: ${this.currentNamespace}
spec:`
    },
    onEdit(record) {
      console.log('onEdit')
      this.formVisible = true
      this.formStatus = EnumFormStatus.Edit
      this.formEntity = {
        ...SERVICE_ENTRY,
        resourceName: record.name
      }
    },
    onDelete(record) {
      this.$Modal.confirm({
        title: '提示',
        content: `是否确认删除 ${record.name}？`,
        loading: true,
        onOk: async () => {
          const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteStatefulset}`, {
            params: {
              namespace: this.currentNamespace,
              clusterId: this.currentClusterId,
              resourceName: record.name
            }
          })
          if (res.success) {
            this.reloadTable()
            this.$Modal.remove()
            this.$Message.success('删除成功！')
          } else {
            this.$Modal.remove()
          }
        }
      })
    },
    makeLabelSelectorList() {
      let tmpSelectorList = []
      if (this.labelSelectorCtrl) {
        tmpSelectorList.push({
          key: this.firstLabelKey,
          op: '=',
          value: this.firstLabelValue
        })
        this.labelSelectorList.forEach((item) => {
          item.op = '='
          tmpSelectorList.push(item)
        })
      }
      this.page.labelSelector = tmpSelectorList
    },
    handleLabelSelectorListAdd() {
      this.labelSelectorList.push({
        key: '',
        op: '',
        value: ''
      })
    },
    handleLabelSelectorListDelete(idx) {
      delete this.labelSelectorList.splice(idx, 1)
    },
    onRolloutRestart(name) {
      this.$Modal.confirm({
        title: 'Tips',
        content: `<p>对 <b>${name}</b> 发起滚动重启, 可刷新界面查看重启状态；</p>`,
        loading: true,
        onOk: async () => {
          await this.rolloutRestart(name)
          this.$Modal.remove()
        }
      })
    },
    async rolloutRestart(name) {
      await ApiStatefulSetRestart({
        clusterId: this.currentClusterId,
        namespace: this.currentNamespace,
        name: name
      })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async reloadTable() {
      this.tableLoading = true
      this.page.clusterId = this.currentClusterId
      this.page.namespace = this.currentNamespace
      this.makeLabelSelectorList()
      await ApiStatefulSetList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    },
    onOpenContainerChart(record) {
      this.containerChartEntity = {
        cluster: this.currentCluster,
        namespace: this.currentNamespace,
        clusterId: this.currentClusterId,
        name: record.name
      }
      this.containerChartVisible = true
    }
  },
  mounted() {
    this.reloadTable()
    this.$refs.input.focus({
      cursor: 'start'
    })
  },
  watch: {
    K8SKey() {
      this.reloadTable()
      this.$refs.input?.focus({
        cursor: 'end'
      })
    }
  }
}
</script>

<style scoped lang="less">
.operation {
  display: flex;
  align-items: baseline;
  .checkbox {
    width: 100px;
  }
  .label-selector-button {
    width: 60px;
  }
}
.name-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .name {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    flex-wrap: nowrap;
    width: ~'calc(100% - 20px)';
    a {
      font-weight: 600;
    }
    .notice {
      color: #ed4014;
      margin-left: 4px;
    }
  }
  .ivu-icon {
    font-size: 16px;
    color: #2a7cc3;
    cursor: pointer;
  }
}
</style>
