import { ExtractPropTypes, PropType } from 'vue'

export function arrayObjectProps() {
  return {
    data: {
      type: Object
    }, // 表单数据对象（响应式对象）
    name: String, // key
    label: String, // 操作对象标题，由于按钮、弹窗标题提示
    columns: {
      type: Array as PropType<Record<string, any>[]>,
      default: function () {
        return []
      }
    },
    canEdit: Boolean,
    canCopy: Boolean,
    addButtonPosition: { type: String as PropType<'top' | 'bottom'>, default: 'bottom' }, // 添加按钮位置
    max: Number, // 多层输入内容的最大数量,
    initData: [Array, Object],
    getAddData: Function, // 新增时，初始化的对象
    modalWidth: [Number, String],
    /** 表格行是否支持拖拽 */
    draggable: Boolean,
    /** 表格列是否支持拖拽 */
    resizable: Boolean,
    /** 操作列宽 */
    opsWidth: Number
  }
}

export type ArrayObjectProps = Partial<ExtractPropTypes<ReturnType<typeof arrayObjectProps>>>

export enum EnumArrayObjectModalStatus {
  Edit = 'edit',
  Create = 'create'
}
