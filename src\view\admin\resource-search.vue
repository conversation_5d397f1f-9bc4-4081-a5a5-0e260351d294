<template>
  <Container>
    <Alert show-icon>
      <b>跨集群搜索功能</b> 可以搜索到您在多云多集群下被授权的所有Deployment (暂时仅支持该资源).
    </Alert>
    <Alert show-icon>
      <b style="color: #ed4014">当前页面再后续版本中会进行重构， 完善成一个多维度多集群检索资源的功能；</b>
    </Alert>
    <Card :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            v-model="page.search"
            clearable
            placeholder="搜索 Deployment Name"
            search
            style="width: 100%"
            enter-button
            @on-search="handleSearch"
            @on-clear="handleClear"
          />
        </Col>
      </Row>
      <Row type="flex" style="margin-bottom: 16px">
        <Col span="24" class="custom-align-right">
          <Button size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable" style="margin-left: 16px"
            >刷新</Button
          >
        </Col>
      </Row>
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <Drawer title="查看 YAML" :mask-closable="false" width="60" v-model="openYAML">
      <!--      <json-viewer :value="currentDeployYAML"-->
      <!--                   :copyable="true"-->
      <!--                   show-double-quotes-->
      <!--                   :expanded="true"-->
      <!--                   :expand-depth="2"-->
      <!--                   icon-style-->
      <!--                   :preview-mode="true"></json-viewer>-->
      <yaml style="height: 90vh" v-model="currentDeployYAML"></yaml>
    </Drawer>
  </Container>
</template>

<script>
import { Container, Yaml } from '@/components'
import { errorMessage, noticeError } from '@/libs/util'
import { color } from '@/libs/consts'
import { mapMutations } from 'vuex'
import {
  ApiResourceDeploymentSearch,
  ApiResourceGetDeployment,
  ApiResourcePermissionList
} from '@/api/k8s/authResource'

export default {
  name: 'K8sResourceSearch',
  components: { Yaml, Container },
  data() {
    return {
      // yamlData: "- hosts: all\n  become: yes\n  become_method: sudo\n  gather_facts: no\n\n  tasks:\n  - name: \"install {{ package_name }}\"\n    package:\n      name: \"{{ package_name }}\"\n      state: \"{{ state | default('present') }}\"",
      resourceAuthRoles: 'Admin,ClusterAdmin',
      loading: {
        table: false,
        search: false
      },
      searchList: [],
      columns: [
        {
          title: 'Deployment',
          key: 'deployment',
          minWidth: 250,
          tooltip: false,
          render: (h, params) => {
            return h(
              'a',
              {
                style: {
                  color: color.primary,
                  cursor: 'pointer',
                  fontWeight: 500
                },
                on: {
                  click: () => {
                    this.setClusterNamespace(
                      params.row.cluster,
                      params.row.clusterId,
                      params.row.namespace,
                      params.row.uuid,
                      params.row.deployment
                    )
                  }
                }
              },
              params.row.deployment
            )
          }
        },
        {
          title: 'Cluster',
          key: 'cluster',
          tooltip: true,
          width: 200
        },
        {
          title: 'Namespace',
          key: 'namespace',
          tooltip: true,
          width: 150
        },
        {
          title: 'Replicas',
          key: 'replicas',
          align: 'center',
          tooltip: true,
          width: 100
        },
        {
          title: 'AvailReplicas',
          key: 'availReplicas',
          align: 'center',
          width: 115,
          tooltip: true
        },
        {
          title: 'Age',
          key: 'age',
          align: 'center',
          width: 100,
          tooltip: true,
          render: (h, params) => {
            return h('span', {}, `${params.row.age}d`)
          }
        },
        {
          title: 'UpdatedAt',
          key: 'updatedAt',
          width: 180,
          align: 'center'
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 120,
          align: 'center',
          fixed: 'right',
          render: (h, params) => {
            return h('span', {}, [
              h('Icon', {
                props: {
                  type: 'ios-paper-outline',
                  size: 14
                },
                style: {
                  color: color.primary
                }
              }),
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    cursor: 'pointer',
                    fontSize: '12px',
                    marginLeft: '8px'
                  },
                  on: {
                    click: () => {
                      this.openYAML = true
                      this.fetchDeploymentYAML(params.row.clusterId, params.row.namespace, params.row.deployment)
                    }
                  }
                },
                'YAML'
              )
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        search: '',
        total: 0,
        sortField: 'cluster_id'
      },
      currentDeployYAML: '',
      openYAML: false
    }
  },
  methods: {
    ...mapMutations(['setCurrentCluster', 'setCurrentNamespace', 'setCurrentClusterId']),
    setClusterNamespace(cluster, clusterId, namespace, deployId, deployment) {
      // this.setCurrentCluster({uid: this.$store.state.user.userId, cluster: cluster})
      // this.setCurrentNamespace({uid: this.$store.state.user.userId, namespace: namespace})
      // this.setCurrentClusterId({uid: this.$store.state.user.userId, clusterId: clusterId})
      let r = this.$router.resolve({
        path: '/kubernetes/namespace/deployment-detail',
        query: {
          clusterId: clusterId,
          namespace: namespace,
          deployment: deployment,
          cluster: cluster,
          uuid: deployId
        }
      })
      window.open(r.href, '_blank')
    },
    async remoteSearch(query) {
      let tlist = [
        'Alabama',
        'Alaska',
        'Arizona',
        'Arkansas',
        'California',
        'Colorado',
        'Connecticut',
        'Delaware',
        'Florida',
        'Georgia',
        'Hawaii',
        'Idaho',
        'Illinois',
        'Indiana',
        'Iowa',
        'Kansas',
        'Kentucky',
        'Louisiana',
        'Maine',
        'Maryland',
        'Massachusetts',
        'Michigan',
        'Minnesota',
        'Mississippi',
        'Missouri',
        'Montana',
        'Nebraska',
        'Nevada',
        'New hampshire',
        'New jersey',
        'New mexico',
        'New york',
        'North carolina',
        'North dakota',
        'Ohio',
        'Oklahoma',
        'Oregon',
        'Pennsylvania',
        'Rhode island',
        'South carolina',
        'South dakota',
        'Tennessee',
        'Texas',
        'Utah',
        'Vermont',
        'Virginia',
        'Washington',
        'West virginia',
        'Wisconsin',
        'Wyoming'
      ]
      if (query !== '') {
        this.loading.search = true
        setTimeout(() => {
          this.loading.search = false
          const list = tlist.map((item) => {
            return {
              value: item,
              label: item
            }
          })
          this.searchList = list.filter((item) => item.label.toLowerCase().indexOf(query.toLowerCase()) > -1)
        }, 200)
      } else {
        this.searchList = []
      }
    },
    async reloadTable() {
      this.loading.table = true
      await ApiResourceDeploymentSearch(this.page)
        .then((res) => {
          var data = res.data.data
          if (data === null) {
            this.data = []
          } else {
            this.data = data.list
          }
          this.page.total = data.total
        })
        .catch((err) => {
          availReplicas
          this.$Message.error(errorMessage(err))
          throw err
        })
      this.loading.table = false
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    },
    fetchResourcePermission() {
      ApiResourcePermissionList()
        .then((res) => {
          this.resourceAuthRoles = res.data.data.roles.join(',')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    fetchDeploymentYAML(cluster_id, namespace, deployment) {
      ApiResourceGetDeployment(cluster_id, namespace, deployment)
        .then((res) => {
          this.currentDeployYAML = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    }
  },
  mounted() {
    this.reloadTable()
    this.fetchResourcePermission()
  }
}
</script>

<style scoped></style>
