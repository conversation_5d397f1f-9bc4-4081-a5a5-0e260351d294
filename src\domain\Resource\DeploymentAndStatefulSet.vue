<template>
  <div>
    <Alert show-icon>
      页面展示了当前空间中已部署的 <span style="color: #2a7cc3">Deployment(无状态服务)</span> 和
      <span style="color: #2a7cc3">StatefulSet(有状态服务)</span>，您可以从应用详情页面中管理与应用相关的资源。
      <p style="margin-top: 16px; margin-left: 16px">
        <b>标签过滤: </b>
        可以启用标签, 通过标签进行进一步筛选资源, 暂时只支持 "=" 操作符, 并且多个条件之间是 AND 关系;
      </p>
      <p style="margin-top: 16px; margin-left: 16px">
        <b>滚动重启: </b>
        列表中的副本会被一个接一个进行重启, 重启过程中表格中会存在
        <span style="color: #ed4014">[重启或扩缩容中...]</span> 标识;
      </p>
    </Alert>
    <Tabs v-model="tabName" :name="TAB_NAME">
      <TabPane name="deployment" label="Deployment (无状态服务)" icon="md-bonfire" :tab="TAB_NAME">
        <app-deployment></app-deployment>
      </TabPane>
      <TabPane name="stateful-set" label="StatefulSet (有状态服务)" icon="md-checkmark-circle-outline" :tab="TAB_NAME">
        <app-stateful-set></app-stateful-set>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import AppStatefulSet from './StatefulSet/StatefulSetList'
import AppDeployment from './Deployment/DeploymentList'

const TAB_NAME = `deployment-and-stateful-set`

export default {
  name: 'deployment-and-stateful-set',
  components: { AppDeployment, AppStatefulSet },
  props: {
    title: {
      type: String,
      default: () => {
        return '-'
      }
    }
  },
  data() {
    return {
      tabName: 'deployment',
      TAB_NAME
    }
  },
  methods: {
    init() {
      if (this.$route.params.switch !== undefined) {
        this.tabName = this.$route.params.switch
      }
    }
  },
  mounted() {
    this.init()
  }
}
</script>

<style scoped></style>
