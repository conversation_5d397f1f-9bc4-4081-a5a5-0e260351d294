export function formatEnumToLabelValue(enumObj) {
  const arr: { value: string; label: string }[] = []
  for (const [value, label] of Object.entries(enumObj)) {
    arr.push({ value, label: label.toString() })
  }
  return arr
}

export enum EnumLocation {
  MESH_EXTERNAL = 'MESH_EXTERNAL（网格外）',
  MESH_INTERNAL = 'MESH_INTERNAL（网格内）'
}

export enum EnumWorkload {
  endpoints = 'Endpoints（服务端点）',
  workloadSelector = 'WorkloadSelector（pod label选择器）'
}
export enum EnumResolution {
  DNS = 'DNS',
  STATIC = 'STATIC',
  NONE = 'NONE'
}
export enum EnumPorts {
  HTTP = 'HTTP',
  HTTPS = 'HTTPS',
  GRPC = 'GRPC',
  HTTP2 = 'HTTP2',
  MONGO = 'MONGO',
  TCP = 'TCP',
  TLS = 'TLS'
}
