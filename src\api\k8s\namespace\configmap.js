import axios from '@/libs/api.request'


export const ApiConfigmapList = (params) => {
  return axios.request({
    url: `/api/v1/resource/configmap/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiConfigmapGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/configmap/get`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiConfigmapDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/configmap/delete`,
    method: 'delete',
    data: data,
  })
}

export const ApiConfigmapYamlCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/configmap/yaml/create`,
    method: 'post',
    data: data,
  })
}


export const ApiConfigmapYamlUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/configmap/yaml/update`,
    method: 'post',
    data: data,
  })
}
