<script setup lang="ts">
import { ref, computed, watch, getCurrentInstance } from 'vue'
import { useRequest } from 'vue-request'
import { useGet, usePost } from '@/libs/service.request'
import { errorMessage, noticeError } from '@/libs/util'
import Config from '@/config'
import { columns, expireTimeArr } from './setting'
import { ContainerFileDownload } from '@/domain/Resource'

interface Props {
  value: boolean
  entity: {
    container: string
    pod: string
    clusterId: string
    namespace: string
    cluster: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits(['input'])
const { proxy } = getCurrentInstance()
const visible = computed({
  get: () => props.value,
  set: (val) => emit('input', val)
})
const createModalVisible = ref(false)
const initContainerData = {
  imageUrl: '',
  durationSec: 3 * 60 * 60
}
const newContainer = ref({ ...initContainerData })
const createLoading = ref(false)
const fileEntity = ref({})
const fileModalVisible = ref(false)

const openCreateModal = () => {
  newContainer.value = { ...initContainerData }
  getImageList()
  createModalVisible.value = true
}

const onCreateContainer = async () => {
  createLoading.value = true
  const { container: targetContainer, ...rest } = props.entity
  const res = await usePost(`${Config.Api.Base}${Config.Api.CreateEphemeralContainer}`, {
    targetContainer,
    ...rest,
    ...newContainer.value
  })

  if (res.success && res.data.data) {
    createModalVisible.value = false
    getListData()
    handleOpenShell(res.data.data?.name)
  }
  createLoading.value = false
}

// 打开shell
const handleOpenShell = async (containerName) => {
  try {
    const r = proxy.$router.resolve({
      path: '/pod-console',
      query: {
        clusterId: props.entity.clusterId,
        cluster: props.entity.cluster,
        namespace: props.entity.namespace,
        pod: props.entity.pod,
        container: containerName,
        priority: 'true'
      }
    })
    window.open(r.href, '_blank')
  } catch (error) {
    noticeError(proxy, `获取Terminal失败 ${errorMessage(error)}`)
  }
}

const openFeishuDoc = () => {
  window.open('https://q9jvw0u5f5.feishu.cn/wiki/OHJTwMLUriLCQBkX8DBckW3cnPf')
}

const {
  data: listData,
  run: getListData,
  loading
} = useRequest(
  () => {
    return useGet<{ data: Record<string, any>[] }>(`${Config.Api.Base}${Config.Api.GetEphemeralContainerList}`, {
      params: {
        ...props.entity
      }
    })
  },
  {
    manual: true,
    formatResult(res) {
      return res.data.data
    }
  }
)

const {
  data: imageList,
  run: getImageList,
  loading: imageListLoading
} = useRequest(
  () => {
    return useGet<{ data: Record<string, any>[] }>(`${Config.Api.Base}${Config.Api.GetImageList}`, {
      params: {
        category: 'developTools'
      }
    })
  },
  {
    manual: true,
    formatResult(res) {
      return res.data.data
    }
  }
)

const onFileModalOpen = (container) => {
  fileEntity.value = {
    container,
    pod: props.entity.pod,
    clusterId: props.entity.clusterId,
    namespace: props.entity.namespace
  }
  fileModalVisible.value = true
}

watch(visible, (val) => {
  if (val) {
    getListData()
  }
})
</script>

<template>
  <div class="wrapper">
    <Modal v-model="visible" footer-hide width="45" title="调试模式">
      <Alert>
        调试模式: 为已存在的业务容器 (目标容器) 创建出一个携带工具的调试容器, 该容器不会影响业务容器的正常运行
      </Alert>
      <Button size="small" type="primary" icon="md-add" @click="openCreateModal"> 创建调试容器 </Button>
      <Button style="margin-left: 16px" size="small" type="primary" ghost icon="md-refresh" @click="getListData">
        刷新
      </Button>
      <Table style="margin-top: 16px" size="small" :columns="columns" :data="listData" :loading="loading">
        <template #ops="{ row }">
          <a @click="handleOpenShell(row.name)" style="margin-right: 16px">连接</a>
          <a @click="onFileModalOpen(row.name)">上传下载</a>
        </template>
      </Table>
    </Modal>

    <Modal v-model="createModalVisible" title="创建调试容器" width="30">
      <template #footer>
        <Button @click="createModalVisible = false">取消</Button>
        <Button :loading="createLoading" @click="onCreateContainer" type="primary">创建并进入</Button>
      </template>
      <Alert> 通过启动一个 工具链容器 对 目标容器 的进程空间进行共享 </Alert>
      <span>选择工具链镜像：</span>
      <div class="select-area">
        <Select v-model="newContainer.imageUrl" :loading="imageListLoading" class="image-list" placeholder="请选择镜像">
          <Option v-for="item in imageList" :key="item.id" :value="item.imageUrl">{{ item.imageUrl }}</Option>
        </Select>
        <Select v-model="newContainer.durationSec" class="expire-time" placeholder="请选择过期时间">
          <Option v-for="item in expireTimeArr" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </div>
      <div class="feishu-link">
        <a @click="openFeishuDoc">如何自定义工具链镜像 ?</a>
      </div>
    </Modal>
    <ContainerFileDownload :entity="fileEntity" v-model="fileModalVisible" />
  </div>
</template>

<style lang="less" scoped>
.select-area {
  display: inline-flex;
  align-items: center;
  width: ~'calc(100% - 96px)';

  .image-list {
    flex: 1;
    margin-right: 8px;
  }

  .expire-time {
    width: 130px;
  }
}

.feishu-link {
  margin: 16px 0 0 96px;
}
</style>
