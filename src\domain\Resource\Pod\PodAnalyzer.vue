<template>
  <Modal v-model="visible" width="80" footer-hide class-name="modal-wrapper">
    <div slot="header" class="header-wrapper">
      <b>诊断报告 - {{ analyzeData.name }}</b>
      <Tag title="phase" :color="phaseColor">{{ analyzeData.phase }}</Tag>
      <Tag title="status" :color="analyzeData.status === 'running' ? 'success' : 'error'">{{ analyzeData.status }}</Tag>
      <a :href="analyzeData.monitorUrl" target="_blank">
        <Tag title="monitor" color="blue" style="cursor: pointer">
          <Icon type="ios-pulse" style="font-weight: bold" />
        </Tag>
      </a>
    </div>
    <div>
      <Spin fix v-if="loading">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      </Spin>
      <!--      <Row>-->
      <!--        <Card style="margin-top: 16px">-->
      <!--          <h4 style="margin-bottom: 16px">推荐指标</h4>-->
      <!--          <template v-for="(c, idx) in analyzeData.containerMetrics">-->
      <!--            <div :key="c.container" style="margin-left: 16px">-->
      <!--              <h5 style="margin-bottom: 8px">[{{idx + 1}}] {{c.container}}</h5>-->
      <!--              <Table size="small" :columns="metricCols" :data="c.recommendedMetrics" ></Table>-->
      <!--            </div>-->
      <!--          </template>-->
      <!--        </Card>-->
      <!--      </Row>-->
      <Space direction="vertical">
        <Card>
          <Space direction="vertical">
            <h4>发生事件</h4>
            <Table height="200" size="small" :columns="eventCols" :data="analyzeData.events" />
          </Space>
        </Card>

        <Card>
          <Space direction="vertical">
            <h4 style="color: #ed4014">发生的错误</h4>
            <div style="font-weight: 600" v-if="analyzeData.aiResult !== undefined">
              {{ analyzeData.aiResult.error }}
            </div>
            <h4 style="color: #19be6b">处理建议</h4>
            <TxtEditor
              v-if="analyzeData.aiResult !== undefined"
              :value="analyzeData.aiResult.solution"
              :forbiddenEdit="true"
              class="text-editor"
            />
          </Space>
        </Card>

        <Card>
          <Space direction="vertical">
            <h4>崩溃日志</h4>
            <TxtEditor :value="analyzeData?.crashLog ?? ''" :forbiddenEdit="true" class="text-editor" />
          </Space>
        </Card>
      </Space>
    </div>
  </Modal>
</template>

<script>
import { relativeTime } from '@/libs/tools'
import TxtEditor from '@/components/txt-editor'
import Space from '@/components/space'

export default {
  name: 'PodAnalyzer',
  props: {
    analyzeData: Object,
    loading: Boolean,
    value: Boolean
  },
  components: { TxtEditor, Space },
  data() {
    return {
      metricCols: [
        {
          title: '指标',
          key: 'metrics',
          width: 150,
          tooltip: true
        },
        {
          title: '当前(request/limit)',
          key: 'current',
          tooltip: true
        },
        {
          title: '推荐(request/limit)',
          key: 'recommended',
          tooltip: true
        }
      ],
      metrics: [
        {
          container: 'spark-kubernetes-driver',
          recommendedMetrics: [
            {
              metrics: 'CPU',
              current: '100m/2',
              recommended: '100m/2'
            },
            {
              metrics: 'MEMORY',
              current: '4505Mi/4505Mi',
              recommended: '4505Mi/4505Mi'
            }
          ]
        }
      ],
      eventCols: [
        {
          title: 'Type',
          key: 'type',
          width: 100,
          tooltip: true
        },
        {
          title: 'Reason',
          key: 'reason',
          width: 150,
          tooltip: true
        },
        {
          title: 'Age',
          key: 'age',
          tooltip: true,

          width: 140,
          render: (h, params) => {
            return h('div', relativeTime(params.row.age))
          }
        },
        {
          title: 'Message',
          key: 'message'
        }
      ],
      eventData: [
        {
          type: 'Warning',
          reason: 'FailedScheduling',
          age: 1687935739,
          message: '0/46 nodes are available: 46 persistentvolumeclaim "pvc-cfs-prod-tc-bj-zt-rmcd-python" not found.'
        },
        {
          type: 'Normal',
          reason: 'NotTriggerScaleUp',
          age: 1687104031,
          message: "pod didn't trigger scale-up: "
        }
      ],
      aiResult: {
        error: '0/46 节点可用，未发现46个持久卷声明"pvc-cfs-prod-tc-bj-zt-rmcd-python"。',
        solution:
          '1. 检查PVC名称是否正确；\n2. 检查PVC是否已被删除或当地区Internet访问不畅；\n3. 如果PVC已被删除，请手动重新创建PVC；\n4. 强烈建议进行数据备份。'
      }
    }
  },
  computed: {
    visible: {
      get: function () {
        return this.$props.value
      },
      set: function (value) {
        this.$emit('input', value)
      }
    },
    phaseColor: function () {
      switch (this.$props.analyzeData?.phase) {
        case 'Pending':
          return 'primary'
        case 'Failed':
          return 'error'
        default:
          return 'success'
      }
    }
  }
}
</script>

<style scoped lang="less">
.header-wrapper {
  display: flex;
  align-items: center;
  b {
    margin-right: 16px;
    color: #000000d9;
    font-size: 14px;
  }
}
.text-editor {
  min-height: 100px;
  max-height: 300px;
  overflow: auto;
}
/deep/.modal-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    height: 84vh;
    overflow: auto;
  }
}
</style>
