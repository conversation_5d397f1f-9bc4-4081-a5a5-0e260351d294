export enum EnumResourceType {
  Deployment = 'deployments',
  StatefulSets = 'statefulsets',
  Pods = 'pods',
  Services = 'services',
  Secrets = 'secrets',

  HPA = 'horizontalpodautoscalers',
  Configmaps = 'configmaps',
  VirtualServices = 'virtualservices',
  DestinationRules = 'destinationrules',
  SideCars = 'sidecars',
  ServiceEntries = 'serviceentries',
  Gateways = 'gateways',
  Nodes = 'nodes',
  PVC = 'persistentvolumeclaims',
  WasmPlugins = 'wasmplugins',
  Job = 'jobs',
  EndPoint = 'endpoints',
  CronJob = 'cronjobs',
  Telemetry = 'telemetries'
}

export enum EnumAction {
  Yaml = 'YAML',
  Relative = '关联',
  Log = '日志',
  Shell = 'Shell'
}

export const ResourceName: Record<EnumResourceType, string> = {
  [EnumResourceType.Deployment]: 'Deployment',
  [EnumResourceType.Pods]: 'Pod',
  [EnumResourceType.Services]: 'Service',
  [EnumResourceType.Secrets]: 'Secret',
  [EnumResourceType.StatefulSets]: 'StatefulSet',

  [EnumResourceType.HPA]: 'HPA',
  [EnumResourceType.Configmaps]: 'Configmap',
  [EnumResourceType.VirtualServices]: 'VirtualService',
  [EnumResourceType.DestinationRules]: 'DestinationRule',
  [EnumResourceType.SideCars]: 'SideCar',
  [EnumResourceType.ServiceEntries]: 'ServiceEntry',
  [EnumResourceType.Gateways]: 'Gateway',
  [EnumResourceType.Nodes]: 'Node',
  [EnumResourceType.PVC]: 'PVC',
  [EnumResourceType.WasmPlugins]: 'WasmPlugin',
  [EnumResourceType.Job]: 'Job',
  [EnumResourceType.EndPoint]: 'EndPoint',
  [EnumResourceType.CronJob]: 'CronJob',
  [EnumResourceType.Telemetry]: 'Telemetry'
}

export const ResourceActions: Record<EnumResourceType, EnumAction[]> = {
  [EnumResourceType.Deployment]: [EnumAction.Yaml, EnumAction.Relative],
  [EnumResourceType.Pods]: [EnumAction.Yaml, EnumAction.Relative, EnumAction.Shell, EnumAction.Log],
  [EnumResourceType.Services]: [EnumAction.Yaml, EnumAction.Relative],
  [EnumResourceType.Secrets]: [EnumAction.Yaml],
  [EnumResourceType.StatefulSets]: [EnumAction.Yaml, EnumAction.Relative],

  [EnumResourceType.HPA]: [EnumAction.Yaml],
  [EnumResourceType.Configmaps]: [EnumAction.Yaml],
  [EnumResourceType.VirtualServices]: [EnumAction.Yaml],
  [EnumResourceType.DestinationRules]: [EnumAction.Yaml],
  [EnumResourceType.SideCars]: [EnumAction.Yaml],
  [EnumResourceType.ServiceEntries]: [EnumAction.Yaml],
  [EnumResourceType.Gateways]: [EnumAction.Yaml],
  [EnumResourceType.Nodes]: [EnumAction.Yaml],
  [EnumResourceType.PVC]: [EnumAction.Yaml],
  [EnumResourceType.WasmPlugins]: [EnumAction.Yaml],
  [EnumResourceType.Job]: [EnumAction.Yaml],
  [EnumResourceType.EndPoint]: [EnumAction.Yaml],
  [EnumResourceType.CronJob]: [EnumAction.Yaml],
  [EnumResourceType.Telemetry]: [EnumAction.Yaml]
}

export const CONTAINER_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'State',
    key: 'state',
    slot: 'state'
  },
  {
    title: 'StartedAt',
    key: 'startedAt',
    slot: 'startedAt'
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: '60'
  }
]
