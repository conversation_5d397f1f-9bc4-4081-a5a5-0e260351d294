<script lang="ts" setup>
import Space from '@/components/space'
import { DestinationRule, EnumComponentType } from '@/domain/Resource/DestinationRule'
import { VirtualService, EnumComponentType as EnumVirtualServiceComponentType } from '@/domain/Resource/VirtualService'
import { RelativeGateway } from '@/domain/Gateway'
</script>

<template>
  <Space direction="vertical">
    <Alert type="warning" show-icon
      >您可从此处查看<a
        href="https://q9jvw0u5f5.feishu.cn/wiki/V0eZwlsw0iRSm6kzqVacLlOunrh?source_type=message&from=message&disposable_login_token=eyJ1c2VyX2lkIjoiNzE2NTY5MTE5NTQ2MzgxMTA3NiIsImRldmljZV9sb2dpbl9pZCI6IjcxNjU3MDAyNzU3MzM4MDcxMDgiLCJ0aW1lc3RhbXAiOjE3MDAxMTg0NzIsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.6108ab75691df81553db9cbbf4232d17d0eeebc53788540b5b6c53134587fa9a"
        target="_blank"
        >如何在牵星平台为我的应用配置VS</a
      ></Alert
    >

    <Card>
      <h4 style="margin-bottom: 16px">VirtualService（虚拟服务）</h4>
      <VirtualService :type="EnumVirtualServiceComponentType.AssociatedDeployment" />
    </Card>

    <Card>
      <h4 style="margin-bottom: 16px">DestinationRule（目标规则）</h4>
      <DestinationRule :type="EnumComponentType.Associated" />
    </Card>
    <Card>
      <h4 style="margin-bottom: 16px">Gateway（网关）</h4>
      <RelativeGateway />
    </Card>
  </Space>
</template>

<style lang="less" scoped></style>
