import { ref, onMounted, getCurrentInstance, watch } from 'vue'

import Config from '@/config'
import { Response, useDelete, useGet, PageList } from '@/libs/service.request'
import { useStore } from '@/libs/useVueInstance'

import { YamlHistoryParams } from '@/components/yaml'
import { ActionType, TableColumn, TableRequest } from '@/components/pro-table'

import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import useSingleK8SService from '@/libs/useSingleK8SService'
import { useRequest } from 'vue-request'

interface Category {
  group: string
  name: string
  namespaced: boolean
  resource: string
  version: string
  kind: string
}

export default function useCRDService() {
  const { proxy } = getCurrentInstance()
  const { K8SKey } = useSingleK8SService()

  const store = useStore()
  const K8SInstance = ref<{ namespace: string; clusterId: string }>()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()

  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()
  const categoryData = ref()
  const currentCategory = ref<Category>()
  const detail = ref({
    visible: false,
    data: {}
  })

  const {
    data: originCategoryData,
    loading: originCategoryDataLoading,
    run: getOriginCategoryData
  } = useRequest(
    () => {
      return useGet<Response<Category[]>>(
        `${Config.Api.Base}${Config.Api.GetCRDList}?clusterId=${K8SInstance.value.clusterId}`
      )
    },
    {
      manual: true,
      formatResult(res) {
        const data = res.data.data
        categoryData.value = data
        currentCategory.value = data?.[0]
        return data
      }
    }
  )

  const {
    data: tableColumns,
    loading: tableColumnsLoading,
    run: getTableColumns
  } = useRequest(
    () => {
      return useGet<Response<TableColumn[]>>(`${Config.Api.Base}${Config.Api.GetCRDObjectTableColumns}`, {
        params: {
          ...currentCategory.value,
          ...(currentCategory.value.namespaced ? K8SInstance.value : { clusterId: K8SInstance.value.clusterId })
        },
        skipErrorHandler: true
      })
    },
    {
      manual: true,
      formatResult(res) {
        if (res.success) {
          const data = res.data.data?.map((item) => {
            return {
              ...item,
              ...(item.key === 'name' ? { slot: 'name', width: 300, fixed: 'left' } : null),
              ...(item.key === 'ready' ? { slot: 'ready', width: 80, align: 'center' } : null),
              tooltip: true,
              tooltipTheme: 'light',
              minWidth: 100
            }
          })

          data.push(
            {
              title: 'Age',
              key: 'creationTimestamp',
              slot: 'creationTimestamp',
              minWidth: 160
            } as any,
            {
              title: 'Ops',
              key: 'ops',
              slot: 'ops',
              width: 160,
              align: 'center',
              fixed: 'right'
            } as any
          )
          return data
        } else {
          return []
        }
      }
    }
  )

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      ...currentCategory.value
    }
    yamlInitData.value = `apiVersion: ${currentCategory.value.group}/${currentCategory.value.version} 
kind: ${currentCategory.value.kind}
metadata:
    name: 必须修改
${
  currentCategory.value.namespaced
    ? `    namespace: ${K8SInstance.value.namespace}
spec:`
    : 'spec:'
}
    `
  }

  const onDelete = (record: Record<string, string>) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteCRDObject}`, {
          params: {
            ...currentCategory.value,
            ...(currentCategory.value.namespaced ? K8SInstance.value : { clusterId: K8SInstance.value.clusterId }),
            name: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onEdit = (record: Record<string, string>) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...currentCategory.value,
      resourceName: record.name
    }
  }

  const onViewYaml = (record: Record<string, string>) => {
    yamlHistoryParams.value = {
      ...(currentCategory.value.namespaced ? K8SInstance.value : { clusterId: K8SInstance.value.clusterId }),
      kind: currentCategory.value.kind,
      uuid: record.uuid
    }
    formEntity.value = {
      ...currentCategory.value,
      ...(currentCategory.value.namespaced ? K8SInstance.value : { clusterId: K8SInstance.value.clusterId }),
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: Record<string, string>) => {
    console.log('onViewDetail')
    detail.value = {
      visible: true,
      data: record
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<Record<string, string>[]>>(
      `${Config.Api.Base}${Config.Api.GetCRDObjectList}?search=${params.searchValue ?? ''}&page=${
        params.pageIndex
      }&size=${params.pageSize}`,
      {
        params: {
          ...currentCategory.value,
          ...(currentCategory.value.namespaced ? K8SInstance.value : { clusterId: K8SInstance.value.clusterId })
        },
        skipErrorHandler: true
      }
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSearchCategoryData = (val) => {
    if (val) {
      categoryData.value = originCategoryData.value.filter((item) => item.name.includes(val))
    } else {
      categoryData.value = originCategoryData.value
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value.reload()
  }

  const initK8SInstance = () => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    store.commit('getCurrentNamespace', store.state.user.userId)
    K8SInstance.value = {
      namespace: store.state.k8s.currentNamespace,
      clusterId: store.state.k8s.currentClusterId
    }
  }

  const onSelectCategory = (val) => {
    currentCategory.value = val
  }

  onMounted(() => {
    initK8SInstance()
    getOriginCategoryData()
  })

  watch(K8SKey, () => {
    initK8SInstance()
  })

  watch(K8SInstance, (val, old) => {
    if (val.clusterId !== old.clusterId) {
      getOriginCategoryData()
    }
    if (val.namespace !== old.namespace) {
      refObject.tableRef.value.reload()
    }
  })

  watch(
    () => [currentCategory.value, refObject.tableRef.value],
    () => {
      if (currentCategory.value && refObject.tableRef.value) {
        getTableColumns()
        refObject.tableRef.value.reload()
      }
    }
  )
  return {
    getTableData,
    refObject,
    onCreate,
    onEdit,
    onDelete,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    onSubmitSuccess,
    onViewDetail,

    originCategoryDataLoading,
    categoryData,
    onSearchCategoryData,
    currentCategory,
    onSelectCategory,
    tableColumns,
    tableColumnsLoading,
    detail,
    K8SInstance
  }
}
