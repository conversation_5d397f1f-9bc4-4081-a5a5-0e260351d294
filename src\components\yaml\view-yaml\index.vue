<script lang="ts" setup>
import { Yaml, LinkButton } from '@/components'
import useViewYamlService, { ResourceEntity, YamlHistoryParams } from './useViewYamlService'
import Space from '@/components/space'
import { SyncUnifiedClusterModalColumns } from './setting'

export type ViewYamlProps = {
  resourceEntity: ResourceEntity
  value: boolean
  /** yaml回滚功能入参 */
  yamlHistoryParams?: YamlHistoryParams
  resourceType: string
  /** 不同步到统一集群 */
  notSynchronizeToUnifiedCluster?: boolean
  /** 是否校验yaml数据合法性 */
  isCheckYaml?: boolean
  /** 设置抽屉容器.ivu-drawer-wrap的类名 */
  className?: string
  /** 是否请求时只传集群，不传命名空间 */
  isSkipNamespaceInParams?: boolean
}

const props = defineProps<ViewYamlProps>()

defineEmits(['input'])

const {
  yamlData,
  yamlHistoryList,
  yamlLoading,
  yamlHistoryVersion,
  onApplyVersion,
  isUnifiedCluster,
  isApplyToUnifiedCluster,
  unifiedClusterList,
  syncUnifiedClusterModal,
  onSyncUnifiedCluster
} = useViewYamlService(props)
</script>

<template>
  <div>
    <Drawer
      title="查看 YAML"
      :closable="false"
      width="60"
      :value="props.value"
      @on-visible-change="
        (val) => {
          $emit('input', val)
        }
      "
      :class-name="props.className"
      v-bind="$attrs"
    >
      <div class="view-yaml-wrapper">
        <div v-if="props.yamlHistoryParams && resourceType !== 'rayjobs'" class="yaml-operation">
          <Alert show-icon>没有修改版本原因: 未从平台做过配置修改。</Alert>
          <Space style="align-items: center">
            <span style="font-weight: bold">修改版本: </span>
            <Select v-model="yamlHistoryVersion" style="width: 250px" clearable>
              <Option v-for="item in yamlHistoryList" :value="item.id" :key="item.id">{{ item.createdAt }}</Option>
            </Select>
            <Button type="primary" @click="onApplyVersion" :disabled="!yamlHistoryVersion"> 应用版本 </Button>
            <Checkbox
              :disabled="!yamlHistoryVersion"
              v-show="isUnifiedCluster"
              v-model="isApplyToUnifiedCluster"
              style="color: rgb(231, 79, 76)"
              >同步到统一集群
              <Poptip always trigger="hover" placement="top" transfer transfer-class-name="form-item-poptip">
                <div slot="content">
                  <div>统一集群列表:</div>
                  <div v-for="item in unifiedClusterList" :key="item.id">- {{ item.name }}</div>
                </div>
                <Icon
                  type="ios-alert-outline"
                  style="color: #2a7cc3; font-size: 14px; cursor: pointer; margin-right: 16px"
                /> </Poptip
            ></Checkbox>
          </Space>
        </div>
        <div class="yaml-content">
          <Spin fix v-if="yamlLoading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <yaml v-if="yamlData" :value="yamlData" :forbiddenEdit="true" />
        </div>
      </div>
    </Drawer>
    <Modal width="40" v-model="syncUnifiedClusterModal.visible" title="同步结果集" footer-hide>
      <Table :columns="SyncUnifiedClusterModalColumns" :data="syncUnifiedClusterModal.data">
        <template #name="{ row }"> {{ row.cluster }} / {{ row.namespace }} </template>
        <template #status="{ row }">
          <span v-if="row.status === 'succeed'" style="color: #1abe6b">同步成功</span>
          <span v-else style="color: #ed4014">同步失败{{ row.message ? `（${row.message}）` : '-' }}</span>
        </template>
        <template #ops="{ row }">
          <LinkButton
            :disabled="row.status === 'succeed'"
            text="重试"
            @click="() => onSyncUnifiedCluster(syncUnifiedClusterModal.params)"
          />
        </template>
      </Table>
    </Modal>
  </div>
</template>
<style scoped lang="less">
.view-yaml-wrapper {
  display: flex;
  align-items: center;
  height: ~'calc(100vh - 51px - 32px)';
  flex-direction: column;
  .yaml-operation {
    width: 100%;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
  }
  .yaml-content {
    width: 100%;
    flex: 1;
  }
}
</style>
