import { relativeTime } from '@/libs/tools'
import Space from '@/components/space'
import dayjs from 'dayjs'
import { Tooltip } from 'view-design'
import { Ellipsis } from '@/components'

export const TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Host',
    key: 'hosts',

    render: (h, params) => {
      return params.row.hosts
        ? h(Ellipsis, {
            scopedSlots: {
              default: () =>
                params.row.hosts?.length > 8
                  ? h('div', [
                      params.row.hosts.slice(0, 8)?.map((i) => h(Ellipsis, { props: { type: 'text' } }, i)),
                      h('div', '...')
                    ])
                  : params.row.hosts?.map((i) => h('div', i)),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Location',
    key: 'location',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Resolution',
    key: 'resolution',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    width: 160,
    render: (h, params) => {
      return h(Ellipsis, relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]

export const PORTS_TABLE_COLUMNS = [
  { title: 'Address', key: 'address' },
  {
    title: 'Port',
    key: 'ports',
    render: (h, params) => {
      return h(
        Space,
        params.row.ports?.data?.map((i) => h('span', `${i.key}:${i.value}`))
      )
    }
  }
]

export const DETAIL_CONFIG = {
  BASE_INFO: [
    {
      title: 'Name',
      key: 'name',
      render: (h, data) => {
        return h('b', data.name)
      }
    },
    {
      title: 'Namespace',
      key: 'namespace'
    },
    {
      title: 'CreationTime',
      key: 'creationTime',
      render: (h, data) => {
        return h('span', dayjs(data?.creationTimestamp * 1000).format('YYYY-MM-DD HH:mm'))
      }
    },
    {
      title: 'Labels',
      key: 'labels',
      slot: 'labels'
    }
  ],
  SERVICE_ENTRY: [
    {
      title: '位置(Location)',
      key: 'location'
    },
    {
      title: '主机信息(Host)',
      key: 'hosts',
      render: (h, data) => {
        return h(
          'div',
          data?.hosts?.map((host) => h('div', host))
        )
      }
    },
    {
      title: '端口(Port)',
      key: 'ports',
      render: (h, data) => {
        return h(
          Space,
          data.ports?.map((port) => h('span', `${port.name}|${port.number}|${port.protocol}`))
        )
      }
    },
    {
      title: '解析方式(Resolution)',
      key: 'resolution'
    }
  ]
}
