{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "moduleResolution": "node", "experimentalDecorators": true, "allowJs": true, "checkJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env"], "paths": {"@/*": ["src/*"], "_c/*": ["src/components/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "vueCompilerOptions": {"target": 2.7}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}