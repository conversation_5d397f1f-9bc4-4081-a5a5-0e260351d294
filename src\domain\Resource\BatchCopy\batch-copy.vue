<script lang="ts" setup>
import useBatchCopyService, { BATCH_COPY_RESULT_COLUMNS } from './useBatchCopyService'
import { Empty, LinkButton, ViewYaml } from '@/components'

const props = defineProps({
  /** 控制弹窗开关 */
  value: Boolean,

  /** 资源类型(api) */
  resourceType: { type: String, default: '' }
})
const emit = defineEmits(['input'])

const {
  data,
  visible,
  cluster,
  clusterID,
  namespace,
  targetClusterId,
  targetNamespace,
  clusterList,
  namespaceList,
  onSelectCluster,
  resourceList,
  targetResourceKeys,
  onResourceListChange,
  resourceListLoading,
  onBatchCopy,
  batchCopyResultVisible,
  batchCopyResult,
  onRetry,
  viewYamlVisible,
  yamlEntity,
  onOpenYaml
} = useBatchCopyService(props, emit)
</script>

<template>
  <div>
    <Modal
      :title="`批量复制${props.resourceType}`"
      :mask-closable="false"
      :scrollable="false"
      :width="45"
      v-model="visible"
    >
      <div class="batch-operate-wrapper">
        <div class="cluster-namespace">
          <div class="item-wrapper">
            <div class="item">
              <span class="title">原集群：</span><span class="value">{{ cluster }}</span
              ><span v-show="false">{{ clusterID }}</span>
            </div>
            <div class="item">
              <span class="title">原命名空间：</span><span class="value">{{ namespace }}</span>
            </div>
          </div>
          <div class="item-wrapper">
            <div class="item">
              <span class="title">目标集群：</span>
              <Select v-model="targetClusterId" filterable @on-select="onSelectCluster">
                <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="item">
              <span class="title">目标命名空间：</span>
              <Select v-model="targetNamespace" filterable>
                <Option v-for="item in namespaceList" :value="item.name" :key="targetClusterId + item.name">{{
                  item.name
                }}</Option>
              </Select>
            </div>
          </div>
        </div>
        <div class="resource-list-wrapper">
          <Spin fix v-if="resourceListLoading" />
          <Transfer
            v-if="resourceList?.length"
            :data="resourceList"
            :target-keys="targetResourceKeys"
            @on-change="onResourceListChange"
          ></Transfer>
          <Empty v-else />
        </div>
      </div>
      <template #footer>
        <Button type="text" @click="() => (visible = false)">取消</Button>
        <Button type="primary" @click="onBatchCopy">确定</Button>
      </template>
    </Modal>
    <Modal
      title="复制结果"
      :mask-closable="false"
      :scrollable="false"
      :width="45"
      v-model="batchCopyResultVisible"
      footer-hide
      class-name="batch-result-modal"
    >
      <div class="count-wrapper">
        <div class="dot total">总数：{{ batchCopyResult?.length ?? 0 }}</div>
        <div class="dot succeed">成功：{{ batchCopyResult?.filter((i) => i.status === 'succeed')?.length ?? 0 }}</div>
        <div class="dot failed">失败：{{ batchCopyResult?.filter((i) => i.status === 'failed')?.length ?? 0 }}</div>
      </div>
      <Table :data="batchCopyResult" :columns="BATCH_COPY_RESULT_COLUMNS" :height="380">
        <template #status="{ row }">
          <span :class="`status-${row.status}`">
            <Icon :type="row.status === 'succeed' ? 'ios-checkmark-circle' : 'ios-close-circle'" />
            {{ row.status === 'succeed' ? '成功' : '失败' }}</span
          >
        </template>
        <template #ops="{ row }">
          <LinkButton v-if="row.status === 'succeed'" @click="() => onOpenYaml(row)" text="YAML" />
          <LinkButton v-else @click="() => onRetry(row)" text="重试" danger />
        </template>
      </Table>
      <view-yaml
        class-name="batch-result-yaml-drawer"
        :resourceType="props.resourceType"
        v-model="viewYamlVisible"
        :resource-entity="yamlEntity"
        resource-version="V2"
        :isCheckYaml="false"
        notSynchronizeToUnifiedCluster
      />
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.batch-operate-wrapper {
  .cluster-namespace {
    display: flex;
    flex-direction: row;
    width: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 16px;
    .item-wrapper {
      flex: 1 0 0%;
      .item {
        display: flex;
        align-items: center;
        height: 32px;
        &:not(:last-child) {
          margin-bottom: 8px;
        }
        .title {
          width: 110px;
          display: inline-block;
        }
        .value {
          font-weight: 600;
        }
      }
    }
  }
  .resource-list-wrapper {
    margin-top: 16px;
    width: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 16px;
    .ivu-transfer {
      /deep/.ivu-transfer-list {
        width: ~'calc(50% - 30px)';
      }
    }
  }
}
.status-succeed,
.status-failed {
  display: flex;
  align-items: center;
  .ivu-icon {
    font-size: 16px;
    margin-right: 4px;
  }
}
.status-succeed {
  color: #67c23a;
}
.status-failed {
  color: #f56c6c;
}
.count-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  .dot {
    margin-right: 8px;
    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }
    &.total::before {
      background-color: #409eff;
    }
    &.succeed::before {
      background-color: #67c23a;
    }
    &.failed::before {
      background-color: #f56c6c;
    }
  }
}
</style>
