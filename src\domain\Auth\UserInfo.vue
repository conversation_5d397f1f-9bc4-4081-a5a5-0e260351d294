<script lang="ts" setup>
import { Space, DescIcon } from '@/components'

import dayjs from 'dayjs'
import { useRequest } from 'vue-request'
import { useGet } from '@/libs/service.request'
import Config from '@/config'

interface UserProfile {
  createTimestamp: number
  isActive: boolean
  isAdmin: boolean
  isClusterAdmin: boolean
  nickName: string
  userName: string
}

const { loading, data: basicInfo } = useRequest(
  () => {
    return useGet<{ data: UserProfile }>(`${Config.Api.Base}${Config.Api.GetUserProfile}`)
  },
  {
    formatResult: (res) => {
      const userInfo = res.data.data

      return {
        username: { value: userInfo?.userName, label: '用户名' },
        nickName: { value: userInfo?.nickName, label: '姓名' },
        isActive: { value: userInfo?.isActive, label: '激活' },
        isAdmin: {
          value: userInfo?.isAdmin,
          label: '管理员',
          desc: '拥有平台所有权限，所有操作都无限制 (可忽略下面的权限显示)'
        },
        isClusterAdmin: {
          value: userInfo?.isClusterAdmin,
          label: '集群管理员',
          desc: '几乎拥有所有权限除了一些后台管理相关的权限'
        },
        timing: {
          value: Math.abs(dayjs(userInfo?.createTimestamp * 1000).diff(dayjs(), 'day')) + '天',
          label: '使用时长'
        }
      }
    }
  }
)

const iconKeys = ['isAdmin', 'isActive', 'isClusterAdmin']
</script>

<template>
  <div class="basic-info-wrapper">
    <Spin v-if="loading" fix />
    <div class="basic-info" v-if="basicInfo">
      <Row type="flex" v-for="[key, item] in Object.entries(basicInfo)" :key="key">
        <Col span="10">
          <Space :size="8">
            <span>{{ item.label }}</span>
            <DescIcon :desc="item.desc" placement="right" />
          </Space>
        </Col>
        <Col span="14">
          <template v-if="iconKeys.includes(key)">
            <Icon v-if="item.value" type="ios-checkmark-circle" style="color: #19be6b" :size="16" />
            <Icon v-else type="ios-close-circle" style="color: crimson" :size="16" />
          </template>
          <template v-else>
            {{ item.value }}
          </template>
        </Col>
      </Row>
    </div>
  </div>
</template>

<style lang="less" scoped>
.basic-info-wrapper {
  width: 100%;
  height: 100%;
  .basic-info {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    > .ivu-row {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      /* flex: 1 0 0%; */
      //   border-bottom: 1px solid #e9e9e9;
      width: 100%;
      justify-content: space-between;
      padding: 4px 0;
    }
  }
}
</style>
