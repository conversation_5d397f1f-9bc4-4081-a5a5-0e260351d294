import { ActionType, TableRequest } from '@/components/pro-table'
import { PageList, useGet } from '@/libs/service.request'
import { ref, getCurrentInstance, watch, onBeforeMount, computed } from 'vue'
import Config from '@/config'
import { GatewayMgt } from './type'
import { YamlHistoryParams } from '@/components/yaml'
// import { NAMESPACE_LIST } from './setting'
import { useStore } from '@/libs/useVueInstance'
import useSingleK8SService from '@/libs/useSingleK8SService'

export const SERVICE_ENTRY = { resource: 'deployments', group: 'apps', version: 'v1' }
export const GatewayNamespaceList = ['istio-ingress', 'istio-system']

export default function useGatewayManagementService() {
  const store = useStore()
  const { proxy } = getCurrentInstance()
  useSingleK8SService({
    setClusterOnly: true
  })

  //   const namespace = ref(proxy.$route.query.namespace ?? NAMESPACE_LIST[0].value)
  //   const namespaceList = ref(NAMESPACE_LIST)
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const formEntity = ref()
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const gatewayInfo = ref<{ visible: boolean; data?: GatewayMgt }>({ visible: false })
  const gatewayInfoModal = ref()
  const logModal = ref<{ visible: boolean; data?: GatewayMgt }>({
    visible: false
  })
  const refPodObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewPodYamlVisible = ref(false)
  const podYamlEntity = ref()
  const logMergeModal = ref({
    visible: false,
    data: {} as GatewayMgt
  })
  const cluster = computed(() => {
    store.commit('getCurrentCluster', store.state.user.userId)
    const data = store.state.k8s.currentCluster
    return data
  })
  const clusterId = computed(() => {
    store.commit('getCurrentClusterID', store.state.user.userId)
    const data = store.state.k8s.currentClusterId
    return data
  })

  const namespace = computed(() => {
    store.commit('getCurrentNamespace', store.state.user.userId)
    const data = store.state.k8s.currentNamespace
    return data
  })

  const openLogMergeModal = (record) => {
    logMergeModal.value = {
      visible: true,
      data: record
    }
  }

  const openLogModal = async (record) => {
    logModal.value = {
      visible: true,
      data: record
    }
    refPodObject.tableRef.value?.reload()
  }
  const onOpenYamlFromTable = (record) => {
    /** 强行修改drawer层级，修复iview modal和drawer 每次点击都叠加z-index，但叠加的数量不一致导致的覆盖问题 */
    const modal = document.getElementsByClassName('pod-modal')?.[0] as HTMLElement
    const drawer = document.getElementsByClassName('pod-yaml-drawer')?.[0] as HTMLElement
    if (modal && drawer) {
      drawer.style.zIndex = (Number(modal.style.zIndex) + 1).toString()
    }

    viewPodYamlVisible.value = true

    podYamlEntity.value = {
      namespace: namespace.value,
      clusterId: clusterId.value,
      resourceName: record.name,
      resource: 'pods',
      group: '',
      version: 'v1'
    }
  }
  const jumpToPodLog = (record) => {
    const router = proxy.$router.resolve({
      path: '/kubernetes/logs',
      query: {
        clusterId: record.clusterId,
        namespace: record.namespace,
        cluster: record.cluster,
        pod: record.name,
        container: 'istio-proxy'
      }
    })
    window.open(router.href, '_blank')
  }

  const getLogModalTableData: TableRequest = async (params) => {
    const res = await useGet<{ data: GatewayMgt[] }>(
      `${Config.Api.Base}${Config.Api.GetGatewayManageRelativeLogTableData}?&clusterId=${clusterId.value}&namespace=${namespace.value}&objectName=${logModal.value.data.name}`
    )
    return {
      success: res.success,
      total: res.data?.data?.length ?? 0,
      data: res.data?.data ?? []
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<GatewayMgt[]>>(
      `${Config.Api.Base}${Config.Api.GetGatewayManageTableData}?searchKey=${params.searchKey ?? ''}&searchValue=${
        params.searchValue ?? ''
      }&clusterId=${clusterId.value}&namespace=${namespace.value}&page=${params.pageIndex}&size=${params.pageSize}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onViewDetail = async (record: GatewayMgt) => {
    console.log('onViewDetail')
    gatewayInfo.value = { visible: true, data: record }
    gatewayInfoModal.value.modalIndex = 920
    gatewayInfoModal.value.handleGetModalIndex = () => {
      return gatewayInfoModal.value.modalIndex - 1000
    }
  }
  const onViewRoute = async (record: GatewayMgt) => {
    window.open(
      `${window.location.origin}/gateway-route-map?deployment=${record.name}&deploymentNamespace=${namespace.value}&clusterId=${clusterId.value}`
    )
  }

  const onViewYaml = (record: GatewayMgt) => {
    yamlHistoryParams.value = {
      kind: 'deployment',
      uuid: record.uid
    }
    formEntity.value = {
      namespace: namespace.value,
      clusterId: clusterId.value,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  onBeforeMount(() => {
    if (proxy.$route.query.clusterId) {
      store.commit('setCurrentClusterId', {
        uid: store.state.user.userId,
        clusterId: proxy.$route.query.clusterId
      })
    }
  })

  watch(
    () => [clusterId.value, namespace.value, refObject.tableRef.value],
    () => {
      clusterId.value && namespace.value && refObject.tableRef.value?.reload()
    }
  )

  return {
    clusterId,
    cluster,
    namespace,

    getTableData,
    refObject,
    onViewDetail,
    onViewYaml,
    viewYamlVisible,
    formEntity,
    yamlHistoryParams,

    gatewayInfo,
    gatewayInfoModal,
    onViewRoute,

    logModal,
    openLogModal,
    onOpenYamlFromTable,
    jumpToPodLog,
    getLogModalTableData,
    refPodObject,
    viewPodYamlVisible,
    podYamlEntity,

    logMergeModal,
    openLogMergeModal
  }
}
