import { handleCollapse, onLinkBtnClick, onNodeHover, onTableBtnClick, onYamlBtnClick } from './node'
import { onZoomChange } from './view'
// 监听画布缩放，缩小到一定程度，节点显示缩略样式
let currentLevel = undefined
export const bindEvent = (graph, { message, openTableInModal, openYamlWindow }) => {
  graph.on('collapse-text:click', (e) => {
    handleCollapse(graph, e, currentLevel)
  })
  graph.on('collapse-back:click', (e) => {
    handleCollapse(graph, e, currentLevel)
  })

  graph.on(`node:mouseover`, (e) => {
    onNodeHover(graph, e, 'enter')
  })
  graph.on(`node:mouseout`, (e) => {
    onNodeHover(graph, e, 'leave')
  })

  graph.on(`table-btn-shape:click`, (e) => {
    onTableBtnClick(graph, e, message, openTableInModal)
  })

  graph.on(`link-btn-shape:click`, (e) => {
    onLinkBtnClick(graph, e)
  })

  graph.on(`yaml-btn-shape:click`, (e) => {
    onYamlBtnClick(graph, e, openYamlWindow)
  })

  graph.on('viewportchange', (e) => {
    if (e.action !== 'zoom') return
    currentLevel = onZoomChange(graph, currentLevel)
  })
}
export default bindEvent
