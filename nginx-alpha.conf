user root;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
        worker_connections 768;
}

http {
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 600;
        types_hash_max_size 2048;


        include /etc/nginx/mime.types;
        default_type application/octet-stream;


        ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
        ssl_prefer_server_ciphers on;


        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;


        gzip on;
        server {
                listen       80 backlog=1024;
                server_name  alpha-cloud.ttyuyin.com;
                return 301 https://alpha-cloud.ttyuyin.com$request_uri;
        }
        server {
                # 服务器端口使用443，开启ssl, 这里ssl就是上面安装的ssl模块
                listen       443  ssl;
                # 域名，多个以空格分开
                server_name  alpha-cloud.ttyuyin.com;
		proxy_send_timeout      3600s;
                proxy_read_timeout      3600s;
		proxy_connect_timeout   3600s;

                # resolver
                # resolver ************ valid=60s;
                resolver kube-dns.kube-system.svc.cluster.local valid=60s;
                resolver_timeout 3s;

                # ssl证书地址
                ssl_certificate     /etc/nginx/ssl/tls.crt;  # pem文件的路径
                ssl_certificate_key  /etc/nginx/ssl/tls.key; # key文件的路径

                # ssl验证相关配置
                ssl_session_timeout  5m;    #缓存有效期
                ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;    #加密算法
                ssl_protocols TLSv1 TLSv1.1 TLSv1.2;    #安全链接可选的加密协议
                ssl_prefer_server_ciphers on;   #使用服务器端的首选算法
                
                location /api/v1/llm {
                        client_max_body_size 4096m;
                        proxy_pass http://tt-cloud-ops-brain:8003;
                        proxy_redirect off;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                }

                location /api/v1/ws {
                        client_max_body_size 4096m;
                        proxy_pass http://alpha-cloud-enterprise:8000;
                        proxy_set_header        Host $host;
                        proxy_set_header        X-Real-IP $remote_addr;
                        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header        Upgrade $http_upgrade;
                        proxy_set_header        Connection "upgrade";
                        proxy_send_timeout      36000s;
                        proxy_read_timeout      36000s;
                }

                location /api/v2/ws {
                        client_max_body_size 4096m;
                        proxy_pass http://alpha-cloud-enterprise:8000;
                        proxy_set_header        Host $host;
                        proxy_set_header        X-Real-IP $remote_addr;
                        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header        Upgrade $http_upgrade;
                        proxy_set_header        Connection "upgrade";
                        proxy_send_timeout      36000s;
                        proxy_read_timeout      36000s;
                }

                location /api/v1/query_range {
                        proxy_pass http://yw-hw-bj-sre-monitor-api.ttyuyin.com;
                        proxy_set_header User-Agent "star-constack";
                }

                location /api {
                        client_max_body_size 4096m;
                        proxy_pass http://alpha-cloud-enterprise:8000;
                        proxy_redirect off;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                }
                location / {
                        root   /usr/share/nginx/html/;
                        index  index.html;
                        try_files $uri $uri/ /index.html;
                }

                error_page   500 502 503 504  /50x.html;
                location = /50x.html {
                        root   html;
                }
        }
        
        server {
                listen 8100 ssl http2;
                
                # ssl证书地址
                ssl_certificate     /etc/nginx/ssl/tls.crt;  # pem文件的路径
                ssl_certificate_key  /etc/nginx/ssl/tls.key; # key文件的路径

                # ssl验证相关配置
                ssl_session_timeout  5m;    #缓存有效期
                ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;    #加密算法
                ssl_protocols TLSv1 TLSv1.1 TLSv1.2;    #安全链接可选的加密协议
                ssl_prefer_server_ciphers on;   #使用服务器端的首选算法

                location / {
                        grpc_pass grpc://alpha-cloud-enterprise:8100;
                }
        }  


        server {
                listen 8107 ssl http2;

                grpc_read_timeout 3000s; # These are recommended everywhere, but I haven't had any success
                grpc_send_timeout 3000s; #
                grpc_socket_keepalive on;
                
                # ssl证书地址
                ssl_certificate     /etc/nginx/ssl/tls.crt;  # pem文件的路径
                ssl_certificate_key  /etc/nginx/ssl/tls.key; # key文件的路径
                proxy_send_timeout      3600s;
                proxy_read_timeout      3600s;
		proxy_connect_timeout   3600s;

                # ssl验证相关配置
                ssl_session_timeout  5m;    #缓存有效期
                ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;    #加密算法
                ssl_protocols TLSv1 TLSv1.1 TLSv1.2;    #安全链接可选的加密协议
                ssl_prefer_server_ciphers on;   #使用服务器端的首选算法

                location / {
                        grpc_pass grpc://alpha-tt-cloud-continuousdeployment:8107;
                }
        }

         server {
                listen       8007  ssl;

                # 域名，多个以空格分开
                server_name  alpha-cloud.ttyuyin.com;

		proxy_send_timeout      3600s;
                proxy_read_timeout      3600s;
		proxy_connect_timeout   3600s;

                # resolver
                resolver kube-dns.kube-system.svc.cluster.local valid=60s;
                resolver_timeout 3s;

                # ssl证书地址
                ssl_certificate     /etc/nginx/ssl/tls.crt;  # pem文件的路径
                ssl_certificate_key  /etc/nginx/ssl/tls.key; # key文件的路径

                # ssl验证相关配置
                ssl_session_timeout  5m;    #缓存有效期
                ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;    #加密算法
                ssl_protocols TLSv1 TLSv1.1 TLSv1.2;    #安全链接可选的加密协议
                ssl_prefer_server_ciphers on;   #使用服务器端的首选算法

                location /api {
                        client_max_body_size 4096m;
                        proxy_pass http://alpha-tt-cloud-continuousdeployment:8007;
                        proxy_redirect off;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header REMOTE-HOST $remote_addr;
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                }

                location / {
                        root   /usr/share/nginx/html/;
                        index  index.html;
                        try_files $uri $uri/ /index.html;
                }

                error_page   500 502 503 504  /50x.html;
                location = /50x.html {
                        root   html;
                }
        }
}
