<template>
  <div class="dialog-pod-list">
    <Modal
      ref="podListModal"
      v-model="visible"
      width="90"
      title="容器组"
      footer-hide
      @on-cancel="
        () => {
          showDaemonSet = false
        }
      "
    >
      <div>
        <Alert show-icon>CPU 和 Memory 的百分比是 Pod 实际使用资源与 Node 可分配资源；</Alert>
        <Alert show-icon type="warning"
          >列表排序按优先级由低到高进行排序， 驱逐时应从低优先级开始
          <span style="color: #ed4014">驱逐（删除）</span>；</Alert
        >
      </div>
      <div class="refresh-btn">
        <Input
          search
          clearable
          v-model="searchValue"
          placeholder="搜索 Name"
          @on-search="
            () => {
              refreshTable(false)
            }
          "
          @on-clear="
            () => {
              refreshTable(false)
            }
          "
          style="width: 300px"
        ></Input>
        <Button
          size="small"
          type="primary"
          icon="md-refresh"
          @click="
            () => {
              refreshTable(false)
            }
          "
          style="margin-left: 16px"
          >刷新</Button
        >
        <Checkbox @on-change="refreshTable" style="margin-left: 16px; float: right" v-model="showDaemonSet"
          >展示 DaemonSet 关联 Pod</Checkbox
        >
      </div>

      <Table size="small" :columns="columns" :data="data" :loading="loading" height="500">
        <template #status="{ row }">
          <Tag :color="getColor(row.status)">{{ row.status }}</Tag>
        </template>
      </Table>
    </Modal>

    <Drawer
      ref="yamlDrawer"
      title="查看 YAML"
      closable
      width="50"
      v-model="openYaml"
      class="yaml-drawer"
      :mask-closable="true"
    >
      <yaml style="height: 90vh" v-model="currentYAML"></yaml>
      <Spin fix v-if="yamlLoading"></Spin>
    </Drawer>
  </div>
</template>

<script>
import { Yaml } from '@/components'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { relativeTime } from '@/libs/tools'
import { color } from '@/libs/consts'
import { useGet } from '@/libs/service.request'
import { ApiNodePodsDelete, ApiNodePodMetrics } from '@/api/k8s/node'

import { ApiResourceYamlGet } from '@/api/k8s/resource'

import Config from '@/config'

const podPriority = {
  'prod-s': 2000,
  'prod-p1': 1900,
  'prod-p2': 1800,
  'prod-p3': 1700,
  'prod-p4': 1600,
  'offline-p1': 1500,
  'offline-p2': 1400,
  '-': 0
}
export default {
  name: 'dialog-pod-list',
  props: ['value', 'currentNode', 'currentClusterId'],
  components: {
    Yaml
  },
  data() {
    return {
      showDaemonSet: false,
      podListModal: null,
      yamlDrawer: null,

      columns: [
        {
          title: 'Name',
          key: 'name',
          minWidth: 260,
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'Namespace',
          key: 'namespace',
          tooltip: true,
          tooltipTheme: 'light',
          minWidth: 140,
          filters: [],
          filterMethod(value, row) {
            if (value === row.namespace) {
              console.log(value, row.namespace)
              return row
            }
          }
        },
        {
          title: 'Status',
          key: 'status',
          slot: 'status',
          width: 120,
          align: 'center'
        },
        {
          title: 'Ready',
          key: 'ready',
          width: 80,
          align: 'center',
          tooltip: true
        },
        {
          title: '优先级',
          key: 'pc',
          width: 110,
          align: 'center',
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'CPU (core)',
          minWidth: 160,

          render: (h, params) => {
            if (params.row.cpu === undefined) {
              return h('Icon', {
                class: {
                  'demo-spin-icon-load': true
                },
                props: {
                  type: 'ios-loading'
                }
              })
            }
            let color = this.calculateColor(params.row.cpu.percent)
            return h('div', {}, [
              h('span', {}, `${params.row.cpu.usage} / ${params.row.cpu.total}`),
              h('Progress', {
                props: {
                  percent: params.row.cpu.percent,
                  strokeColor: [color, color]
                }
              })
            ])
          }
        },
        {
          title: 'Mem (GiB)',
          minWidth: 160,

          render: (h, params) => {
            if (params.row.mem === undefined) {
              return h('Icon', {
                class: {
                  'demo-spin-icon-load': true
                },
                props: {
                  type: 'ios-loading'
                }
              })
            }
            let color = this.calculateColor(params.row.mem.percent)
            return h('div', {}, [
              h('span', {}, `${params.row.mem.usage} / ${params.row.mem.total}`),
              h('Progress', {
                props: {
                  percent: params.row.mem.percent,
                  strokeColor: [color, color]
                }
              })
            ])
          }
        },
        {
          title: 'IP',
          key: 'ip',
          width: 120,
          align: 'center',
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'Restarts',
          key: 'restarts',
          align: 'center',
          width: 100,
          tooltip: true,
          tooltipTheme: 'light'
        },
        {
          title: 'Age',
          key: 'age',
          width: 130,
          align: 'center'
        },

        {
          title: '操作',
          key: 'ops',
          align: 'center',
          width: 140,

          render: (h, params) => {
            return h('span', {}, [
              h(
                'a',
                {
                  style: {
                    color: color.primary,
                    cursor: 'pointer',
                    fontSize: '12px'
                  },
                  on: {
                    click: async () => {
                      // 获取pod的yaml
                      this.openYaml = true
                      if (this.yamlDrawer) {
                        this.yamlDrawer.modalIndex = 999
                        this.yamlDrawer.handleGetModalIndex = () => {
                          return this.yamlDrawer.modalIndex - 1000
                        }
                      }
                      this.yamlLoading = true
                      this.currentYAML = ''
                      try {
                        const res = await ApiResourceYamlGet({
                          resource: 'pods',
                          version: 'v1',
                          cluster_id: this.currentClusterId,
                          namespace: params.row.namespace,
                          resource_name: params.row.name,
                          is_edit: false
                        })
                        this.currentYAML = res.data.data.data
                      } catch (error) {
                        noticeError(this, `获取Node YAML失败, ${errorMessage(error)}`)
                      }
                      this.yamlLoading = false
                    }
                  }
                },
                'YAML'
              ),
              h(
                'a',
                {
                  style: {
                    color: color.error,
                    cursor: 'pointer',
                    fontSize: '12px',
                    marginLeft: '16px'
                  },
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: 'Tips',
                        content: '<p>确认驱逐操作 ? 确认后耐心等待</p>',
                        loading: true,
                        onOk: () => {
                          ApiNodePodsDelete({
                            clusterId: this.currentClusterId,
                            node: this.currentNode,
                            pod: params.row.name
                          })
                            .then((res) => {
                              noticeSucceed(this, '驱逐成功')
                              this.refreshTable()
                            })
                            .catch((err) => {
                              noticeError(this, errorMessage(err))
                            })
                            .finally(() => {
                              this.$Modal.remove()
                            })
                        }
                      })
                    }
                  }
                },
                '驱逐'
              )
            ])
          }
        }
      ],
      tableData: [],
      openYaml: false,
      currentYAML: '',
      yamlLoading: false,
      searchValue: '',
      loading: false,
      data: [],
      nodeTotalCpu: 0,
      nodeTotalMem: 0,
      hashPodMetrics: {}
    }
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },

  watch: {
    visible: async function () {
      if (this.visible) {
        if (this.podListModal) {
          this.podListModal.modalIndex = 990
          this.podListModal.handleGetModalIndex = () => {
            return this.podListModal.modalIndex - 1000
          }
        }
        this.data = []
        await this.getPodList(this.currentNode, '', false, true)
      }
    }
  },
  methods: {
    // 处理table所需的数据
    formatListData(data) {
      let namespaceSetList = []
      let namespaceFilterList = []
      const list = data.map((item) => {
        // 容器个数
        let containerCount = 0
        let readyCount = 0
        let restarts = 0
        if (item.status.containerStatuses !== undefined) {
          containerCount = item.status.containerStatuses.length
          // 计算ready状态的容器个数
          readyCount = item.status.containerStatuses.reduce((pre, cur) => {
            if (cur.ready === true) return pre + 1
            return pre
          }, 0)

          restarts = item.status.containerStatuses.reduce((pre, cur) => {
            return pre + cur.restartCount
          }, 0)
        }

        if (namespaceSetList.indexOf(item.metadata.namespace) === -1) {
          namespaceFilterList.push({
            label: item.metadata.namespace,
            value: item.metadata.namespace
          })
          namespaceSetList.push(item.metadata.namespace)
        }
        let cpu
        let mem
        if (item.cpuUsage !== undefined) {
          cpu = {
            usage: (item.cpuUsage / 1000).toFixed(2),
            total: (this.nodeTotalCpu / 1000).toFixed(0),
            percent: Number.parseFloat(((item.cpuUsage / this.nodeTotalCpu) * 100).toFixed(2))
          }
        }
        if (item.memUsage !== undefined) {
          mem = {
            usage: (item.memUsage / 1024 / 1024 / 1024 / 1024).toFixed(2),
            total: (this.nodeTotalMem / 1024 / 1024 / 1024 / 1024).toFixed(2),
            percent: Number.parseFloat(((item.memUsage / this.nodeTotalMem) * 100).toFixed(2))
          }
        }
        return {
          name: item.metadata.name,
          ready: `${readyCount}/${containerCount}`,
          status: item.status.phase,
          age: relativeTime(item.status.startTime),
          restarts,
          ip: item.status.podIP,
          namespace: item.metadata.namespace,
          pc: item.spec.priorityClassName === undefined ? '-' : item.spec.priorityClassName,
          cpu: cpu,
          mem: mem
        }
      })

      console.log(`node total cpu: ${this.nodeTotalCpu}`)
      console.log(`node total mem: ${this.nodeTotalMem}`)
      list
        .sort(function (a, b) {
          let ap = podPriority[a.pc] === undefined ? 100000 : podPriority[a.pc]
          let bp = podPriority[b.pc] === undefined ? 100000 : podPriority[b.pc]
          return ap - bp
        })
        .sort()

      this.columns[1].filters = namespaceFilterList

      return list
    },
    getColor(status) {
      switch (status && status.toLowerCase()) {
        case 'running':
          return color['success']
        case 'pending':
          return color['info']
        default:
          return color['error']
      }
    },
    calculateColor(percent) {
      if (percent >= 90) {
        return '#ed4014'
      }
      if (percent >= 60) {
        return '#ff9900'
      }
      return '#19be6b'
    },
    refreshTable(refreshMetrics) {
      if (refreshMetrics === undefined) {
        refreshMetrics = true
      }
      this.getPodList(this.currentNode, this.searchValue, this.showDaemonSet, refreshMetrics)
    },
    // 获取podList数据
    async getPodList(node, searchVal, showDaemonSet, refreshMetrics) {
      this.loading = true
      const res = await useGet(
        `${Config.Api.Base}${Config.Api.GetPodList}?cluster_id=${this.currentClusterId}&node=${node}`
      )

      // 是否展示 DemonSet 关联 Pod
      if (!showDaemonSet) {
        res.data.pods = res.data.pods.filter((item) => {
          if (item.metadata.ownerReferences && item.metadata.ownerReferences[0].kind !== 'DaemonSet') {
            return item
          }
        })
      }

      let pods =
        searchVal !== ''
          ? res.data.pods.filter((item) => {
              if (item.metadata.name.indexOf(searchVal) !== -1) {
                return item
              }
            })
          : res.data.pods

      this.nodeTotalCpu = res.data.nodeTotalCpu
      this.nodeTotalMem = res.data.nodeTotalMem

      // 异步获取metrics
      if (refreshMetrics) {
        const res = await ApiNodePodMetrics(this.currentClusterId, this.currentNode)

        res.data.data.podMetrics.forEach((item) => {
          this.hashPodMetrics[item.podName] = item.metrics
        })
      }

      pods.forEach((item, index, arr) => {
        if (this.hashPodMetrics[item.metadata.name] !== undefined) {
          arr[index].cpuUsage = this.hashPodMetrics[item.metadata.name].cpuUsage
          arr[index].memUsage = this.hashPodMetrics[item.metadata.name].memUsage
        }
      })
      this.data = this.formatListData(pods)
      this.loading = false
    }
  }
}
</script>

<style lang="less" scoped>
.yaml-drawer {
  :deep(.ivu-drawer-mask) {
    z-index: 2000 !important;
  }
  :deep(.ivu-drawer-wrap) {
    z-index: 2000 !important;
  }
}

.refresh-btn {
  margin-bottom: 16px;
}
</style>
