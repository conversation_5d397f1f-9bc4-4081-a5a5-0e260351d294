import axios from '@/libs/api.request'

export const ApiRsStatisticsCount = () => {
  return axios.request({
    url: '/api/v1/cluster/statistics/list',
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiRSTendencyCount = () => {
  return axios.request({
    url: '/api/v1/cluster/statistics/tendency-count',
    method: 'get',
    data: {},
    params: {}
  })
}


export const ApiKubectlResourceOpChart = () => {
  return axios.request({
    url: '/api/v1/internal/proxy/graph/resource/get',
    method: 'get'
  })
}

export const ApiKubectlHttpMethodOpChart = () => {
  return axios.request({
    url: '/api/v1/internal/proxy/graph/http-method/get',
    method: 'get'
  })
}
