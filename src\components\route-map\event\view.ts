export const onZoomChange = (graph, currentLevel) => {
  const currentZoom = graph.getZoom()
  let toLevel = currentLevel
  const briefZoomThreshold = Math.max(graph.getZoom(), 0.75)
  if (currentZoom < briefZoomThreshold) {
    toLevel = 0
  } else {
    toLevel = 1
  }
  if (toLevel !== currentLevel) {
    graph.getNodes().forEach((node) => {
      graph.updateItem(node, {
        level: toLevel
      })
    })
  }
  return toLevel
}
