import { ExtractPropTypes, PropType } from 'vue'

export function multiInputProps() {
  return {
    name: String, // 唯一标识（与 data表单数据对象 中的映射一致）
    data: {
      type: Object,
      default: function () {
        return {}
      }
    }, // 表单数据对象
    addLabel: String, // 添加的提示
    onDelete: Function,
    onInit: Function,
    onAdd: Function,
    addButtonPosition: { type: String as PropType<'top' | 'bottom'>, default: 'bottom' }, // 添加按钮位置
    hiddenDefaultDeleteBtn: Boolean, // 是否隐藏默认删除按钮
    max: Number // 多层输入内容的最大数量
  }
}
export type MultiInputProps = Partial<ExtractPropTypes<ReturnType<typeof multiInputProps>>>
