import { computed, onMounted, ref, watch } from 'vue'
import { usePagination } from 'vue-request'
import { TableProps, TableParams } from './tableProps'
import { isEmpty } from 'lodash-es'

const DEFAULT_PAGE_SIZE = 10

export default function useBasicTableService(props: TableProps) {
  const requestReady = ref(!!props.request)
  const total = ref<number>(0)
  const searchKey = ref<string>()
  const searchValue = ref<string>()
  const inputRef = ref<any>()
  const tableRef = ref()
  let forbiddenCurrentChange = false
  const searchObj = computed(() =>
    props.search ? props.search?.filter((i) => i.value === searchKey.value)?.[0] : undefined
  )
  const tableId = `ProTable_${Math.random()}`

  const defaultParams: TableParams = [
    {
      pageSize: (props.pagination && props.pagination?.defaultPageSize) || DEFAULT_PAGE_SIZE,
      pageIndex: 1
    }
  ]

  const {
    data: dataSource,
    run,
    loading,
    current: pageIndex,
    pageSize,
    params,
    changeCurrent,
    changePagination: onPaginationChange
  } = usePagination(
    (params) => {
      params.searchKey = searchKey.value
      params.searchValue = searchValue.value
      return props.request(params)
    },
    {
      formatResult: (res) => {
        total.value = res.total ?? 0
        return res.data
      },
      ready: requestReady,
      manual: props.manualRequest,
      pagination: {
        currentKey: 'pageIndex'
      },
      defaultParams
    }
  )

  const actionType = computed(() => ({
    reload: (isJumpToFirst = true) => {
      const newParams = !isEmpty(params.value) ? params.value : defaultParams
      if (isJumpToFirst) {
        newParams[0].pageIndex = 1
      }
      run(...newParams)
    },
    reset: () => {
      console.log('TBD')
    }
  }))

  const pagination = computed(() => {
    return {
      total: total.value,
      pageIndex: pageIndex.value,
      current: pageIndex.value,
      pageSize: pageSize.value
    }
  })
  const onCurrentChange = (current: number) => {
    if (!forbiddenCurrentChange) {
      changeCurrent(current)
    }
    forbiddenCurrentChange = false
  }
  const onPageSizeChange = (pageSize: number) => {
    if (pagination.value.current !== 1) {
      forbiddenCurrentChange = true
    }
    onPaginationChange(1, pageSize)
  }

  watch(
    actionType,
    () => {
      props.actionRef?.tableRef && (props.actionRef.tableRef.value = actionType.value)
    },
    { immediate: true }
  )

  onMounted(() => {
    if (props.search && props.search.length > 0) {
      // 存在初始化的数据，取其第一个值渲染，否则默认search第一个值
      const initSearchItem = props.search.filter((i) => i.initData)?.[0] ?? props.search?.[0]

      searchKey.value = initSearchItem?.value
      searchValue.value = initSearchItem?.initData
    }
    inputRef.value?.focus({
      cursor: 'start'
    })
  })

  return {
    pagination,
    dataSource,
    loading,
    tableId,
    onCurrentChange,
    onPageSizeChange,
    searchKey,
    searchValue,
    searchObj,
    actionType,
    inputRef
  }
}
