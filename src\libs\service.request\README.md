---
title: 请求
order: 2
group:
  path: /
nav:
  title: 服务包
  order: 2
  path: /service
---

# 请求

request 基于 axios 提供了一套统一的网络请求和错误处理方案。

## 何时使用

对接接口时：

- 懒得为本次请求套接`try catch`
- 不想频繁引入`loading`组件并控制`loading.show()` `loading.hide()`
- 遇到异常时懒得每次都写`console.log("请求异常" + err)` 或 `message.warn("请求异常" + err)`
- 接口报错时难以获取状态码信息，需要打开控制台刷新定位 debug
- 对接时遇到后端菜鸟，接口报错不返回错误信息
- 遇到服务器崩溃，抛出的源异常用户看不懂，对系统意见大
- 没时间搭建前端错误日志机制

此时可以使用 `framework` 中包装的服务来减少开发成本，把时间专注在业务逻辑上

## 代码演示

### 基础使用

使用 `useGet` | `usePost` | `usePut` | `useDelete` 等函数请求接口

```tsx | pure
import { usePost } from 'framework';

const getData = async () => {
  let res = await usePost('/api/user', { name: 1 });

  if (res?
  .success) {
    // 请求成功的业务逻辑
  }
};
```

### 兼容 antd 表格

使用 `useProTablePromiseByPost` | `useProTablePromiseByGet` 等函数请求接口

```tsx | pure
import { useProTablePromiseByPost, ProTable } from 'framework';

type User = {
  name: string;
  type: number;
};

export const Index = () => {
  return (
    <ProTable
      columns={[{ title: '名称', dataIndex: 'name' }]}
      request={async (params) => {
        let res = await useProTablePromiseByPost<User[]>({
          url: '/api/user',
          params: {
            name: 1,
          },
        });
        return res;
      }}
    />
  );
};
export default Index;
```

### 对接`SQL Sugar`接口（后端通用接口）

使用 `formatCondition`、`EnumConditionalType` 等配套函数请求接口

```tsx | pure
import { usePost, formatCondition, EnumConditionalType } from 'framework';

type User = {
  name: string;
  type: number;
  code: string;
  categoryId: string;
};

const promiseFunction = async (params) => {
  let Conditions = [];
  Conditions.push(
    formatCondition('code', params.keyword, EnumConditionalType.Like),
    formatCondition('name', params.name, EnumConditionalType.Like),
    formatCondition('type', params.type, EnumConditionalType.Equal),
    formatCondition('categoryId', params.categoryId, EnumConditionalType.In),
  );
  let res = await usePost<User[]>({
    url: '/api/user',
    params: {
      PageIndex: params.current,
      PageSize: params.pageSize,
      OrderField: 'Created',
      OrderType: 'desc',
      Conditions: Conditions.filter((item) => item),
    },
  });
  return res;
};
```

## 请求拦截器

因为`framework` 的其他模块也会需要在请求中绑定请求头数据（比如多语言模块绑定`Accept-Language`数据），为了**保存请求头中的数据**，业务代码在额外定制请求头时需使用框架提供的拦截器，否则会影响框架中的其他服务包运行。

```tsx | pure
import { axiosInterceptorRequest } from 'framework';

/** 更新系统中的token绑定 */
const rebindToken = (token) => {
  if (prevToken !== token) {
    // 卸载上次拦截
    axiosInterceptorRequest({
      headers: { Authorization: token },
    });
  }
};
```

## 配置

### 全局配置

在 src/app.ts 中你可以配置一些运行时的配置项来实现系统的自定义需求，示例配置如下：

```tsx | pure
import { RequestConfigProvide } from 'framework';

RequestConfigProvide({
  timeout: 3000,
  skipLoading: true,
  errorShowType: 'WARN_MESSAGE',
});
```

### 独立配置

单个请求的 `config` 处也可以单独配置特定参数且覆盖全局配置，来实现自定义需求：

```tsx | pure
import { useGet } from 'framework';

const getData = async () => {
  let res = await useGet('/api/user?name=1', {
    timeout: 6000,
    skipLoading: true,
    errorShowType: 'SILENT',
  });

  if (res.success) {
    // 请求成功的业务逻辑
  }
};
```

### timeout

定义了请求的超时时间，默认为 `3000ms` 。

### skipLoading

定义了本次请求的是否跳过全屏 loading，默认为 `false` 。

### skipErrorHandler

定义本次请求的是否跳过默认的错误处理，默认为 `false` 。

### errorShowType

定义了请求错误时提示的 showType 的类型，陈列如下：

```tsx | pure
export enum ErrorShowType {
  SILENT = 'SILENT', // 不提示错误
  WARN_MESSAGE = 'WARN_MESSAGE', // 警告信息提示,message warn弹出错误信息
  ERROR_MESSAGE = 'ERROR_MESSAGE', // 错误信息提示: message error ,记录日志，弹出错误信息及日志ID
  NOTIFICATION = 'NOTIFICATION', // 通知提示 notification 记录日志，弹出错误信息、日志信息及反馈服务器按钮
}
```
