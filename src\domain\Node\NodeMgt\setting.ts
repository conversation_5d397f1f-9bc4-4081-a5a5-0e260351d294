import { color } from '@/libs/consts'
import { Progress, Tag } from 'view-design'

const calculateColor = (percent) => {
  if (percent >= 90) {
    return '#ed4014'
  }
  if (percent >= 60) {
    return '#ff9900'
  }
  return '#19be6b'
}

export const TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name',
    fixed: 'left',
    width: 300
  },
  {
    title: 'Status',
    key: 'phase',
    width: 100,
    render: (h, params) => {
      return h(
        Tag,
        {
          props: {
            color: params.row.phase === 1 ? 'success' : 'default'
          }
        },
        params.row.phase === 1 ? 'Ready' : 'NotReady'
      )
    }
  },
  {
    title: '调度',
    key: 'is_schedulable',
    align: 'center',
    width: 80,
    render: (h, params) => {
      const text = params.row.is_schedulable === true ? '允许' : '禁止'
      const c = params.row.is_schedulable === true ? color.success : color.error
      return h(
        'b',
        {
          style: {
            color: c
          }
        },
        text
      )
    }
  },

  {
    title: 'INTERNAL-IP',
    key: 'internal_ip',
    tooltip: true,
    width: 140
  },
  {
    title: 'Age',
    key: 'created_at',
    width: 100
  },
  {
    title: 'CPU (core)',
    minWidth: 200,
    render: (h, params) => {
      if (params.row.cpu !== null && params.row.cpu !== undefined) {
        const color = calculateColor(params.row.cpu.percent)

        return h(
          'div',
          {
            style: {
              padding: '8px'
            }
          },
          [
            h('span', {}, `${params.row.cpu.usage} / ${params.row.cpu.total}`),
            h(Progress, {
              props: {
                percent: params.row.cpu.percent,
                strokeColor: [color, color],
                strokeWidth: 8
              }
            })
          ]
        )
      }
      return h('div', {})
    }
  },
  {
    title: 'Mem (GiB)',
    minWidth: 200,

    render: (h, params) => {
      if (params.row.mem !== null && params.row.mem !== undefined) {
        const color = calculateColor(params.row.mem.percent)
        return h(
          'div',
          {
            style: {
              padding: '8px'
            }
          },
          [
            h('span', {}, `${params.row.mem.usage} / ${params.row.mem.total}`),
            h(Progress, {
              props: {
                percent: params.row.mem.percent,
                strokeColor: [color, color],
                strokeWidth: 8
              }
            })
          ]
        )
      }
      return h('div', {})
    }
  },
  {
    title: 'Pods',
    minWidth: 200,

    render: (h, params) => {
      if (params.row.pods !== null && params.row.pods !== undefined) {
        const color = calculateColor(params.row.pods.percent)
        return h(
          'div',
          {
            style: {
              padding: '8px'
            }
          },
          [
            h('span', {}, `${params.row.pods.usage} / ${params.row.pods.total}`),
            h(Progress, {
              props: {
                percent: parseFloat(params.row.pods.percent),
                strokeColor: [color, color],
                strokeWidth: 8
              }
            })
          ]
        )
      }
      return h('div', {})
    }
  },
  {
    title: '操作',
    key: 'ops',
    slot: 'ops',
    align: 'center',
    fixed: 'right',
    width: 140
  }
]
