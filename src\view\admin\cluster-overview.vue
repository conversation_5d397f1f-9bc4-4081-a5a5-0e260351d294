<template>
  <Container>
    <!-- <Alert  show-icon>
      <b>牵星容器云平台</b>
      主要用于管理我司所有Kubernetes集群。通过该平台，您可以查看容器运行的状态、操作部分K8S资源以及配置服务网格。这使得您在一个统一的界面中管理和监控Kubernetes资源。
    </Alert>
    <Alert  show-icon>
      在右上角 <span style="color: #2a7cc3">集群切换</span> 按钮切换到具体的集群及命名空间下进行资源操作 [
      <a
        target="_blank"
        href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnR2o35nWutWWZmcArIMX7dr"
        style="color: #2a7cc3; font-size: 12px"
        >操作手册</a
      >
      ]
    </Alert> -->

    <div>
      <Row type="flex" :gutter="15">
        <Col span="6">
          <Card class="custom-align-center" style="height: 160px; padding-top: 16px">
            <Tooltip content="可接入集群" placement="top" style="text-align: center">
              <Avatar
                icon="ios-color-filter"
                size="default"
                style="color: rgb(64, 169, 255); background-color: rgb(230, 247, 255)"
              />
              <count-to :end="statisticsCount.cluster" class="count-text" style="margin-top: 8px"></count-to>
              <div style="font-size: 12px; color: #808695; margin-top: 8px">Cluster 总量</div>
            </Tooltip>
          </Card>
        </Col>
        <Col span="6">
          <Card class="custom-align-center" style="height: 160px; padding-top: 16px">
            <Tooltip content="节点" placement="top" style="text-align: center">
              <Avatar
                icon="ios-pin"
                size="default"
                style="color: rgb(255, 169, 64); background-color: rgb(255, 247, 230)"
              />
              <count-to :end="statisticsCount.nodes" class="count-text" style="margin-top: 8px"></count-to>
              <div style="font-size: 12px; color: #808695; margin-top: 8px">Node 总量</div>
            </Tooltip>
          </Card>
        </Col>
        <Col span="6">
          <Card class="custom-align-center" style="height: 160px; padding-top: 16px">
            <Tooltip content="Pod" placement="top" style="text-align: center">
              <Avatar
                icon="ios-keypad"
                size="default"
                style="color: rgb(146, 84, 222); background-color: rgb(249, 240, 255)"
              />
              <count-to :end="statisticsCount.pod" class="count-text" style="margin-top: 8px"></count-to>
              <div style="font-size: 12px; color: #808695; margin-top: 8px">Pod 总量</div>
            </Tooltip>
          </Card>
        </Col>
        <Col span="6">
          <Card class="custom-align-center" style="height: 160px; padding-top: 16px">
            <Tooltip content="Service" placement="top" style="text-align: center">
              <Avatar
                icon="md-git-network"
                size="default"
                style="color: rgb(64, 169, 255); background-color: rgb(230, 247, 255)"
              />
              <count-to :end="statisticsCount.svc" class="count-text" style="margin-top: 8px"></count-to>
              <div style="font-size: 12px; color: #808695; margin-top: 8px">Service 总量</div>
            </Tooltip>
          </Card>
        </Col>
      </Row>
    </div>
    <div style="margin-top: 15px">
      <Row :gutter="15">
        <Col span="18">
          <Row style="">
            <Card style="height: 370px">
              <!--              <p slot="title">-->
              <!--                <Avatar icon="ios-pulse" size="small"-->
              <!--                        style="color: rgb(24, 144, 255); background-color: rgb(230, 247, 255);"></Avatar>&nbsp;-->
              <!--                <span style="font-size: 14px; font-weight: normal">最近一年的资源变化</span>-->
              <!--              </p>-->
              <div ref="chartDom" style="height: 300px"></div>
            </Card>
          </Row>
          <Row style="margin-top: 15px">
            <Card style="height: 350px">
              <div ref="httpMethodOpChartDom" style="height: 300px"></div>
            </Card>
          </Row>
          <Row style="margin-top: 15px; height: 350px">
            <Card>
              <div ref="resourceOpChartDom" style="height: 300px"></div>
            </Card>
          </Row>
        </Col>
        <Col span="6">
          <Card>
            <Spin fix v-if="loading.rsAmount">
              <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            </Spin>
            <Row type="flex">
              <Col :span="12" class="custom-align-center">
                <i-circle
                  :size="60"
                  :percent="clusterCPUPercent"
                  :stroke-color="clusterCpuColor"
                  dashboard
                  :trail-width="8"
                >
                  <span style="font-size: 12px">{{ clusterCPUPercent }}%</span>
                </i-circle>
              </Col>
              <Col :span="12">
                <div style="font-size: 14px; margin-top: 8px">
                  CPU core <Tag color="primary" style="float: right">Total</Tag>
                </div>
                <div style="font-size: 12px; font-weight: bold; margin-top: 16px">
                  <span style="font-size: 16px">{{ clusterCPUPCurrent }}</span> / {{ clusterCPUPTotal }}
                </div>
              </Col>
            </Row>
          </Card>
          <Card style="margin-top: 15px">
            <Spin fix v-if="loading.rsAmount">
              <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            </Spin>
            <Row type="flex">
              <Col :span="12" class="custom-align-center">
                <i-circle
                  :size="60"
                  :trail-width="8"
                  :percent="clusterMemPercent"
                  dashboard
                  :stroke-color="clusterMemColor"
                >
                  <span style="font-size: 12px">{{ clusterMemPercent }}%</span>
                </i-circle>
              </Col>
              <Col :span="12">
                <div style="font-size: 14px; margin-top: 8px">
                  Mem <span style="font-size: 12px">GiB</span> <Tag color="primary" style="float: right">Total</Tag>
                </div>
                <div style="font-size: 12px; font-weight: bold; margin-top: 16px">
                  <span style="font-size: 15px">{{ clusterMemCurrent }}</span> / {{ clusterMemTotal }}
                </div>
              </Col>
            </Row>
          </Card>
          <Card style="margin-top: 15px">
            <Spin fix v-if="loading.rsAmount">
              <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            </Spin>
            <Row type="flex">
              <Col :span="12" class="custom-align-center">
                <template v-if="clusterPodPercent > 100">
                  <i-circle :size="60" :trail-width="8" :percent="100" :stroke-color="clusterPodColor" dashboard>
                    <span style="font-size: 12px">{{ clusterPodPercent }}%</span>
                  </i-circle>
                </template>
                <template v-else>
                  <i-circle
                    :size="60"
                    :trail-width="8"
                    :percent="clusterPodPercent"
                    :stroke-color="clusterPodColor"
                    dashboard
                  >
                    <span style="font-size: 12px">{{ clusterPodPercent }}%</span>
                  </i-circle>
                </template>
              </Col>
              <Col :span="12">
                <div style="font-size: 14px; margin-top: 8px">
                  <span>Pods</span> <Tag color="primary" style="float: right">Total</Tag>
                </div>
                <div style="font-size: 12px; font-weight: bold; margin-top: 16px">
                  <span style="font-size: 16px">{{ clusterPodCurrent }}</span> / {{ clusterPodTotal }}
                </div>
              </Col>
            </Row>
          </Card>
          <Card style="height: 235px; margin-top: 15px">
            <p slot="title">
              <Avatar
                icon="ios-people"
                size="small"
                shape="square"
                style="color: rgb(255, 169, 64); background-color: rgb(255, 247, 230)"
              ></Avatar
              >&nbsp;
              <span style="font-size: 14px; font-weight: normal">用户数量</span>
            </p>
            <div class="custom-align-center" style="display: flex">
              <count-to :end="userCount" class="count-text-1" style="margin-top: 8px; font-size: 60px"></count-to>
            </div>
            <div class="custom-align-center" style="font-size: 12px; color: #808695; margin-top: 8px">USER 总量</div>
          </Card>
          <Card style="height: 235px; margin-top: 16px">
            <p slot="title">
              <Avatar
                icon="ios-cafe"
                size="small"
                shape="square"
                style="color: rgb(235, 47, 150); background-color: rgb(255, 240, 246)"
              ></Avatar
              >&nbsp;
              <span style="font-size: 14px; font-weight: normal">用户请求数</span>
            </p>
            <div class="custom-align-center" style="display: flex">
              <count-to
                :simplify="true"
                :end="OpCount"
                class="count-text-1"
                unit-class="unit-text-1 "
                style="margin-top: 8px"
              ></count-to>
            </div>
            <div class="custom-align-center" style="font-size: 12px; color: #808695; margin-top: 8px">最近一周总量</div>
          </Card>
        </Col>
      </Row>
    </div>
  </Container>
</template>

<script>
import { Container } from '@/components'
import CountTo from '_c/count-to'
import echarts from 'echarts'
import { on, off } from '@/libs/tools'
import {
  ApiRsStatisticsCount,
  ApiRSTendencyCount,
  ApiKubectlHttpMethodOpChart,
  ApiKubectlResourceOpChart
} from '@/api/brief'
import { errorMessage, noticeError } from '@/libs/util'
import { ApiUserCount, ApiUserOpCount } from '@/api/user'
import { ApiOpenResourceMetricsClustersAmount } from '@/api/k8s/openapi'
import { getLastRoute } from '@/router'

export default {
  components: { Container, CountTo },
  name: 'home',
  //   inject: ['VersionNoticeServiceProvide'],
  computed: {
    clusterCpuColor() {
      let color = '#19be6b'

      if (this.clusterCPUPercent >= 60) {
        color = '#ff9900'
      }
      if (this.clusterCPUPercent >= 90) {
        color = '#ed4014'
      }

      return color
    },
    clusterMemColor() {
      let color = '#19be6b'

      if (this.clusterMemPercent >= 60) {
        color = '#ff9900'
      }
      if (this.clusterMemPercent >= 90) {
        color = '#ed4014'
      }

      return color
    },
    clusterPodColor() {
      let color = '#19be6b'

      if (this.clusterPodPercent >= 60) {
        color = '#ff9900'
      }
      if (this.clusterPodPercent >= 90) {
        color = '#ed4014'
      }

      return color
    }
  },
  data: () => {
    return {
      mTitle: '概览',
      statisticsCount: {
        cluster: 33,
        nodes: 420,
        pod: 2200,
        svc: 520
      },
      userCount: 0,
      OpCount: 0,
      myChart: null,
      xArray: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      yMap: {
        cluster: [120, 132, 101, 134, 90, 230, 210],
        node: [220, 182, 191, 234, 290, 330, 310],
        pod: [150, 232, 201, 154, 190, 330, 410],
        service: [320, 332, 301, 334, 390, 330, 320]
      },
      clusterCPUPercent: 0,
      clusterCPUPCurrent: 0,
      clusterCPUPTotal: 0,
      clusterMemPercent: 0,
      clusterMemCurrent: 0,
      clusterMemTotal: 0,
      clusterPodPercent: 0,
      clusterPodCurrent: 0,
      clusterPodTotal: 0,
      loading: {
        rsAmount: false
      }
    }
  },
  methods: {
    resize() {
      return this.myChart.resize()
    },
    getStatisticsCount() {
      ApiRsStatisticsCount()
        .then((res) => {
          this.statisticsCount = res.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    getUserCount() {
      ApiUserCount()
        .then((res) => {
          this.userCount = res.data.data.count
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    getOpCount() {
      ApiUserOpCount()
        .then((res) => {
          this.OpCount = res.data.data.count
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    fetchClusterResourceAmount() {
      this.loading.rsAmount = true
      ApiOpenResourceMetricsClustersAmount()
        .then((res) => {
          let data = res.data.data.data
          if (data !== null) {
            this.clusterCPUPCurrent = (data.cpu.usage / 1000).toFixed(1)
            this.clusterCPUPTotal = (data.cpu.total / 1000).toFixed(0)
            this.clusterCPUPercent = Number(((data.cpu.usage / data.cpu.total) * 100).toFixed(1))

            this.clusterMemCurrent = (data.mem.usage / 1024 / 1024 / 1024).toFixed(1)
            this.clusterMemTotal = (data.mem.total / 1024 / 1024 / 1024).toFixed(1)
            this.clusterMemPercent = Number(((data.mem.usage / data.mem.total) * 100).toFixed(1))

            this.clusterPodCurrent = data.pods.usage
            this.clusterPodTotal = data.pods.total
            this.clusterPodPercent = Number(((data.pods.usage / data.pods.total) * 100).toFixed(1))
          }
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
        .finally(() => {
          this.loading.rsAmount = false
        })
    },
    async kubectlResourceOpChart() {
      var yAxis = []
      var series = []
      await ApiKubectlResourceOpChart()
        .then((res) => {
          let data = res.data.data.data
          console.log(data)
          yAxis = data.y_axis.slice(0, 10)
          series = data.x_axis[0].data.slice(0, 10)
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      let option = {
        title: {
          text: 'ApiServerProxy 资源操作统计 [TOP 10]',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          right: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: yAxis
        },
        series: [
          {
            name: 'count',
            type: 'bar',
            data: series
          }
        ]
      }
      this.$nextTick(() => {
        let myChart = echarts.init(this.$refs.resourceOpChartDom)
        myChart.setOption(option)
        on(window, 'resize', function () {
          myChart.resize()
        })
      })
    },

    async kubectlHttpMethodOpChart() {
      var xAxis = []
      var series = []
      await ApiKubectlHttpMethodOpChart()
        .then((res) => {
          let data = res.data.data.data
          console.log(data)
          xAxis = data.x_axis
          series = data.y_axis.filter((item) => {
            item.type = 'line'
            // item.areaStyle =  {}
            // item.emphasis = {
            //   focus: 'series'
            // }
            return item
          })
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      console.log(series)
      let option = {
        title: {
          text: 'ApiServerProxy http请求统计',
          textStyle: {
            fontSize: 14
          }
        },
        legend: {
          right: 10,
          data: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'TOTAL']
        },
        tooltip: {
          trigger: 'axis'
        },
        dataZoom: [
          {
            type: 'inside',
            realtime: true,
            start: 40,
            end: 100,
            xAxisIndex: 0
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: series
      }
      this.$nextTick(() => {
        let myChart = echarts.init(this.$refs.httpMethodOpChartDom)
        myChart.setOption(option)
        on(window, 'resize', function () {
          myChart.resize()
        })
      })
    },
    async k8sResourceChart() {
      await ApiRSTendencyCount()
        .then((res) => {
          let data = res.data.data
          this.xArray = data.times
          this.yMap = {
            pod: data.pods,
            cluster: data.clusters,
            node: data.nodes,
            service: data.services
          }
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      let option = {
        title: {
          text: '最近一年的资源变化',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          right: 10,
          data: ['Cluster', 'Node', 'Pod', 'Service']
        },
        dataZoom: [
          {
            type: 'inside',
            realtime: true,
            start: 40,
            end: 100,
            xAxisIndex: 0
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.xArray
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Cluster',
            type: 'line',
            // stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.yMap.cluster
          },
          {
            name: 'Node',
            type: 'line',
            // stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.yMap.node
          },
          {
            name: 'Pod',
            type: 'line',
            // stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.yMap.pod
          },
          {
            name: 'Service',
            type: 'line',
            // stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.yMap.service
          }
        ]
      }
      this.$nextTick(() => {
        this.myChart = echarts.init(this.$refs.chartDom)
        this.myChart.setOption(option)
        on(window, 'resize', this.resize)
      })
    }
  },
  mounted() {
    // const lastRoute = getLastRoute()
    // if (lastRoute.path === '/login') {
    //   this.VersionNoticeServiceProvide.onCompareVersion()
    // }
    this.getStatisticsCount()
    this.getUserCount()
    this.k8sResourceChart()
    this.kubectlHttpMethodOpChart()
    this.kubectlResourceOpChart()
    this.getOpCount()
    this.fetchClusterResourceAmount()
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
  }
}
</script>

<style>
.count-text {
  font-size: 30px;
  font-weight: bold;
}

.count-text-1 {
  font-size: 60px;
  font-weight: 400;
}

.unit-text-1 {
  font-size: 35px;
  font-weight: 400;
}
</style>
