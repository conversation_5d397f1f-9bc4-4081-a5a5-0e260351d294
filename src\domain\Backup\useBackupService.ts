import { computed, getCurrentInstance, ref, watch, nextTick } from 'vue'
import { EnumTabType } from './type'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useRequest } from 'vue-request'
import { PageList, useDelete, useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { getTableColumns } from './setting'
import { Form } from 'view-design'

export default function useBackupService() {
  const { proxy } = getCurrentInstance()

  const activeTab = ref(EnumTabType.backupStrategy)
  const clusterId = ref()
  const cluster = ref()
  const namespace = ref()
  const keyword = ref()
  const groupResource = ref()
  const createModalVisible = ref<boolean>(false)
  const formRef = ref<Form>()
  const formData = ref<Record<string, any>>({
    isPersistent: 'true',
    objectNames: [],
    desc: ''
  })
  const objectNameSearch = ref()
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const objectNameRefObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewDesignTableRef = {
    tableRef: ref(null)
  }
  const yamlModal = ref({ visible: false, data: '' })

  const columns = computed(() => getTableColumns(activeTab.value))
  /**
   * 创建逻辑中，Namespace是否合法：
   * 1.被启用且已填入值；
   * 2.无需启用
   * */
  const isCreateNamespaceIllegal = computed(() => {
    return !formData.value.namespaced || (formData.value.namespaced && !!formData.value.namespace)
  })

  const isCreateNamespaceRequired = computed(() => {
    return !!formData.value.groupResource && !!formData.value.namespaced
  })

  const ruleValidate = computed(() => ({
    clusterId: [{ required: true, message: '请选择集群' }],
    groupResource: [{ required: true, message: '请选择API 组 / 资源' }],
    ...(formData.value.namespaced
      ? { namespace: [{ required: true, message: '请选择命名空间', trigger: 'change' }] }
      : {})
  }))

  const onClusterChange = (item) => {
    if (item) {
      clusterId.value = item.value
      cluster.value = item.label
      getNamespaceList()
      getGroupResourceList()
    } else {
      clusterId.value = undefined
      cluster.value = undefined
    }
  }

  const onSearch = () => {
    refObject.tableRef.value?.reload()
  }

  const { data: clusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetBackupStrategyClusterList}`
      )
    },
    {
      formatResult: (res) => res.data.data
    }
  )
  const { data: groupResourceList, run: getGroupResourceList } = useRequest(
    () => {
      return useGet<{ data: { group: number; resource: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetBackupStrategyGroupResourceList}`,
        {
          params: {
            clusterId: clusterId.value
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data?.map((i) => `${i.group ? i.group : '-'} / ${i.resource ? i.resource : '-'}`)
    }
  )
  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetBackupStrategyNamespaceList}`, {
        params: {
          clusterId: clusterId.value
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const getTableData: TableRequest = async (params) => {
    const [group, resource] = groupResource.value?.split(' / ') ?? []
    const res = await useGet<PageList<[]>>(
      `${Config.Api.Base}${
        activeTab.value === EnumTabType.backupList
          ? Config.Api.GetBackUpObjectList
          : Config.Api.GetBackupOrRecoverStrategyList
      }`,
      {
        params: {
          page: params.pageIndex,
          size: params.pageSize,
          clusterId: clusterId.value,
          namespace: namespace.value,
          group: group === '-' ? '' : group,
          resource: resource === '-' ? '' : resource,
          ...(activeTab.value === EnumTabType.backupList
            ? {
                search: keyword.value
              }
            : {
                mode: activeTab.value === EnumTabType.backupStrategy ? 'backup' : 'recover'
              })
        }
      }
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onExec = (record) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认执行此策略？`,
      loading: true,
      onOk: async () => {
        const res = await usePost(
          `${Config.Api.Base}${
            activeTab.value === EnumTabType.backupStrategy
              ? Config.Api.ExecBackupStrategy
              : Config.Api.ExecRecoverStrategy
          }`,
          {
            pid: record.id
          }
        )
        if (res.success) {
          proxy.$Message.success('执行成功！')
          refObject.tableRef.value?.reload()

          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }
  const onDelete = (record) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.id}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(
          `${Config.Api.Base}${
            activeTab.value === EnumTabType.backupStrategy
              ? Config.Api.DeleteBackupStrategy
              : Config.Api.DeleteRecoverStrategy
          }/${record.id}`
        )
        if (res.success) {
          proxy.$Message.success('删除成功！')
          refObject.tableRef.value?.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onCreate = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        if (onValidObjectNames()) {
          proxy.$Modal.confirm({
            title: '提示',
            content: `是否确认创建策略？`,
            loading: true,
            onOk: async () => {
              console.log('params', formData.value)
              const { objectNames, objectIds, ...rest } = formData.value
              const res = await usePost(
                `${Config.Api.Base}${
                  activeTab.value === EnumTabType.backupStrategy
                    ? Config.Api.CreateBackupStrategy
                    : Config.Api.CreateRecoverStrategy
                }`,
                {
                  ...rest,
                  ...(activeTab.value === EnumTabType.backupStrategy
                    ? {
                        objectNames
                      }
                    : {
                        objectIds: objectIds?.map((i) => i.toString())
                      }),
                  isPersistent: JSON.parse(formData.value.isPersistent)
                }
              )
              if (res.success) {
                proxy.$Message.success('创建成功！')
                createModalVisible.value = false
                refObject.tableRef.value?.reload()
                proxy.$Modal.remove()
              } else {
                proxy.$Modal.remove()
              }
            }
          })
        }
      }
    })
  }

  const onOpenYaml = (record) => {
    yamlModal.value = {
      visible: true,
      data: record.yaml
    }
  }

  const onOpenCreateModal = () => {
    getCreateClusterList()
    createModalVisible.value = true
    formData.value = {
      isPersistent: 'true',
      desc: '',
      objectNames: [],
      objectIds: []
    }
  }

  const onCreateClusterChange = (item) => {
    formData.value = {
      isPersistent: 'true',
      desc: '',
      objectNames: [],
      objectIds: []
    }
    if (item) {
      formData.value.clusterId = item.value
      formData.value.clusterName = item.label
      getCreateGroupResourceList()
    }
  }

  const onCreateGroupResourceChange = (item) => {
    if (item?.value) {
      const [group, resource, namespaced] = item.value.split('/')
      formData.value.groupResource = item.value
      formData.value.group = group
      formData.value.resource = resource
      formData.value.namespaced = namespaced
      formData.value.namespace && (formData.value.namespace = undefined)
      formData.value.objectNames = []
      formData.value.objectIds = []
      if (namespaced) {
        getCreateNamespaceList()
      }
    } else {
      formData.value.groupResource = undefined
      formData.value.group = undefined
      formData.value.resource = undefined
      formData.value.namespaced = undefined
      formData.value.namespace = undefined
      formData.value.objectNames = []
      formData.value.objectIds = []
    }
  }

  const onCreateNamespaceChange = (item) => {
    if (!item) {
      formData.value.objectNames = []
      formData.value.objectIds = []
    }
  }

  const { data: createClusterList, run: getCreateClusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${
          activeTab.value === EnumTabType.backupStrategy
            ? Config.Api.GetBackupStrategyClusterList
            : Config.Api.GetRecoverStrategyClusterList
        }`
      )
    },
    {
      formatResult: (res) => res.data.data,
      manual: true
    }
  )
  const { data: createGroupResourceList, run: getCreateGroupResourceList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${
          activeTab.value === EnumTabType.backupStrategy
            ? Config.Api.GetBackupStrategyGroupResourceList
            : Config.Api.GetRecoverStrategyGroupResourceList
        }`,
        {
          params: {
            clusterId: formData.value?.clusterId
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) =>
        res.data?.data?.map((i) => {
          const group = i.group ? i.group : '-'
          const resource = i.resource ? i.resource : '-'
          return {
            value: `${i.group}/${i.resource}/${!!i.namespaced}`,
            label: `${group} / ${resource}`
          }
        })
    }
  )
  const { data: createNamespaceList, run: getCreateNamespaceList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${
          activeTab.value === EnumTabType.backupStrategy
            ? Config.Api.GetBackupStrategyNamespaceList
            : Config.Api.GetRecoverStrategyNamespaceList
        }`,
        {
          params: {
            clusterId: formData.value?.clusterId
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const { data: createObjectNameList, run: getCreateObjectNameList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${
          activeTab.value === EnumTabType.backupStrategy
            ? Config.Api.GetBackupStrategyObjectName
            : Config.Api.GetRecoverStrategyNamespaceList
        }`,
        {
          params: {
            ...(activeTab.value === EnumTabType.backupStrategy
              ? {
                  clusterId: formData.value?.clusterId,
                  namespace: formData.value?.namespace,
                  group: formData.value.group,
                  resource: formData.value.resource
                }
              : {
                  clusterId: formData.value?.clusterId
                })
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data?.data
    }
  )

  const getObjectNamesTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<[]>>(`${Config.Api.Base}${Config.Api.GetRecoverStrategyObjectNameList}`, {
      params: {
        page: params.pageIndex,
        size: params.pageSize,
        // clusterId: 10001
        // namespace: 'infra'
        clusterId: formData.value.clusterId,
        namespace: formData.value.namespace,
        group: formData.value.group,
        resource: formData.value.resource,
        search: objectNameSearch.value
      }
    })
    onSetSelectionCheck(res.data.list)
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSetSelectionCheck = (data: Record<string, any>) => {
    setTimeout(() => {
      data?.map((i, index) => {
        if (!viewDesignTableRef.tableRef.value.objData[index]) return
        const isChecked = formData.value.objectNames?.includes?.(`${i.objectName}/${i.createdAt}`)
        viewDesignTableRef.tableRef.value.objData[index]._isChecked = isChecked
      })
    }, 0)
  }
  const clearSelection = () => {
    formData.value.objectNames = []
    formData.value.objectIds = []
    onSetSelectionCheck(viewDesignTableRef.tableRef.value.data)
  }

  const onSearchObjectNames = () => {
    objectNameRefObject.tableRef.value?.reload()
  }

  const onSelectObjectNames = (items: Record<string, any>[], method: 'push' | 'pop') => {
    console.log(123123, items)
    if (method === 'push') {
      items?.forEach((i) => {
        formData.value.objectNames.push(`${i.objectName}/${i.createdAt}`)
        formData.value.objectIds.push(i.id)
      })
    } else {
      items?.forEach((i) => {
        formData.value.objectNames = formData.value.objectNames.filter((o) => o !== `${i.objectName}/${i.createdAt}`)
        formData.value.objectIds = formData.value.objectIds.filter((o) => o !== i.id)
      })
    }
  }

  const onValidObjectNames = (): boolean => {
    let isObjectNamesValid = true
    // 校验objectNames是否存在同名的不同版本的数据
    const objectNames = Array.from(new Set(formData.value.objectNames?.map((i) => i.split('/')?.[0] ?? [])))
    if (objectNames.length !== formData.value.objectNames?.length) {
      isObjectNamesValid = false
    }

    // 检测不通过，提示异常数据重复了，让用户删掉
    if (!isObjectNamesValid) {
      proxy.$Message.warning('ObjectName（资源对象）选择了对象不同版本的数据，请检查后再提交')
    }
    // 检测通过允许提交
    return isObjectNamesValid
  }

  watch(activeTab, () => {
    activeTab.value && refObject.tableRef.value?.reload()
  })

  watch(
    [() => formData.value.namespaced, () => formData.value.namespace, () => formData.value.groupResource],
    async () => {
      if (formData.value.groupResource) {
        if (isCreateNamespaceIllegal.value) {
          formData.value.objectNames = []
          await nextTick()
          activeTab.value === EnumTabType.backupStrategy ? getCreateObjectNameList() : onSearchObjectNames()
        }
      }
    },
    {
      deep: true
    }
  )

  return {
    activeTab,
    groupResource,
    groupResourceList,
    clusterId,
    onClusterChange,
    clusterList,
    namespace,
    namespaceList,
    keyword,
    onSearch,
    onCreate,
    onOpenCreateModal,
    columns,
    getTableData,
    refObject,
    onExec,
    onDelete,
    onOpenYaml,
    createModalVisible,
    formData,
    onCreateClusterChange,
    createClusterList,
    createNamespaceList,
    createGroupResourceList,
    onCreateGroupResourceChange,
    isCreateNamespaceIllegal,
    createObjectNameList,
    onCreateNamespaceChange,
    ruleValidate,
    isCreateNamespaceRequired,
    formRef,
    getObjectNamesTableData,
    objectNameRefObject,
    objectNameSearch,
    onSearchObjectNames,
    onSelectObjectNames,
    yamlModal,
    clearSelection,
    viewDesignTableRef
  }
}
