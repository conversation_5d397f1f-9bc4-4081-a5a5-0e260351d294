import { Ref, ref, unref } from 'vue'

/**
 * 解决view-design Select组件在开启多选和可搜索时，输入的搜索值失焦后没有自动清除的交互bug
 * @returns selectRef 绑定在Select组件的ref上
 * @returns onOpenChangeToClearQuery 绑定在Select组件的@on-open-change上
 */
export const useSelectQueryClear = () => {
  const selectRef = ref()
  const onOpenChangeToClearQuery = (open) => {
    if (open || !selectRef.value.query) return
    selectRef.value.onQueryChange('')
  }

  return { selectRef, onOpenChangeToClearQuery }
}

/**
 * @param obj 需要转换为查询参数的对象
 * @returns queryObj 根据传入的对象生成的ref对象
 * @returns setQueryObj 重新设置queryObj的值
 * @returns getQueryStr 获取queryObj转换后的查询参数
 */
export const useObjTransferQueryStr = (initObj?: object | Ref<object>) => {
  const queryObj = ref(unref(initObj))

  const setQueryObj = (obj: object | Ref<object>) => {
    queryObj.value = unref(obj)
  }

  const getQueryStr = () => {
    return Object.keys(queryObj.value)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(queryObj.value[key])}`)
      .join('&')
  }

  return { queryObj, setQueryObj, getQueryStr }
}
