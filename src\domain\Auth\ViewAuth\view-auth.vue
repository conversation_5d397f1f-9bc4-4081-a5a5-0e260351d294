<script lang="ts" setup>
import { <PERSON><PERSON>utton, Yaml } from '@/components'
import useViewAuthService from './useViewAuthService'
import { k8sTableCols, kubeconfigTableCols } from './setting'
const {
  authMode,
  //   viewerTableData,

  //   K8SList,
  //   K8SListLoading,
  kubectlProxyListLoading,
  kubectlProxyList,
  kubectlWebShellListLoading,
  kubectlWebShellList,

  openYaml,
  yamlVisible,
  yamlData,
  onDownloadConfig,
  onDownloadMergeConfig,
  onKubectlConsole,
  onSearchKubectlProxyList,
  onSearchKubectlWebShellList,
  onKubectlRebuild
  //   onSearchK8SList
} = useViewAuthService()
</script>

<template>
  <div class="wrapper">
    <Tabs :value="authMode" type="card">
      <!-- <TabPane :label="viewerLabel" name="viewer">
        <Table height="233" size="small" :columns="viewerTableCols" :data="viewerTableData"></Table>
      </TabPane> -->

      <TabPane label="KubeConfig 配置" name="kube-config-proxy">
        <div class="tab-content">
          <Alert class="kube-config-alert">
            <span>
              KubeConfig 配置下载, 可用于连接授权的集群。
              <a href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnvPxbpYWECGpJVo3ZnBf2hd"
                ><Icon type="ios-link" />权限申请</a
              >
            </span>
            <LinkButton text="配置合并下载" @click="onDownloadMergeConfig" />
          </Alert>
          <Input
            placeholder="请输入关键词搜索集群"
            style="margin-bottom: 16px"
            search
            @on-search="onSearchKubectlProxyList"
          />

          <Spin fix v-if="kubectlProxyListLoading" />
          <Collapse accordion simple v-if="kubectlProxyList">
            <Panel v-for="item in kubectlProxyList" :key="item.cluster" :name="item.cluster">
              <div class="kubectl-title">
                <span>集群 - {{ item.cluster }}</span>

                <span>
                  <Icon
                    type="md-refresh"
                    @click.stop="() => onKubectlRebuild(item.list?.[0].clusterId)"
                    title="重建权限"
                    style="margin-right: 8px"
                  />
                  <Icon type="md-download" @click.stop="() => onDownloadConfig(item.list?.[0].clusterId)" />
                </span>
              </div>
              <div slot="content">
                <Table size="small" :columns="kubeconfigTableCols" :data="item.list" max-height="150">
                  <template #yaml="{ row }"> <LinkButton text="YAML" @click="() => openYaml(row)" /> </template
                ></Table>
              </div>
            </Panel>
          </Collapse>
        </div>
      </TabPane>
      <TabPane label="Kubectl WebConsole" name="kube-config-web">
        <div class="tab-content">
          <Alert
            >WebConsole 的作用是让用户便捷在平台上直接使用 kubectl 连接授权的集群
            <a href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnvPxbpYWECGpJVo3ZnBf2hd"><Icon type="ios-link" />权限申请</a>
          </Alert>
          <Input
            placeholder="请输入关键词搜索集群"
            style="margin-bottom: 16px"
            search
            @on-search="onSearchKubectlWebShellList"
          />

          <Spin fix v-if="kubectlWebShellListLoading" />
          <Collapse accordion simple v-if="kubectlWebShellList">
            <Panel v-for="item in kubectlWebShellList" :key="item.cluster" :name="item.cluster">
              <div class="kubectl-title">
                <span>集群 - {{ item.cluster }}</span>
                <svg
                  @click.stop="() => onKubectlConsole(item.list?.[0].clusterId, item.cluster)"
                  viewBox="0 0 1024 1024"
                  width="18"
                  height="18"
                >
                  <path
                    d="M170.666667 896c-46.933333 0-85.333333-38.4-85.333334-85.333333V213.333333c0-46.933333 38.4-85.333333 85.333334-85.333333h682.666666c46.933333 0 85.333333 38.4 85.333334 85.333333v597.333334c0 46.933333-38.4 85.333333-85.333334 85.333333H170.666667z m0-85.333333h682.666666V298.666667H170.666667v512z m405.333333-85.333334c-12.8 0-21.333333-8.533333-21.333333-21.333333v-42.666667c0-12.8 8.533333-21.333333 21.333333-21.333333h170.666667c12.8 0 21.333333 8.533333 21.333333 21.333333v42.666667c0 12.8-8.533333 21.333333-21.333333 21.333333h-170.666667z m-337.066667 0l170.666667-170.666666-170.666667-170.666667h119.466667l140.8 140.8c17.066667 17.066667 17.066667 42.666667 0 59.733333L358.4 725.333333H238.933333z"
                    fill="#2a7cc3"
                  ></path>
                </svg>
              </div>
              <div slot="content">
                <Table size="small" :columns="kubeconfigTableCols" :data="item.list" max-height="150">
                  <template #yaml="{ row }"> <LinkButton text="YAML" @click="() => openYaml(row)" /> </template
                ></Table>
              </div>
            </Panel>
          </Collapse>
        </div>
      </TabPane>
      <!-- <TabPane label="授权资源" name="k8s">
        <div class="tab-content">
          <Alert
            >所有授权的 k8s 集群, 当前授权方式是按集群空间进行授权。
            <a href="https://q9jvw0u5f5.feishu.cn/wiki/wikcnvPxbpYWECGpJVo3ZnBf2hd"><Icon type="ios-link" />权限申请</a>
          </Alert>
          <Input placeholder="请输入关键词搜索集群" style="margin-bottom: 16px" search @on-search="onSearchK8SList" />

          <Spin fix v-if="K8SListLoading" />
          <Collapse accordion simple>
            <Panel v-for="item in K8SList" :key="item.cluster" :name="item.cluster">
              <span>集群 - {{ item.cluster }}</span>
              <div slot="content">
                <Table size="small" :columns="k8sTableCols" :data="item.children" max-height="150"></Table>
              </div>
            </Panel>
          </Collapse>
        </div>
      </TabPane> -->
    </Tabs>
    <Modal v-model="yamlVisible" title="查看YAML" footer-hide :width="40">
      <yaml v-if="yamlData" :value="yamlData" :forbiddenEdit="true" style="height: 60vh" />
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.wrapper {
  /deep/.ivu-tabs-bar {
    margin: 0;
  }
  .tab-content {
    height: 240px;
    padding: 16px 4px 0;
    overflow: auto;
  }
}
.kube-config-alert {
  padding: 8px 16px;
  /deep/ .ivu-alert-message {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.kubectl-title {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px 0 0;
  width: ~'calc(100% - 32px)';
  .ivu-icon {
    color: #2a7cc3;
    font-size: 16px;
  }
}
</style>
