import G6 from '@antv/g6'

export const tooltip = new G6.Tooltip({
  // offsetX and offsetY include the padding of the parent container
  offsetX: 20,
  offsetY: 30,
  // the types of items that allow the tooltip show up
  // 允许出现 tooltip 的 item 类型
  itemTypes: ['node'],
  // custom the tooltip's content
  // 自定义 tooltip 内容
  getContent: (e) => {
    const outDiv = document.createElement('div')
    //outDiv.style.padding = '0px 0px 20px 0px';
    const nodeName = e.item.getModel().name as string
    let formateNodeName = ''
    for (let i = 0; i < nodeName.length; i++) {
      formateNodeName = `${formateNodeName}${nodeName[i]}`
      if (i !== 0 && i % 20 === 0) formateNodeName = `${formateNodeName}<br/>`
    }
    outDiv.innerHTML = `${formateNodeName}`
    return outDiv
  },
  shouldBegin: (e) => {
    if (e.target.get('name') === 'name-shape' || e.target.get('name') === 'mask-label-shape') return true
    return false
  }
})

export default tooltip
