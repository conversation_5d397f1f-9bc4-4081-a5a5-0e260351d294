import axios from "@/libs/api.request";

export const ApiPRAuthK8sAuthList = (cluster_id, namespace, kind) => {
  return axios.request({
    url: '/api/v1/pr-auth/authority/k8s/list',
    method: 'get',
    params: {
      cluster_id: cluster_id,
      namespace: namespace,
      kind: kind
    }
  })
}

export const ApiPRAuthViewerAuthList = () => {
  return axios.request({
    url: '/api/v1/pr-auth/authority/viewer/list',
    method: 'get',
    params: {
    }
  })
}

export const ApiPRAuthK8sWithUserGet = (uid, cluster_id, namespace, kind) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/k8s-with-user/get/${uid}`,
    method: 'get',
    data: {},
    params: {
      cluster_id:cluster_id,
      namespace: namespace,
      kind: kind,
    }
  })
}


export const ApiPRAuthViewerWithUserGet = (uid) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/viewer-with-user/get/${uid}`,
    method: 'get',
    data: {},
    params: {}
  })
}

export const ApiPRAuthViewerWithUserUpdate = (uid, auth_id) => {
  return axios.request({
    url: `/api/v1/pr-auth/role/viewer-with-user/update/${uid}`,
    method: 'post',
    data: {
      auth_id: [auth_id]
    },
    params: {}
  })
}

export const ApiPRAuthViewerWithUserDelete = (uid, auth_id) => {
  return axios.request({
    url: `/api/v1/pr-auth/role/viewer-with-user/delete/${uid}`,
    method: 'delete',
    data: {
      auth_id: [auth_id]
    },
    params: {}
  })
}

export const ApiPRAuthK8sWithUserUpdate = (uid, auth_id) => {
  return axios.request({
    url: `/api/v1/pr-auth/role/k8s-with-user/update/${uid}`,
    method: 'post',
    data: {
      auth_id: [auth_id]
    },
    params: {}
  })
}


export const ApiPRAuthK8sWithUserDelete = (uid, auth_id) => {
  return axios.request({
    url: `/api/v1/pr-auth/role/k8s-with-user/delete/${uid}`,
    method: 'delete',
    data: {
      auth_id: [auth_id]
    },
    params: {}
  })
}


export const ApiPRAuthK8sGenerate = () => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/k8s/generate-auth`,
    method: 'post',
  })
}


export const ApiPRAuthK8sClear = (cluster_id) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/k8s/clear`,
    method: 'post',
    data: {
      cluster_id: cluster_id
    }
  })
}


export const ApiPRAAuthK8sNamespaceGet = (uid, cluster_id, namespace) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/namespace-auth/get/${uid}`,
    method: 'get',
    data: {},
    params: {
      cluster_id: cluster_id,
      namespace: namespace
    }
  })
}

export const ApiPRAAuthK8sNamespaceCreate = (uid, cluster_id, namespace, auth) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/namespace-auth/create`,
    method: 'post',
    data: {
      cluster_id: cluster_id,
      namespace: namespace,
      uid: uid,
      auth: auth,},
    params: {

    }
  })
}


export const ApiPRAAuthK8sNamespaceDelete = (uid, cluster_id, namespace, auth) => {
  return axios.request({
    url: `/api/v1/pr-auth/authority/namespace-auth/delete`,
    method: 'delete',
    data: {
      cluster_id: cluster_id,
      namespace: namespace,
      uid: uid,
      auth: auth,
    },
    params: {
    }
  })
}
