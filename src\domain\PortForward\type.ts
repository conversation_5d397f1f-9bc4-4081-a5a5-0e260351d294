export enum EnumComponentType {
  AssociatedDeployment = 'AssociatedDeployment', // 关联deployment模式
  AssociatedStatefulset = 'AssociatedStatefulset', // 关联Statefulset模式
  AssociatedPod = 'AssociatedPod', // 关联Pod模式
  Independent = 'Independent' // 独立模式
}

export interface Entity {
  cluster: string
  clusterId: number
  namespace: string
  relativePodName?: string
  relativeName?: string
}

export interface PortForward {
  clusterId: number
  clusterName: string
  createdAt: string
  deletedAt: null
  editor: string
  id: number
  name: string
  namespace: string
  pod: string
  proxyAddress: string
  proxyPort: number
  status: string
  targetPort: number
  updatedAt: string
}
