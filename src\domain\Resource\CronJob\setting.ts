import { relativeTime } from '@/libs/tools'

export const TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  {
    title: 'Schedule',
    key: 'schedule',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Suspend',
    key: 'suspend',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Active',
    key: 'active',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'LastSchedule',
    key: 'lastSchedule',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,
    tooltipTheme: 'light',
    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]

export const RELATED_JOB_COLUMNS = [
  {
    title: 'Name',
    key: 'name'
  },
  {
    title: 'Completions',
    key: 'completions',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Duration',
    key: 'duration',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,
    tooltipTheme: 'light',
    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]
