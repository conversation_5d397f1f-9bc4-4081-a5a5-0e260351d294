<script lang="ts" setup>
import { formItemProps } from '@/components/pro-form/pro-from-item/type'
import { ProFormItem } from '@/components/pro-form'
import { set } from 'vue'

const props = defineProps({
  ...formItemProps(),
  placeholder: String, // 占位内容
  disabled: Boolean,
  type: String,
  number: <PERSON><PERSON><PERSON>
})
</script>

<template>
  <ProFormItem v-bind="props">
    <template #default="{ data }">
      <Input
        v-if="!props.readonly"
        :key="props.dataReloadFlag"
        :disabled="props.disabled"
        :number="props.number"
        :type="props.type"
        :value="!props.mode ? data?.[props.name] : data?.[props.name]?.data"
        @on-change="
          (e) => (!props.mode ? set(data, props.name, e.target.value) : set(data[props.name], 'data', e.target.value))
        "
        :placeholder="props.placeholder || `请输入${props.label}`"
      >
        <template v-for="slotName in Object.keys($slots)" #[slotName]>
          <slot :name="slotName" />
        </template>
      </Input>
      <Tag v-if="props.readonly" color="primary">{{ !props.mode ? data?.[props.name] : data?.[props.name]?.data }}</Tag>
    </template>
  </ProFormItem>
</template>

<style lang="less" scoped></style>
