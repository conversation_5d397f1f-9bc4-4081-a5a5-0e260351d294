# 只在开发模式中被载入
VUE_APP_PORT = 8001

# app 版本标识
VUE_APP_VERSION_TAG = "开发版 (Dev)"

# 网站根目录
VUE_APP_PUBLIC_PATH = /

# 是否开启mock
VUE_APP_USE_MOCK = false

# 网站前缀
VUE_APP_BASE_URL = /

# 是否删除console
VUE_APP_DROP_CONSOLE = true


# API 接口地址
# VUE_APP_GLOB_API_URL = "http://10.112.66.253:32234"
# VUE_APP_GLOB_API_URL = "https://alpha-cloud.ttyuyin.com"
# 峰
VUE_APP_GLOB_API_URL = "http://192.168.81.14:8000"
# 广
# VUE_APP_GLOB_API_URL = "http://192.168.64.58:8000"
VUE_APP_GLOB_MOCK_API_URL = ""

# 运维平台
VUE_APP_OPERATION_PLATFORM = "http://testing-yw.ttyuyin.com/index/dashboard"

# 监控平台接口
VUE_APP_MONITOR_API_URL = 'http://10.192.253.49:59286'

