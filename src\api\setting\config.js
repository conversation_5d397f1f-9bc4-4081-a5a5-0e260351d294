import axios from '@/libs/api.request'

export const apiConfigList = (page) => {
  return axios.request({
    url: '/api/v1/setting/list',
    method: 'get',
    params: page
  })
}

export const apiConfigGet = (pid) => {
  return axios.request({
    url: `/api/v1/setting/list/${pid}`,
    method: 'get'
  })
}

// {
//   "key": "test-create",
//   "value": "test-create",
//   "category": "system",
//   "desc": "test-create"
// }
export const apiConfigCreate = ({key, value, category, desc}) => {
  return axios.request({
    url: `/api/v1/setting/create`,
    data: {
      key,
      value,
      category,
      desc
    },
    method: 'post'
  })
}

// {
//   "value": "string1",
//   "category": "system",
//   "desc": "已修改为 string1"
// }
export const apiConfigUpdate = ({pid, value, category, desc}) => {
  return axios.request({
    url: `/api/v1/setting/update/${pid}`,
    data: {
      value,
      category,
      desc
    },
    method: 'patch'
  })
}

export const apiConfigDelete = ({pid}) => {
  return axios.request({
    url: `/api/v1/setting/delete/${pid}`,
    method: 'delete'
  })
}
