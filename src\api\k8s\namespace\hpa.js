import axios from '@/libs/api.request'


export const ApiHpaList = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiHpaGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/get`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiHpaDelete = (data) => {
  return axios.request({
    url: `/api/v1/resource/hpa/delete`,
    method: 'delete',
    data: data,
  })
}

export const ApiHpaYamlCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/hpa/yaml/create`,
    method: 'post',
    data: data,
  })
}


export const ApiHpaYamlUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/hpa/yaml/update`,
    method: 'post',
    data: data,
  })
}

// ---

export const ApiHpaFormGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/form/get`,
    method: 'get',
    params: params,
  })
}
export const ApiHpaFormCreate = (data) => {
  return axios.request({
    url: `/api/v1/resource/hpa/form/create`,
    method: 'post',
    data: data,
  })
}
export const ApiHpaFormUpdate = (data) => {
  return axios.request({
    url: `/api/v1/resource/hpa/form/update`,
    method: 'post',
    data: data,
  })
}
export const ApiHpaFormCanConvert = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/form/is-convert`,
    method: 'get',
    params,
  })
}
export const ApiHpaKedaIsSupport = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/keda/is-support`,
    method: 'get',
    params,
  })
}
export const ApiHpaTimezoneList = () => {
  return axios.request({
    url: `/api/v1/resource/hpa/timezone/list`,
    method: 'get',
  })
}
export const ApiHpaWorkloadNameList = (params) => {
  return axios.request({
    url: `/api/v1/resource/hpa/form/workload-name/list`,
    method: 'get',
    params,
  })
}


