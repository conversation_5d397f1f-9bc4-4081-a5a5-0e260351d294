import { PageList, useGet } from '@/libs/service.request'
import Config from '@/config'
import { useRequest } from 'vue-request'
import { ActionType, TableRequest } from '@/components/pro-table'
import { ref, watch } from 'vue'
import dayjs from 'dayjs'

const COLUMNS = [
  {
    title: 'Object',
    key: 'object',
    tooltip: true,
    tooltipTheme: 'light',
    maxWidth: 320
  },
  {
    title: 'Namespace',
    key: 'namespace',
    width: 120
  },
  {
    title: 'Type',
    key: 'type',
    slot: 'type',
    width: 120
  },
  {
    title: 'Reason',
    key: 'reason',
    tooltip: true,
    tooltipTheme: 'light',
    width: 120
  },

  {
    title: 'Message',
    key: 'message',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'age',
    slot: 'age',
    width: 160
  }
]

export default function useEventService() {
  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }

  const eventType = ref()
  const resourceKind = ref()
  const resourceName = ref()

  const cluster = ref()
  const clusterId = ref()
  const namespace = ref()
  const timeRange = ref([dayjs().subtract(1, 'hour').format('YYYY/MM/DD HH:mm'), dayjs().format('YYYY/MM/DD HH:mm')])

  const message = ref()

  const onClusterChange = (item) => {
    clusterId.value = item.value
    cluster.value = item.label
    getNamespaceList()
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<[]>>(`${Config.Api.Base}${Config.Api.GetEventList}`, {
      params: {
        page: params.pageIndex,
        size: params.pageSize,
        cluster: cluster.value,
        namespace: namespace.value,
        resourceName: resourceName.value,
        resourceKind: resourceKind.value,
        type: eventType.value,
        search: message.value,
        ...(timeRange.value?.[0]
          ? {
              startTimestamp: dayjs(timeRange.value[0]).valueOf() / 1000,
              endTimestamp: dayjs(timeRange.value[1]).valueOf() / 1000
            }
          : null)
      }
    })
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSearch = () => {
    refObject.tableRef.value?.reload()
  }

  const { data: clusterList } = useRequest(
    () => {
      return useGet<{ data: { id: number; name: string; env: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetClusterList}`
      )
    },
    {
      formatResult: (res) => res.data.data
    }
  )
  const { data: namespaceList, run: getNamespaceList } = useRequest(
    () => {
      return useGet<{ data: { name: string; business: string }[] }>(
        `${Config.Api.Base}${Config.Api.GetNamespaceList}`,
        {
          params: {
            clusterName: cluster.value
          }
        }
      )
    },
    {
      manual: true,
      formatResult: (res) => res.data.data
    }
  )

  const { data: namespaceTypeKindData, run: getNamespaceTypeKind } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetEventNamespaceTypeKindList}`, {
        params: {
          cluster: cluster.value
        }
      })
    },
    {
      manual: true,
      initialData: {},
      formatResult: (res) => res.data.data
    }
  )

  const { data: resourceNameList, run: getResourceName } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetEventObjectList}`, {
        params: {
          cluster: cluster.value,
          namespace: namespace.value,
          kind: resourceKind.value,
          ...(timeRange.value?.[0]
            ? {
                startTimestamp: dayjs(timeRange.value[0]).valueOf() / 1000,
                endTimestamp: dayjs(timeRange.value[1]).valueOf() / 1000
              }
            : null)
        }
      })
    },
    {
      manual: true,
      ready: resourceKind,
      formatResult: (res) => res.data?.data,
      onSuccess: (data) => {
        resourceName.value = undefined
      }
    }
  )

  watch(
    [cluster],
    () => {
      if (cluster.value) {
        eventType.value = undefined
        resourceKind.value = undefined
        getNamespaceTypeKind()
        onSearch()
      }
    },
    { immediate: true }
  )

  return {
    refObject,
    getTableData,
    COLUMNS,
    onSearch,
    cluster,
    clusterId,
    clusterList,

    namespace,
    eventType,
    resourceName,
    resourceKind,
    timeRange,
    message,
    namespaceTypeKindData,
    getNamespaceTypeKind,
    resourceNameList,
    getResourceName,
    onClusterChange,
    namespaceList
  }
}
