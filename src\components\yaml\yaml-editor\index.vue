<template>
  <div class="yaml-editor">
    <component ref="textarea" :is="isDiffMode ? 'div' : 'textarea'" />
  </div>
</template>

<script>
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
/** --------- style --------- */
import 'codemirror/addon/lint/lint.css'
import './darcula.css'
import 'codemirror/mode/yaml/yaml'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/yaml-lint'
/** --------- style --------- */

/** --------- search --------- */
import 'codemirror/addon/scroll/annotatescrollbar.js'
import 'codemirror/addon/search/matchesonscrollbar.js'
import 'codemirror/addon/search/match-highlighter.js'
import 'codemirror/addon/search/jump-to-line.js'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/search/searchcursor.js'
import 'codemirror/addon/search/search.js'
/** --------- search --------- */

/** --------- diff --------- */
import 'codemirror/addon/merge/merge.js'
import 'codemirror/addon/merge/merge.css'
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/brace-fold' //折叠js
import 'codemirror/addon/fold/xml-fold' //折叠xml和html
import 'codemirror/addon/fold/markdown-fold' //折叠md
import 'codemirror/addon/fold/comment-fold' //折叠注释

import DiffMatchPatch from 'diff-match-patch'
window.diff_match_patch = DiffMatchPatch
window.DIFF_DELETE = -1
window.DIFF_INSERT = 1
window.DIFF_EQUAL = 0
/** --------- diff --------- */

window.jsyaml = require('js-yaml') // 引入js-yaml为codemirror提高语法检查核心支持

export default {
  name: 'yaml',
  // eslint-disable-next-line vue/require-prop-types
  props: ['value', 'forbiddenEdit', 'isDiffMode', 'lastYamlData'],
  data() {
    return {
      yamlEditor: false,
      lastSearchValue: ''
    }
  },
  watch: {
    value() {
      if (this.isDiffMode) {
        // 更新diff模式下的数据
        this.yamlEditor.right.orig.setValue(this.value)
      } else {
        if (!this.yamlEditor) return
        const editorValue = this.yamlEditor.getValue()
        if (this.value !== editorValue) {
          this.yamlEditor.setValue(this.value ?? '')
          this.$nextTick(() => {
            this.yamlEditor.focus()
          })
        }
      }
    },
    forbiddenEdit() {
      this.yamlEditor.setOption('readOnly', this.forbiddenEdit)
    },
    lastYamlData() {
      this.$nextTick(() => {
        if (this.isDiffMode) {
          this.yamlEditor.edit.setValue(this.lastYamlData)
        }
      })
    }
  },
  mounted() {
    if (this.isDiffMode) {
      this.yamlEditor = CodeMirror.MergeView(this.$refs.textarea, {
        value: this.lastYamlData, //上次内容
        origLeft: null,
        orig: this.value, //本次内容
        lineNumbers: true, //显示行号
        mode: 'text/x-yaml',
        highlightDifferences: true,
        connect: 'align',
        readonly: true, //只读 不可修改
        theme: 'darcula', // 编辑器主题
        indentWithTabs: false, // 使用空格进行缩进
        tabSize: 2, // 每个缩进的空格数
        indentUnit: 2, // 每个缩进的空格数
        lint: true // 开启语法检查
      })
    } else {
      this.yamlEditor = CodeMirror.fromTextArea(this.$refs.textarea, {
        lineNumbers: true, // 显示行号
        mode: 'text/x-yaml', // 语法model
        gutters: ['CodeMirror-lint-markers'], // 语法检查器
        theme: 'darcula', // 编辑器主题
        indentWithTabs: false, // 使用空格进行缩进
        tabSize: 2, // 每个缩进的空格数
        indentUnit: 2, // 每个缩进的空格数
        lint: true, // 开启语法检查
        inputStyle: 'contenteditable', // 让文本处于可编辑状态(该模式兼容性好)
        smartIndent: true,
        ...(!!this.forbiddenEdit ? { readOnly: true } : null),
        extraKeys: {
          'Ctrl-F': (cm) => {
            cm.execCommand('find')
            this.addSearchCloseObserver()
          },
          'Cmd-F': (cm) => {
            cm.execCommand('find')
            this.addSearchCloseObserver()
          },
          Enter: (cm) => {
            if (this.lastSearchValue) {
              cm.execCommand('findNext')
            } else if (!this.forbiddenEdit) {
              const line = cm.getLine(cm.getCursor().line)
              const match = line.match(/^(\s*)/)
              const indent = match ? match[0] : ''
              cm.replaceSelection('\n', 'end')
            }
          },
          Tab: (cm) => {
            this.$Message.error('请使用空格键缩进')

            return
          },
          'Shift-Enter': 'findPrev'
        }
      })

      this.yamlEditor.setValue(this.value ?? '')
      this.yamlEditor.on('change', (cm) => {
        this.$emit('changed', cm.getValue())
        this.$emit('input', cm.getValue())
      })
      this.$nextTick(() => {
        this.yamlEditor.focus()
      })
    }
  },
  methods: {
    getValue() {
      return this.yamlEditor.getValue()
    },
    refresh() {
      this.yamlEditor.refresh()
    },
    addSearchCloseObserver() {
      var dialog = document.querySelector('.CodeMirror-dialog')
      if (dialog) {
        var searchField = dialog.querySelector('.CodeMirror-search-field')
        var observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.removedNodes.length > 0) {
              this.lastSearchValue = searchField.value
              if (!this.lastSearchValue) {
                this.yamlEditor.setSelection(this.yamlEditor.getCursor(), this.yamlEditor.getCursor())
              }
              observer.disconnect()
            }
          })
        })
        observer.observe(dialog.parentNode, { childList: true })
      }
    }
  }
}
</script>

<style lang="less">
.yaml-editor {
  height: 100%;
  position: relative;
}
.yaml-editor .CodeMirror {
  height: 100%;
  min-height: 300px;
  font-family: Consolas, Monaco, monospace;
  font-size: 14px;
}

.yaml-editor .CodeMirror-scroll {
  min-height: 300px;
}

/deep/.CodeMirror-selected {
  background: #b36539;
}

.CodeMirror-merge-copy,
.CodeMirror-merge-scrolllock-wrap {
  display: none !important;
}
</style>
