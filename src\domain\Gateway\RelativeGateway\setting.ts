import { relativeTime } from '@/libs/tools'
import { Tooltip } from 'view-design'
export const GATEWAY_TABLE_COLUMNS = [
  {
    title: 'Name',
    key: 'name',
    tooltip: true
  },
  {
    title: 'Namespace',
    key: 'namespace',
    width: 220
  },
  {
    title: 'Hosts',
    key: 'hosts',
    render: (h, params) => {
      return params.row.hosts
        ? h(Tooltip, {
            class: 'text-ellipsis',
            props: {
              transferClassName: 'text-ellipsis',
              transfer: true,
              placement: 'top-start',
              theme: 'light'
            },
            scopedSlots: {
              default: () => params.row.hosts.join(','),
              content: () => params.row.hosts?.map((i) => h('p', `- ${i}`))
            }
          })
        : h('div', '-')
    }
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,

    width: 160,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]
