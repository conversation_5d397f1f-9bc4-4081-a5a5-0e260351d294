<script lang="ts" setup>
import { PropType } from 'vue'
import { EventTable, BatchCopy } from '@/domain/Resource'
import { ProTable, LinkButton, ViewYaml, ResourceForm, Space } from '@/components'

import { EnumComponentType } from './type'
import useSVCService from './useSVCService'
import { COLUMNS, POD_COLUMNS, PORT_COLUMNS } from './setting'

const props = defineProps({
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  },
  // 路由初始化搜索
  name: String
})

const {
  getTableData,
  refObject,

  onEdit,
  onDelete,

  onViewYaml,
  viewYamlVisible,
  yamlHistoryParams,
  formVisible,
  formEntity,
  formStatus,

  onSubmitSuccess,
  onCreate,
  yamlInitData,
  detailModalVisible,
  detail,
  detailLoading,
  onOpenDetail,
  K8SInstance,
  podData,
  podDataLoading,
  getPodData,
  portData,
  batchCopyModalVisible
} = useSVCService(props)
</script>

<template>
  <div>
    <Alert show-icon>
      当前页面中您可以管理 Service 资源。请谨慎操作修改，因为不当的修改 可能会影响已部署的服务。
    </Alert>
    <pro-table
      :columns="COLUMNS"
      :request="getTableData"
      manual-request
      :action-ref="refObject"
      row-key="uuid"
      v-bind="{
        ...(props.type === EnumComponentType.Independent
          ? {
              search: [{ value: 'keyword', label: '名称', initData: props.name }],
              'on-create': onCreate,
            }
          : { pagination: false })
      }"
    >
      <template #operate-buttons>
        <Button
          v-if="props.type !== EnumComponentType.AssociatedDeployment"
          size="small"
          type="primary"
          ghost
          icon="md-copy"
          @click="() => (batchCopyModalVisible = true)"
          >批量复制</Button
        >
      </template>
      <template #name="{ row }">
        <link-button @click="() => onOpenDetail(row)" :text="row.name" style="font-weight: 600" ellipsis tooltip />
      </template>
      <template #ports="{ row }">
        <Poptip trigger="hover" transfer>
          <template #content>
            <div class="port-poptip-wrapper">
              <template v-for="(pMap, key) in row.ports">
                <Tag v-if="pMap.nodePort" :key="key"> {{ pMap.port }}:{{ pMap.nodePort }} / {{ pMap.protocol }}</Tag>
                <Tag v-else :key="key"> {{ pMap.port }} / {{ pMap.protocol }}</Tag>
              </template>
            </div></template
          >
          <div class="ports-wrapper">
            <template v-for="(pMap, key) in row.ports">
              <Tag v-if="pMap.nodePort" color="#009fa7" :key="key">
                {{ pMap.port }}:{{ pMap.nodePort }} / {{ pMap.protocol }}</Tag
              >
              <Tag v-else color="#657fa8" :key="key"> {{ pMap.port }} / {{ pMap.protocol }}</Tag>
            </template>
          </div>
        </Poptip>
      </template>
      <template #ops="{ row }">
        <space justify>
          <link-button @click="() => onViewYaml(row)" text="YAML" />
          <link-button v-if="props.type === EnumComponentType.Independent" @click="() => onEdit(row)" text="编辑" />
          <link-button
            v-if="props.type === EnumComponentType.Independent"
            @click="() => onDelete(row)"
            text="删除"
            type="danger"
          />
        </space>
      </template>
    </pro-table>
    <Drawer title="Service" :closable="false" width="50" v-model="detailModalVisible">
      <Spin v-if="detailLoading" fix="">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <Card>
        <h4>详情</h4>
        <Row style="margin-top: 16px">
          <Col :span="8" class="info-key">Namespace / Name</Col>
          <Col :span="16" class="info-value"> {{ detail?.metadata?.namespace }} / {{ detail?.metadata?.name }}</Col>
        </Row>
        <Row style="margin-top: 8px">
          <Col :span="8" class="info-key">创建时间</Col>
          <Col :span="16" class="info-value"> {{ detail?.metadata?.creationTimestamp }}</Col>
        </Row>
        <Row style="margin-top: 8px">
          <Col :span="8" class="info-key">类型</Col>
          <Col :span="16" class="info-value"> {{ detail?.spec?.type }}</Col>
        </Row>
        <Row style="margin-top: 8px">
          <Col :span="8" class="info-key">ClusterIP</Col>
          <Col :span="16" class="info-value"> {{ detail?.spec?.clusterIP }}</Col>
        </Row>
      </Card>
      <Card style="margin-top: 16px">
        <h4>端口</h4>
        <Table
          :loading="detailLoading"
          style="margin-top: 16px"
          size="small"
          :columns="PORT_COLUMNS"
          :data="portData"
        ></Table>
      </Card>

      <Card style="margin-top: 16px">
        <EventTable :uuid="detail?.metadata?.uid" :clusterId="K8SInstance?.clusterId" />
      </Card>

      <Card v-if="props.type === EnumComponentType.Independent" style="margin-top: 16px">
        <div style="margin: 8px 0px; display: flex; align-item: center; justify-content: space-between">
          <h4>关联 Pod</h4>
          <Button @click="() => getPodData(detail?.metadata?.uid)" size="small" icon="ios-refresh" type="primary" ghost
            >刷新</Button
          >
        </div>
        <Table :loading="podDataLoading" :columns="POD_COLUMNS" :data="podData"></Table>
      </Card>
    </Drawer>

    <resource-form
      resourceType="service"
      v-model="formVisible"
      :status="formStatus"
      :resource-entity="formEntity"
      :onSubmitCallBack="onSubmitSuccess"
      :yamlInitData="yamlInitData"
      resource-version="V2"
      width="1200"
      forbiddenForm
      notSynchronizeToUnifiedCluster
      isSkipCheck
    />

    <view-yaml
      resourceType="service"
      v-model="viewYamlVisible"
      :resource-entity="formEntity"
      :yaml-history-params="yamlHistoryParams"
      isCheckYaml
      notSynchronizeToUnifiedCluster
    />
    <BatchCopy v-model="batchCopyModalVisible" resourceType="Service" />
  </div>
</template>

<style lang="less" scoped>
.dashed-wrapper {
  padding: 16px 16px 0 16px;
  border: 1px dashed#dcdee2;
  background: #fff;
  border-radius: 4px;
}

.single-line {
  display: flex;
  align-items: center;

  > div {
    margin: 0;

    &:last-child {
      flex: 1 0 0%;
    }
  }
}
.filter-from-item-margin > .ivu-form-item {
  margin-bottom: 0;
}
.filter-last-from-item-margin > .ivu-form-item:last-child {
  margin-bottom: 0;
}
.ports-wrapper {
  overflow: hidden;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>

<style lang="less">
.text-ellipsis {
  display: flex;
  margin: 0;
  > .ivu-tooltip-rel {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ivu-tooltip-inner {
    max-width: unset;
  }
}
.input-suffix-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-left: 1px solid #cccc;
}
.port-poptip-wrapper {
  max-width: 658px;
  max-height: 240px;
  white-space: pre-wrap;
}
</style>
