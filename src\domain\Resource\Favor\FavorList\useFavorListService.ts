import Config from '@/config'
import { useDelete, useGet } from '@/libs/service.request'

import { useRequest } from 'vue-request'
import {
  EnumFavorResourceName,
  EnumFavorResourceNameToKey,
  EnumFavorResourcePath,
  EnumFavorResourceTitle,
  EnumFavorResourceTitleToName
} from '../enum'
import { getCurrentInstance, ref } from 'vue'
import { useStore } from '@/libs/useVueInstance'

export default function useFavorBtnService() {
  const resourceType = ref<EnumFavorResourceTitle>()
  const { proxy } = getCurrentInstance()
  const store = useStore()

  const {
    data: favorList,
    loading: favorListLoading,
    run: getFavorList
  } = useRequest(
    () => {
      return useGet<{ data: { resourceName: string }[] }>(`${Config.Api.Base}${Config.Api.GetResourceFavorList}`, {
        params: resourceType.value
          ? {
              search: EnumFavorResourceName[EnumFavorResourceTitleToName[resourceType.value]]
            }
          : {}
      })
    },
    {
      formatResult: (res) => {
        const list = res.data.data?.map((i) => ({
          ...i,
          resourceType: EnumFavorResourceNameToKey[i.resourceName] ?? '-'
        }))
        return list
      },
      initialData: []
    }
  )
  const resourceTypeList = Object.values(EnumFavorResourceTitle)

  const onEnterFavor = (record) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认跳转至 ${record.clusterName} / ${record.namespace} 的 ${
        EnumFavorResourceTitle[record.resourceType] ?? '-'
      } 资源？`,
      loading: true,
      onOk: () => {
        if (EnumFavorResourcePath?.[EnumFavorResourceNameToKey?.[record.resourceName]]) {
          store.commit('setCurrentCluster', { uid: store.state.user.userId, cluster: record.clusterName })
          store.commit('setCurrentNamespace', { uid: store.state.user.userId, namespace: record.namespace })
          store.commit('setCurrentClusterId', { uid: store.state.user.userId, clusterId: record.clusterId })
          window.open(
            `${window.location.origin}${EnumFavorResourcePath[EnumFavorResourceNameToKey[record.resourceName]]}`,
            '_blank'
          )
        } else {
          proxy.$Message.warning(`无法找到资源${record.resourceName}的有效地址，请联系相关人员排查！`)
        }
        proxy.$Modal.remove()
      }
    })
  }

  const onDisfavor = async (record) => {
    const res = await useDelete(`${Config.Api.Base}${Config.Api.RemoveResourceFavor}/`, {
      params: {
        collectionId: record.id
      }
    })
    if (res.success) {
      getFavorList()
      proxy.$Message.success(
        `已取消收藏${record.clusterName} / ${record.namespace}下的${EnumFavorResourceTitle[record.resourceType]}资源`
      )
    }
  }

  return {
    favorList,
    favorListLoading,
    resourceType,
    resourceTypeList,
    onEnterFavor,
    getFavorList,
    onDisfavor
  }
}
