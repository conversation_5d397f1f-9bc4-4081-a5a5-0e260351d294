<template>
  <Poptip
    always
    v-if="desc"
    trigger="hover"
    :content="desc"
    :placement="placement ?? 'left'"
    transfer
    transfer-class-name="desc-components-poptip"
  >
    <Icon type="ios-alert-outline" class="desc-components-alter-icon" />
  </Poptip>
</template>

<script>
export default {
  name: 'desc-icon',
  props: {
    desc: {
      type: String,
      default: () => {
        return ''
      }
    },
    placement: String
  }
}
</script>

<style lang="less">
.desc-components-alter-icon {
  font-size: 14px;
  cursor: pointer;
  color: #2a7cc3;
}
.desc-components-poptip {
  .ivu-poptip-body-content-inner {
    white-space: break-spaces;
  }
}
</style>
