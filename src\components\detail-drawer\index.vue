<script lang="ts" setup>
import { detailDrawerProps } from './type'
import { computed } from 'vue'

import useDetailDrawerService from './useDetailDrawerService'

const props = defineProps(detailDrawerProps())
const emit = defineEmits(['input'])

const visible = computed({
  get() {
    return props.value
  },
  set(value) {
    emit('input', value)
  }
})

const { detailData, detailLoading } = useDetailDrawerService(props, visible)
</script>

<template>
  <Drawer :title="props.title" :scrollable="false" width="45" v-model="visible" class-name="detail-drawer-wrapper">
    <Spin fix v-if="detailLoading">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <slot name="default" :data="detailData"></slot>
  </Drawer>
</template>

<style lang="less" scoped>
:deep(.detail-drawer-wrapper) {
  .ivu-drawer-body {
    > div {
      &:not(:first-child) {
        margin-top: 16px;
      }
    }
  }
}
</style>
