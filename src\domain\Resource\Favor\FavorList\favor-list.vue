<script lang="ts" setup>
import useFavorListService from './useFavorListService'
import { EnumFavorResourceTitle } from '../enum'
import { LinkButton, Space } from '@/components'

const { favorList, favorListLoading, resourceType, resourceTypeList, onEnterFavor, getFavorList, onDisfavor } =
  useFavorListService()

const COLUMNS = [
  {
    title: '资源类型',
    key: 'resourceType',
    slot: 'resourceType'
  },
  {
    title: '命名空间',
    key: 'namespace',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: '集群',
    key: 'clusterName',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    align: 'center',
    width: 120
  }
]
</script>

<template>
  <div>
    <Alert
      >在<strong> K8s 资源 </strong>和<strong> Istio 资源 </strong>菜单栏下，可点击 🌟 进行资源收藏，
      以便从工作台快速进入。</Alert
    >

    <Select
      v-model="resourceType"
      style="margin-bottom: 16px"
      filterable
      placeholder="请选择资源类型"
      @on-change="getFavorList"
      clearable
    >
      <Option v-for="item in resourceTypeList" :value="item" :key="item">{{ item }}</Option>
    </Select>
    <div>
      <Table height="234" size="small" :columns="COLUMNS" :data="favorList" :loading="favorListLoading">
        <template #resourceType="{ row }">
          {{ EnumFavorResourceTitle[row.resourceType] ?? '-' }}
        </template>
        <template #ops="{ row }">
          <Space>
            <LinkButton @click="() => onEnterFavor(row)" text="进入" />
            <LinkButton @click="() => onDisfavor(row)" text="取消收藏"
          /></Space>
        </template>
      </Table>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
