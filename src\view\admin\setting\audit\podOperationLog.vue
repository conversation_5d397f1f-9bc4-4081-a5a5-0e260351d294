<template>
  <Container>
    <Card :bordered="false">
      <Row style="margin-bottom: 16px" :gutter="10">
        <Col span="5">
          <Select clearable filterable v-model="filterMap.username" placeholder="用户名(英文)">
            <Option v-for="item in userList" :value="item.username" :key="item.username">{{ item.username }}</Option>
          </Select>
        </Col>
        <Col span="5">
          <Select
            @on-clear="onClearCluster"
            clearable
            filterable
            v-model="filterMap.clusterId"
            placeholder="Cluster"
            @on-change="onClusterChange"
          >
            <Option v-for="item in clusterList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </Col>
        <Col span="4">
          <Select
            @on-clear="onClearNs"
            clearable
            filterable
            v-model="filterMap.namespace"
            placeholder="Namespace"
            @on-change="onNamespaceChange"
          >
            <Option v-for="item in namespaceList" :value="item.metadata.name" :key="item.metadata.name">{{
              item.metadata.name
            }}</Option>
          </Select>
        </Col>
        <Col span="5">
          <Select
            @on-clear="onClearPod"
            clearable
            filterable
            v-model="filterMap.pod"
            placeholder="Pod"
            @on-change="onPodChange"
          >
            <Option v-for="item in podList" :value="item.metadata.name" :key="item.metadata.name">{{
              item.metadata.name
            }}</Option>
          </Select>
        </Col>
        <Col span="5">
          <Select clearable filterable v-model="filterMap.container" placeholder="Container">
            <Option v-for="item in containerList" :value="item.name" :key="item.name">{{ item.name }}</Option>
          </Select>
        </Col>
      </Row>
      <Row style="margin-bottom: 16px" :gutter="10">
        <Col span="10">
          <Input v-model="searchMap.input" clearable placeholder="搜索 Input 内容" style="width: 100%" />
        </Col>
        <Col span="10">
          <DatePicker
            v-model="selectDatetime"
            type="datetimerange"
            style="width: 100%"
            format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"
            @on-change="onDateTimeChange"
          ></DatePicker>
        </Col>
        <Col span="4">
          <Button type="primary" icon="ios-search" @click="handleSearch" style="width: 100%">查询</Button>
        </Col>
      </Row>
      <Row style="margin-top: 8px">
        <Button size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable">刷新</Button>
        <Button size="small" type="primary" style="margin-left: 16px" ghost icon="ios-infinite" @click="resetFrom"
          >重置
        </Button>
      </Row>
    </Card>
    <Card style="margin-top: 16px" :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
  </Container>
</template>

<script>
import { Container } from '@/components'
import { errorMessage, noticeError } from '@/libs/util'
import { ApiEsDocList } from '@/api/es'
import { getRelativeTime, TimeTrans } from '@/libs/tools'
import { apiLogicFilterList } from '@/api/simplelogic'
import { ApiOpenClusterList, ApiOpenContainerList, ApiOpenNamespaceList, ApiOpenPodList } from '@/api/k8s/openapi'

export default {
  name: 'k8sPodOpLog',
  components: { Container },
  filters: {
    dateFormat: (msg) => {
      return TimeTrans(msg)
    },
    relativeTime: (msg) => {
      var t = new Date(msg)
      return getRelativeTime(t.getTime())
    }
  },
  data() {
    return {
      searchMap: {
        input: ''
      },
      filterMap: {
        username: '',
        clusterId: '',
        namespace: '',
        pod: '',
        container: ''
      },
      datetimeRage: {
        range_start: null,
        range_end: null
      },
      searchField: {
        index: 'pod.operation',
        range_field: 'created',
        range_start: '1660005562',
        range_end: '1660891407',
        sort_field: 'created',
        // sort_type: true = asc, false = desc
        sort_type: false,
        page: '1',
        size: '10',
        match_map: {},
        filter_map: {},
        search_map: {}
      },
      loading: {
        table: false,
        edit: true
      },
      columns: [
        // {
        //   title: 'UserID',
        //   key: 'userId',
        //   tooltip: true
        // },
        {
          title: 'Username',
          key: 'username',
          width: 120,
          tooltip: true
        },

        {
          title: 'ClusterName',
          key: 'cluster',
          tooltip: true
        },
        {
          title: 'Namespace',
          key: 'namespace',
          tooltip: true
        },
        {
          title: 'Pod',
          key: 'pod',
          tooltip: true
        },
        {
          title: 'Container',
          key: 'container',
          width: 130,
          tooltip: true
        },
        {
          title: 'Input',
          key: 'input',
          minWidth: 180,
          tooltip: true
        },
        {
          title: 'Created',
          key: 'created',
          width: 180,
          align: 'center',
          tooltip: true,
          render: (h, params) => {
            return h('div', {}, TimeTrans(params.row.created))
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      userList: [],
      clusterList: [],
      namespaceList: [],
      podList: [],
      containerList: [],
      selectDatetime: []
    }
  },
  methods: {
    onClearCluster() {
      this.filterMap.namespace = ''
      this.filterMap.pod = ''
      this.filterMap.container = ''
    },
    onClearNs() {
      this.filterMap.pod = ''
      this.filterMap.container = ''
    },
    onClearPod() {
      this.filterMap.container = ''
    },
    onDateTimeChange(value) {
      console.log(value)
      this.datetimeRage.range_start = new Date(value[0]).getTime() / 1000
      this.datetimeRage.range_end = new Date(value[1]).getTime() / 1000
    },
    onClusterChange(value) {
      this.namespaceList = []
      this.filterMap.namespace = ''
      this.filterMap.pod = ''
      this.filterMap.container = ''
      this.podList = []
      this.containerList = []
      if (value !== '') {
        this.fetchNamespaceList()
      }
    },
    onNamespaceChange(value) {
      this.podList = []
      this.filterMap.pod = ''
      this.filterMap.container = ''
      this.containerList = []
      if (value !== '') {
        this.fetchPodList()
      }
    },
    onPodChange(value) {
      this.containerList = []
      this.filterMap.container = ''
      if (value !== '') {
        this.fetchContainerList()
      }
    },
    resetFrom() {
      this.searchMap = {
        input: ''
      }
      this.filterMap = {
        username: '',
        clusterId: '',
        namespace: '',
        pod: '',
        container: ''
      }
      this.setDatetimeRange()
    },
    async reloadTable() {
      this.loading.table = true
      this.searchField.range_start = this.datetimeRage.range_start
      this.searchField.range_end = this.datetimeRage.range_end
      this.searchField.page = this.page.page
      this.searchField.size = this.page.size

      if (this.filterMap.username === '') {
        delete this.searchField.filter_map.username
      } else {
        this.searchField.filter_map.username = this.filterMap.username
      }

      if (this.filterMap.clusterId === '') {
        delete this.searchField.match_map.clusterId
      } else {
        this.searchField.match_map.clusterId = this.filterMap.clusterId
      }

      if (this.filterMap.namespace === '') {
        delete this.searchField.match_map.namespace
      } else {
        this.searchField.match_map.namespace = this.filterMap.namespace
      }

      if (this.filterMap.pod === '') {
        delete this.searchField.match_map.pod
      } else {
        this.searchField.match_map.pod = this.filterMap.pod
      }

      if (this.filterMap.container === '') {
        delete this.searchField.match_map.container
      } else {
        this.searchField.match_map.container = this.filterMap.container
      }

      // this.searchField.filter_map = this.filterMap
      if (this.searchMap.input === '') {
        this.searchField.search_map = {}
      } else {
        this.searchField.search_map = this.searchMap
      }
      await ApiEsDocList(this.searchField)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
      this.loading.table = false
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    },
    setDatetimeRange() {
      let now = new Date()
      this.datetimeRage.range_end = parseInt(now.getTime() / 1000)
      this.datetimeRage.range_start = parseInt(now.setDate(now.getDate() - 1) / 1000)
      let start = now.getTime()
      let end = new Date().getTime()
      this.selectDatetime = [start, end]
    },
    async fetchUserList() {
      await apiLogicFilterList({ field: 'username', table: 'tt_user' }).then((res) => {
        this.userList = res.data.data.list
      })
    },
    async fetchClusterList() {
      await ApiOpenClusterList(1)
        .then((res) => {
          this.clusterList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
    },
    async fetchNamespaceList() {
      await ApiOpenNamespaceList(this.filterMap.clusterId).then((res) => {
        this.namespaceList = res.data.data.data
      })
    },
    async fetchPodList() {
      await ApiOpenPodList(this.filterMap.clusterId, this.filterMap.namespace).then((res) => {
        this.podList = res.data.data.data
      })
    },
    async fetchContainerList() {
      await ApiOpenContainerList(this.filterMap.clusterId, this.filterMap.namespace, this.filterMap.pod).then((res) => {
        this.containerList = res.data.data.data
      })
    }
  },
  mounted() {
    this.setDatetimeRange()
    this.reloadTable()
    this.fetchUserList()
    this.fetchClusterList()
  }
}
</script>

<style scoped></style>
