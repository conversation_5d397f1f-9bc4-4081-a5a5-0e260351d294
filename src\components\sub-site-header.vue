<script lang="ts" setup>
import { CustomBreadCrumb, Space } from './'
</script>

<template>
  <div class="sub-site-wrapper">
    <Space :size="8" class="left">
      <CustomBreadCrumb showIcon />
      <slot name="left" />
    </Space>
    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.sub-site-wrapper {
  padding: 16px;
  height: 44px;
  background: #ffffff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* border-width: 1px 1px 0 1px; */
  position: fixed;
  top: 51px;
  left: 13.5vw;
  width: calc(86vw);
  z-index: 901;
  border-radius: 8px;
  box-shadow: 0 1px 8px #999999;
  .right {
    padding: 0px 12px 0 0;
  }
  .left {
    align-items: center;
  }
}
</style>
