import { useDelete, useGet, usePost } from '@/libs/service.request'
import Config from '@/config'
import { computed, getCurrentInstance, nextTick, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
import { relativeTime } from '@/libs/tools'
import { ConditionMap, Pod, PodInApi } from './type'

export default function usePodCardListService(props, emit) {
  const { proxy } = getCurrentInstance()

  const openAnalyzer = ref(false)
  const crashLogModalVisible = ref(false)
  const crashLogModalEntity = ref({})
  const fileModalVisible = ref(false)
  const fileEntity = ref({})

  const ephemeralContainerModalVisible = ref(false)
  const ephemeralContainerEntity = ref({})

  const portForwardModalVisible = ref(false)
  const portForwardEntity = ref({})

  const podYamlVisible = ref(false)
  const originPodList = ref<Pod[]>()
  const filterPodName = ref()

  const currentPod = computed<Pod>({
    get() {
      return props.value ?? {}
    },
    set(value) {
      emit('input', value)
    }
  })

  const apiMap = {
    Deployment: {
      getPodList: Config.Api.GetPodListWidthDeployment,
      deletePod: Config.Api.DeletePodWidthDeployment
    },
    StatefulSet: {
      getPodList: Config.Api.GetPodListWidthStatefulset,
      deletePod: Config.Api.DeletePodWidthStatefulset
    },
    Rayjob: {
      getPodList: Config.Api.GetPodListWidthRayjob
      // deletePod: Config.Api.DeletePodWidthRayjob
    }
  }
  const {
    data: podList,
    run: getPodList,
    loading: podListLoading
  } = useRequest(
    (isKeepValue?: boolean) => {
      return useGet<{ data: PodInApi[] }>(`${Config.Api.Base}${apiMap[props.kind].getPodList}`, {
        params: {
          ...props.entity
        }
      })
    },
    {
      manual: true,
      formatResult: (res) => {
        const data: Pod[] = res.data.data?.map((i) => {
          const conditionsMap: ConditionMap = {} as ConditionMap
          i.conditions?.map((i) => {
            conditionsMap[i.type] = i
          })
          return {
            ...i,
            age: relativeTime(i.creationTimestamp),
            conditions: conditionsMap
          }
        })
        originPodList.value = data
        return onFilterPodName()
      },
      onSuccess: (data, [isKeepValue]) => {
        if (!(isKeepValue && currentPod.value && data.find((i) => i.name === currentPod.value.name))) {
          currentPod.value = data[0]
        } else {
          // 更新currentPod内容为原对象的最新内容
          currentPod.value = data.find((i) => i.name === currentPod.value.name)
        }
      }
    }
  )

  const onFilterPodName = () => {
    let filterRes
    if (filterPodName.value) {
      filterRes = originPodList.value?.filter((i) => i.name.includes(filterPodName.value))
    } else {
      filterRes = originPodList.value
    }
    podList.value = filterRes
    return filterRes
  }

  const {
    data: analyzeData,
    run: handleOpenAnalyzer,
    loading: analyzeLoading
  } = useRequest(
    (podName) => {
      openAnalyzer.value = true
      return useGet(`${Config.Api.Base}${Config.Api.GetPodAnalyze}`, {
        params: {
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          name: podName
        }
      })
    },
    {
      manual: true,
      initialData: {},
      formatResult: (res) => res?.data?.data
    }
  )

  const onRebuildPod = async (name) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认重建: ${name}`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${apiMap[props.kind].deletePod}`, {
          params: {
            clusterId: props.entity.clusterId,
            namespace: props.entity.namespace,
            name: name
          }
        })
        if (res.success) {
          proxy.$Message.success(`重建${name}成功`)
          getPodList(true)
        }
        proxy.$Modal.remove()
      }
    })
  }

  const {
    data: containerMetrics,
    run: getContainerMetrics,
    loading: containerMetricsLoading
  } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetContainerMetrics}`, {
        params: {
          clusterId: props.entity.clusterId,
          namespace: props.entity.namespace,
          podName: currentPod.value.name
        },
        // pod 重建时，会出现一分钟内的 404，所以需要跳过错误处理
        skipErrorHandler: true
      })
    },
    {
      manual: true,
      initialData: {},
      formatResult: (res) => {
        const containerMetrics = {}
        if (res.success) {
          console.log(555555555, containerMetrics)
          res.data.data?.forEach((item) => {
            console.log(item)
            containerMetrics[item.containerName] = item
          })
        }

        return containerMetrics
      }
    }
  )

  const onChoosePod = (pod: Pod) => {
    currentPod.value = pod
  }

  const onOpenLog = (container) => {
    const route = proxy.$router.resolve({
      path: '/kubernetes/logs',
      query: {
        clusterId: props.entity.clusterId,
        namespace: props.entity.namespace,
        deployment: props.entity.name,
        cluster: props.entity.cluster,
        pod: currentPod.value.name,
        container: container
      }
    })
    window.open(route.href, '_blank')
  }

  const onBashClick = (container) => {
    const r = proxy.$router.resolve({
      path: '/pod-console',
      query: {
        clusterId: props.entity.clusterId,
        namespace: props.entity.namespace,
        cluster: props.entity.cluster,
        pod: currentPod.value.name,
        container: container,
        priority: 'true'
      }
    })
    window.open(r.href, '_blank')
  }

  const handleProxyOpenLog = async (container, logLevel) => {
    const res = await usePost(`${Config.Api.Base}${Config.Api.SetDeployPodSidecarLogLevel}`, {
      clusterId: props.entity.clusterId,
      namespace: props.entity.namespace,
      pod: currentPod.value.name,
      level: logLevel
    })
    if (res.success) {
      proxy.$Message.success('设置日志级别成功')
    }
  }

  const openCrashLog = async (container) => {
    crashLogModalVisible.value = true
    crashLogModalEntity.value = { ...props.entity, podName: currentPod.value.name, container }
  }

  const onFileModalOpen = (container) => {
    fileEntity.value = {
      container,
      pod: currentPod.value.name,
      clusterId: props.entity.clusterId,
      namespace: props.entity.namespace
    }
    fileModalVisible.value = true
  }

  const handleOpenEphemeralContainerModal = (container) => {
    ephemeralContainerEntity.value = {
      container,
      pod: currentPod.value.name,
      clusterId: props.entity.clusterId,
      cluster: props.entity.cluster,
      namespace: props.entity.namespace
    }
    ephemeralContainerModalVisible.value = true
  }

  const onOpenPortForwardModal = (relativePodName) => {
    portForwardEntity.value = {
      relativePodName,
      clusterId: props.entity.clusterId,
      cluster: props.entity.cluster,
      namespace: props.entity.namespace,
      relativeName: props.entity.name
    }
    portForwardModalVisible.value = true
  }

  const { data: podYaml, run: getPodYaml } = useRequest(
    () => {
      podYamlVisible.value = true
      return useGet(`${Config.Api.Base}${Config.Api.ResourceV1}${Config.Api.GetLatestYaml}`, {
        params: {
          cluster_id: props.entity.clusterId,
          namespace: props.entity.namespace,
          resource_name: currentPod.value.name,
          kind: 'Pod'
        }
      })
    },
    {
      manual: true,
      initialData: '',
      formatResult: (res) => res.data.data
    }
  )

  watch(
    () => props.entity,
    () => {
      props.entity?.name && getPodList()
    },
    { deep: true, immediate: true }
  )

  watch(currentPod, () => {
    currentPod.value.name && getContainerMetrics()
  })

  return {
    currentPod,
    podList,
    getPodList,
    podListLoading,

    openAnalyzer,
    handleOpenAnalyzer,
    analyzeData,
    analyzeLoading,
    onRebuildPod,
    onChoosePod,
    onOpenLog,

    containerMetrics,
    containerMetricsLoading,
    getContainerMetrics,
    onBashClick,
    handleProxyOpenLog,
    openCrashLog,
    crashLogModalVisible,
    crashLogModalEntity,
    onFileModalOpen,
    fileEntity,
    fileModalVisible,
    handleOpenEphemeralContainerModal,
    ephemeralContainerEntity,
    ephemeralContainerModalVisible,
    podYaml,
    getPodYaml,

    podYamlVisible,
    onOpenPortForwardModal,
    portForwardModalVisible,
    portForwardEntity,
    onFilterPodName,
    filterPodName
  }
}
