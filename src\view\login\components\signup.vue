<template>
  <div>

    <Form ref="signUpForm" :model="form" :rules="rules" @keydown.enter.native="handleOk">
      <FormItem prop="username" label="Username">
        <Input v-model="form.username">
        </Input>
      </FormItem>
      <FormItem prop="nickName" label="Nick name">
        <Input v-model="form.nickName">
        </Input>
      </FormItem>
      <FormItem prop="password" label="Password">
        <Input type="password" v-model="form.password">
        </Input>
      </FormItem>
      <FormItem prop="confirmPassword" label="Confirm password">
        <Input type="password" v-model="form.passwordConfirm">
        </Input>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import {ApiSignUp} from "@/api/user";
import {errorMessage} from "@/libs/util";

export default {
  name: "SignUp",
  props: {
    ok: {
      type: <PERSON>olean,
      default: false,
    }
  },
  watch: {
    ok(nVal) {
      console.log(nVal)
      if (nVal === true) {
        this.submit()
      }
    },
  },
  data() {
    return {
      form: {
        username: '',
        nickName: '',
        password: '',
        confirmPassword: '',
      },
      rules: {
        username: [
          {required: true, message: 'Cannot be empty', trigger: 'blur'}
        ],
        nickName: [
          {required: true, message: 'Cannot be empty', trigger: 'blur'}
        ],
        password: [
          {required: true, message: 'Cannot be empty', trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: 'Cannot be empty', trigger: 'blur'}
        ],
      }
    }
  },
  methods: {

  }

}
</script>

<style scoped>

</style>
