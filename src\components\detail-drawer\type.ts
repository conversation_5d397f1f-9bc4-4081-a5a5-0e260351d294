import { ExtractPropTypes, PropType } from 'vue'

export function detailDrawerProps() {
  return {
    /* 标题 */
    title: String,
    /** 资源类型(api) */
    resourceType: String,
    /** 表单打开 / 关闭状态 */
    value: Boolean,
    /** 资源实体 */
    resourceEntity: Object as PropType<ResourceEntity>,
    /** 请求表单数据的格式化回调 */
    onInitFormat: Function
  }
}

export interface ResourceEntity {
  namespace: string // K8SInstance
  clusterId: string // K8SInstance
  resourceName?: string // 资源唯一id
}

export type DetailDrawerProps = Partial<ExtractPropTypes<ReturnType<typeof detailDrawerProps>>>
