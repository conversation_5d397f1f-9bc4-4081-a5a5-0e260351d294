@font-face {
  font-family: "iconfont"; /* Project id 4325389 */
  src: url('iconfont.woff2?t=1703834559102') format('woff2'),
       url('iconfont.woff?t=1703834559102') format('woff'),
       url('iconfont.ttf?t=1703834559102') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-rongqizu:before {
  content: "\e604";
}

.icon-conversation1:before {
  content: "\e725";
}

.icon-info2:before {
  content: "\e61d";
}

.icon-icon-question:before {
  content: "\e66b";
}

.icon-home:before {
  content: "\e656";
}

.icon-ziyuan:before {
  content: "\e617";
}

.icon-map:before {
  content: "\e607";
}

.icon-Deployment:before {
  content: "\e621";
}

.icon-docker:before {
  content: "\e616";
}

.icon-istio:before {
  content: "\e600";
}

.icon-kubernetes1:before {
  content: "\ebee";
}

