import { ref, getCurrentInstance, watch, set, computed } from 'vue'

import Config from '@/config'
import { ActionType, TableRequest } from '@/components/pro-table'
import { useDelete, useGet, PageList, usePost } from '@/libs/service.request'
import { YamlHistoryParams } from '@/components/yaml'

import { Gateway } from './type'
import { EnumFormStatus, ResourceEntity } from '@/components/resource-form'
import { EnumArrayObjectModalStatus } from '@/components/pro-form'
import { useRequest } from 'vue-request'

export const SERVICE_ENTRY = { resource: 'gateways', group: 'networking.istio.io', version: 'v1beta1' }

interface Props {
  initFlag: boolean
  resourceName: string
  namespace: string
  clusterId: string
}

export default function useGatewayService(props: Props) {
  const { proxy } = getCurrentInstance()

  const refObject = {
    tableRef: ref<ActionType | null>(null)
  }
  const viewYamlVisible = ref(false)
  const yamlHistoryParams = ref<YamlHistoryParams>()
  const viewDetailVisible = ref(false)
  const formVisible = ref(false)
  const formStatus = ref<EnumFormStatus>(EnumFormStatus.Blank)
  const formEntity = ref<ResourceEntity>()
  const yamlInitData = ref<string>()

  const credentialNameModal = ref<{
    visible: boolean
    name?: string
    hosts?: string[]
    successCallback?: (name) => void
  }>({
    visible: false
  })

  const virtualServiceModal = ref<{
    visible: boolean
    gatewayName?: any
  }>({
    visible: false
  })

  const formInitData = computed(() => ({
    namespace: props.namespace,
    name: 'default',
    egress: {
      data: []
    },
    workloadSelector: {
      data: []
    }
  }))

  const onCreate = () => {
    console.log('onCreate')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Blank
    formEntity.value = {
      namespace: props.namespace,
      clusterId: props.clusterId,
      ...SERVICE_ENTRY
    }
    yamlInitData.value = `apiVersion: networking.istio.io/v1beta1 
      kind: Gateway
      metadata:
        name: 必须修改
        namespace: ${props.namespace}
      spec:
    `
  }

  const onDelete = (record: Gateway) => {
    proxy.$Modal.confirm({
      title: '提示',
      content: `是否确认删除 ${record.name}？`,
      loading: true,
      onOk: async () => {
        const res = await useDelete(`${Config.Api.Base}${Config.Api.DeleteGateway}`, {
          params: {
            namespace: props.namespace,
            clusterId: props.clusterId,
            resourceName: record.name
          }
        })
        if (res.success) {
          refObject.tableRef.value?.reload()
          proxy.$Modal.remove()
        } else {
          proxy.$Modal.remove()
        }
      }
    })
  }

  const onEdit = (record: Gateway) => {
    console.log('onEdit')
    formVisible.value = true
    formStatus.value = EnumFormStatus.Edit
    formEntity.value = {
      ...SERVICE_ENTRY,
      namespace: props.namespace,
      clusterId: props.clusterId,
      resourceName: record.name
    }
  }

  const onEditVS = (record: Gateway) => {
    virtualServiceModal.value = {
      visible: true,
      gatewayName: record.name
    }
  }
  const onViewYaml = (record: Gateway) => {
    yamlHistoryParams.value = {
      kind: 'Gateway',
      uuid: record.uid
    }
    formEntity.value = {
      clusterId: props.clusterId,
      namespace: record.namespace,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
    viewYamlVisible.value = true
  }

  const onViewDetail = async (record: Gateway) => {
    console.log('onViewDetail')
    viewDetailVisible.value = true
    formEntity.value = {
      namespace: props.namespace,
      clusterId: props.clusterId,
      ...SERVICE_ENTRY,
      resourceName: record.name
    }
  }

  const onInitDetailFormat = (data) => {
    return {
      ...data,
      labels: Object.entries(data?.labels || {})?.map(([key, value]) => ({ key, value }))
    }
  }

  const getTableData: TableRequest = async (params) => {
    const res = await useGet<PageList<Gateway[]>>(
      `${Config.Api.Base}${Config.Api.GetGatewayTableData}?searchKey=${params.searchKey}&searchValue=${
        params.searchValue ?? ''
      }&clusterId=${props.clusterId}&namespace=${props.namespace}&page=${params.pageIndex}&size=${
        params.pageSize
      }&resourceName=${props.resourceName}`
    )
    return {
      success: res.success,
      total: res.data?.total ?? 0,
      data: res.data?.list ?? []
    }
  }

  const onSubmitServerForm = (data, type, record, index) => {
    if (data.servers?.length) {
      data.servers?.splice(index, type === EnumArrayObjectModalStatus.Edit ? 1 : 0, record)
    } else {
      data.servers = [record]
    }
  }

  const onSubmitSuccess = () => {
    refObject.tableRef.value?.reload()
  }

  const { data: hostList } = useRequest(
    () => {
      return useGet(`${Config.Api.Base}${Config.Api.GetGatewayHost}`)
    },
    {
      formatResult: (res) => {
        return res.data.data?.map((i) => ({ label: i, value: i }))
      }
    }
  )

  const { data: tlsCredentialNameList, run: getTlsCredentialNameList } = useRequest(
    () => {
      return useGet(
        `${Config.Api.Base}${Config.Api.GetTlsModeList}?namespace=${props.namespace}&clusterId=${props.clusterId}`
      )
    },
    {
      formatResult: (res) => {
        return res.data.data?.map((i) => ({ label: i, value: i }))
      },
      manual: true
    }
  )

  const onOpenCreateCredentialNameModal = async (record) => {
    if (!record.hosts?.length) {
      proxy.$Message.warning('请先将Host（目标服务）补充完整！')
      return
    }
    const res = await usePost(
      `${Config.Api.Base}${Config.Api.GenerateCredentialName}`,
      {
        namespace: props.namespace,
        clusterId: props.clusterId,
        hosts: record.hosts
      },
      { skipLoading: false }
    )
    if (res.success && res.data.data) {
      credentialNameModal.value = {
        visible: true,
        name: res.data.data,
        hosts: record.hosts,
        successCallback: (name) => {
          set(record.tls.data.credentialName, 'data', name)
        }
      }
    } else {
      proxy.$Message.warning('您所选择主机二级域不相同, 不符合规范！')
    }
  }
  const onCreateCredentialName = async () => {
    const name = credentialNameModal.value.name
    if (name) {
      const res = await usePost(
        `${Config.Api.Base}${Config.Api.CreateCredentialName}`,
        {
          namespace: props.namespace,
          clusterId: props.clusterId,
          hosts: credentialNameModal.value.hosts,
          certificateName: name
        },
        { skipLoading: false }
      )
      if (res.success) {
        proxy.$Message.success('创建证书名成功')
        tlsCredentialNameList.value?.unshift({
          value: name,
          label: name
        })
        credentialNameModal.value.successCallback(name)
        credentialNameModal.value = { visible: false }
      }
    } else {
      proxy.$Message.warning('请将证书名补充完整！')
    }
  }
  watch(
    () => props.initFlag,
    () => {
      if (props.initFlag) {
        getTlsCredentialNameList()
        refObject.tableRef.value?.reload()
      }
    },
    {
      immediate: true
    }
  )

  return {
    getTableData,
    refObject,
    onCreate,
    onEdit,
    onDelete,
    onViewYaml,
    viewYamlVisible,
    yamlHistoryParams,
    formVisible,
    formEntity,
    formStatus,
    yamlInitData,
    formInitData,
    onSubmitServerForm,
    onSubmitSuccess,
    onViewDetail,
    viewDetailVisible,
    onInitDetailFormat,
    tlsCredentialNameList,
    credentialNameModal,
    virtualServiceModal,
    onOpenCreateCredentialNameModal,
    onCreateCredentialName,
    onEditVS,
    hostList
  }
}
