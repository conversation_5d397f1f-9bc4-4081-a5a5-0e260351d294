export enum EnumComponentType {
  AssociatedDeployment = 'AssociatedDeployment', // 关联deployment模式
  AssociatedStatefulset = 'AssociatedStatefulset', // 关联Statefulset模式
  Independent = 'Independent', // 独立模式
  Rayjob = 'Rayjob' // rayjob模式
}
export interface Service {
  uuid: string
  name: string
  type: string
  cluster_ip: string
  lb_ip: string
  ports: string
  created: string
}

export interface ServiceYaml {
  metadata: Metadata
  spec: Spec
  status: { loadBalancer: { ingress: { ip: string }[] } }
}
export interface Spec {
  clusterIP: string
  externalTrafficPolicy?: string
  ports: Port[]
  selector: Record<string, string>
  sessionAffinity: string
  type: string
}

export interface Port {
  name: string
  nodePort: number
  port: number
  protocol: string
  targetPort: number
}

export interface Metadata {
  annotations: Annotations
  creationTimestamp: string
  finalizers?: string[]
  labels: Labels
  managedFields?: ManagedField[]
  name: string
  namespace: string
  resourceVersion: string
  selfLink: string
  uid: string
}

export interface Annotations {
  'field.cattle.io/publicEndpoints'?: string
  'kubectl.kubernetes.io/last-applied-configuration'?: string
  'meta.helm.sh/release-name': string
  'meta.helm.sh/release-namespace': string
  'service.beta.kubernetes.io/ucloud-load-balancer-id-provision'?: string
  'service.beta.kubernetes.io/ucloud-load-balancer-type'?: string
}

export interface Labels {
  app: string
  'app.kubernetes.io/managed-by': string
  'app.kubernetes.io/tt-monitor-of'?: string
  chart: string
  cluster_id?: string
  env?: string
  heritage: string
  'io.cattle.field/appId': string
  lang?: string
  release: string
  type?: string
  uuid?: string
}

export interface ManagedField {
  apiVersion: string
  fieldsType: string
  fieldsV1: Record<string, string>
  manager: string
  operation: string
  time: string
}
