<script lang="ts" setup>
import { computed, PropType } from 'vue'
import { Entity, EnumComponentType } from './type'
import { PortForward } from './'

const props = defineProps({
  value: {
    type: Boolean,
    default: false
  },
  type: {
    type: String as PropType<EnumComponentType>,
    default: EnumComponentType.Independent
  },
  entity: { type: Object as PropType<Entity>, default: () => ({}) }
})
const emit = defineEmits(['input'])

const visible = computed({
  get() {
    return props.value
  },
  set(value) {
    emit('input', value)
  }
})
</script>

<template>
  <Modal title="端口转发" width="65" v-model="visible" footer-hide>
    <PortForward v-bind="props" />
  </Modal>
</template>

<style lang="less" scoped></style>
