import { Ellipsis } from '@/components'
import { relativeTime } from '@/libs/tools'

export const getTableColumns = (isAssociated?) => [
  {
    title: 'Name',
    key: 'name',
    slot: 'name'
  },
  ...(isAssociated
    ? [
        {
          title: 'Namespace',
          key: 'namespace',
          width: 200,
          tooltip: true,
          tooltipTheme: 'light'
        }
      ]
    : []),
  {
    title: 'Host',
    key: 'host',
    tooltip: true,
    tooltipTheme: 'light'
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    width: 160,
    render: (h, params) => {
      return h(Ellipsis, relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 200,
    align: 'center'
  }
]

export const TRAFFIC_POLICIES_TABLE_COLUMNS = [
  { title: '策略名称', key: 'name' },
  {
    title: '策略种类',
    key: 'category',
    render: (h, params) => {
      return h(Ellipsis, params.row.loadBalancer?.switchCtl ? 'loadBalancer' : '-')
    }
  },
  {
    title: '标签',
    key: 'labels',
    width: '240',
    slot: 'table-labels'
  }
]

export const RELATED_POD_COLUMNS = [
  {
    title: 'Namespace',
    key: 'namespace',

    width: 150,
    tooltip: true
  },
  {
    title: 'Name',
    key: 'name',

    width: 200,
    tooltip: true
  },
  {
    title: 'Status',
    key: 'status',

    width: 90,
    tooltip: true
  },
  {
    title: 'Age',
    key: 'creationTimestamp',
    tooltip: true,

    width: 100,
    render: (h, params) => {
      return h('div', relativeTime(params.row.creationTimestamp))
    }
  },
  {
    title: 'Ops',
    key: 'ops',
    slot: 'ops',
    width: 90,
    align: 'center'
  }
]
export const DETAIL_BASE_INFO = [
  {
    title: 'Name',
    key: 'name',
    render: (h, data) => {
      return h('b', data.name)
    }
  },
  {
    title: 'Namespace',
    key: 'namespace'
  },

  {
    title: 'Host',
    key: 'host'
  },
  {
    title: 'Labels',
    key: 'labels',
    slot: 'labels'
  }
]
