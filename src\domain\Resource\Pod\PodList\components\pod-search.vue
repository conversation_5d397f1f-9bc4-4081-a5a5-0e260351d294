<template>
  <div class="component-pod-search">
    <Card :bordered="false">
      <Row style="margin-bottom: 16px" :gutter="10">
        <Col span="4">
          <Select
            v-model="selectStatus"
            clearable
            @on-clear="handleFilter"
            @on-select="handleFilter"
            style="width: 100%"
            placeholder="过滤状态"
          >
            <Option v-for="item in statusFilter" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </Col>
        <Col span="20">
          <Input
            ref="input"
            v-model="searchData"
            clearable
            placeholder="搜索 Pod名"
            search
            style="width: 100%"
            @on-search="handleSearch"
            @on-clear="handleClear"
          />
        </Col>
      </Row>
      <Row type="flex" align="middle">
        <Col flex="24">
          <Button size="small" type="primary" icon="md-add" @click="showAddYaml"> 创建 </Button>
          <Button style="margin-left: 16px" size="small" type="primary" ghost icon="md-refresh" @click="refreshTable">
            刷新
          </Button>
        </Col>
      </Row>
    </Card>
    <Drawer title="新建 YAML" width="50" v-model="openYaml" :mask-closable="false">
      <yaml style="max-height: 87%; overflow: auto" v-model="currentYaml" ref="refYaml"></yaml>
      <div class="drawer-footer">
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleSubmit">确认</Button>
      </div>
    </Drawer>
  </div>
</template>

<script>
import { Yaml } from '@/components'

import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { ApiCreatePod } from '@/api/k8s/pod'
import { nextTick } from 'vue'
import useSingleK8SService from '@/libs/useSingleK8SService'

export default {
  name: 'component-pod-search',
  props: ['podList'],
  components: {
    Yaml
  },
  inject: ['main'],
  setup() {
    const { K8SKey } = useSingleK8SService()
    return { K8SKey }
  },
  computed: {
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    }
  },
  data() {
    return {
      selectStatus: '',
      statusFilter: [
        {
          label: 'Running',
          value: 'Running'
        },
        {
          label: 'Pending',
          value: 'Pending'
        },
        {
          label: 'Succeeded',
          value: 'Succeeded'
        },
        {
          label: 'Failed',
          value: 'Failed'
        },
        {
          label: 'Unknown',
          value: 'Unknown'
        }
      ],
      searchData: this.$route.query.name ?? '',
      currentYaml: '',
      openYaml: false
    }
  },
  methods: {
    handleFilter(status) {
      if (status === undefined) {
        this.refreshTable()
        this.main.selectStatus = ''
        return
      }
      this.main.selectStatus = status.value
      this.$emit('reloadTable', { search: this.searchData, status: status.value })
    },
    handleSearch(searchText) {
      this.main.searchData = searchText
      this.$emit('reloadTable', { search: searchText, status: this.selectStatus })
    },
    handleClear() {
      this.$emit('reloadTable')
    },
    // 新建yaml
    showAddYaml() {
      this.openYaml = true
      this.currentYaml = 'apiVersion: v1\nkind: Pod\nmetadata:\nspec:'
      this.$nextTick(() => {
        this.$refs.refYaml.refresh()
      })
    },
    // 刷新列表
    refreshTable() {
      this.$emit('reloadTable', {
        page: this.podList.page,
        size: this.podList.size,
        search: this.searchData,
        status: this.selectStatus
      })
    },
    // 新建pod
    async handleSubmit() {
      try {
        const payload = {
          cluster_id: this.currentClusterId,
          yaml: this.currentYaml
        }
        await ApiCreatePod(payload)
        this.handleCancel()
        this.refreshTable()
        noticeSucceed(this, '新建成功')
      } catch (error) {
        noticeError(this, `新建失败, ${errorMessage(error)}`)
      }
    },
    handleCancel() {
      this.openYaml = false
    }
  },
  mounted() {
    let podStatus = this.$route.query.status
    if (podStatus !== undefined) {
      this.selectStatus = podStatus
    }
    this.$refs.input.focus({
      cursor: 'start'
    })
  },
  watch: {
    K8SKey() {
      this.refreshTable()
    }
  }
}
</script>

<style scoped>
.drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 16px 16px;
  text-align: right;
  background: #fff;
}
</style>
