.cm-s-darcula {
  font-family: Consolas, Menlo, Monaco, '<PERSON><PERSON>', 'Liberation Mono', 'DejaVu Sans Mono',
    'Bitstream Vera Sans Mono', 'Courier New', monospace, serif;
}
.cm-s-darcula.CodeMirror {
  background: #333333;
  color: #ffffffb5;
  /*   color: #35b392; */
}

.cm-s-darcula span.cm-meta {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-number {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-keyword {
  color: #35b392;
  line-height: 1em;
  font-weight: bold;
}
.cm-s-darcula span.cm-def {
  color: #ffffffb5;
  font-style: italic;
}
.cm-s-darcula span.cm-variable {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-variable-2 {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-variable-3 {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-type {
  color: #ffffffb5;
  font-weight: bold;
}
.cm-s-darcula span.cm-property {
  color: #35b392;
}
.cm-s-darcula span.cm-operator {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-string {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-string-2 {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-comment {
  color: #ffffffb5;
  font-style: italic;
}
.cm-s-darcula span.cm-link {
  color: #35b392;
}
.cm-s-darcula span.cm-atom {
  color: #35b392;
}
.cm-s-darcula span.cm-error {
  color: #bc3f3c;
}
.cm-s-darcula span.cm-tag {
  color: #ffffffb5;
  font-weight: bold;
  font-style: italic;
  text-decoration: underline;
}
.cm-s-darcula span.cm-attribute {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-qualifier {
  color: #ffffffb5;
}
.cm-s-darcula span.cm-bracket {
  color: #ffffffb5;
}

.cm-s-darcula span.cm-special {
  color: #ff9e59;
}
.cm-s-darcula span.cm-matchhighlight {
  color: #ffffff;
  background-color: rgba(50, 89, 48, 0.7);
  font-weight: normal;
}
.cm-s-darcula span.cm-searching {
  color: #ffffff;
  background-color: rgba(61, 115, 59, 0.7);
  font-weight: normal;
}

.cm-s-darcula .CodeMirror-cursor {
  border-left: 1px solid #a9b7c6;
}
.cm-s-darcula .CodeMirror-activeline-background {
  background: #323232;
}
.cm-s-darcula .CodeMirror-gutters {
  background: #333333;
  border-right: 1px solid #313335;
}
.cm-s-darcula .CodeMirror-guttermarker {
  color: #ffee80;
}
.cm-s-darcula .CodeMirror-guttermarker-subtle {
  color: #d0d0d0;
}
.cm-s-darcula .CodeMirrir-linenumber {
  color: #606366;
}
.cm-s-darcula .CodeMirror-matchingbracket {
  background-color: #3b514d;
  color: #ffef28 !important;
  font-weight: bold;
}

.cm-s-darcula div.CodeMirror-selected {
  background: red;
}

.CodeMirror-hints.darcula {
  font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
  color: #9c9e9e;
  background-color: #3b3e3f !important;
}

.CodeMirror-hints.darcula .CodeMirror-hint-active {
  background-color: #494d4e !important;
  color: #9c9e9e !important;
}

.cm-s-darcula div.CodeMirror-selected {
  background: rgb(0 116 254);
  color: #f9f9f9;
}
.cm-s-darcula .CodeMirror-line::selection,
.cm-s-darcula .CodeMirror-line > span::selection,
.cm-s-darcula .CodeMirror-line > span > span::selection {
  background: rgb(0 116 254);
  color: #f9f9f9;
}
.cm-s-darcula .CodeMirror-line::-moz-selection,
.cm-s-darcula .CodeMirror-line > span::-moz-selection,
.cm-s-darcula .CodeMirror-line > span > span::-moz-selection {
  background: rgb(0 116 254);
  color: #f9f9f9;
}
