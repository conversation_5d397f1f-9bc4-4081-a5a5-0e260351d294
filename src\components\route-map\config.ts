import { EnumStatus, EnumToUnion, RouterItem } from './type'

export const StatusColors = {
  [EnumStatus.error]: '#d8001b',
  [EnumStatus.succeed]: '#19be6b',
  [EnumStatus.warning]: '#f59b22'
}

export enum EnumNodeType {
  Domain = 'Domain',
  Gateway = 'Gateway',
  VirtualService = 'VirtualService',
  ['Http.Route'] = 'Http.Route',
  DestinationRule = 'DestinationRule',
  Service = 'Service',
  Pod = 'Pod'
}

export const EnumNodeTypeLabel: Record<EnumNodeType, string> = {
  [EnumNodeType.Domain]: 'Domain',
  [EnumNodeType.Gateway]: 'GW',
  [EnumNodeType.VirtualService]: 'VS',
  [EnumNodeType['Http.Route']]: 'Http.Route',
  [EnumNodeType.DestinationRule]: 'DR',
  [EnumNodeType.Service]: 'Service',
  [EnumNodeType.Pod]: 'Pod'
}

const LinkUrl: Record<EnumNodeType, string> = {
  [EnumNodeType.Domain]: '',
  [EnumNodeType.Gateway]: '/gateway/gateway-management',
  [EnumNodeType.VirtualService]: '/kubernetes/resource/mesh/virtualservice',
  [EnumNodeType['Http.Route']]: '',
  [EnumNodeType.DestinationRule]: '/kubernetes/resource/mesh/destinationrule',
  [EnumNodeType.Service]: '/kubernetes/resource/namespace/services',
  [EnumNodeType.Pod]: '/kubernetes/resource/namespace/pod'
}

export const getLinkUrl = (item: RouterItem) => {
  const { nodeType, name, clusterId, namespace, clusterName } = item
  return LinkUrl[nodeType]
    ? `${window.location.origin}${LinkUrl[nodeType]}?name=${name}&clusterId=${clusterId}&cluster=${clusterName}&namespace=${namespace}`
    : window.location.origin
}
