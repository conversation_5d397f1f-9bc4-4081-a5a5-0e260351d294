<script lang="ts" setup>
import { formItemProps } from './pro-from-item/type'

import { ProFormItem } from '@/components/pro-form'
import { computed, PropType, set } from 'vue'
import Space from '@/components/space'
import { useSelectQueryClear } from '@/libs/useTools'

const props = defineProps({
  ...formItemProps(),
  placeholder: String, // 占位内容
  options: {
    type: Array as PropType<{ value: string; label: string; [key: string]: any }[]>,
    default: function () {
      return []
    }
  }, // 下拉选择器内容
  allowCreate: Boolean,
  multiple: Boolean,
  afterChange: Function as PropType<(val, isInit) => void>
})

const options = computed(() => props.options)
const tempOptions = computed(() => {
  if (!value.value) return options.value
  else if (props.multiple) {
    const extraValue = []
    value.value?.forEach((item) => {
      if (!options.value?.filter((i) => i.value === item)?.length) {
        extraValue.push(item)
      }
    })
    return [...options.value, ...extraValue?.map((i) => ({ label: i, value: i }))]
  } else {
    if (!options.value?.filter((i) => i.value !== value.value)) {
      return [...options.value, { label: value.value, value: value.value }]
    } else {
      return options.value
    }
  }
})
const { selectRef, onOpenChangeToClearQuery } = useSelectQueryClear()
const onOpenChange = (open) => {
  props.multiple && onOpenChangeToClearQuery(open)
}
const onCreate = (val) => {
  options.value?.push({
    value: val,
    label: val,
    isInit: true
  })
}

const value = computed({
  get: () => {
    return !props.mode ? props.data[props.name] : props.data[props.name]?.data
  },
  set: (val) => {
    !props.mode ? set(props.data, props.name, val) : set(props.data[props.name], 'data', val)

    const isInit = !!options.value?.filter((i) => i.value === val)?.[0]?.isInit
    props.afterChange && props.afterChange?.(val, isInit)
  }
})
</script>

<template>
  <ProFormItem v-bind="props">
    <template #default>
      <span v-if="props.readonly">
        <Space v-if="props.multiple" style="align-items: center">
          <Tag color="primary" v-for="item in value" :key="item">{{ item }}</Tag>
        </Space>
        <Tag v-else color="primary">{{ item }}</Tag>
      </span>
      <Select
        v-else
        filterable
        transfer
        v-model="value"
        :placeholder="props.placeholder || `请${props.allowCreate ? '输入或' : ''}选择${props.label}`"
        :allow-create="props.allowCreate"
        @on-create="onCreate"
        :multiple="props.multiple"
        ref="selectRef"
        @on-open-change="onOpenChange"
      >
        <Option v-for="item in tempOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
      </Select>
    </template>
  </ProFormItem>
</template>

<style lang="less" scoped></style>
