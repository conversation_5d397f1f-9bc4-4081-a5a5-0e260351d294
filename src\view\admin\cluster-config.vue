<template>
  <Container>
    <div>
      <Alert show-icon type="info">
        <b>集群上线操作</b>
        <p slot="desc">
          1. 点击 "同步集群数据" 按钮从 CMDB 同步基础数据；<br />
          2. 从云商拷贝/下载 kubeconfig, 获取 admin 权限的 kubeconfig；<br />
          3. 【操作】【配置】 补全 kubeconfig；<br />
          4. 点击 "生成集群权限" 让系统产生集群的资源权限;<br />
          5. 重启牵星服务。(部署完istio组件，也需要重启)
        </p>
      </Alert>
      <Alert show-icon>
        <b>集群下线操作</b>
        <p slot="desc">
          1. 选择需要下线的集群，点击【删除集群权限】；<br />
          2. 修改需要下线的集群配置，删除其kubeconfig内容，无需删除集群记录；<br />
          3. 点击 【应用配置】重启informer连接器，期间会影响整个平台对容器资源的操作；
        </p>
      </Alert>
    </div>
    <Card :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            ref="input"
            v-model="page.s_name"
            clearable
            placeholder="搜索 集群名"
            search
            style="width: 100%"
            @on-search="handleSearch"
            @on-clear="handleClear"
          />
        </Col>
      </Row>
      <Row type="flex">
        <Col span="12">
          <Button size="small" type="primary" ghost icon="md-refresh" @click="ReloadTable" style="margin-left: 16px">
            刷新
          </Button>
        </Col>

        <Col span="12" class="custom-align-right">
          <!--          <a style="color: #2a7cc3; cursor: pointer; font-size: 12px; align-self: center; margin-left: 16px;">了解相关功能-->
          <!--            <Icon type="md-help-circle"/>-->
          <!--          </a>-->
          <Button
            type="primary"
            ghost
            size="small"
            icon="md-bulb"
            @click="GenerateClusterAuthData"
            style="margin-left: 16px"
            >生成集群权限
          </Button>
          <Button
            type="error"
            ghost
            size="small"
            icon="md-close"
            @click="ClearClusterAuthData"
            style="margin-left: 16px"
            >删除集群权限
          </Button>
          <Button type="primary" size="small" @click="SyncFromCmdb" icon="md-cloud-download" style="margin-left: 16px"
            >同步集群数据
          </Button>
          <Button type="success" size="small" @click="RestartConnector" icon="ios-play" style="margin-left: 16px"
            ><b>应用配置</b>（重连ApiServer）
          </Button>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 16px" :bordered="false">
      <Table
        size="small"
        @on-selection-change="handleClusterTableSelectionChange"
        :loading="loading.table"
        :columns="columns"
        :data="data"
      ></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <Modal v-model="editModal" :title="editModalTitle" :loading="loading.edit" @on-ok="editTable">
      <Form :model="editModalForm" ref="editModalForm" label-position="top" :rules="editModalRule">
        <FormItem label="Name" prop="name">
          <Input
            v-if="editModalState === 'create'"
            v-model="editModalForm.name"
            placeholder="Enter something..."
          ></Input>
          <Tag v-else color="primary">{{ editModalForm.name }}</Tag>
        </FormItem>
        <FormItem label="RegionId" prop="regionId">
          <Input v-model="editModalForm.regionId" placeholder="Enter something..."></Input>
        </FormItem>
        <FormItem label="Description" prop="desc">
          <Input v-model="editModalForm.desc" placeholder="Enter something..."></Input>
        </FormItem>
        <FormItem label="KubeConfig" prop="kubeconfig">
          <Input v-model="editModalForm.kubeconfig" :rows="8" type="textarea" placeholder="Enter something..."></Input>
        </FormItem>
      </Form>
    </Modal>
    <div>
      <Drawer title="集群详情" :closable="false" width="70" v-model="ClusterModal">
        <Spin fix v-if="loading.clusterModalSpin"></Spin>
        <Row :gutter="14">
          <Col span="12">
            <Card style="height: 388px">
              <h4>详情</h4>
              <Row type="flex" :gutter="20" style="margin-top: 20px">
                <Col span="10" class="info-key">名称</Col>
                <Col span="14" class="info-value">{{ clusterInfo.name }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">导入时间</Col>
                <Col span="14" class="info-value">{{ clusterInfo.createdAt }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">数据源</Col>
                <Col span="14" class="info-value">{{ clusterInfo.dataResource }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">状态</Col>
                <Col span="14" class="info-value">
                  <Tag color="success" v-if="clusterInfo.isReady === 1">就绪</Tag>
                  <Tag color="error" v-else>未就绪</Tag>
                </Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">描述</Col>
                <Col span="14" class="info-value">{{ clusterInfo.desc }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">GitVersion</Col>
                <Col span="14" class="info-value">{{ clusterInfo.version }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">BuildData</Col>
                <Col span="14" class="info-value">{{ clusterInfo.buildData }}</Col>
              </Row>
              <Row type="flex" :gutter="20" style="margin-top: 16px">
                <Col span="10" class="info-key">Platform</Col>
                <Col span="14" class="info-valueo">{{ clusterInfo.platform }}</Col>
              </Row>
            </Card>
          </Col>
          <Col span="12">
            <Card>
              <h4 style="margin-bottom: 16px">Namespaces</h4>
              <Table size="small" height="316" :columns="NamespaceColumns" :data="NamespaceData"></Table>
            </Card>
          </Col>
        </Row>

        <Row style="margin-top: 16px">
          <Col span="24">
            <Card>
              <h4 style="margin-bottom: 16px">Nodes</h4>
              <Table size="small" :columns="NodesColumns" :data="NodesData"></Table>
            </Card>
          </Col>
        </Row>
      </Drawer>
    </div>
  </Container>
</template>

<script>
import { Container } from '@/components'
import { errorMessage, noticeError, noticeSucceed, noticeWarn } from '@/libs/util'
import { color } from '@/libs/consts'
import {
  ApiClusterCreate,
  ApiClusterDelete,
  ApiClusterDetail,
  ApiClusterList,
  ApiClusterRestartConnector,
  ApiClusterSyncFromCmdb,
  ApiClusterUpdate
} from '@/api/k8s/cluster'
import { ApiPRAuthK8sClear, ApiPRAuthK8sGenerate } from '@/api/platformRsAuh'
import LinkButton from '@/components/link-button'
import Space from '@/components/space'
import linkButton from '@/components/link-button'

export default {
  name: 'k8s-cluster-config',
  components: { Container },
  data() {
    return {
      ClusterModal: false,
      clusterInfo: {
        name: 'k8s-hw-gz-yy-urrc-test',
        createdAt: '2022-03-17 09:52:18',
        dataResource: 'cmdb',
        isReady: 1,
        desc: '',
        version: 'v1.23.5',
        buildData: '2022-03-16 23:52:18',
        platform: 'linux/adm64'
      },
      NodesColumns: [
        {
          title: '名称',
          key: 'name'
        },
        {
          title: 'Status',
          key: 'phase',
          render: (h, params) => {
            let phase = params.row.phase === 1 ? 'Ready' : 'NotReady'
            let t = params.row.phase === 1 ? 'success' : 'default'
            return h(
              'Tag',
              {
                props: {
                  color: t
                }
              },
              phase
            )
          }
        },
        {
          title: 'Roles',
          key: 'roles',
          tooltipTheme: 'light',
          tooltip: true,
          render: (h, params) => {
            return h('div', {}, params.row.role.join(','))
          }
        },
        {
          title: 'INTERNAL-IP',
          key: 'internal_ip',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'Age',
          key: 'created_at'
        },
        {
          title: 'OS-IMAGE',
          key: 'image',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'CONTAINER-RUNTIME',
          key: 'runtime',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'KERNEL-VERSION',
          key: 'kernel_version',
          tooltipTheme: 'light',
          tooltip: true
        }
      ],
      NodesData: [],
      NamespaceData: [],
      NamespaceColumns: [
        {
          title: 'Name',
          key: 'name',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '状态',
          key: 'phase',
          width: 150,
          align: 'center',
          render: (h, params) => {
            var s = params.row.phase
            var color = 'error'
            if (s === 'Active') {
              color = 'success'
            }
            return h(
              'Tag',
              {
                props: {
                  color: color
                }
              },
              s
            )
          }
        }
      ],

      editModal: false,
      editModalTitle: '创建',
      editModalState: 'create',
      editModalRule: {
        name: [
          {
            required: true,
            message: 'Required name.',
            trigger: 'blur'
          }
        ],
        kubeconfig: [
          {
            required: true,
            message: 'Required kubeconfig.',
            trigger: 'blur'
          }
        ]
      },
      editModalForm: {
        id: null,
        name: '',
        regionId: '',
        desc: '',
        kubeconfig: ''
      },
      loading: {
        table: false,
        edit: true,
        clusterModalSpin: false
      },
      columns: [
        {
          type: 'selection',
          width: 50
        },
        {
          title: 'ID',
          key: 'id',
          tooltip: true,
          tooltipTheme: 'light',
          width: 90
        },
        {
          title: 'Name',
          key: 'name',
          width: 220,
          render: (h, params) => {
            return h(linkButton, {
              props: {
                text: params.row.name,
                ellipsis: true,
                tooltip: true
              },
              style: {
                fontWeight: 600
              },
              on: {
                click: () => {
                  this.GetClusterDetail(params.row.id)
                  this.ClusterModal = true
                }
              }
            })
          }
        },
        {
          title: 'ApiServer',
          key: 'api_server',
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center',
          width: 120
        },
        {
          title: '状态',
          key: 'is_ready',
          width: 100,
          align: 'center',
          filters: [
            {
              label: '就绪',
              value: 1
            },
            {
              label: '未就绪',
              value: 0
            }
          ],
          filterMultiple: false,
          filterRemote: async (values, row) => {
            console.log(values)
            console.log(row)
            this.page.page = 1
            if (values.length === 0) {
              delete this.page.f_is_ready
              this.ReloadTable()
              return
            }
            this.page['f_is_ready'] = values[0]
            this.ReloadTable()
          },
          tooltip: true,
          tooltipTheme: 'light',
          render: (h, params) => {
            let colorMap = {
              1: 'success',
              0: 'error'
            }

            let textMap = {
              1: '就绪',
              0: '未就绪'
            }
            return h(
              'Tag',
              {
                props: {
                  color: colorMap[params.row.is_ready]
                }
              },
              textMap[params.row.is_ready]
            )
          }
        },
        {
          title: 'UUID',
          key: 'uuid',
          width: 100,
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '云商',
          key: 'cloud',
          width: 100,
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: '区域',
          key: 'region_id',
          width: 120,
          align: 'center',
          tooltipTheme: 'light',
          tooltip: true
        },

        {
          title: '描述',
          key: 'desc',
          tooltipTheme: 'light',
          tooltip: true
        },
        {
          title: 'UpdatedAt',
          key: 'updated_at',
          width: 180,
          align: 'center'
        },
        {
          title: '操作',
          key: 'ops',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h(Space, { props: { justify: true } }, [
              h(LinkButton, {
                props: {
                  text: '配置'
                },
                on: {
                  click: () => {
                    this.editModalState = 'update'
                    this.editModal = true
                    this.editModalTitle = '更新'
                    this.editModalForm = {
                      id: params.row.id,
                      name: params.row.name,
                      regionId: params.row.region_id,
                      desc: params.row.desc,
                      kubeconfig: params.row.kubeconfig
                    }
                  }
                }
              }),
              h(LinkButton, {
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: 'Tips',
                      content: `<p>Confirm delete: ${params.row.name} ?.</p>`,
                      loading: true,
                      onOk: async () => {
                        await ApiClusterDelete(params.row.id)
                          .then((res) => {
                            noticeSucceed(this, 'succeed.')
                          })
                          .catch((err) => {
                            noticeError(this, err)
                          })
                        this.ReloadTable()
                        this.$Modal.remove()
                      }
                    })
                  }
                },
                props: {
                  text: '删除',
                  type: 'danger'
                }
              })
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        s_name: '',
        f_is_ready: null,
        total: 0
      },
      clusterIdSelectionList: []
    }
  },
  methods: {
    handleClusterTableSelectionChange(selections) {
      this.clusterIdSelectionList = selections.map((item) => {
        return item.id
      })
      console.log(this.clusterIdSelectionList)
    },
    ClearClusterAuthData() {
      if (this.clusterIdSelectionList.length === 0) {
        noticeWarn(this, '需要先选中集群.')
        return
      }
      this.$Modal.confirm({
        title: '清除集群权限',
        content: `<p>清除集群权限及其关系, 集群ID: ${this.clusterIdSelectionList[0]}，确认操作 ?</p>`,
        loading: true,
        onOk: () => {
          ApiPRAuthK8sClear(this.clusterIdSelectionList[0])
            .then((res) => {
              noticeSucceed(this, '清理成功')
            })
            .catch((err) => {
              noticeError(this, `清理失败， ${errorMessage(err)}`)
            })
            .finally(() => {
              setTimeout(() => {
                this.$Modal.remove()
              }, 2000)
            })
        }
      })
    },
    GenerateClusterAuthData() {
      this.$Modal.confirm({
        title: '资源权限,',
        content: '<p>生成所有集群的资源权限, 由于资源数据较多需要几分钟的时间， 请耐心等待。 确认操作 ?</p>',
        loading: true,
        onOk: () => {
          ApiPRAuthK8sGenerate()
            .then((res) => {
              noticeSucceed(this, '发送请求成功.')
            })
            .catch((err) => {
              noticeError(this, `发送请求失败: ${errorMessage(err)}`)
            })
            .finally(() => {
              setTimeout(() => {
                this.$Modal.remove()
              }, 3000)
            })
        }
      })
    },
    RestartConnector() {
      this.$Modal.confirm({
        title: '重启连接器',
        content: '<p>Confirm operation ?</p>',
        loading: true,
        onOk: () => {
          ApiClusterRestartConnector()
            .then((res) => {
              noticeSucceed(this, 'succeed.')
              this.$Modal.remove()
            })
            .catch((err) => {
              noticeError(this, errorMessage(err))
              this.$Modal.remove()
            })
        }
      })
    },
    SyncFromCmdb() {
      this.$Modal.confirm({
        title: '同步集群数据',
        content: '<p>Confirm operation ?</p>',
        loading: true,
        onOk: () => {
          ApiClusterSyncFromCmdb()
            .then((res) => {
              noticeSucceed(this, 'succeed.')
              this.$Modal.remove()
            })
            .catch((err) => {
              noticeError(this, errorMessage(err))
              this.$Modal.remove()
            })
        }
      })
    },
    CreateConfig() {
      this.$refs['editModalForm'].resetFields()
      this.editModal = true
      this.editModalState = 'create'
    },
    async create() {
      await ApiClusterCreate({
        name: this.editModalForm.name,
        regionId: this.editModalForm.regionId,
        desc: this.editModalForm.desc,
        kubeconfig: this.editModalForm.kubeconfig
      })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      this.ReloadTable()
    },
    async update() {
      await ApiClusterUpdate(this.editModalForm.id, this.editModalForm)
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
        })
      this.ReloadTable()
    },
    async editTable() {
      if (this.editModalState === 'create') {
        await this.create()
      } else {
        await this.update()
      }
      setTimeout(() => {
        this.editModal = false
      }, 1000)
    },

    async ReloadTable() {
      this.loading.table = true
      await ApiClusterList(this.page)
        .then((res) => {
          var data = res.data.data
          this.page.total = data.total
          this.data = data.list
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
      this.loading.table = false
    },
    pageChange(page) {
      this.page.page = page
      this.ReloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.ReloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.ReloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.ReloadTable()
    },
    async GetClusterDetail(cid) {
      this.loading.clusterModalSpin = true
      await ApiClusterDetail(cid)
        .then((res) => {
          let data = res.data.data
          let ds = data.isSync === 1 ? 'cmdb' : 'local'
          this.clusterInfo = {
            name: data.name,
            createdAt: data.createdAt,
            dataResource: ds,
            isReady: data.isReady,
            desc: data.desc,
            version: data.version.git_version,
            buildData: data.version.build_date,
            platform: data.version.platform
          }
          this.NodesData = data.nodes
          this.NamespaceData = data.namespaces
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
      this.loading.clusterModalSpin = false
    }
  },
  mounted() {
    this.ReloadTable()
    this.$refs.input.focus({
      cursor: 'start'
    })
  }
}
</script>

<style scoped>
.info-key {
  color: #808695;
}

.info-value {
  color: #515a6e;
  font-family: Consolas, Menlo, Bitstream Vera Sans Mono, Monaco, 微软雅黑, monospace !important;
}
</style>
