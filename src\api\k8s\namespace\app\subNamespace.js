import axios from '@/libs/api.request'



export const ApiSubNamespaceList = (params) => {
  return axios.request({
    url: `/api/v1/subnamespace/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiSubNamespaceDelete = (data) => {
  return axios.request({
    url: `/api/v1/subnamespace/delete`,
    method: 'delete',
    data: data,
  })
}


export const ApiSubNamespaceCreate = (data) => {
  return axios.request({
    url: `/api/v1/subnamespace/create`,
    method: 'post',
    data: data,
  })
}

export const ApiSubNamespaceResourceNameList = (params) => {
  return axios.request({
    url: `/api/v1/subnamespace/resource-name/list`,
    method: 'get',
    params: params,
  })
}

export const ApiSubNamespaceResourceCopy = (data) => {
  return axios.request({
    url: `/api/v1/subnamespace/resource/copy`,
    method: 'post',
    data: data,
  })
}



