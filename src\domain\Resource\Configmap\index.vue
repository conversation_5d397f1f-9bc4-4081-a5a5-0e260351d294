<template>
  <div>
    <Alert show-icon>
      当前页面中您可以管理 Configmap 资源. 请谨慎操作修改，因为
      <span style="color: #ed4014">不当的修改</span> 可能会影响已部署的服务.
    </Alert>
    <Card v-if="type === 'Independent'" style="margin-bottom: 16px" :bordered="false">
      <Row style="margin-bottom: 16px">
        <Col span="24">
          <Input
            ref="input"
            v-model="page.search"
            clearable
            placeholder="搜索 Configmap 名称"
            search
            enter-button
            style="width: 100%"
            @on-search="handleSearch"
            @on-clear="handleClear"
          />
        </Col>
      </Row>
      <Row type="flex">
        <Col span="12">
          <Button type="primary" size="small" icon="ios-add" @click="handleCreate">创建</Button>
          <Button style="margin-left: 16px" size="small" type="primary" ghost icon="ios-copy" @click="() => batchCopyModalVisible = true">批量复制</Button>
          <Button style="margin-left: 16px" size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable">刷新</Button>
        </Col>
      </Row>
    </Card>
    <Button v-else style="margin-bottom: 16px" size="small" type="primary" ghost icon="ios-refresh" @click="reloadTable"
      >刷新</Button
    >
    <Card v-if="type === 'Independent'" :bordered="false">
      <Table size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
      <Page
        style="margin-top: 16px"
        :current="page.page"
        :page-size="page.size"
        :total="page.total"
        show-sizer
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-total
      >
      </Page>
    </Card>
    <Table v-else size="small" :loading="loading.table" :columns="columns" :data="data"></Table>
    <div>
      <Drawer :title="yamlTitle" width="60" v-model="openYaml">
        <Alert show-icon>没有修改版本原因: 未从平台做过配置修改。</Alert>
        <div>
          <Select
            @on-change="setCurrentYaml"
            @on-clear="
              () => {
                this.handleYamlGet(this.currentResourceName)
              }
            "
            v-model="selectedVersionId"
            clearable
            placeholder="历史版本"
            style="width: 250px; margin-bottom: 16px"
          >
            <Option v-for="item in historyVersionList" :value="item.id" :key="item.id">{{ item.createdAt }}</Option>
          </Select>
        </div>
        <div style="max-height: 90%; overflow: auto">
          <yaml v-model="currentYaml" ref="refYaml" :forbiddenEdit="true" />
        </div>
      </Drawer>
    </div>
    <div>
      <Drawer :title="detailTitle" :closable="false" width="60" v-model="openDetail">
        <detail v-if="openDetail" :detail="detailObject"></detail>
      </Drawer>
    </div>
    <div>
      <Drawer :title="editTitle" width="60" v-model="openEdit" :styles="styles" :mask-closable="false">
        <edit-form
          v-if="openEdit"
          ref="editForm"
          :cluster-id="currentClusterId"
          :namespace="currentNamespace"
          :create-or-update="editMode"
          :resource-name="editName"
          @createWithYaml="createWithYaml"
          @updateWithYaml="updateWithYaml"
        ></edit-form>
        <div class="drawer-footer">
          <Button :loading="loading.edit" style="margin-right: 16px" type="primary" @click="handleCommit">提交</Button>
          <Button @click="openEdit = false">取消</Button>
        </div>
      </Drawer>
    </div>
    <BatchCopy v-model="batchCopyModalVisible" resourceType="ConfigMap" />
  </div>
</template>

<script>
import { LinkButton, Yaml } from '@/components'
import { errorMessage, noticeError, noticeSucceed } from '@/libs/util'
import { color } from '@/libs/consts'
import { relativeTime } from '@/libs/tools'
import { ApiResourceOpRecordList, ApiResourceYamlGet } from '@/api/k8s/resource'
import Detail from './components/detail'
import EditForm from './components/editForm'
import { BatchCopy } from '@/domain/Resource'
import {
  ApiConfigmapDelete,
  ApiConfigmapGet,
  ApiConfigmapYamlCreate,
  ApiConfigmapYamlUpdate
} from '@/api/k8s/namespace/configmap'
import { EnumComponentType } from '@/domain/Resource/config'
import Config from '@/config'
import { useGet } from '@/libs/service.request'
import useSingleK8SService from '@/libs/useSingleK8SService'

export default {
  name: 'k8s-configmap',
  components: { Yaml, Detail, EditForm, BatchCopy },
  props: { type: { type: EnumComponentType, default: EnumComponentType.Independent } },
  setup() {
    const { K8SKey } = useSingleK8SService()
    return { K8SKey }
  },
  data() {
    return {
      batchCopyModalVisible: false,
      styles: {
        // height: 'calc(100% - 55px)',
        overflow: 'unset',
        // paddingBottom: '53px',
        position: 'static'
      },
      selectedVersionId: null,
      historyVersionList: [],
      historyVersionSelected: '',
      openYaml: false,
      yamlTitle: '详情',
      currentYaml: 'apiVersion:\nkind:\nmetadata:\nspec:',
      detailTitle: '详情',
      detailObject: undefined,
      openDetail: false,
      loading: {
        table: false,
        edit: false
      },
      editTitle: '',
      openEdit: false,
      editMode: 'create',
      editName: '',
      currentResourceName: '',
      columns: [
        {
          title: 'Uuid',
          key: 'uuid',
          tooltip: true,
          tooltipTheme: 'light',
          width: 200
        },
        {
          title: 'Name',
          key: 'name',
          minWidth: 150,
          render: (h, params) => {
            return h(LinkButton, {
              props: {
                text: params.row.name,
                ellipsis: true,
                tooltip: true
              },
              style: {
                fontWeight: 600
              },
              on: {
                click: async () => {
                  this.detailTitle = `详情 ${params.row.name}`
                  await ApiConfigmapGet({
                    clusterId: this.currentClusterId,
                    namespace: this.currentNamespace,
                    name: params.row.name
                  })
                    .then((res) => {
                      this.openDetail = true
                      this.detailObject = res.data.data.data
                    })
                    .catch((err) => {
                      this.$Message.error(errorMessage(err))
                      throw err
                    })
                }
              }
            })
          }
        },
        {
          title: 'Data',
          key: 'data',
          tooltipTheme: 'light',
          tooltip: true,
          align: 'center',
          width: 120
        },
        {
          title: 'Age',
          key: 'age',
          tooltip: true,
          tooltipTheme: 'light',
          width: 180,
          render: (h, params) => {
            return h('div', {}, relativeTime(params.row.creationTimestamp))
          }
        },
        {
          title: 'Ops',
          key: 'ops',
          width: 160,
          align: 'center',
          render: (h, params) => {
            return h('div', {}, [
              h(
                'a',
                {
                  style: {
                    cursor: 'pointer',
                    color: color.primary,
                    marginRight: '16px'
                  },
                  on: {
                    click: async () => {
                      this.openYaml = true
                      this.yamlTitle = `${params.row.name}`
                      this.fetchHistoryVersionList(params.row.uuid)
                      await this.handleYamlGet(params.row.name)
                      this.currentResourceName = params.row.name
                      this.$nextTick(() => {
                        this.$refs.refYaml.refresh()
                      })
                    }
                  }
                },
                'YAML'
              ),
              ...(this.type === EnumComponentType.Independent
                ? [
                    h(
                      'a',
                      {
                        style: {
                          cursor: 'pointer',
                          color: color.primary,
                          marginRight: '16px'
                        },
                        on: {
                          click: () => {
                            this.handleUpdate(params.row.name)
                          }
                        }
                      },
                      '编辑'
                    ),
                    h(
                      'a',
                      {
                        style: {
                          cursor: 'pointer',
                          color: color.error
                        },
                        on: {
                          click: () => {
                            this.$Modal.confirm({
                              title: 'Tips',
                              content: `<p>确认删除: <b>${params.row.name}</b></p>`,
                              loading: true,
                              onOk: async () => {
                                await this.deleteRecord(params.row.name)
                                this.reloadTable()
                                this.$Modal.remove()
                              }
                            })
                          }
                        }
                      },
                      '删除'
                    )
                  ]
                : [])
            ])
          }
        }
      ],
      data: [],
      page: {
        page: 1,
        size: 10,
        search: this.$route.query?.name ?? '',
        total: 0,
        clusterId: '',
        namespace: ''
      }
    }
  },
  computed: {
    currentNamespace() {
      return this.type === EnumComponentType.Independent
        ? this.$store.state.k8s.currentNamespace
        : this.$route.query.namespace
    },
    currentClusterId() {
      return this.type === EnumComponentType.Independent
        ? this.$store.state.k8s.currentClusterId
        : this.$route.query.clusterId
    },
    relativeName() {
      return this.type === EnumComponentType.AssociatedDeployment
        ? this.$route.query.deployment
        : this.$route.query.name
    }
  },
  methods: {
    handleCreate() {
      this.openEdit = true
      this.editTitle = '创建'
      this.editMode = 'create'
    },
    handleUpdate(name) {
      this.openEdit = true
      this.editTitle = `编辑 ${name}`
      this.editMode = 'update'
      this.editName = name
    },
    handleCommit() {
      this.$refs.editForm.commitYamlOrForm()
    },
    async createWithYaml(data) {
      await ApiConfigmapYamlCreate(data)
        .then((res) => {
          noticeSucceed(this, '创建成功')
          this.openEdit = false
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, `创建失败; ${errorMessage(err)}`)
        })
    },
    async updateWithYaml(data) {
      await ApiConfigmapYamlUpdate(data)
        .then((res) => {
          noticeSucceed(this, '更新成功')
          this.openEdit = false
          this.reloadTable()
        })
        .catch((err) => {
          noticeError(this, `更新失败；${errorMessage(err)}`)
        })
    },
    setCurrentYaml(selectedId) {
      let yaml = ''
      this.historyVersionList.forEach((item) => {
        if (selectedId === item.id) {
          yaml = item.yaml
        }
      })
      this.$nextTick(() => {
        this.currentYaml = yaml
        this.$refs.refYaml.refresh()
      })
    },
    async deleteRecord(name) {
      await ApiConfigmapDelete({
        clusterId: parseInt(this.currentClusterId),
        namespace: this.currentNamespace,
        name: name
      })
        .then((res) => {
          noticeSucceed(this, 'succeed.')
        })
        .catch((err) => {
          noticeError(this, errorMessage(err))
          throw err
        })
    },
    async fetchHistoryVersionList(uuid) {
      await ApiResourceOpRecordList({
        uuid: uuid,
        clusterId: this.currentClusterId,
        namespace: this.namespace,
        kind: 'ConfigMap'
      })
        .then((res) => {
          this.historyVersionList = res.data.data.data
        })
        .catch((err) => {
          noticeError(this, `获取历史版本失败, ${errorMessage(err)}`)
          throw err
        })
    },
    async handleYamlGet(resourceName, kind) {
      let resource
      let group
      let version
      // if (kind === "多维度HPA") {
      //   resource = "scaledobjects"
      //   group = "keda.sh"
      //   version = "v1alpha1"
      // } else {
      resource = 'configmaps'
      group = ''
      version = 'v1'
      // }
      await ApiResourceYamlGet({
        resource: resource,
        group: group,
        version: version,
        cluster_id: this.currentClusterId,
        namespace: this.currentNamespace,
        is_edit: false,
        resource_name: resourceName
      })
        .then((res) => {
          console.log(res.data.data)
          this.currentYaml = res.data.data.data
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
    },
    async reloadTable() {
      this.loading.table = true
      this.page.clusterId = this.currentClusterId
      this.page.namespace = this.currentNamespace

      let path = ''
      let query = {}
      switch (this.type) {
        case EnumComponentType.Independent:
          path = Config.Api.GetConfigMapList
          query = {
            ...this.page
          }
          break

        case EnumComponentType.AssociatedDeployment:
          path = Config.Api.GetConfigMapListWidthDeployment
          query = {
            ...this.page,
            workloadName: this.relativeName
          }
          break
        default:
          path = Config.Api.GetConfigMapListWidthStatefulset
          query = {
            ...this.page,
            name: this.relativeName
          }
          break
      }

      await useGet(`${Config.Api.Base}${path}`, {
        params: query
      })
        .then((res) => {
          this.page.total = res.data.total
          this.data = this.type === EnumComponentType.Independent ? res.data?.list : res.data.data
        })
        .catch((err) => {
          this.$Message.error(errorMessage(err))
          throw err
        })
        .finally(() => {
          this.loading.table = false
        })
    },
    pageChange(page) {
      this.page.page = page
      this.reloadTable()
    },
    pageSizeChange(pageSize) {
      this.page.page = 1
      this.page.size = pageSize
      this.reloadTable()
    },
    handleSearch() {
      this.page.page = 1
      this.reloadTable()
    },
    handleClear() {
      this.page.page = 1
      this.reloadTable()
    }
  },
  mounted() {
    this.reloadTable()
    this.$refs.input?.focus({
      cursor: 'start'
    })
  },
  watch: {
    K8SKey() {
      this.reloadTable()
    }
  }
}
</script>

<style scoped></style>
