import axios from '@/libs/api.request'



export const ApiNamespaceOverviewList = (params) => {
  return axios.request({
    url: `/api/v1/cluster/namespace/overview/list`,
    method: 'get',
    data: {},
    params: params
  })
}


export const ApiNamespaceTelemetryEnvoyAccessLogGet = (params) => {
  return axios.request({
    url: `/api/v1/cluster/namespace/telemetry/envoy-accessLog/status/get`,
    method: 'get',
    params: params
  })
}


export const ApiNamespaceTelemetryEnvoyAccessLogSet = (data) => {
  return axios.request({
    url: `/api/v1/cluster/namespace/telemetry/envoy-accessLog/status/set`,
    method: 'post',
    data: data
  })
}




