import axios from '@/libs/api.request'

// 获取用户的kubeconfig状态
export const ApiGetActivationStatus = (user_id) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/activation-status/get',
    method: 'get',
    params: {
      user_id
    }
  })
}

// 改变激活状态
export const ApiUpdateActivationStatus = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/activation-status/update',
    method: 'post',
    data
  })
}

// 权限配置页的下载接口
export const ApiDownloadKubeConfig = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/manager/download',
    method: 'post',
    data
  })
}

// 获取kubeconfig列表
export const ApiGetKubeConfigList = (params) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/list',
    method: 'get',
    params
  })
}

// 新增global级别的权限
export const ApiCreateGlobalKubeConfigRole = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/global/bind',
    method: 'post',
    data
  })
}

// 新增namespace级别的权限
export const ApiCreateNsKubeConfigRole = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/namespace/bind',
    method: 'post',
    data
  })
}

// 删除kubeconfig权限
export const ApiDeleteKubeConfigRole = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/unbind',
    method: 'post',
    data
  })
}

// 修改kubeconfig权限
export const ApiUpdateKubeConfigRole = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/update',
    method: 'post',
    data
  })
}

// 重建kubeconfig权限
export const ApiRebuildKubeConfigRole = (data) => {
  return axios.request({
    url: '/api/v1/kubeconfig/auth-role/rebuild',
    method: 'post',
    data
  })
}

// 集群列表页的下载
export const ApiDownloadKubeConfigOnCluster = (cluster_id) => {
  return axios.request({
    url: '/api/v1/kubeconfig/download',
    method: 'post',
    data: {
      cluster_id
    }
  })
}

// kubeconfig合并下载
export const ApiKubeconfigMergeDownload = () => {
  return axios.request({
    url: '/api/v1/kubeconfig/merge-download',
    method: 'post'
  })
}
