<script lang="ts" setup>
import useStatefulSetDetailService from './useStatefulSetDetailService'
import { Yaml, Space, CommonIcon } from '@/components'
import LogMergeModal from '@/domain/Resource/LogMergeModal/index.vue'
import {
  ResourceSummary,
  WorkloadMeta,
  Service,
  Secrets,
  PVC,
  Configmap,
  WorkloadPodContainerChart,
  PodCardList
} from '@/domain/Resource'
import Hpa from './hpa/index.vue'

const statefulSetDetailService = useStatefulSetDetailService()
const {
  gotoAppDeployment,
  urlParams,

  onOpenYAML,
  yamlModal,

  onSubscribeOrNot,
  isSubscribe,
  rolloutRestart,
  loading,

  // replicaCount,
  // onOpenReplicaCountModal,

  // onSetReplicaCount,

  chosenTab,
  onResourceCardClick,
  currentPod,
  tabKeyMap,
  onReloadResource,

  logMergeModalVisible
} = statefulSetDetailService
</script>

<template>
  <div class="wrapper">
    <Space class="header">
      <Button size="small" type="text" icon="ios-arrow-back" @click="gotoAppDeployment">
        <b>返回</b>
      </Button>
      <Divider type="vertical" />
      <b>RayJob</b>
      <b
        >[ {{ urlParams.cluster }} / {{ urlParams.namespace }} /
        <b style="color: #2a7cc3"> {{ urlParams.name }} </b> ]</b
      >
      <Button size="small" type="primary" ghost icon="ios-paper-outline" @click="onOpenYAML">YAML</Button>
      <!-- <Button
        @click="onSubscribeOrNot"
        size="small"
        type="primary"
        ghost
        :icon="isSubscribe ? 'ios-megaphone' : 'ios-megaphone-outline'"
        >{{ isSubscribe ? '退订' : '订阅' }}</Button
      >
      <Button @click="onOpenReplicaCountModal" size="small" ghost type="primary" icon="ios-settings"
        >调整副本（当前：{{ replicaCount.count }}）</Button
      >
      <Button @click="rolloutRestart" size="small" type="error" ghost icon="ios-play">滚动重启</Button>

      <Poptip popper-class="more-options-popper">
        <div slot="content" class="more-options">
          <div @click.stop="() => (logMergeModalVisible = true)">日志合并</div>
        </div>

        <Button size="small" type="primary" ghost>
          更多选项
          <Icon type="ios-arrow-down" />
        </Button>
      </Poptip> -->

      <Button
        @click="() => onReloadResource()"
        type="warning"
        size="small"
        shape="circle"
        ghost
        icon="md-refresh"
        style="line-height: 24px"
      ></Button>

      <Button size="small" type="primary" ghost style="margin-left: auto">
        <common-icon type="_conversation1" />
        <a
          style="margin-left: 4px"
          target="_blank"
          href="https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=1eas73cf-b77c-43ec-95d1-926c1ed492c8"
        >
          联系我们
        </a>
      </Button>
    </Space>
    <div class="content-wrapper">
      <ResourceSummary
        v-if="JSON.stringify(urlParams) !== '{}'"
        type="Rayjob"
        :onResourceCardClick="onResourceCardClick"
        :clusterId="urlParams.clusterId"
        :namespace="urlParams.namespace"
        :name="urlParams.name"
      />
      <div>
        <Tabs type="card" v-model="chosenTab" name="statefulset">
          <!-- <TabPane label="元数据" name="meta">
            <WorkloadMeta
              :key="tabKeyMap.meta"
              v-if="chosenTab === 'meta'"
              :reloadFlag="loading"
              type="statefulset"
            ></WorkloadMeta>
          </TabPane> -->
          <TabPane label="运行时 (Pod)" name="runtime">
            <PodCardList
              v-show="chosenTab === 'runtime'"
              :key="tabKeyMap.runtime"
              v-model="currentPod"
              :entity="urlParams"
              kind="Rayjob"
            />
          </TabPane>
          <TabPane label="Service" name="service">
            <Card>
              <h4 style="margin-bottom: 16px">Service</h4>
              <Service :key="tabKeyMap.service" type="Rayjob" v-if="chosenTab === 'service'" />
            </Card>
          </TabPane>
          <TabPane label="HPA" name="hpa">
            <Hpa :key="tabKeyMap.hpa" :loading="loading" v-if="chosenTab === 'hpa'" :sysName="urlParams.name"></Hpa>
          </TabPane>
          <!-- <TabPane label="Configmap" name="configmap">
            <Card>
              <h4 style="margin-bottom: 16px">ConfigMap</h4>
              <Configmap :key="tabKeyMap.configmap" type="Rayjob" v-if="chosenTab === 'configmap'" />
            </Card>
          </TabPane>
          <TabPane label="Secret" name="secret">
            <Card>
              <h4 style="margin-bottom: 16px">Secrets（秘文管理）</h4>
              <Secrets :key="tabKeyMap.secret" type="AssociatedStatefulset" v-if="chosenTab === 'secret'" />
            </Card>
          </TabPane>
          <TabPane label="PVC" name="pvc">
            <Card>
              <h4 style="margin-bottom: 16px">PVC（持久卷声明）</h4>
              <PVC :key="tabKeyMap.pvc" type="AssociatedStatefulset" v-if="chosenTab === 'pvc'" />
            </Card>
          </TabPane>
          <TabPane label="服务网格" name="servicemesh" disabled> </TabPane>
          <TabPane label="监控图" name="monitor">
            <WorkloadPodContainerChart
              v-if="chosenTab === 'monitor'"
              :key="tabKeyMap.monitor"
              kind="StatefulSet"
              :reloadFlag="true"
              :entity="urlParams"
              :init-pod-name="currentPod.name"
            />
          </TabPane> -->
        </Tabs>
      </div>
    </div>
    <Drawer title="查看 YAML" :closable="false" width="60" v-model="yamlModal.visible">
      <yaml v-model="yamlModal.data" :forbiddenEdit="true"></yaml>
    </Drawer>
    <!-- <Modal title="调整副本数" width="15" v-model="replicaCount.visible">
      从 <InputNumber v-model="replicaCount.count" readonly /> 调整至
      <InputNumber :max="1000" :min="0" v-model="replicaCount.tempCount" />
      <template #footer>
        <Button @click="() => (replicaCount.visible = false)">取消</Button>
        <Button type="primary" @click="onSetReplicaCount">确认</Button>
      </template>
    </Modal> -->

    <LogMergeModal
      v-model="logMergeModalVisible"
      :cluster="urlParams.cluster"
      :clusterId="urlParams.clusterId"
      :namespace="urlParams.namespace"
      :workloadName="urlParams.name"
      workloadKind="StatefulSet"
    />
  </div>
</template>

<style lang="less" scoped>
.wrapper {
  .header {
    padding: 16px;
    display: inline-flex;
    align-items: center;
    border-bottom: 1px solid #e9e9e9;
  }
  .content-wrapper {
    margin-top: 12px;
    height: ~'calc(100vh - 57px)';
    padding: 0 16px;
    overflow: auto;
    // /deep/.ivu-tabs-tabpane {
    //   height: ~'calc(100vh - 32px - 16px - 130px - 58px - 32px)';
    //   overflow: hidden auto;
    // }
  }
  .more-options-popper {
    min-width: auto;
    .ivu-poptip-body {
      padding: 4px 8px;
    }
  }

  .more-options {
    width: 120px;

    div {
      cursor: pointer;
      padding: 8px 16px;
      &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }
  }
}
.space {
  > :not(:last-child) {
    margin-bottom: 8px;
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
</style>
