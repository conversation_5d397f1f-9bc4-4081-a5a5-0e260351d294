<template>
  <div>
    <Alert show-icon> 当前页面中您可以管理 Pod 资源, 可进行删除或重建容器操作; </Alert>
    <pod-search @reloadTable="getPodList" :podList="podList" />
    <pod-table :podList="podList" :loading="tableLoading" @reloadTable="getPodList" />
  </div>
</template>

<script>
import PodSearch from './components/pod-search.vue'
import PodTable from './components/pod-table.vue'

import { relativeTime } from '@/libs/tools'
import { errorMessage, noticeError } from '@/libs/util'
import { ApiGetPodList } from '@/api/k8s/pod'

export default {
  name: 'k8s-pod',
  components: {
    PodSearch,
    PodTable
  },

  created() {
    let podStatus = this.$route.query.status

    this.getPodList(podStatus ? { status: podStatus } : {})
  },
  computed: {
    currentNamespace() {
      this.$store.commit('getCurrentNamespace', this.$store.state.user.userId)
      return this.$store.state.k8s.currentNamespace
    },
    currentClusterId() {
      this.$store.commit('getCurrentClusterID', this.$store.state.user.userId)
      return this.$store.state.k8s.currentClusterId
    }
  },
  provide() {
    return {
      main: this
    }
  },
  data() {
    return {
      searchData: '',
      selectStatus: '',
      tableLoading: false,
      mTitle: '容器组 (Pods)',
      podList: {
        list: [],
        size: 10,
        page: 1,
        total: 0
      }
    }
  },
  methods: {
    // 获取pod list
    async getPodList(pageInfo = {}) {
      this.tableLoading = true
      try {
        const payload = {
          cluster_id: this.currentClusterId,
          namespace: this.currentNamespace,
          search: this.$route.query.name ?? '',
          page: 1,
          size: 10,
          ...pageInfo
        }
        const res = await ApiGetPodList(payload)
        // 处理table所需的数据
        const list = res.data.data.list.map((item) => {
          let containerCount = 0
          let readyCount = 0
          let restartCount = 0
          // 容器个数
          if (item.data.status.containerStatuses !== undefined) {
            containerCount = item.data.status.containerStatuses.length
            // 计算ready状态的容器个数
            readyCount = item.data.status.containerStatuses.reduce((pre, cur) => {
              if (cur.ready === true) return pre + 1
              return pre
            }, 0)

            restartCount = item.data.status.containerStatuses.reduce((pre, cur) => {
              return pre + cur.restartCount
            }, 0)
          }

          // 当ownerReferences长度不为0时判断为重建，否则为删除
          const isRebuild = !!(item.data.metadata.ownerReferences && item.data.metadata.ownerReferences.length)

          return {
            name: item.data.metadata.name,
            status: item.status,
            reason: item.reason,
            ready: `${readyCount}/${containerCount}`,
            restarts: restartCount,
            age: relativeTime(item.data.status.startTime),
            ip: item.data.status.podIP,
            node: item.data.status.hostIP,
            isRebuild,
            uid: item.data.metadata.uid,
            pc: item.data.spec.priorityClassName === undefined ? '-' : item.data.spec.priorityClassName,
            data: item.data, // 保存pod的数据
            cpu: item.metrics.cpuUsage,
            mem: item.metrics.memUsage,
            cpuLimit: item.metrics.cpuLimit,
            memLimit: item.metrics.memLimit,
            workload: item.workloadName,
            memRequest: item.metrics.memRequest,
            cpuRequest: item.metrics.cpuRequest
          }
        })
        this.podList = { ...res.data.data, list }
      } catch (error) {
        noticeError(this, `列表数据获取失败，${errorMessage(error)}`)
      }
      this.tableLoading = false
    }
  }
}
</script>

<style scoped></style>
