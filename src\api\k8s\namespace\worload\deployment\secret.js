import axios from '@/libs/api.request'


export const ApiDeploySecretList = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/secret/list`,
    method: 'get',
    data: {},
    params: params
  })
}

export const ApiDeploySecretGet = (params) => {
  return axios.request({
    url: `/api/v1/resource/deployment/related/secret/get`,
    method: 'get',
    data: {},
    params: params
  })
}

