export enum EnumComponentType {
  Associated = 'Associated', // 关联模式
  Independent = 'Independent' // 独立模式
}

export interface ResourceFormProps {
  /** 资源状态，默认Independent */
  type?: EnumComponentType.Associated | EnumComponentType.Independent
  /** 后端版本 - 不影响入参返参 */
  resourceVersion?: 'V1' | 'V2'
  /** 资源类型(api) */
  resourceType: String
  /** 表单打开 / 关闭状态 */
  value: Boolean
  /** 表单状态 */
  status: EnumFormStatus
  /** 资源实体 */
  resourceEntity: ResourceEntity
  /** 初始化的yaml数据 */
  yamlInitData?: string
  /** 初始化的表单数据 */
  formInitData?: Record<string, any>
  /** 提交成功回调 */
  onSubmitCallBack: () => void
  /** 请求表单数据的格式化回调 */
  onInitFormat?: (data: Record<string, any>) => Record<string, any>
  /** 提交表单数据的格式化回调 */
  onSubmitFormat?: (params: FormSubmitParams) => FormSubmitParams
  /** 表单宽度 */
  width?: number | string
  /** 不同步到统一集群 */
  notSynchronizeToUnifiedCluster?: boolean
  /** 预览的额外入参 */
  getPreviewParams?: () => Record<string, string>
  /** 强制禁用form模式 */
  forbiddenForm?: boolean
  /** 是否跳过内部检查 - 额外兼容不存在内部检查的资源 */
  isSkipCheck?: boolean
  /** 是否请求时只传集群，不传命名空间 */
  isSkipNamespaceInParams?: boolean
}

/** 资源表单模式 */
export enum EnumResourceFormModel {
  Yaml = 'Yaml',
  Form = 'Form'
}

/** 表单状态 */
export enum EnumFormStatus {
  Edit,
  //   Preview,
  Blank
}

export interface ResourceEntity {
  resourceName?: string // 资源唯一id
  clusterId?: string // 关联资源存在跟当前集群不一致的集群，需要特别处理
  clusterName?: string // 关联资源存在跟当前集群不一致的集群，需要特别处理
  namespace?: string // 关联资源存在跟当前命名空间不一致的命名空间，需要特别处理
  resource: string // 资源分类
  group: string
  version: string
}

export interface FormSubmitParams {
  clusterId: string
  namespace?: string
  resourceName?: string
  isApplyToUnifiedCluster: boolean
  data: Record<string, any>
}
