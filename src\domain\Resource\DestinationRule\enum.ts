export function formatEnumToLabelValue(enumObj) {
  const arr: { value: string; label: string }[] = []
  for (const [value, label] of Object.entries(enumObj)) {
    arr.push({ value, label: label.toString() })
  }
  return arr
}

export enum EnumPorts {
  HTTP = 'HTTP',
  HTTPS = 'HTTPS',
  GRPC = 'GRPC',
  HTTP2 = 'HTTP2',
  MONGO = 'MONGO',
  TCP = 'TCP',
  TLS = 'TLS'
}
export enum EnumTrafficPoliciesType {
  default = 'Default（默认策略）',
  subset = 'Subset（子策略）'
}

export enum EnumComponentType {
  Associated = 'Associated', // 关联模式
  Independent = 'Independent' // 独立模式
}

export enum EnumBasic {
  simple = '简单模式', // 简单模式
  consistentHash = '会话保持' // 会话保持
}

export enum EnumLbPolicySimple {
  RANDOM = 'RANDOM',
  ROUND_ROBIN = 'ROUND_ROBIN',
  LEAST_REQUEST = 'LEAST_REQUEST',
  LEAST_CONN = 'LEAST_CONN'
}

export enum EnumConsistentHash {
  useSourceIp = 'useSourceIp（根据源IP）',
  httpHeaderName = 'httpHeaderName（根据请求头部名）',
  httpQueryParameterName = 'httpQueryParameterName（根据请求参数名）'
}

export enum EnumLocality {
  distribute = 'distribute（流量分发）',
  failover = 'Failover（故障转移）'
}

export enum EnumHTTP2UpgradePolicy {
  DEFAULT = 'DEFAULT',
  DO_NOT_UPGRADE = 'DO_NOT_UPGRADE',
  UPGRADE = 'UPGRADE'
}
