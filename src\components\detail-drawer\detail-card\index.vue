<script lang="ts" setup>
import { h, defineComponent, ref, useSlots, computed } from 'vue'
import { detailCardProps } from './type'

const slots = useSlots()
const props = defineProps(detailCardProps())

const render = defineComponent({
  props: ['vnode'],
  setup(props) {
    return () => props.vnode
  }
})

// 获取子级插槽名
const childrenSlotNames = computed(() => {
  return props?.config.filter((item) => item.slot).map((i) => i.slot)
})

// 其它父级插槽名
const otherParentSlotNames = computed(() => {
  return Object.keys(slots).filter((i) => !childrenSlotNames.value?.includes(i) && i !== 'default')
})
</script>

<template>
  <div>
    <Card class="detail-card-wrapper">
      <h4>{{ props.title }}</h4>

      <slot name="default" :data="props.data">
        <Row v-for="configItem in props.config" :key="configItem.key" type="flex" :gutter="20" style="margin-top: 16px">
          <template v-if="configItem.key">
            <Col span="10" class="info-key">{{ configItem.title }}</Col>

            <Col span="14" class="info-value">
              <template v-if="configItem.render">
                <render :vnode="configItem.render(h, props.data)" />
              </template>
              <template v-else-if="configItem.slot">
                <slot :name="configItem.slot" :data="props.data" />
              </template>
              <template v-else>
                <Col span="14" class="info-value">{{ props.data[configItem.key] }}</Col>
              </template>
            </Col>
          </template>
        </Row>
      </slot>

      <template v-for="slotName in otherParentSlotNames">
        <slot :name="slotName" :data="props.data" />
      </template>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.info-key {
  font-size: 13px;
  font-weight: bold;
  color: #555555;
}

.info-value {
  font-size: 13px;
  color: #111e3c;
}
</style>
