<script lang="ts" setup>
import { PropType } from 'vue'
import { useRequest } from 'vue-request'

import Config from '@/config'
import { Space } from '@/components'
import { useGet } from '@/libs/service.request'

const props = defineProps({
  type: String as PropType<'statefulset' | 'deployment' | 'Rayjob'>,
  onResourceCardClick: Function as PropType<(tabName: string) => void>,
  clusterId: String,
  namespace: String,
  name: String
})
const apiMap = {
  statefulset: {
    getSummary: Config.Api.GetStatefulsetSummary
  },
  deployment: {
    getSummary: Config.Api.GetDeploymentSummary
  },
  rayjob: {
    getSummary: Config.Api.GetRayJobSummary
  }
}
const { data: briefCount } = useRequest(
  () => {
    return useGet(`${Config.Api.Base}${apiMap[props.type].getSummary}`, {
      params: {
        clusterId: props.clusterId,
        namespace: props.namespace,
        ...(props.type !== 'statefulset' ? { workloadName: props.name } : { name: props.name })
      }
    })
  },
  {
    formatResult: (res) => (props.type === 'statefulset' ? res.data.data : res.data),
    initialData: {}
  }
)
</script>

<template>
  <div class="resource-summary">
    <Collapse simple value="1">
      <Panel name="1">
        <b>资源概要</b>
        <div slot="content" class="resource-summary-content">
          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('runtime')">
              <Avatar :class="`summary-item-counter pod ${briefCount.pod === 0 && 'zero'}`">{{
                briefCount.pod
              }}</Avatar>
              <span :class="`summary-item-title ${briefCount.pod === 0 && 'zero'}`">Pod</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('service')">
              <Avatar :class="`summary-item-counter service ${briefCount.service === 0 && 'zero'}`">{{
                briefCount.service
              }}</Avatar>
              <span :class="`summary-item-title ${briefCount.service === 0 && 'zero'}`">Service</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('hpa')">
              <Avatar :class="`summary-item-counter hpa ${briefCount.hpa === 0 && 'zero'}`">{{
                briefCount.hpa
              }}</Avatar>
              <span :class="`summary-item-title ${briefCount.hpa === 0 && 'zero'}`">HPA</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('configmap')">
              <Avatar :class="`summary-item-counter configmap ${briefCount.configmap === 0 && 'zero'}`">{{
                briefCount.configmap
              }}</Avatar>
              <span :class="`summary-item-title ${briefCount.configmap === 0 && 'zero'}`">Configmap</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('secret')">
              <Avatar :class="`summary-item-counter secret ${briefCount.secret === 0 && 'zero'}`">
                {{ briefCount.secret }}
              </Avatar>
              <span :class="`summary-item-title ${briefCount.secret === 0 && 'zero'}`">Secret</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('pvc')">
              <Avatar :class="`summary-item-counter pvc ${briefCount.pvc === 0 && 'zero'}`">{{
                briefCount.pvc
              }}</Avatar>
              <span :class="`summary-item-title ${briefCount.pvc === 0 && 'zero'}`">PVC</span>
            </div>
          </Card>

          <Card :padding="16">
            <div class="summary-item" @click="props.onResourceCardClick('servicemesh')">
              <Space class="summary-item-counter">
                <Avatar size="small" :class="`vs ${briefCount.vs === 0 && 'zero'}`">
                  <b>{{ briefCount.vs }}</b>
                </Avatar>
                <Avatar size="small" :class="`dr ${briefCount.dr === 0 && 'zero'}`">
                  <b>{{ briefCount.dr }}</b>
                </Avatar>
                <Avatar size="small" :class="`gw ${briefCount.gw === 0 && 'zero'}`">
                  <b>{{ briefCount.gw }}</b>
                </Avatar>
              </Space>
              <span
                :class="`summary-item-title ${
                  (briefCount.vs === 0 || briefCount.dr === 0 || briefCount.gw === 0) && 'zero'
                }`"
                >服务网格</span
              >
            </div>
          </Card>
        </div>
      </Panel>
    </Collapse>
  </div>
</template>
<style lang="less" scope>
.resource-summary {
  .ivu-collapse {
    border: none;
    margin-bottom: 16px;
  }
  .ivu-collapse-content,
  .ivu-collapse-header,
  .ivu-collapse-content-box {
    padding: 0 !important;
  }
  .resource-summary-content {
    display: flex;
    align-items: center;
    .ivu-card {
      flex: 1 0 0%;
      &:not(:last-child) {
        margin-right: 16px;
      }
    }
  }
  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 58px;
    .summary-item-counter {
      margin-bottom: 8px;
      font-weight: 600;
      display: flex;
      justify-content: center;
      &.pod {
        background: #c4a4fa;
      }
      &.service {
        background: #4e83fd8c;
      }
      &.hpa {
        background: #62d257;
      }
      &.pvc {
        background: #fa8c16;
      }
      &.configmap {
        background: #f76964;
      }
      &.secret {
        background: #eb2f96;
      }
      .vs {
        background: #722ed1;
      }
      .dr {
        background: #722ed1c9;
      }
      .gw {
        background: #722ed1a8;
      }
      &.zero,
      .zero {
        background: #c5c8ce;
      }
    }
    .summary-item-title {
      font-size: 12px;
      font-weight: 600;
      color: #515a6e;
      &.zero {
        color: #c5c8ce;
      }
    }
  }
}
</style>
